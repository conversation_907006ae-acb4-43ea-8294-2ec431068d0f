<?php

class Upload123Pan
{

    /**
     * API 根地址
     */
    // 定义 API 的根 URL, 方便统一管理
    const API_BASE_URL = 'https://open-api.123pan.com';

    /**
     * @var string 客户端ID
     */
    // 用于存储客户端 ID
    private $clientID;

    /**
     * @var string 客户端密钥
     */
    // 用于存储客户端密钥
    private $clientSecret;

    /**
     * @var string 访问令牌
     */
    // 用于存储从 API 获取的访问令牌 (Access Token)
    private $accessToken;

    /**
     * @var string 令牌过期时间 (ISO 8601 格式)
     */
    // 用于存储访问令牌的过期时间
    private $expiredAt;

    /**
     * 构造函数
     * @param string $clientID 客户端ID
     * @param string $clientSecret 客户端密钥
     */
    public function __construct($clientID, $clientSecret)
    {
        // 初始化时，将传入的客户端 ID 赋值给类的属性
        $this->clientID = $clientID;
        // 初始化时，将传入的客户端密钥赋值给类的属性
        $this->clientSecret = $clientSecret;
    }

    /**
     * 设置 Access Token.
     * 调用者负责维护和传入有效的 token.
     *
     * @param string $token Access Token 字符串
     * @param string $expiredAt ISO 8601 格式的过期时间字符串.
     */
    public function setAccessToken($token, $expiredAt)
    {
        // 将外部传入的访问令牌字符串赋值给类的属性
        $this->accessToken = $token;
        // 将外部传入的过期时间字符串赋值给类的属性
        $this->expiredAt = $expiredAt;
    }

    /**
     * 从 API 获取一个新的 Access Token.
     * 调用此方法会立即发起网络请求获取新 Token.
     * 调用方负责缓存此方法的返回结果.
     *
     * @return array 成功返回包含 'accessToken' 和 'expiredAt' 的数组.
     * @throws \Exception
     */
    public function getAccessToken()
    {
        // 设置获取 Access Token 的完整 API 端点 URL
        $url = self::API_BASE_URL . '/api/v1/access_token';

        // 准备请求体数据，包含 clientID 和 clientSecret
        $postData = [
            'clientID' => $this->clientID,
            'clientSecret' => $this->clientSecret
        ];

        // 准备请求头，指定平台和内容类型为 JSON
        $headers = [
            'Platform: open_platform',
            'Content-Type: application/json'
        ];

        // 调用私有的 _request 方法发起 POST 请求
        $requestResult = $this->_request('POST', $url, $headers, json_encode($postData));

        // 检查 cURL 请求本身是否发生错误（如网络问题）
        if ($requestResult['error']) {
            // 如果有 cURL 错误，则抛出异常并附上错误信息
            throw new \Exception('cURL Error: ' . $requestResult['error']);
        }

        // 检查 HTTP 响应状态码是否为 200 (成功)
        if ($requestResult['httpCode'] !== 200) {
            // 如果状态码不是 200，则抛出异常并附上状态码和响应体
            throw new \Exception('HTTP Error: ' . $requestResult['httpCode'] . ' Response: ' . $requestResult['response']);
        }

        // 将获取到的 JSON 格式的响应字符串解码为 PHP 数组
        $result = json_decode($requestResult['response'], true);

        // 检查 JSON 解码过程是否出错
        if (json_last_error() !== JSON_ERROR_NONE) {
            // 如果 JSON 解码失败，则抛出异常并附上错误信息
            throw new \Exception('JSON Decode Error: ' . json_last_error_msg());
        }

        // 检查解码后的结果是否符合预期结构（code 为 0 且包含 accessToken）
        if (isset($result['code']) && $result['code'] === 0 && isset($result['data']['accessToken'])) {
            // 如果一切正常，返回包含 Token 信息的 data 部分
            return $result['data'];
        }

        // 如果 API 返回的业务码不为 0 或结构不符，则抛出异常
        throw new \Exception('API Error on getting access token: ' . (isset($result['message']) ? $result['message'] : $requestResult['response']));
    }

    /**
     * 创建目录
     *
     * @param string $name 目录名称
     * @param int $parentID 父级目录ID，根目录为 0
     * @return int 成功返回目录ID
     * @throws \Exception
     */
    public function createDirectory($name, $parentID = 0)
    {
        // 设置创建目录的完整 API 端点 URL
        $url = self::API_BASE_URL . '/upload/v1/file/mkdir';

        // 准备并编码要发送的 JSON 数据，包含目录名和父目录ID
        $postData = json_encode([
            'name' => $name,
            'parentID' => $parentID,
        ]);

        // 准备请求头，包含认证用的 Bearer Token
        $headers = [
            'Content-Type: application/json',
            'Platform: open_platform',
            'Authorization: Bearer ' . $this->accessToken
        ];

        // 调用私有方法发起 POST 请求
        $requestResult = $this->_request('POST', $url, $headers, $postData);

        // 检查 cURL 请求本身是否发生错误
        if ($requestResult['error']) {
            // 如果有 cURL 错误，则抛出异常
            throw new \Exception('cURL Error: ' . $requestResult['error']);
        }

        // 检查 HTTP 响应状态码是否为 200 (成功)
        if ($requestResult['httpCode'] !== 200) {
            // 如果状态码不是 200，则抛出异常
            throw new \Exception('HTTP Error: ' . $requestResult['httpCode'] . ' Response: ' . $requestResult['response']);
        }

        // 将获取到的 JSON 响应解码为 PHP 数组
        $result = json_decode($requestResult['response'], true);

        // 检查 JSON 解码过程是否出错
        if (json_last_error() !== JSON_ERROR_NONE) {
            // 如果解码失败，抛出异常
            throw new \Exception('JSON Decode Error: ' . json_last_error_msg());
        }

        // 检查 API 响应是否成功并包含目录 ID
        if (isset($result['code']) && $result['code'] === 0 && isset($result['data']['dirID'])) {
            // 返回新创建的目录 ID
            return $result['data']['dirID'];
        }

        // 如果 API 返回业务错误，则抛出异常
        throw new \Exception('API Error on creating directory: ' . (isset($result['message']) ? $result['message'] : $requestResult['response']));
    }

    /**
     * 创建文件（预上传）
     *
     * @param string $localFilePath 本地文件路径
     * @param int $parentFileID 父级目录ID
     * @param string|null $remoteFilename 在网盘上显示的名称，为 null 则使用本地文件名
     * @param array $extraOptions 额外的创建文件选项，如 ['containDir' => true]
     * @return array 成功返回预上传信息(或秒传信息)
     * @throws \Exception
     */
    public function createFileEntry($localFilePath, $parentFileID = 0, $remoteFilename = null, array $extraOptions = [])
    {
        // 检查本地文件是否存在且可读
        if (!file_exists($localFilePath) || !is_readable($localFilePath)) {
            // 如果文件不存在或不可读，抛出异常
            throw new \Exception('File not found or is not readable: ' . $localFilePath);
        }

        // 如果未指定远程文件名，则使用本地文件名作为默认值
        $filename = is_null($remoteFilename) ? basename($localFilePath) : $remoteFilename;
        // 获取本地文件的精确大小（字节）
        $fileSize = filesize($localFilePath);
        // 计算本地文件的 MD5 值，作为文件的 etag
        $fileEtag = md5_file($localFilePath);

        // 设置创建文件（预上传）的 API 端点
        $url = self::API_BASE_URL . '/upload/v2/file/create';

        // 准备预上传请求的基础数据
        $postData = [
            'parentFileID' => $parentFileID,
            'filename' => $filename,
            'etag' => $fileEtag,
            'size' => $fileSize,
        ];

        // 将额外的选项（如自动创建目录）合并到请求数据中
        $postData = array_merge($postData, $extraOptions);

        // 将整个请求数据编码为 JSON 字符串
        $postData = json_encode($postData);

        // 准备请求头，包含认证信息
        $headers = [
            'Content-Type: application/json',
            'Platform: open_platform',
            'Authorization: Bearer ' . $this->accessToken
        ];

        // 调用私有方法发起 POST 请求
        $requestResult = $this->_request('POST', $url, $headers, $postData);

        // 检查 cURL 请求错误
        if ($requestResult['error']) {
            throw new \Exception('cURL Error: ' . $requestResult['error']);
        }
        // 检查 HTTP 状态码
        if ($requestResult['httpCode'] !== 200) {
            throw new \Exception('HTTP Error: ' . $requestResult['httpCode'] . ' Response: ' . $requestResult['response']);
        }

        // 解码 JSON 响应
        $result = json_decode($requestResult['response'], true);

        // 检查 JSON 解码错误
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception('JSON Decode Error: ' . json_last_error_msg());
        }

        // 检查 API 是否成功返回数据
        if (isset($result['code']) && $result['code'] === 0 && isset($result['data'])) {
            // 成功则返回预上传信息
            return $result['data'];
        }

        // 如果 API 返回业务错误，则抛出异常
        throw new \Exception('API Error on creating file entry: ' . (isset($result['message']) ? $result['message'] : $requestResult['response']));
    }

    /**
     * 通过分片上传完整文件
     *
     * @param string $localFilePath 本地文件路径
     * @param int $parentFileID 父级目录ID
     * @param string|null $remoteFilename 在网盘上显示的名称，为 null 则使用本地文件名
     * @param array $extraOptions 额外的创建文件选项，如 ['containDir' => true]
     * @return int 成功返回文件ID
     * @throws \Exception
     */
    public function uploadFile($localFilePath, $parentFileID = 0, $remoteFilename = null, array $extraOptions = [])
    {
        // 第一步：调用创建文件条目方法，获取预上传信息
        $preUploadInfo = $this->createFileEntry($localFilePath, $parentFileID, $remoteFilename, $extraOptions);

        // 检查 API 是否返回秒传成功标志
        if (isset($preUploadInfo['reuse']) && $preUploadInfo['reuse'] === true && isset($preUploadInfo['fileID'])) {
            // 如果是秒传，直接返回文件 ID，流程结束
            return $preUploadInfo['fileID'];
        }

        // 对于非秒传情况，检查响应中是否包含必要的分片上传信息
        if (!isset($preUploadInfo['preuploadID']) || !isset($preUploadInfo['sliceSize']) || !isset($preUploadInfo['servers'])) {
            // 如果缺少信息，则无法进行分片上传，抛出异常
            throw new \Exception('Missing pre-upload information from API response.');
        }

        // 从预上传信息中提取 preuploadID
        $preuploadID = $preUploadInfo['preuploadID'];
        // 提取分片大小
        $sliceSize = $preUploadInfo['sliceSize'];
        // 提取分片上传的目标服务器地址（通常只有一个）
        $uploadServerUrl = $preUploadInfo['servers'][0];
        // 拼接成完整的分片上传 URL
        $sliceUploadUrl = $uploadServerUrl . '/upload/v2/file/slice';

        // 以二进制只读模式打开本地文件
        $fileHandle = @fopen($localFilePath, 'rb');
        // 检查文件是否成功打开
        if (!$fileHandle) {
            // 如果打开失败，抛出异常
            throw new \Exception('Failed to open file for reading: ' . $localFilePath);
        }

        // 初始化分片编号，从 1 开始
        $sliceNo = 1;
        // 使用 try-finally 结构确保文件句柄在任何情况下都会被关闭
        try {
            // 循环读取文件，直到文件末尾
            while (!feof($fileHandle)) {
                $tempSliceHandle = null;
                try {
                    // 从文件中读取一个分片大小的数据块
                    $chunk = fread($fileHandle, $sliceSize);
                    // 如果读取失败或读取到的数据为空，则跳过此次循环
                    if ($chunk === false || strlen($chunk) === 0) {
                        continue;
                    }

                    // --- 将分片写入临时文件以进行流式上传 ---
                    // 创建一个唯一的临时文件，它会在关闭时自动删除
                    $tempSliceHandle = tmpfile();
                    // 将分片数据写入临时文件
                    fwrite($tempSliceHandle, $chunk);
                    // 获取临时文件的元数据，包括其路径
                    $tempSliceMeta = stream_get_meta_data($tempSliceHandle);
                    $tempSlicePath = $tempSliceMeta['uri'];
                    // 创建一个 CURLFile 对象，这会使 cURL 从文件流上传而不是内存变量
                    $sliceFile = new \CURLFile($tempSlicePath, 'application/octet-stream', 'slice' . $sliceNo);
                    // -----------------------------------------

                    // 计算当前读取出分片的 MD5 值
                    $sliceMD5 = md5($chunk);

                    // 准备分片上传的请求体，这是一个 multipart/form-data 格式的数组
                    $postBody = [
                        'preuploadID' => $preuploadID,
                        'sliceNo'   => $sliceNo,
                        'sliceMD5'  => $sliceMD5,
                        'slice'     => $sliceFile,
                    ];

                    // 准备分片上传的请求头
                    $headers = [
                        // 特殊标记，用于告知 _request 方法这是一个 multipart 请求
                        'Content-Type: multipart/form-data',
                        // 通过添加空的 Expect 头来禁用 '100-continue' 行为, 解决大文件上传时部分服务器的兼容性问题
                        'Expect:',
                        'Platform: open_platform',
                        'Authorization: Bearer ' . $this->accessToken
                    ];

                    // 发起分片上传请求
                    $requestResult = $this->_request('POST', $sliceUploadUrl, $headers, $postBody);

                    // 检查分片上传请求是否出错
                    if ($requestResult['error'] || $requestResult['httpCode'] !== 200) {
                        // 如果出错，抛出包含详细信息的异常
                        throw new \Exception('Failed to upload slice #' . $sliceNo . '. HTTP Code: ' . $requestResult['httpCode'] . '. Error: ' . $requestResult['error'] . '. Response: ' . $requestResult['response']);
                    }

                    // 解码分片上传的响应
                    $result = json_decode($requestResult['response'], true);
                    // 检查 API 是否返回分片上传错误
                    if (!isset($result['code']) || $result['code'] !== 0) {
                        // 如果有业务错误，抛出异常
                        throw new \Exception('API returned an error for slice #' . $sliceNo . ': ' . (isset($result['message']) ? $result['message'] : $requestResult['response']));
                    }

                    // 当前分片上传成功，分片编号自增，准备上传下一片
                    $sliceNo++;
                } finally {
                    // 确保每次循环后都关闭临时文件句柄，从而触发其自动删除
                    if (is_resource($tempSliceHandle)) {
                        fclose($tempSliceHandle);
                    }
                }
            }
        } finally {
            // 确保文件句柄被关闭，释放资源
            fclose($fileHandle);
        }

        // 所有分片上传完成后，调用"完成上传"方法，并返回最终的文件ID
        return $this->_completeUpload($preuploadID);
    }

    /**
     * 获取文件直链
     *
     * @param int $fileID 文件ID
     * @return string 成功返回直链URL
     * @throws \Exception
     */
    public function getDirectLink($fileID)
    {
        // 拼接获取文件直链的 API URL，并使用 http_build_query 处理查询参数
        $url = self::API_BASE_URL . '/api/v1/direct-link/url?' . http_build_query(['fileID' => $fileID]);

        // 准备请求头
        $headers = [
            'Platform: open_platform',
            'Authorization: Bearer ' . $this->accessToken
        ];

        // 发起 GET 请求
        $requestResult = $this->_request('GET', $url, $headers);

        // 检查请求是否出错
        if ($requestResult['error'] || $requestResult['httpCode'] !== 200) {
            throw new \Exception('HTTP Error: ' . $requestResult['httpCode'] . ' Error: ' . $requestResult['error'] . '. Response: ' . $requestResult['response']);
        }

        // 解码 JSON 响应
        $result = json_decode($requestResult['response'], true);

        // 检查解码是否成功，以及 API 是否成功返回直链 URL
        if (json_last_error() === JSON_ERROR_NONE && isset($result['code']) && $result['code'] === 0 && isset($result['data']['url'])) {
            // 返回获取到的直链 URL
            return $result['data']['url'];
        }

        // 如果 API 返回业务错误，则抛出异常
        throw new \Exception('API Error on getting direct link: ' . (isset($result['message']) ? $result['message'] : $requestResult['response']));
    }

    /**
     * 启用直链空间
     *
     * @param int $fileID 要启用直链空间的文件夹的 fileID
     * @return string 成功启用直链空间的文件夹的名称
     * @throws \Exception
     */
    public function enableDirectLinkSpace($fileID)
    {
        // 设置启用直链空间的 API 端点
        $url = self::API_BASE_URL . '/api/v1/direct-link/enable';

        // 准备请求体数据
        $postData = json_encode([
            'fileID' => $fileID,
        ]);

        // 准备请求头
        $headers = [
            'Content-Type: application/json',
            'Platform: open_platform',
            'Authorization: Bearer ' . $this->accessToken
        ];

        // 发起 POST 请求
        $requestResult = $this->_request('POST', $url, $headers, $postData);

        // 检查请求是否出错
        if ($requestResult['error'] || $requestResult['httpCode'] !== 200) {
            throw new \Exception('HTTP Error on enabling direct link space. Code: ' . $requestResult['httpCode'] . '. Error: ' . $requestResult['error'] . '. Response: ' . $requestResult['response']);
        }

        // 解码 JSON 响应
        $result = json_decode($requestResult['response'], true);

        // 检查解码是否成功，以及 API 是否成功返回文件名
        if (json_last_error() === JSON_ERROR_NONE && isset($result['code']) && $result['code'] === 0 && isset($result['data']['filename'])) {
            // 返回文件夹名称
            return $result['data']['filename'];
        }

        // 如果 API 返回业务错误，则抛出异常
        throw new \Exception('API Error on enabling direct link space: ' . (isset($result['message']) ? $result['message'] : $requestResult['response']));
    }

    /**
     * 获取单个文件详情
     *
     * @param int $fileID 文件ID
     * @return array 成功返回文件详情数组
     * @throws \Exception
     */
    public function getFileDetail($fileID)
    {
        // 拼接获取文件详情的 API URL
        $url = self::API_BASE_URL . '/api/v1/file/detail?' . http_build_query(['fileID' => $fileID]);

        // 准备请求头
        $headers = [
            'Platform: open_platform',
            'Authorization: Bearer ' . $this->accessToken
        ];

        // 发起 GET 请求
        $requestResult = $this->_request('GET', $url, $headers);

        // 检查请求是否出错
        if ($requestResult['error'] || $requestResult['httpCode'] !== 200) {
            throw new \Exception('HTTP Error on getting file detail. Code: ' . $requestResult['httpCode'] . '. Error: ' . $requestResult['error'] . '. Response: ' . $requestResult['response']);
        }

        // 解码 JSON 响应
        $result = json_decode($requestResult['response'], true);

        // 检查解码是否成功，以及 API 是否成功返回数据
        if (json_last_error() === JSON_ERROR_NONE && isset($result['code']) && $result['code'] === 0 && isset($result['data'])) {
            // 返回文件详情数据
            return $result['data'];
        }

        // 如果 API 返回业务错误，则抛出异常
        throw new \Exception('API Error on getting file detail: ' . (isset($result['message']) ? $result['message'] : $requestResult['response']));
    }

    /**
     * 获取文件下载地址
     *
     * @param int $fileID 文件ID
     * @return string 成功返回下载URL，失败则抛出异常
     * @throws \Exception
     */
    public function getDownloadUrl($fileID)
    {
        // 注意：根据文档，此处的参数是 fileId (d小写)
        // 拼接获取文件下载地址的 API URL
        $url = self::API_BASE_URL . '/api/v1/file/download_info?' . http_build_query(['fileId' => $fileID]);

        // 准备请求头
        $headers = [
            'Platform: open_platform',
            'Authorization: Bearer ' . $this->accessToken
        ];

        // 发起 GET 请求
        $requestResult = $this->_request('GET', $url, $headers);

        // 检查请求是否出错
        if ($requestResult['error'] || $requestResult['httpCode'] !== 200) {
            throw new \Exception('HTTP Error on getting download URL. Code: ' . $requestResult['httpCode'] . '. Error: ' . $requestResult['error'] . '. Response: ' . $requestResult['response']);
        }

        // 解码 JSON 响应
        $result = json_decode($requestResult['response'], true);

        // 检查解码是否成功，以及 API 是否成功返回下载 URL
        if (json_last_error() === JSON_ERROR_NONE && isset($result['code']) && $result['code'] === 0 && isset($result['data']['downloadUrl'])) {
            // 返回获取到的下载 URL
            return $result['data']['downloadUrl'];
        }

        // 如果 API 返回业务错误，则抛出异常
        throw new \Exception('API Error on getting download URL: ' . (isset($result['message']) ? $result['message'] : $requestResult['response']));
    }

    /**
     * 获取文件列表
     *
     * @param array $options 查询参数.
     *  - parentFileId (int) (必填) 父级目录ID, 根目录为 0.
     *  - limit (int) (必填) 每页数量, 最大 100.
     *  - searchData (string) (可选) 搜索关键词.
     *  - trashed (bool) (可选) 是否查看回收站.
     *  - searchMode (int) (可选) 0:模糊搜索, 1:精确搜索.
     *  - lastFileId (int) (可选) 翻页查询时需要填写.
     * @return array 成功返回包含 fileList 和 lastFileId 的数组
     * @throws \Exception
     */
    public function listFiles(array $options = [])
    {
        // 设置默认的查询选项
        $defaultOptions = [
            'parentFileId' => 0,
            'limit' => 100,
        ];
        // 将用户传入的选项与默认选项合并，用户选项会覆盖默认值
        $finalOptions = array_merge($defaultOptions, $options);

        // 拼接获取文件列表的 API URL
        $url = self::API_BASE_URL . '/api/v2/file/list?' . http_build_query($finalOptions);

        // 准备请求头
        $headers = [
            'Platform: open_platform',
            'Authorization: Bearer ' . $this->accessToken
        ];

        // 发起 GET 请求
        $requestResult = $this->_request('GET', $url, $headers);

        // 检查请求是否出错
        if ($requestResult['error'] || $requestResult['httpCode'] !== 200) {
            throw new \Exception('HTTP Error on listing files. Code: ' . $requestResult['httpCode'] . '. Error: ' . $requestResult['error'] . '. Response: ' . $requestResult['response']);
        }

        // 解码 JSON 响应
        $result = json_decode($requestResult['response'], true);

        // 检查解码是否成功，以及 API 是否成功返回数据
        if (json_last_error() === JSON_ERROR_NONE && isset($result['code']) && $result['code'] === 0 && isset($result['data'])) {
            // 返回包含文件列表等信息的 data 部分
            return $result['data'];
        }

        // 如果 API 返回业务错误，则抛出异常
        throw new \Exception('API Error on listing files: ' . (isset($result['message']) ? $result['message'] : $requestResult['response']));
    }

    /**
     * 通知服务器所有分片上传完成
     *
     * @param string $preuploadID 预上传ID
     * @param int $maxRetries 最大重试次数
     * @param int $retryDelay 每次重试间隔（秒）
     * @return int 成功返回文件ID
     * @throws \Exception
     */
    private function _completeUpload($preuploadID, $maxRetries = 5, $retryDelay = 1)
    {
        // 设置完成上传的 API 端点
        $url = self::API_BASE_URL . '/upload/v2/file/upload_complete';

        // 开始轮询，设置最大重试次数
        for ($i = 0; $i < $maxRetries; $i++) {
            // 准备请求体，包含 preuploadID
            $postData = json_encode([
                'preuploadID' => $preuploadID,
            ]);

            // 准备请求头
            $headers = [
                'Content-Type: application/json',
                'Platform: open_platform',
                'Authorization: Bearer ' . $this->accessToken
            ];

            // 发起 POST 请求
            $requestResult = $this->_request('POST', $url, $headers, $postData);

            // 检查请求本身是否出错（比如网络问题）
            if ($requestResult['error'] || $requestResult['httpCode'] !== 200) {
                // 如果还未达到最大重试次数，则等待后继续下一次尝试
                if ($i < $maxRetries - 1) {
                    // 等待指定的秒数
                    sleep($retryDelay);
                    // 继续下一次循环
                    continue;
                }
                // 如果这是最后一次尝试但仍然失败，则抛出连接异常
                throw new \Exception('Failed to confirm upload after ' . $maxRetries . ' retries due to connection issues.');
            }

            // 解码 JSON 响应
            $result = json_decode($requestResult['response'], true);

            // 检查解码是否成功，以及 API 是否返回了数据
            if (json_last_error() === JSON_ERROR_NONE && isset($result['code']) && $result['code'] === 0 && isset($result['data'])) {
                // 检查 API 是否明确返回"已完成"状态和文件ID
                if (isset($result['data']['completed']) && $result['data']['completed'] === true && isset($result['data']['fileID'])) {
                    // 如果已完成，返回最终的文件 ID，流程结束
                    return $result['data']['fileID'];
                }
            }

            // 如果 API 尚未返回完成状态，且还未达到最大重试次数
            if ($i < $maxRetries - 1) {
                // 等待后进行下一次轮询
                sleep($retryDelay);
            }
        }

        // 如果循环结束（达到最大重试次数）仍未返回成功，则抛出最终的失败异常
        throw new \Exception('Failed to confirm upload completion after ' . $maxRetries . ' retries.');
    }

    /**
     * 执行一个 cURL 请求
     *
     * @param string $method HTTP 方法 (e.g., 'GET', 'POST')
     * @param string $url 请求 URL
     * @param array $headers 请求头
     * @param string|null|array $body 请求体 (字符串或 multipart 数组)
     * @return array 包含 httpCode, response, error 的数组
     */
    private function _request($method, $url, $headers = [], $body = null)
    {
        // 初始化是否为 multipart 请求的标志为 false
        $isMultipart = false;
        // 检查请求体是否为数组（这是我们用来标记 multipart 请求的约定）
        if (is_array($body)) {
            // 遍历请求头
            foreach ($headers as $header) {
                // 检查 Content-Type 头是否为 multipart/form-data
                if (stripos($header, 'Content-Type: multipart/form-data') === 0) {
                    // 如果是，则设置标志位为 true
                    $isMultipart = true;
                    // 并跳出循环
                    break;
                }
            }
        }

        // 初始化一个 cURL 会话
        $ch = curl_init();

        // 默认最终的请求头就是传入的请求头
        $finalHeaders = $headers;
        // 如果是 multipart 请求
        if ($isMultipart) {
            // 则需要过滤掉我们手动设置的 Content-Type，让 cURL 自动生成正确的 Content-Type（包含 boundary）
            $finalHeaders = array_filter($headers, function ($h) {
                return stripos($h, 'Content-Type:') !== 0;
            });
        }

        // 准备 cURL 的选项数组
        $options = [
            CURLOPT_URL => $url, // 设置请求的 URL
            CURLOPT_RETURNTRANSFER => true, // 设置为 true，表示 curl_exec() 将返回结果而不是直接输出
            CURLOPT_HTTPHEADER => $finalHeaders, // 设置最终的请求头
            CURLOPT_CUSTOMREQUEST => strtoupper($method), // 设置请求方法 (GET, POST, etc.)
        ];

        // 如果请求方法是 POST
        if (strtoupper($method) === 'POST') {
            // 明确告诉 cURL 这是一个 POST 请求
            $options[CURLOPT_POST] = true;
            // 设置 POST 请求的请求体
            $options[CURLOPT_POSTFIELDS] = $body;
        }

        // 将所有选项批量应用到 cURL 会话上
        curl_setopt_array($ch, $options);

        // 执行 cURL 请求，并将响应保存在 $response 变量中
        $response = curl_exec($ch);
        // 获取响应的 HTTP 状态码
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        // 获取 cURL 请求过程中发生的错误信息（如果没有错误则为空字符串）
        $error = curl_error($ch);
        // 关闭 cURL 会话，释放资源
        curl_close($ch);

        // 返回一个包含状态码、响应体和错误信息的数组
        return [
            'httpCode' => $httpCode,
            'response' => $response,
            'error' => $error,
        ];
    }
}
