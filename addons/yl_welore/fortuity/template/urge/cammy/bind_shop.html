<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <title>绑定商品</title>
    <link rel="stylesheet" href="assets/css/bootstrap.min.css?v=1.0">
    <link rel="stylesheet" href="./assets/css/amazeui.min.css?v=1.0"/>
    <style>.pagination{font-size:12px;}.select-transfer{cursor:pointer;}.select-transfer:hover{color:red;}</style>
</head>
<body>
<div class="am-g">
    <div class="am-form" style="min-height: 465px;">
        <table class="am-table am-table-striped am-table-hover table-main">
            <tr>
                <th class="text-center" style="width:25%;">
                    ID
                </th>
                <th class="text-center" style="width:25%;">
                    商品图片
                </th>
                <th class="text-center" style="width:25%;">
                    商品名称
                </th>
                <th class="text-center" style="width:25%;">
                    操作
                </th>
            </tr>
            {volist name="list" id="vo"}
            <tr>
                <td class="text-center" style="line-height: 70px;">
                    {$vo.id}
                </td>
                <td class="text-center">
                    <img src="{$vo.product_img|athumbnail}" style="object-fit: scale-down;width: 65px;height: 65px;">
                </td>
                <td class="text-center" style="line-height: 70px;">
                    <span title="{$vo.product_name}">
                    {$vo.product_name|subtext=12}
                    </span>
                </td>
                <td class="text-center" style="line-height: 70px;">
                    <span class="select-transfer" onclick="transferCard('{$vo.id}','{$vo.product_name}');">
                        选择
                    </span>
                </td>
            </tr>
            {/volist}
        </table>
    </div>
</div>
<div class="am-cf text-center">
    {$list->render()}
</div>
</body>
<script src="assets/js/jquery.min.js?v=1.0"></script>
<script src="assets/js/bootstrap.min.js?v=1.0"></script>
<script>
    var transferCard = function (sid, shopName) {
        parent.layer.confirm('您确定要绑定当前选择的商品吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            parent.cardRefrain(sid, shopName);
            parent.layer.closeAll();
        }, function (index) {
            parent.layer.close(index);
        });
    }
</script>
</html>