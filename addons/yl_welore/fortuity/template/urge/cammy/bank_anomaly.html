{extend name="/base"/}
{block name="main"}
<style>.customize-span,.customize-span:hover{padding:3px 10px;color:black !important;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}.am-table > thead:first-child > tr:first-child > th{text-align:center;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 货币卡密
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索卡密代码...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-4">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-xs">
                        <a href="javascript:void(0);" class="customize-span" onclick="newAnomaly();">
                            <span class="am-icon-adn"></span> 新增货币卡密
                        </a>
                    </div>
                </div>
            </div>
            <div class="am-u-sm-8">
                <div class="am-form-group text-right">
                    <label>
                        <span style="font-size:16px;font-weight: 500;">卡密类型</span>
                        <select id="egon" data-am-selected="{btnSize: 'sm'}" onchange="filterAnomaly();">
                            <option value="0" {if $egon==0}selected{/if}>全部类型</option>
                            <option value="1" {if $egon==1}selected{/if}>贝壳</option>
                            <option value="2" {if $egon==2}selected{/if}>积分</option>
                            <option value="3" {if $egon==3}selected{/if}>会员</option>
                        </select>
                    </label>
                    <label style="margin-left: 15px;">
                        <span style="font-size:16px;font-weight: 500;">是否使用</span>
                        <select id="isUse" data-am-selected="{btnSize: 'sm'}" onchange="filterAnomaly();">
                            <option value="0" {if $isUse==0}selected{/if}>请选择</option>
                            <option value="1" {if $isUse==1}selected{/if}>未使用</option>
                            <option value="2" {if $isUse==2}selected{/if}>已使用</option>
                        </select>
                    </label>
                    <label style="margin-left: 15px;">
                        <span style="font-size:16px;font-weight: 500;">批量操作</span>
                        <select id="bulk-selected" data-am-selected="{btnSize: 'sm'}" onchange="selectionBulk(this);">
                            <option value="0">请选择</option>
                            <option value="1">批量绑定商品</option>
                            <option value="2">批量销毁卡密</option>
                        </select>
                    </label>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover table-main" style="text-align:center;">
                        <thead>
                        <tr>
                            <th width="11.11%">
                                <input id="withole" type="checkbox" class="tpl-table-fz-check">全选
                            </th>
                            <th width="11.11%">卡密代码</th>
                            <th width="11.11%">卡密类型</th>
                            <th width="11.11%">卡密面值</th>
                            <th width="11.11%">是否使用</th>
                            <!--
                            <th width="11.11%">是否售出</th>
                            -->
                            <th width="11.11%">卡密状态</th>
                            <th width="11.11%">创建时间</th>
                            <th width="11.11%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                <input type="checkbox" class="tpl-table-fz-check elctive" value="{$vo.id}">
                            </td>
                            <td class="am-text-middle">
                                <span>{$vo.card_code}</span>
                            </td>
                            <td class="am-text-middle">
                                {switch $vo.financial_type}
                                {case 0}其他{/case}
                                {case 1}贝壳{/case}
                                {case 2}积分{/case}
                                {case 3}会员{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.financial_type!=3}{$vo.face_value}{else}{:intval($vo.face_value)}天{/if}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.is_use}已使用{else}未使用{/if}
                            </td>
                            <!--
                            <td class="am-text-middle">
                                {if $vo.is_use}已售出{else}未售出{/if}
                            </td>
                            -->
                            <td class="am-text-middle">
                                <span title="点击切换卡密状态">
                                {if $vo.status == 0}
                                <span style="color: red;cursor: pointer;" onclick="eturvy('{$vo.id}','1');">
                                    已禁用
                                </span>
                                {else}
                                <span style="color: lightgreen;cursor: pointer;" onclick="eturvy('{$vo.id}','0');">
                                    正常
                                </span>
                                {/if}
                                </span>
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$vo.create_time)}
                            </td>
                            <td class="am-text-middle">
                                <span onclick="eraseAnomaly('{$vo.id}',0);">
                                    <span style="color:#fa2222;padding:4px 12px;background:#fdfdfd;cursor: pointer;border: 1px solid #ccc;word-break: keep-all;">
                                        销毁卡密
                                    </span>
                                </span>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
<div id="mutual" class="hide">
    <div class="am-g tpl-amazeui-form" style="background: #fcfcfc;width: 100%;height: 100%;">
        <div class="am-form am-form-horizontal">
            <div class="am-form-group" style="margin-top: 35px;">
                <label class="am-u-sm-4 am-form-label">卡密类型</label>
                <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                    <select class="financeType" style="width: 236px;" onchange="selectType(this);">
                        <option value="-1">请选择</option>
                        <option value="1">贝壳</option>
                        <option value="2">积分</option>
                        <option value="3">会员</option>
                    </select>
                </div>
            </div>
            <div class="am-form-group" style="margin-top: 30px;">
                <label class="am-u-sm-4 am-form-label">卡密面值</label>
                <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;display: flex;align-items: center;">
                    <input class="faceValue" type="text" style="width: 240px;" oninput="grender(this,2)">
                    <input type="text" style="width: 240px;display: none;" oninput="grender(this)">
                    <span style="margin-left: 10px;display: none;">天</span>
                </div>
            </div>
            <div class="am-form-group" style="margin-top: 30px;">
                <label class="am-u-sm-4 am-form-label">卡密数量</label>
                <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;display: flex;align-items: center;">
                    <input class="cardNum" type="text" style="width: 240px;" oninput="grender(this)">
                    <span style="margin-left: 10px;">张</span>
                </div>
            </div>
            <div class="am-form-group" style="margin-top: 30px;">
                <label class="am-u-sm-4 am-form-label">卡密状态</label>
                <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;display: flex;align-items: center;">
                    <select class="status" style="width: 236px;">
                        <option value="-1">请选择</option>
                        <option value="0">禁用</option>
                        <option value="1">正常</option>
                    </select>
                </div>
            </div>
            <div class="am-form-group" style="margin-top: 30px;display: flex;justify-content: center;">
                <div style="cursor:pointer;color:#ffffff;background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;">
                    <div style="width: 100%;height: 100%;" onclick="holdSave()">
                        生成卡密
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    $(function () {
        $('#withole').click(function () {
            var wecked = $(this).prop('checked');
            if (wecked) {
                $('.elctive').prop('checked', true);
            } else {
                $('.elctive').prop('checked', false);
            }
        });
        $('.elctive').click(function () {
            var jack = 1;
            $('.elctive').each(function () {
                var weck = $(this).prop('checked');
                if (weck === false) {
                    jack = 0;
                    return false;
                }
            });
            if (jack) {
                $('#withole').prop('checked', true);
            } else {
                $('#withole').prop('checked', false);
            }
        });
    });

    var selectionBulk = function (obj){
        var tired = [];
        $('.elctive').each(function () {
            if ($(this).prop('checked')) {
                tired.push($(this).val());
            }
        });
        if (Number(obj.value) !== 0 && tired.length === 0) {
            layer.alert('很抱歉，当前没有选中要操作的数据', {'title': '系统提示'});
        } else {
            if (Number(obj.value) > 0) {
                switch (Number(obj.value)) {
                    case 1:
                        layer.open({
                            type: 2,
                            anim: 2,
                            title: false,
                            area: ['900px', '580px'],
                            scrollbar: false,
                            closeBtn: true,
                            shadeClose: true,
                            content: ["{:url('cammy/bindShop')}", 'no'],
                        });
                        break;
                    case 2:
                        eraseAnomaly(tired, 1);
                        break;
                }
            }
        }
        setTimeout(function () {
            if (Number(obj.value) !== 0) {
                obj.value = '0';
                $('#bulk-selected').trigger('changed.selected.amui');
            }
        }, 1200);
    }

    var cardRefrain = function (sid) {
        var tired = [];
        $('.elctive').each(function () {
            if ($(this).prop('checked')) {
                tired.push($(this).val());
            }
        });
        $.post("{:url('cammy/bindShop')}", {'fid': tired, 'sid': sid}, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1600}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2200});
            }
        }, 'json');
    }


    var grender = function (obj, limit) {
        if (limit === 2) {
            obj.value = obj.value.replace(/[^\d.]/g, "");
            obj.value = obj.value.replace(/^\./g, "");
            obj.value = obj.value.replace(/\.{2,}/g, ".");
            obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
            obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
        } else {
            obj.value = Number((obj.value.match(/^\d+(?:\.\d{0})?/)));
        }
    }

    var selectType = function (obj) {
        if (Number($(obj).val()) === 3) {
            $('.faceValue').hide();
            $('.faceValue + input').show();
            $('.faceValue + input + span').show();
        } else {
            $('.faceValue').show();
            $('.faceValue + input').hide();
            $('.faceValue + input + span').hide();
        }
    }

    var newAnomaly = function () {
        layer.open({
            type: 1,
            title: false,
            scrollbar: false,
            closeBtn: true,
            area: ['600px', '380px'],
            shadeClose: true,
            content: $('#mutual').html()
        });
    }

    var isLock = false;
    var holdSave = function () {
        if (!isLock) {
            var setData = {};
            setData['financeType'] = Number($('.layui-layer .financeType').val());
            if (setData['financeType'] === -1) {
                layer.msg('请选择要生成的卡密类型！');
                return;
            } else {
                if (setData['financeType'] === 3) {
                    setData['faceValue'] = Number($('.layui-layer .faceValue + input').val());
                    if (setData['faceValue'] <= 0) {
                        layer.msg('请输入要生成的会员天数！');
                        return;
                    }
                } else {
                    setData['faceValue'] = $.trim($('.layui-layer .faceValue').val());
                    if (setData['faceValue'] === '') {
                        layer.msg('请输入卡密面值！');
                        return;
                    }
                }
            }
            setData['cardNum'] = Number($('.layui-layer .cardNum').val());
            if (setData['cardNum'] === 0) {
                layer.msg('请选择要生成的卡密数量！');
                return;
            }
            if (setData['cardNum'] > 100) {
                layer.msg('一次性最多生成100张卡密，请重新输入！');
                return;
            }
            setData['status'] = Number($('.layui-layer .status').val());
            if (setData['status'] === -1) {
                layer.msg('请选择要生成的卡密状态！');
                return;
            }
            isLock = true;
            $.post("{:url('cammy/newAnomaly')}", setData, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200}, function () {
                        isLock = false;
                    });
                }
            }, 'json');
        }
    }

    var filterAnomaly = function (obj){
        var egon = $.trim($('#egon').val());
        var isUse = $.trim($('#isUse').val());
        var fz_name = $.trim($('#fz_name').val());
        location.href = "{:url('cammy/bankAnomaly')}&egon=" + egon + "&isUse=" + isUse + "&hazy_name=" + fz_name + "&page={$page}";
    }

    var eturvy = function (fid, status) {
        var confirmTitle = '您确定要';
        if (Number(status) === 0) {
            confirmTitle += '禁用';
        } else {
            confirmTitle += '启用';
        }
        confirmTitle += '这张卡密吗？';
        layer.confirm(confirmTitle, {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('cammy/updateAnomalyStatus')}", {'fid': fid, 'status': status}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1000});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

    var eraseAnomaly = function (fids, type) {
        var setData = {};
        if (type === 0) {
            setData[0] = fids;
        } else {
            setData = fids;
        }
        layer.confirm('您确定要销毁当前卡密吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('cammy/delAnomaly')}", {fid: setData}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200});
                }
            });
        }, function (index) {
            layer.close(index);
        });
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('cammy/bankAnomaly')}&egon={$egon}&isUse={$isUse}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('cammy/bankAnomaly')}&page={$page}";
        }
    }

</script>
{/block}