{extend name="/base"/}
{block name="main"}
<style>.customize-span,.customize-span:hover{padding:3px 10px;color:black !important;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}.am-table > thead:first-child > tr:first-child > th{text-align:center;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 货币卡密兑换记录
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索卡密代码...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form-group text-right">
                    <label>
                        <span style="font-size:16px;font-weight: 500;">卡密类型</span>
                        <select id="egon" data-am-selected="{btnSize: 'sm'}" onchange="filterAnomaly();">
                            <option value="0" {if $egon==0}selected{/if}>全部类型</option>
                            <option value="1" {if $egon==1}selected{/if}>贝壳</option>
                            <option value="2" {if $egon==2}selected{/if}>积分</option>
                            <option value="3" {if $egon==3}selected{/if}>会员</option>
                        </select>
                    </label>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover table-main" style="text-align:center;">
                        <thead>
                        <tr>
                            <th width="10%">卡密代码</th>
                            <th width="10%">卡密类型</th>
                            <th width="10%">卡密面值</th>
                            <th width="10%">用户名称</th>
                            <th width="10%">用户IP</th>
                            <th width="10%">兑换时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                <span>{$vo.card_code}</span>
                            </td>
                            <td class="am-text-middle">
                                {switch $vo.financial_type}
                                {case 1}贝壳{/case}
                                {case 2}积分{/case}
                                {case 3}会员{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.financial_type!=3}{$vo.face_value}{else}{:intval($vo.face_value)}天{/if}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.user.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user.user_wechat_open_id}&page=1" title="{$vo.user.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&openid={$vo.user.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user.user_nick_name|emoji_decode}">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                {$vo.use_ip}
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$vo.use_time)}
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var filterAnomaly = function (obj){
        var egon = $.trim($('#egon').val());
        var fz_name = $.trim($('#fz_name').val());
        location.href = "{:url('cammy/exchangeAnomaly')}&egon=" + egon + "&hazy_name=" + fz_name + "&page={$page}";
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('cammy/exchangeAnomaly')}&egon={$egon}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('cammy/exchangeAnomaly')}&page={$page}";
        }
    }

</script>
{/block}