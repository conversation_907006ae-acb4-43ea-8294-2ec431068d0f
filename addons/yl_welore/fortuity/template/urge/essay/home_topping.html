{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-location-arrow {margin-right: 5px;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    
    .am-btn-toolbar .am-btn {
        background-color: #23b7e5;
        color: white;
        border-color: #23b7e5;
        border-radius: 3px;
    }

    .action-btn {display: inline-block; padding: 4px 10px; background: #fff; border: 1px solid #ddd; color: #666; border-radius: 3px; font-size: 12px; cursor: pointer; transition: all 0.3s;}
    .action-btn:hover {border-color: #23b7e5; color: #23b7e5; background-color: #f5fafd;}
    .action-btn.danger {color: #dd514c;}
    .action-btn.danger:hover {border-color: #dd514c; color: #dd514c; background-color: #fff8f8;}

    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-top: 15px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}

    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a, .am-pagination > .am-active > a:hover {background-color: #23b7e5;border-color: #23b7e5;color:#fff;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}

    /* Modal Style - adapted from index.html theme */
    #shandsel .am-modal-dialog {border-radius: 4px;}
    #shandsel .am-modal-hd {border-bottom: 1px solid #f0f0f0; padding: 15px 20px; font-size: 16px; font-weight: 500;}
    #shandsel .am-modal-bd {padding: 25px 30px;}
    #shandsel .am-form-group {margin-bottom: 20px;}
    #shandsel .am-form-label {font-weight: 500;}
    #shandsel .am-btn-primary {background-color: #23b7e5; border-color: #23b7e5; border-radius: 3px;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-location-arrow"></span> 首页置顶列表
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索帖子...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-btn-toolbar">
                    <button type="button" class="am-btn am-btn-default am-btn-sm" data-am-modal="{target: '#shandsel', closeViaDimmer: 0, width: 400, height: 260}">
                        <span class="am-icon-plus"></span> 新增置顶
                    </button>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <form class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="5%">帖子ID</th>
                            <th width="18%">发帖用户</th>
                            <th width="22%">发帖 标题 / 内容</th>
                            <th width="10%">帖子类型</th>
                            <th width="10%">风格样式</th>
                            <th width="15%">发帖时间</th>
                            <th width="10%">置顶时间</th>
                            <th width="10%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>{$vo.id}</td>
                            <td>
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td>
                                <a href="{:url('essay/setails')}&uplid={$vo.id}" target="_blank">
                                {if $vo.study_title}
                                    {$vo.study_title|emoji_decode|subtext=20}
                                {elseif $vo.study_content}
                                    {$vo.study_content|emoji_decode|subtext=20|$expressionHtml}
                                {else}
                                    无
                                {/if}
                                </a>
                            </td>
                            <td>
                                {if $vo.study_type==0} 图文 {elseif $vo.study_type==1} 语音 {elseif $vo.study_type==2} 视频 {/if}
                            </td>
                            <td>
                                <span class="action-btn" onclick="cutoverStyle('{$vo.hid}','{$vo.style_type===0?1:0}');">
                                {if $vo.style_type==0} 样式 1 {elseif $vo.style_type==1} 样式 2 {/if}
                                </span>
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.adapter_time)}</td>
                            <td>{:date('Y-m-d H:i:s',$vo.top_time)}</td>
                            <td>
                                <button type="button" class="action-btn danger" onclick="cancelStrike('{$vo.hid}');">取消置顶</button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel">
        <div class="am-modal-dialog">
            <div class="am-modal-hd">
                置顶帖子
                <a href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd am-form">
                <div class="am-form-group">
                    <label class="am-u-sm-4 am-form-label">帖子编号</label>
                    <div class="am-u-sm-8">
                        <input type="number" id="prid" oninput="digitalCheck(this);" class="tpl-form-input" placeholder="请输入要置顶帖子的编号">
                    </div>
                </div>
                <div class="am-form-group">
                    <label class="am-u-sm-4 am-form-label">风格样式</label>
                    <div class="am-u-sm-8">
                        <select id="illutype">
                            <option value="0">样式 1</option>
                            <option value="1">样式 2</option>
                        </select>
                    </div>
                </div>
                <div class="am-form-group">
                     <div class="am-u-sm-8 am-u-sm-offset-4">
                        <button type="button" class="am-btn am-btn-primary am-btn-sm" onclick="sendGifts();">
                            确定置顶
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    var digitalCheck = function (obj) {
        obj.value = obj.value.replace(/[^\d]/g, "");
        if (obj.value > 2147483646) {
            obj.value = 2147483646;
        }
    }

    var sendGifts = function () {
        var prid = $.trim($('#prid').val());
        var illutype = $.trim($('#illutype').val());
        if (!prid || prid == 0) {
            layer.msg('请输入正确的帖子编号');
            return;
        }
        layer.confirm('您确定要置顶这条帖子吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function (index) {
            layer.close(index);
            var loadIndex = layer.load(1, { shade: [0.1,'#fff'] });
            $.post("{:url('essay/rutoping')}", {'prid': prid, 'illutype': illutype}, function (data) {
                layer.close(loadIndex);
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1600}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
        });
    }

    var cancelStrike = function (hid) {
        layer.confirm('您确定要取消置顶当前帖子吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function (index) {
            layer.close(index);
            var loadIndex = layer.load(1, { shade: [0.1,'#fff'] });
            $.post("{:url('essay/home_topping')}", {'hid': hid}, function (data) {
                layer.close(loadIndex);
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1600}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }, 'json');
        });
    }

    var cutoverStyle = function (hid, sid) {
        layer.confirm('您确定要切换此条置顶的风格样式吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function (index) {
            layer.close(index);
            var loadIndex = layer.load(1, { shade: [0.1,'#fff'] });
            $.post("{:url('essay/pickingStyle')}", {'hid': hid, 'sid': sid}, function (data) {
                layer.close(loadIndex);
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1600}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
        });
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        var url = "{:url('essay/home_topping')}&tory_id={$tory_id}&page={$page}";
        if (fz_name) {
            url += "&hazy_name=" + encodeURIComponent(fz_name);
        }
        location.href = url;
    }
</script>
{/block}