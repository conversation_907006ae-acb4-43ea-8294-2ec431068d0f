{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .am-form {position: relative;overflow: visible;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-file-text {margin-right: 5px;color: #23b7e5;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .action-btn {display: inline-block;padding: 4px 8px;background: #fff;border: 1px solid #ddd;color: #23b7e5;border-radius: 3px;font-size: 12px;cursor: pointer;transition: all 0.3s;}
    .action-btn:hover {border-color: #23b7e5;color: #23b7e5;background-color: #f5fafd;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
    .filter-btn-group .am-btn {
        margin-right: 10px;
        border-radius: 3px;
    }
    .filter-btn-group .am-btn.active {
        background-color: #23b7e5;
        color: white;
        border-color: #23b7e5;
    }
    .batch-actions-group > .am-btn + .am-btn {
        margin-left: 8px;
    }
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-file-text"></span> 帖子信息
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}"
                       placeholder="搜索标题或用户...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-7">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-sm filter-btn-group">
                        <a href="{:url('essay/index')}&egon=0" type="button" class="am-btn am-btn-default {if $egon==0}active{/if}">全部</a>
                        <a href="{:url('essay/index')}&egon=1" type="button" class="am-btn am-btn-default {if $egon==1}active{/if}">待审核</a>
                        <a href="{:url('essay/index')}&egon=2" type="button" class="am-btn am-btn-default {if $egon==2}active{/if}">已审核</a>
                        <a href="{:url('essay/index')}&egon=3" type="button" class="am-btn am-btn-default {if $egon==3}active{/if}">已打回</a>
                        <a href="{:url('essay/index')}&egon=4" type="button" class="am-btn am-btn-default {if $egon==4}active{/if}">回收站</a>
                    </div>
                </div>
            </div>
            {if $egon!=4}
            <div class="am-u-sm-12 am-u-md-5" style="text-align: right;">
                <div class="am-btn-group am-btn-group-sm batch-actions-group">
                    <button type="button" class="am-btn am-btn-success" onclick="rebatch('1');">批量通过</button>
                    <button type="button" class="am-btn am-btn-secondary" onclick="rebatch('2');">批量打回</button>
                    <button type="button" class="am-btn am-btn-danger" onclick="rebatch('3');">批量删除</button>
                </div>
            </div>
            {/if}
        </div>

        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            {if $egon!=4}
                            <th width="4%">
                                <input id="withole" type="checkbox" class="tpl-table-fz-check">全选
                            </th>
                            {/if}
                            <th width="7%">ID</th>
                            <th width="10%">圈子名称</th>
                            <th width="14%">发帖 标题/内容</th>
                            <th width="15%">发帖用户</th>
                            <th width="9%">帖子类型</th>
                            <th width="9%">功能类型</th>
                            <th width="10%">发帖时间</th>
                            <th width="7%">审核状态</th>
                            <th width="15%" colspan="2">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            {if $egon!=4}
                            <td>
                                <input type="checkbox" class="tpl-table-fz-check elctive" value="{$vo.id}">
                            </td>
                            {/if}
                            <td>{$vo.id}</td>
                            <td>
                                <a href="{:url('compass/fence')}&hazy_name={$vo.realm_name}&page=1" title="{$vo.realm_name}" target="_blank">
                                    {$vo.realm_name|subtext=6}
                                </a>
                            </td>
                            <td>
                                {if $vo.study_title!=''}
                                {$vo.study_title|emoji_decode|strip_tags|subtext=12}
                                {elseif strlen(strip_tags($vo.study_content))}
                                {$vo.study_content|emoji_decode|strip_tags|subtext=12|$expressionHtml}
                                {else}
                                无
                                {/if}
                            </td>
                            <td>
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|emoji_decode}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td>
                                {switch $vo.study_type}{case 0}图文{/case}{case 1}语音{/case}{case 2}视频{/case}{case 3}活动{/case}{case 4|5}投票{/case}{case 6}视频号{/case}{/switch}帖
                            </td>
                            <td>
                                {if $vo.is_buy == 1}付费帖子{elseif $vo.welfare}红包帖子{else}普通帖子{/if}
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.adapter_time)}</td>
                            <td>
                                {if $vo.study_status == 0}
                                <span class="am-text-warning">待审核</span>
                                {elseif $vo.study_status == 1}
                                <span class="am-text-success">已通过</span>
                                {elseif $vo.study_status == 2}
                                <span class="am-text-secondary">已打回</span>
                                {/if}
                            </td>
                            <td>
                                <button type="button" class="action-btn" onclick="uploof('{$vo.id}','{$vo.token}');">
                                    <span class="am-icon-search"></span> 查看
                                </button>
                            </td>
                            <td>
                                <button type="button" class="action-btn" onclick="exerox(this);">
                                    <input type="hidden" value="/yl_welore/pages/packageA/article/index?id={$vo.id}&type={$vo.study_type}">
                                    复制链接
                                </button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    !function () {
        $('#withole').click(function () {
            var wecked = $(this).prop('checked');
            if (wecked) {
                $('.elctive').prop('checked', true);
            } else {
                $('.elctive').prop('checked', false);
            }
        });
        $('.elctive').click(function () {
            var jack = 1;
            $('.elctive').each(function () {
                var weck = $(this).prop('checked');
                if (weck == false) {
                    jack = 0;
                    return false;
                }
            });
            if (jack) {
                $('#withole').prop('checked', true);
            } else {
                $('#withole').prop('checked', false);
            }
        });
    }();

    var rebatch = function (guid) {
        var tired = false;
        $('.elctive').each(function () {
            if ($(this).prop('checked')) {
                tired = true;
                return false;
            }
        });
        if (!tired) {
            layer.alert('很抱歉，当前没有选中要操作的数据', {'title': '系统提示'});
            return;
        }
        var reason = '';
        switch (parseInt(guid)) {
            case 1:
                layer.confirm('您确定要通过已选中的帖子吗？', {
                    btn: ['确定', '取消'], title: '提示'
                }, function (index) {
                    setrike(guid, reason, index);
                }, function (index) {
                    layer.close(index);
                });
                break;
            case 2:
                layer.prompt({'title': '请您输入打回原因：'}, function (rea_value, index) {
                    if ($.trim(rea_value) == '') {
                        return false;
                    }
                    reason = rea_value;
                    setrike(guid, reason, index);
                });
                break;
            case 3:
                layer.prompt({'title': '请您输入删帖原因：'}, function (rea_value, index) {
                    if ($.trim(rea_value) == '') {
                        return false;
                    }
                    reason = rea_value;
                    setrike(guid, reason, index);
                });
                break;
        }
    }

    var setrike = function (guid, reason,index) {
        layer.close(index);
        var i = 0;
        var j = 0;
        $('.elctive').each(function () {
            if ($(this).prop('checked')) {
                var suid = $(this).val();
                i++;
                $.ajaxSettings.async = false;
                $.post("{:url('essay/setails')}", {
                    'uplid': suid,
                    'pical': guid,
                    'reason': reason
                }, function (data) {
                    if (data.code > 0) {
                        j++;
                    }
                });
                $.ajaxSettings.async = true;
            }
        });
        if (i == j) {
            layer.msg('操作成功', {icon: 1, time: 1000}, function () {
                location.reload();
            });
        } else {
            layer.msg('未知错误', {icon: 5, time: 2000}, function () {
                location.reload();
            });
        }
    }

    var exerox = function (obj) {
        var linkUrl = $(obj).children('input').eq(0).val();
        var oInput = document.createElement('input');
        oInput.value = linkUrl;
        document.body.appendChild(oInput);
        oInput.select(); // 选择对象
        var carried = document.execCommand("Copy"); // 执行浏览器复制命令
        oInput.className = 'oInput';
        oInput.style.display = 'none';
        if (carried) {
            layer.alert('\u94fe\u63a5\u5730\u5740\u5df2\u6210\u529f\u590d\u5236\u5230\u526a\u8d34\u677f\uff0c\u8bf7\u4f7f\u7528\u9f20\u6807\u53f3\u952e\u6216\u952e\u76d8\u7684\u0020\u0043\u0074\u0072\u006c\u002b\u0056\u0020\u7ec4\u5408\u952e\u0020\u8fdb\u884c\u7c98\u8d34\uff01', {'title': '\u7cfb\u7edf\u63d0\u793a'});
        } else {
            layer.alert('\u590d\u5236\u94fe\u63a5\u5730\u5740\u5931\u8d25\uff0c\u8bf7\u6839\u636e\u8df3\u8f6c\u94fe\u63a5\u4e0b\u65b9\u63d0\u793a\u624b\u52a8\u66f4\u6539\uff01', {'title': '\u7cfb\u7edf\u63d0\u793a'});
        }
    }


    var uploof = function (uplid, token) {
        token = $.trim(token);
        var dynamicUrl = $('<a></a>');
        if (token == '') {
            dynamicUrl.attr('href', "{:url('essay/setails')}&uplid=" + uplid);
        } else {
            dynamicUrl.attr('href', "{:url('essay/setails')}&uplid=" + uplid + '&token=' + token);
        }
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();

    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('essay/index')}&egon={$egon}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('essay/index')}&egon={$egon}&page={$page}";
        }
    }
</script>
{/block}