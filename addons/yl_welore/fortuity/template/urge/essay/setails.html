{extend name="/base"/}
{block name="main"}
<style>.post-detail-table{width:100%;border-collapse:collapse;table-layout:fixed;word-wrap:break-word;}.post-detail-table td{font-size:14px;padding:12px 20px;border:1px solid #e8e8e8;}.post-detail-table td:first-child{width:160px;text-align:right;color:#333;background:#fafafa;}.post-detail-table td:not(:first-child){color:#666;}.media-content{max-height:400px;overflow:auto;padding:10px;}.media-content img{max-width:97%;margin:8px 0;border-radius:4px;}.media-content video{width:97%;border-radius:4px;}.media-content audio{width:80%;margin:10px 0;}.action-buttons{text-align:right;margin-bottom:20px;}.action-buttons .btn{margin-left:10px;border-radius:4px;transition:all 0.3s;}.vote-progress{height:3rem;margin:10px 0;background:#f5f5f5;border-radius:4px;overflow:hidden;}.vote-progress-bar{height:100%;background:#5eb95e;transition:width 0.3s;}.vote-text{position:absolute;width:100%;line-height:3rem;color:#333;text-align:center;cursor:pointer;}.comment-item{border:1px solid #eee;border-radius:4px;padding:15px;margin-bottom:15px;}.comment-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:10px;}.comment-user{display:flex;align-items:center;}.comment-avatar{width:30px;height:30px;border-radius:50%;margin-right:10px;}.modal-content{background:#fff;border-radius:8px;padding:20px;}.modal-header{border-bottom:1px solid #eee;padding-bottom:15px;margin-bottom:15px;}.page-wrapper{width:100%;min-height:100%;padding:20px;margin-bottom:20px;background-color:#f5f7fa;border-radius:8px;}.post-detail-container{max-width:800px;margin:0 auto 20px auto;padding:20px;background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.08);}.oneself-content img{max-width:100% !important;height:auto !important;}.am-modal.am-modal-no-btn {position: fixed;top: 0;right: 0;bottom: 0;left: 0;z-index: 1110;display: none;overflow: hidden;background-color: rgba(0, 0, 0, 0.4);}.modal-dialog {position: relative;width: 400px;margin: 100px auto;background: #fff;border-radius: 8px;box-shadow: 0 3px 6px -4px rgba(0,0,0,.12), 0 6px 16px 0 rgba(0,0,0,.08);}.modal-dialog .am-modal-hd {padding: 16px;border-bottom: 1px solid #eee;position: relative;font-size: 16px;font-weight: 500;color: #333;}.modal-dialog .am-modal-hd .am-close {position: absolute;right: 20px;top: 50%;transform: translateY(-50%);font-size: 18px;color: #999;cursor: pointer;}.modal-dialog .am-modal-bd {padding: 20px;display: flex;flex-direction: column;justify-content: center;min-height: 150px;}.modal-dialog .am-form-group {margin: 10px 0;display: flex;align-items: center;}.modal-dialog .am-form-label {width: 80px;text-align: right;padding-right: 12px;color: #333;font-size: 14px;}.modal-dialog .am-form-field-wrap {flex: 1;display: flex;align-items: center;}.modal-dialog .tpl-form-input {flex: 1;height: 36px;padding: 0 10px;border: 1px solid #ddd;border-radius: 4px;outline: none;}.modal-dialog .am-form-help {margin-left: 8px;color: #999;font-size: 14px;}.modal-dialog .btn-container {text-align: center;margin-top: 24px;}.modal-dialog .am-btn {padding: 8px 24px;background: #1890ff;color: #fff;border: none;border-radius: 4px;transition: all 0.3s;font-size: 14px;}.modal-dialog .am-btn:hover {background: #40a9ff;}.am-modal-dialog {position: relative;background: #fff;border-radius: 6px;width: 400px;margin: 15vh auto 0;}.am-modal-hd {padding: 15px;font-size: 16px;font-weight: 500;text-align: left;border-bottom: 1px solid #eee;}.am-modal-bd {padding: 20px;}.am-form-group {margin-bottom: 1rem;display: flex;align-items: center;margin: 10px 0;}.am-form-label {width: 80px;text-align: right;margin-right: 10px;}.am-form-field-wrap {display: flex;align-items: center;flex: 1;}.tpl-form-input {flex: 1;height: 36px;padding: 0 10px;border: 1px solid #ddd;border-radius: 4px;}.am-form-help {margin-left: 8px;color: #999;white-space: nowrap;}.am-form-group.btn-wrap {display: flex;justify-content: center;margin-top: 20px;width: 100%;}.am-btn.am-btn-primary {min-width: 100px;margin: 0 auto;}</style>
<div class="page-wrapper">
    <div class="post-detail-container">
        <header class="post-header">
            <h2 class="am-icon-file-text"> 帖子详情</h2>
            <div class="action-buttons">
                <a href="{:url('essay/index')}" target="_blank">
                    <button type="button" class="am-btn am-btn-default am-btn-sm">帖子列表</button>
                </a>
                <a href="{:url('essay/editWritings')}&prid={$list.id}" target="_blank">
                    <button type="button" class="am-btn am-btn-sm" style="background: #294c64;color: white;">
                        编辑帖子
                    </button>
                </a>
                {if $list.whether_delete==0}
                <button type="button" class="am-btn am-btn-danger am-btn-sm" onclick="satypical('3');">删除此贴</button>
                {/if}
                {if $list.whether_delete==1}
                <button type="button" class="am-btn am-btn-success am-btn-sm" onclick="restoreper('{$list.id}');">恢复此帖</button>
                {/if}
            </div>
        </header>

        <section class="post-content">
            <table class="post-detail-table">
                <tr>
                    <td class="am-text-middle" style="width:35%;">{$defaultNavigate.landgrave}名称</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">{$list.realm_name}</td>
                </tr>
                <tr>
                    <td class="am-text-middle">发帖用户</td>
                    <td class="am-text-middle" colspan="2">
                        {if $list.uvirtual == 0}
                        <a href="{:url('user/index')}&openid={$list.user_wechat_open_id}&page=1" title="{$list.user_nick_name|emoji_decode}" target="_blank">
                            {$list.user_nick_name|emoji_decode}
                        </a>
                        {else}
                        <a href="{:url('user/theoretic')}&hazy_name={$list.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$list.user_nick_name|emoji_decode}">
                            {$list.user_nick_name|emoji_decode}
                        </a>
                        {/if}
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle">{if $list.study_type==3}活动名称{else}发帖标题{/if}</td>
                    <td class="am-text-middle" colspan="2">
                        {if $list.study_title!=''} {$list.study_title|emoji_decode} {else} 无 {/if}
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle">帖子类型</td>
                    <td class="am-text-middle" colspan="2">
                        {switch $list.study_type}{case 0}图文{/case}{case 1}语音{/case}{case 2}视频{/case}{case 3}活动{/case}{case 4}单选投票{/case}{case 5}多选投票{/case}{case 6}视频号{/case}{/switch}帖
                    </td>
                </tr>
                {if $gambitInfo}
                <tr>
                    <td class="am-text-middle">话题类型</td>
                    <td class="am-text-middle" colspan="2">
                        <a href="{:url('essay/index')}&egon=0&tgid={$gambitInfo.id}" target="_blank">
                            {$gambitInfo.gambit_name}
                        </a>
                    </td>
                </tr>
                {/if}
                <tr>
                    <td class="am-text-middle">功能类型</td>
                    <td class="am-text-middle" colspan="2">
                        {if $list.is_buy > 0}付费帖子{elseif $list.welfare}红包帖子{else}普通帖子{/if}
                    </td>
                </tr>
                {if $callPhonePluginKey && $list.call_phone}
                <tr>
                    <td class="am-text-middle">联系方式</td>
                    <td class="am-text-middle" colspan="2">
                        <a href="tel:{$list.call_phone}">
                            {$list.call_phone}
                        </a>
                    </td>
                </tr>
                {/if}
                {if $list.is_buy>0}
                <tr>
                    <td class="am-text-middle">付费类型</td>
                    <td class="am-text-middle" colspan="2">
                        {if !$netDiscPluginKey}
                        内容付费
                        {else}
                        {switch $list.is_buy}
                        {case 1}内容付费{/case}
                        {case 2}文件付费{/case}
                        {case 3}整体付费{/case}
                        {/switch}
                        {/if}
                    </td>
                </tr>
                {if $list.is_buy>1 && $netDiscPluginKey}
                <tr>
                    <td class="am-text-middle">文件详情</td>
                    <td class="am-text-middle" colspan="2">
                        {if $list.buyFilesInfo.is_dir}
                        <span title="{$list.file_name}" style="cursor: pointer;" onclick="viewFiles();">
                            <i class="am-icon-folder-o" style="margin-right: 3px;"></i>
                            {$list.buyFilesInfo.file_name}
                        </span>
                        {else}
                        <a href="{$list.buyFilesInfo.ncInfo.file_address}" target="_blank" title="{$list.buyFilesInfo.file_name}.{$list.buyFilesInfo.ncInfo.file_suffix}">
                            <i class="am-icon-file-text-o" style="margin-right: 3px;"></i>
                            {$list.buyFilesInfo.file_name|subtext=6}.{$list.buyFilesInfo.ncInfo.file_suffix}
                        </a>
                        {/if}
                    </td>
                </tr>
                {/if}
                {/if}
                {if $list.welfare}
                {switch $list.welfare.initial_type}
                {case 0}
                <tr>
                    <td class="am-text-middle">派发类型</td>
                    <td class="am-text-middle" colspan="2">
                        {switch $list.welfare.red_type} {case 0}普通红包{/case} {case 1}拼手气红包{/case} {/switch}
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle">派发金额</td>
                    <td class="am-text-middle" colspan="2">{$list.welfare.initial_conch} ( {$defaultNavigate.currency} )</td>
                </tr>
                <tr>
                    <td class="am-text-middle">派发数量</td>
                    <td class="am-text-middle" colspan="2">{$list.welfare.initial_quantity} ( 个 )</td>
                </tr>
                <tr>
                    <td class="am-text-middle">剩余数量</td>
                    <td class="am-text-middle" colspan="2">{$list.welfare.surplus_quantity} ( 个 )</td>
                </tr>
                <tr>
                    <td class="am-text-middle">剩余金额</td>
                    <td class="am-text-middle" colspan="2">{$list.welfare.surplus_conch} ( {$defaultNavigate.currency} )</td>
                </tr>
                {/case}
                {case 1}
                <tr>
                    <td class="am-text-middle">派发类型</td>
                    <td class="am-text-middle" colspan="2">
                        {switch $list.welfare.red_type}
                        {case 0}普通红包{/case}
                        {case 1}拼手气红包{/case}
                        {/switch}
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle">派发金额</td>
                    <td class="am-text-middle" colspan="2">{$list.welfare.initial_fraction} ( {$defaultNavigate.confer} )</td>
                </tr>
                <tr>
                    <td class="am-text-middle">派发数量</td>
                    <td class="am-text-middle" colspan="2">{$list.welfare.initial_quantity} ( 个 )</td>
                </tr>
                <tr>
                    <td class="am-text-middle">剩余数量</td>
                    <td class="am-text-middle" colspan="2">{$list.welfare.surplus_quantity} ( 个 )</td>
                </tr>
                <tr>
                    <td class="am-text-middle">剩余金额</td>
                    <td class="am-text-middle" colspan="2">{$list.welfare.surplus_fraction} ( {$defaultNavigate.confer} )</td>
                </tr>
                {/case}
                {/switch}
                {/if}
                {if $list.is_buy>0}
                    <tr>
                        <td class="am-text-middle">付费查看价格</td>
                        <td class="am-text-middle" colspan="2">
                            {$list.buy_price} {switch $list.buy_price_type} {case 0} ( {$defaultNavigate.currency} ){/case} {case 1} ( {$defaultNavigate.confer} ){/case} {/switch}
                        </td>
                    </tr>
                    {if $list.study_status!=0}
                    <tr>
                        <td class="am-text-middle">付费购买人数</td>
                        {if $list.user_buy_count==0}
                        <td class="am-text-middle" colspan="2">
                            {$list.user_buy_count|intval} 人
                        </td>
                        {else}
                        <td class="am-text-middle" style="width:40%;">
                            {$list.user_buy_count|intval} 人
                        </td>
                        <td class="am-text-middle" style="width:20%;text-align:center;">
                            <span class="am-icon-search" style="cursor:pointer;" onclick="tance('{$list.id}');">
                                查看详情
                            </span>
                        </td>
                        {/if}
                    </tr>
                    <tr>
                        <td class="am-text-middle">共获收益{switch $list.buy_price_type}{case 0}{$defaultNavigate.currency}{/case}{case 1}{$defaultNavigate.confer}{/case} {/switch} ( 税前 )</td>
                        <td class="am-text-middle" colspan="2">
                            {$list.fraction_buy_count|number_format=2} {switch $list.buy_price_type} {case 0} ( {$defaultNavigate.currency} ){/case} {case 1} ( {$defaultNavigate.confer} ){/case} {/switch}
                        </td>
                    </tr>
                    <tr>
                        <td class="am-text-middle">共获收益{switch $list.buy_price_type}{case 0}{$defaultNavigate.currency}{/case}{case 1}{$defaultNavigate.confer}{/case}{/switch} ( 税后 )</td>
                        <td class="am-text-middle" colspan="2">
                            {$list.fraction_buy_tax_count|number_format=2} {switch $list.buy_price_type} {case 0} ( {$defaultNavigate.currency} ){/case} {case 1} ( {$defaultNavigate.confer} ){/case} {/switch}
                        </td>
                    </tr>
                    {/if}
                {/if}

                {notempty name="paperWechatChannelVideoInfo.feed_token"}
                <tr>
                    <td class="am-text-middle" style="width:35%;">feed-token</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;">
                            {$paperWechatChannelVideoInfo.feed_token}
                        </div>
                    </td>
                </tr>
                {/notempty}
                {notempty name="list.study_content"}
                <tr>
                    <td class="am-text-middle" style="width:35%;">{if $list.study_type==3}活动内容{else}发帖内容{/if}</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;">
                            {$list.study_content|emoji_decode}
                        </div>
                    </td>
                </tr>
                {/notempty}
                {if $pvInfo}
                <tr>
                    <td class="am-text-middle" style="width:35%;">投票选项</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="position:relative;width:100%;overflow:hidden;padding-top:10px;">
                            {volist name="pvInfo" id="vo"}
                            <div class="am-progress" style="height:3rem;">
                                <div class="am-progress-bar am-progress-bar-success" {$vo.ratio}>
                                    <span title="点击查看投票详情" onclick="votingDetails('{$vo.id}');" style="position:absolute;width:100%;color:black;line-height:2.8rem;left:0;cursor:pointer;">
                                        {$vo.ballot_name} {$vo.voters}票
                                    </span>
                                </div>
                            </div>
                            {/volist}
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle">投票截止时间</td>
                    <td class="am-text-middle" colspan="2">
                        {if $list.vote_deadline} {:date('Y-m-d H:i:s',$list.vote_deadline)} {else} 没有截止时间 {/if}
                    </td>
                </tr>
                {/if}
                {if $list.study_type==3}
                <tr>
                    <td class="am-text-middle" style="width:35%;">活动地址</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;">
                            <a href="https://uri.amap.com/marker?position={$briskTeamInfo.brisk_address_longitude},{$briskTeamInfo.brisk_address_latitude}" target="_blank"  style="color:#999;">
                                {$briskTeamInfo.brisk_address}
                            </a>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle" style="width:35%;">活动开始时间</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;">{:date('Y-m-d H:i',$briskTeamInfo.start_time)}</div>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle" style="width:35%;">活动结束时间</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;">{:date('Y-m-d H:i',$briskTeamInfo.end_time)}</div>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle" style="width:35%;">活动人数</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;">{if $briskTeamInfo.number_of_people>0} {$briskTeamInfo.number_of_people} 人 {else} 不限制人数 {/if}</div>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle" style="width:35%;">报名人数</td>
                    <td class="am-text-middle" style="width:65%;">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;">{$briskTeamInfo.userBriskTeamCount} 人</div>
                    </td>
                    <td class="am-text-middle" style="width:20%;text-align:center;">
                        <span class="am-icon-search" style="cursor:pointer;" onclick="understand('{$list.id}');" >查看报名详情</span>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle" style="width:35%;">验证人数</td>
                    <td class="am-text-middle" style="width:65%;" colspan="2">
                        <div class="oneself-content" style="max-height:300px;width:100%;overflow:auto;">{$briskTeamInfo.userBriskTeamWriteOffCount} 人</div>
                    </td>
                </tr>
                {/if}
                {if $list.study_type!=2}
                {notempty name="list.image_part"}
                <tr>
                    <td class="am-text-middle">发帖图片</td>
                    <td class="am-text-middle" colspan="2">
                        <div style="max-height:400px;overflow:auto;">
                            {volist name="list.image_part" id="vo"}
                            <a href="{$vo}" target="_blank">
                                <img src="{$vo}" style="width: 97%;margin: 3px;"/>
                            </a>
                            {/volist}
                        </div>
                    </td>
                </tr>
                {/notempty}
                {/if}
                {notempty name="list.study_voice"}
                <tr>
                    <td class="am-text-middle">发帖语音</td>
                    <td class="am-text-middle" colspan="2">
                        <audio controls="controls" style="width:80%;">
                            <source src="{$list.study_voice}" type="audio/mpeg">
                            您的浏览器不支持音频标签。
                        </audio>
                    </td>
                </tr>
                {/notempty}
                {if $list.study_type==2||$list.study_video!=''}
                <tr>
                    <td class="am-text-middle">发帖视频</td>
                    <td class="am-text-middle" colspan="2">
                        <video id="videoLoad" src="{$list.study_video}" controls="controls" style="width:97%;height:350px;">
                            您的浏览器不支持视频标签。
                        </video>
                    </td>
                </tr>
                {/if}
                <tr>
                    <td class="am-text-middle">浏览次数</td>
                    <td class="am-text-middle" colspan="2">
                        {$list.study_heat} 次
                        <span style="font-size:12px;margin-left:5px;cursor:pointer;" data-am-modal="{target: '#shandsel-1', closeViaDimmer: 1, width: 400, height: 185}" title="修改浏览次数" onclick="sendScamperFrequencySpecifyDefaultValue(0);">
                            <span class="am-icon-edit"></span> 修改浏览次数
                        </span>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle">点赞人数</td>
                    <td class="am-text-middle" colspan="2">
                        {$list.study_laud} 人
                        <span style="font-size:12px;margin-left:5px;cursor:pointer;" data-am-modal="{target: '#shandsel-2', closeViaDimmer: 1, width: 400, height: 185}" title="修改点赞人数" onclick="sendScamperFrequencySpecifyDefaultValue(1);">
                            <span class="am-icon-edit"></span> 修改点赞人数
                        </span>
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle">回复次数</td>
                    <td class="am-text-middle" colspan="2">{$list.study_repount} 次</td>
                </tr>
                <tr>
                    <td class="am-text-middle">发帖位置</td>
                    <td class="am-text-middle" colspan="2">
                        {if $list.address_details}<a href="https://uri.amap.com/marker?position={$list.address_longitude},{$list.address_latitude}" target="_blank" style="color:#999;">{$list.address_details}</a>{else} 未知 {/if}
                    </td>
                </tr>
                <tr>
                    <td class="am-text-middle">发布时间</td>
                    <td class="am-text-middle" colspan="2">{:date('Y-m-d H:i:s',$list.adapter_time)}</td>
                </tr>
                <tr>
                    <td class="am-text-middle">审核状态</td>
                    <td class="am-text-middle" colspan="2">
                        {if $list.study_status==0}
                        <span class="am-text-warning">待审核</span>
                        {elseif $list.study_status==1}
                        <span class="am-text-success">已通过</span>
                        {elseif $list.study_status==2}
                        <span class="am-text-secondary">已打回</span>
                        {/if}
                    </td>
                </tr>
                {if $list.study_status==2}
                <tr>
                    <td class="am-text-middle">打回原因</td>
                    <td class="am-text-middle">{$list.reject_reason}</td>
                </tr>
                {/if}
                {notempty name="list.prove_time"}
                <tr>
                    <td class="am-text-middle">审核时间</td>
                    <td class="am-text-middle" colspan="2">{:date('Y-m-d H:i:s',$list.prove_time)}</td>
                </tr>
                {/notempty}
                {if $list.whether_delete==1}
                <tr>
                    <td class="am-text-middle">删除时间</td>
                    <td class="am-text-middle" colspan="2">{:date('Y-m-d H:i:s',$list.whetd_time)}</td>
                </tr>
                <tr>
                    <td class="am-text-middle">删除原因</td>
                    <td class="am-text-middle" colspan="2">{$list.whether_reason|emoji_decode}</td>
                </tr>
                {/if}
                {if $correctPluginKey}
                <tr>
                    <td class="am-text-middle">点评信息</td>
                    <td class="am-text-middle" colspan="2">
                        {volist name="prsList" id="vo"}
                        <div style="border: 1px dashed #ccc;margin-bottom: 5px;padding: 3px 0 0 3px;">
                            <div style="width:100%;display:flex;">
                                <div style="width: 60%">
                                    <a href="{:url('user/index')}&openid={$vo.user_openid}&page=1" title="{$vo.user_name|emoji_decode}" target="_blank">
                                        <img src="{$vo.user_head_img}" style="width:25px;height:25px;border-radius:50%;"/>
                                        <span style="font-size:13px;margin-left:3px;position:relative;top:2px;">{$vo.user_name|emoji_decode}</span>
                                    </a>
                                </div>
                                <div style="width: 40%;text-align: right;margin-right: 5px;">
                                    <small>
                                        {if $vo.is_show}
                                        <span title="所有用户都能看到此点评">公开点评</span>
                                        {else}
                                        <span title="仅被点评的用户看到此点评">私密点评</span>
                                        {/if}
                                    </small>
                                </div>
                            </div>
                            <div style="width:90%;padding:2px 0 0 9%;">
                                点评分数：{$vo.assess_score}
                            </div>
                            <div style="width:90%;padding:2px 0 0 9%;">
                                {$vo.assess_content|emoji_decode}
                            </div>
                            <div style="width:100%;font-size:13px;padding-left:9%;margin:10px 0;float:none;">
                                点评时间：{:date('Y-m-d H:i:s',$vo.assess_time)}
                            </div>
                            <div style="width:50%;font-size:13px;padding-left:9%;margin:3px 0 10px 0;float:left;">
                                审核状态：{switch $vo.audit_status}
                                {case 0}
                                <span style="color: orange;">待审核</span>
                                {/case}
                                {case 1}
                                <span style="color: green;">已通过</span>
                                {/case}
                                {case 2}
                                <span style="color: red;">已拒绝</span>
                                {/case}
                                {/switch}
                            </div>
                            <div style="width:50%;float:left;font-size:10px;margin:4px 0 10px 0;padding-right:5px;text-align:right;">
                                <span style="cursor:pointer;" onclick="delCorrect('{$vo.id}')">删除</span>
                            </div>
                            <div style="clear:both;"></div>
                        </div>
                        {/volist}
                        <span style="cursor: pointer;color: #1E9FFF;" title="点击新增点评数据" onclick="newCorrect();">
                            新增一条点评信息
                        </span>
                    </td>
                </tr>
                {/if}
            </table>
            {if $list.study_status==0}
            <div class="am-form-group btn-wrap">
                <div class="am-u-sm-12" style="display: flex;justify-content: center;">
                    <button type="button" class="am-btn am-btn-success am-btn-sm  am-round" style="margin-right: 120px;" onclick="satypical('1');">通过
                    </button>
                    <button type="button" class="am-btn am-btn-secondary am-btn-sm  am-round" onclick="satypical('2');">
                        打回
                    </button>
                </div>
            </div>
            {/if}
        </section>
    </div>
</div>

<!-- 修改浏览次数模态窗口 -->
<div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel-1">
    <div class="am-modal-dialog">
        <div class="am-modal-hd">
            修改浏览次数
            <a href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
        </div>
        <div class="am-modal-bd">
            <div class="am-form-group">
                <label class="am-form-label">浏览次数</label>
                <div class="am-form-field-wrap">
                    <input type="number" id="newStudyHeat" oninput="digitalCheck(this);" class="tpl-form-input" placeholder="请输入浏览次数">
                    <span class="am-form-help">次</span>
                </div>
            </div>
            <div class="am-form-group btn-wrap">
                <button type="button" class="am-btn am-btn-primary" onclick="sendScamperFrequency(0);">保存</button>
            </div>
        </div>
    </div>
</div>

<div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel-2">
    <div class="am-modal-dialog">
        <div class="am-modal-hd">
            修改点赞人数
            <a href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
        </div>
        <div class="am-modal-bd">
            <div class="am-form-group">
                <label class="am-form-label">点赞人数</label>
                <div class="am-form-field-wrap">
                    <input type="number" id="newStudyLaud" oninput="digitalCheck(this);" class="tpl-form-input" placeholder="请输入点赞人数">
                    <span class="am-form-help">人</span>
                </div>
            </div>
            <div class="am-form-group btn-wrap">
                <button type="button" class="am-btn am-btn-primary" onclick="sendScamperFrequency(1);">保存</button>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    var tance = function (reid) {
        var page = 1;
        layer.open({
            type: 2,
            id: 'erpUp',
            title: '付费详情',
            shadeClose: true,
            shade: 0.8,
            area: ['600px', '6%'],
            scrollbar: true,
            content: "{:url('essay/buyPaperUser')}&reid=" + reid,
            success: function () {
                $($('#erpUp iframe')[0].contentWindow).scroll(function () {
                    var iframeScrollHeight = $("#erpUp iframe").get(0).contentWindow.document.body.scrollHeight;
                    var iframeScrollTop = $($('#erpUp iframe')[0].contentWindow).scrollTop();
                    var iframeClientHeight = $($('#erpUp iframe')[0].contentWindow).height();
                    if (iframeScrollHeight - iframeClientHeight == iframeScrollTop) {
                        $.ajaxSettings.async = false;
                        $.post("{:url('essay/buyPaperUser')}", {
                            jetty: 1,
                            reid: reid,
                            page: ++page
                        }, function (data) {
                            if (data.length != 0) {
                                for (var i = 0; i < data.length; i++) {
                                    var html = '<tr>';
                                    html += '<td class="am-table-centered am-text-middle" title="' + data[i].user_nick_name + '">';
                                    html += '    <img src="' + data[i].user_head_sculpture + '" style="width:50px;height:50px;border-radius:50%;">';
                                    html += '    <span style="position:relative;top:5px;font-size:12px;">';
                                    if ($.trim(data[i].user_wechat_open_id) != '') {
                                        html += '    <a class="user-name" href="{:url(\'user/index\')}&egon=0&openid=' + data[i].user_wechat_open_id + '&page=1" target="_blank">' + data[i].user_nick_name + '</a>';
                                    } else {
                                        html += '    <a class="user-name" href="{:url(\'user/theoretic\')}&hazy_name=' + $.trim(data[i].user_nick_name) + '&page=1" target="_blank">' + data[i].user_nick_name + '</a>';
                                    }
                                    html += '</span>';
                                    html += '</td>';
                                    html += '<td class="am-table-centered am-text-middle">';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">' + '购买价格</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">' + data[i].buy_price + '{switch $list.buy_price_type} {case 0} ( {$defaultNavigate.currency} ){/case} {case 1} ( {$defaultNavigate.confer} ){/case} {/switch}</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">平台税率</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">'+ Math.round(data[i].buy_taxing * 100) + '%</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">结算金额</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">';
                                    html += '    ' + (data[i].buy_price * 1 - data[i].buy_taxing).toFixed(2) + '{switch $list.buy_price_type} {case 0} ( {$defaultNavigate.currency} ){/case} {case 1} ( {$defaultNavigate.confer} ){/case} {/switch}';
                                    html += '    </div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-right:#dddddd solid 1px;">购买时间</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;">' + data[i].buy_time + '</div>';
                                    html += '</td>';
                                    html += '</tr>';
                                    $($("#erpUp iframe").get(0).contentWindow.document).find('tbody').append(html);
                                }
                            } else {
                                --page;
                            }
                        }, 'json');
                        $.ajaxSettings.async = true;
                    }
                });
            }
        });
    }

    var viewFiles = function () {
        layer.open({
            type: 2,
            anim: 2,
            title: false,
            area: ['650px', '640px'],
            scrollbar: false,
            closeBtn: true,
            shadeClose: true,
            content: ["{:url('cloud/steal')}&uid={$list.buyFilesInfo.user_id}&pid={$list.buyFilesInfo.id}&type=2", 'no'],
        });
    }

    var restoreper = function (prid) {
        layer.confirm("您确定要恢复此帖吗？", {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("{:url('essay/restper')}", {'prid': prid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.href = data.url;
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }

    var satypical = function (pical) {
        var uata = {};
        uata['uplid'] = '{$list.id}';
        uata['pical'] = pical;
        if (pical == 1) {
            layer.confirm("您确定同意通过审核吗？", {
                btn: ['确定', '取消'], title: '提示'
            }, function () {
                equitable(uata);
            }, function (index) {
                layer.close(index);
            });
        } else {
            if (pical == 2) {
                var peason = "请您输入打回原因：";
            } else if (pical == 3) {
                var peason = "请您输入删帖原因：";
            }
            layer.prompt({'title': peason}, function (rea_value, index) {
                if (rea_value == '' || rea_value == 'undefined' || rea_value == null) {
                    return false;
                }
                uata['reason'] = rea_value;
                equitable(uata);
                layer.close(index);
            });
        }
    }

    var sendScamperFrequencySpecifyDefaultValue = function (type) {
        if (type == 0) {
            $('#newStudyHeat').val('{$list.study_heat}');
        } else if (type == 1) {
            $('#newStudyLaud').val('{$list.study_laud}');
        }
    }

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
    }

    var sendScamperFrequency = function (type) {
        var sendData = {};
        sendData['paid'] = '{$list.id}';
        sendData['oUnit'] = 0;
        if (type == 0) {
            sendData['type'] = '0';
            sendData['newStudyHeat'] = $('#newStudyHeat').val();
        } else if (type == 1) {
            sendData['type'] = '1';
            sendData['newStudyLaud'] = $('#newStudyLaud').val();
        }
        $.post("{:url('essay/updateScamperFrequency')}", sendData, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                    location.reload();
                });
            }
        }, 'json');
    }

    var equitable = function (uata) {
        $.ajax({
            type: "post",
            url: "{:url('essay/setails')}",
            data: uata,
            dataType: 'json',
            traditional: true,
            success: function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }
        });
    }
    
    var understand = function (prid) {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('essay/enrollment')}&prid=" + prid);
        dynamicUrl.attr('target', '_blank');
        dynamicUrl.get(0).click();
    }

    var loadThirdPartyVideo = function () {
        var videoType = parseInt('{$list.video_type}');
        var tencentVideoVid = $.trim('{$list.third_part_vid}');
        if (tencentVideoVid === '') {
            return;
        }
        switch (videoType) {
            case 1:
                var urlString = 'https://vv.video.qq.com/getinfo?platform=101001&charge=0&otype=json&defn=shd&vids=' + tencentVideoVid;
                $.ajax({
                    url: urlString,
                    dataType: "jsonp",
                    jsonp: "callback",
                    success: function (data) {
                        try {
                            var fileName = data['vl']['vi'][0]['fn'];
                            var fvkey = data['vl']['vi'][0]['fvkey'];
                            var host = data['vl']['vi'][0]['ul']['ui'][0]['url'];
                            $('#videoLoad').attr({'src': host + fileName + '?vkey=' + fvkey});
                        } catch (e) {
                        }
                    }
                });
                break;
            case 2:

                //{if $videoParsePluginKey}

                $.post("{:url('resolve/parse_url')}", {'url': tencentVideoVid, 'isCover': 0}, function (data) {
                    $('#videoLoad').attr({'src': data.video});
                });

                //{/if}

                break;
        }
    }

    var votingDetails = function (pvid) {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('leading/votingPlenary')}&pvid=" + pvid);
        dynamicUrl.attr('target', '_blank');
        dynamicUrl.get(0).click();
    }


    $(function () {
        loadThirdPartyVideo();
    });
</script>
{if $correctPluginKey}
<script>

    var newCorrect = function () {
        layer.open({
            type: 2,
            anim: 2,
            title: false,
            area: ['600px', '550px'],
            scrollbar: false,
            closeBtn: true,
            shadeClose: true,
            content: ["{:url('people/newCorrect')}&fid={$list.id}", 'no'],
        });
    }

    var delCorrect = function (fid) {
        layer.confirm('您确定要删除这条数据吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('people/delCorrect')}", {'fid': fid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }
</script>
{/if}
{/block}