<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>付费详情</title>
    <style>.am-table-centered{margin:0;}a{color:#000000 !important;text-decoration:none !important;}a:hover{color:#000000 !important;}div{font-size:14px;}</style>
</head>
<link rel="stylesheet" href="assets/css/amazeui.min.css?v=1.0"/>
<body>
<table class="am-table am-table-bordered am-table-centered">
    <tr>
        <th width="30%">付费用户</th>
        <th width="70%">付费详情</th>
    </tr>
    {volist name="list" id="vo"}
    <tr>
        <td class="am-table-centered am-text-middle" title="{$vo.user_nick_name|emoji_decode}">
            <img src="{$vo.user_head_sculpture}" style="width:50px;height:50px;border-radius:50%;">
            <span style="position:relative;top:5px;font-size:12px;">
                {if $vo.uvirtual == 0}
                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                    {$vo.user_nick_name|emoji_decode}
                </a>
                {else}
                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                    {$vo.user_nick_name|emoji_decode}
                </a>
                {/if}
            </span>
        </td>
        <td class="am-table-centered am-text-middle">
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">
                购买价格
            </div>
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">
                {$vo.buy_price} ( {switch $vo.buy_type}{case 0}{$defaultNavigate.currency}{/case}{case 1}{$defaultNavigate.confer}{/case}{/switch} )
            </div>
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">
                平台税率
            </div>
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">
                {$vo.buy_taxing*100}%
            </div>
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">
                结算金额
            </div>
            <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">
                {$vo.buy_price * (1 - $vo.buy_taxing)|number_format=2} ( {switch $vo.buy_type}{case 0}{$defaultNavigate.currency}{/case}{case 1}{$defaultNavigate.confer}{/case}{/switch} )
            </div>
            <div style="float:left;width:50%;height:50%;border-right:#dddddd solid 1px;">
                购买时间
            </div>
            <div style="float:left;width:50%;height:50%;">
                {:date('Y-m-d H:i:s',$vo.buy_time)}
            </div>
        </td>
    </tr>
    {/volist}
</table>
<script src="assets/js/jquery.min.js?v=1.0"></script>
<script src="assets/js/amazeui.min.js?v=1.0"></script>
</body>
</html>