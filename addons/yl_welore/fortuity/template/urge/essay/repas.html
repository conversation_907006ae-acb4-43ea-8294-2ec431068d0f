{extend name="/base"/}
{block name="main"}
<style>
    .am-u-sm-9{margin-top:4px;}.am-form-horizontal .am-form-label{text-align:left;}.tpl-amazeui-form .am-form-label{color:#000000;}[class*=am-u-] + [class*=am-u-]:last-child{float:right;font-size:14px;margin-top:8px;color:#999;}
    .am-table > tbody > tr > td{font-size:14px;margin-top:8px;}.am-table > tbody > tr > td:nth-child(1){text-align:right;color:#000000;padding-right:20px;}.am-table > tbody > tr > td:nth-child(2){padding-left:20px;color:#999;}table{table-layout:fixed;word-wrap:break-word;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-comment-o"></span> 回复详情
        </div>
        <div style="text-align: right">
            <a href="{:url('reply')}" target="_blank">
                <button type="button" class="am-btn am-btn-default am-btn-sm">回复列表</button>
            </a>
            {if $list.whether_delete==0}
            <button type="button" class="am-btn am-btn-danger am-btn-sm" onclick="satypical();">删除回复</button>
            {else}
            <button type="button" class="am-btn am-btn-success am-btn-sm" onclick="restoreReply('{$list.id}');">恢复回复</button>
            {/if}
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12">
                <div class="am-form am-form-horizontal" style="border:solid 1px #cccccc;width:700px;margin: 0 auto;padding:25px 3% 10px 3%;overflow: hidden;box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 1);">

                    <table class="am-table am-table-bordered">
                        <tr>
                            <td class="am-text-middle" style="width:35%;">帖子标题</td>
                            <td class="am-text-middle" style="width:65%;">
                                <a href="{:url('setails')}&uplid={$list.paper_id}" target="_blank">
                                    {if $list.study_title!=''} {$list.study_title|emoji_decode} {else} ———— {/if}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle">回复用户</td>
                            <td class="am-text-middle">
                                <a href="{:url('user/index')}&egon=0&openid={$list.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    <span style="font-size:13px;position:relative;top:2px;">{$list.user_nick_name|emoji_decode}</span>
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle">回复类型</td>
                            <td class="am-text-middle">
                                {if $list.reply_type==0}{if $list.reply_content!=''}文字{else}图片{/if}{elseif $list.reply_type==1}语音{/if}
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle">当前楼层</td>
                            <td class="am-text-middle">
                                {$list.phase}楼
                            </td>
                        </tr>
                        {if $list.reply_content}
                        <tr>
                            <td class="am-text-middle">回复内容</td>
                            <td class="am-text-middle">{$list.reply_content|emoji_decode}</td>
                        </tr>
                        {/if}
                        {if $list.image_part[0]}
                        <tr>
                            <td class="am-text-middle">回复图片</td>
                            <td class="am-text-middle">
                                <div style="max-height:400px;width:100%;overflow:auto;">
                                    {volist name="list.image_part" id="vo"}
                                    <a href="{$vo}" target="_blank">
                                        <img src="{$vo}" style="width:100%;"/>
                                    </a>
                                    {/volist}
                                </div>
                            </td>
                        </tr>
                        {/if}
                        {if $list.reply_type==1}
                        <tr>
                            <td class="am-text-middle">回复语音</td>
                            <td class="am-text-middle">
                                {if $list.reply_voice}
                                <audio src="{$list.reply_voice}" controls="controls" style="width:100%;"></audio>
                                {else}
                                无内容
                                {/if}
                            </td>
                        </tr>
                        {/if}
                        <tr>
                            <td class="am-text-middle">点赞人数</td>
                            <td class="am-text-middle">
                                {$list.praise} 人
                                <span style="font-size:12px;margin-left:5px;cursor:pointer;" data-am-modal="{target: '#shandsel-2', closeViaDimmer: 0, width: 400, height: 185}" title="修改点赞人数" onclick="sendScamperFrequencySpecifyDefaultValue();">
                                    <span class="am-icon-edit"></span> 修改点赞人数
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td class="am-text-middle">回复时间</td>
                            <td class="am-text-middle">{:date('Y-m-d H:i:s',$list.apter_time)}</td>
                        </tr>
                        <tr>
                            <td class="am-text-middle">审核状态</td>
                            <td class="am-text-middle">
                                {if $list.reply_status==0}
                                <span class="am-text-warning">待审核</span>
                                {elseif $list.reply_status==1}
                                <span class="am-text-success">已通过</span>
                                {elseif $list.reply_status==2}
                                <span class="am-text-danger">未通过</span>
                                {/if}
                            </td>
                        </tr>
                        {if $list.prove_time}
                        <tr>
                            <td class="am-text-middle">审核时间</td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$list.prove_time)}
                            </td>
                        </tr>
                        {/if}
                        {if $list.whether_delete==1}
                        <tr>
                            <td class="am-text-middle">删除时间</td>
                            <td class="am-text-middle">{:date('Y-m-d H:i:s',$list.whetd_time)}</td>
                        </tr>
                        <tr>
                            <td class="am-text-middle">删除原因</td>
                            <td class="am-text-middle">{$list.whether_reason}</td>
                        </tr>
                        {/if}
                        {notempty name="rep_dux"}
                        <tr>
                            <td class="am-text-middle">评论回复</td>
                            <td class="am-text-middle">
                                {volist name="rep_dux" id="vo"}
                                <div style="width:90%;">
                                    <a href="{:url('user/index')}&egon=0&openid={$vo.user_openid}&page=1" title="{$vo.user_name|emoji_decode}" target="_blank">
                                        <img src="{$vo.user_head_img}" style="width:25px;height:25px;border-radius:50%;" />
                                        <span style="font-size:13px;margin-left:3px;position:relative;top:2px;">{$vo.user_name|emoji_decode}</span>
                                    </a>
                                </div>
                                <div style="width:90%;padding:2px 0 0 9%;">
                                    {$vo.duplex_content}
                                </div>
                                <div style="width:90%;padding:2px 0 0 9%;">
                                    审核状态：<a href="{:url('tissue/discuss')}">{if $vo.duplex_status == 0}
                                    <span class="am-text-warning">待审核</span>
                                    {elseif $vo.duplex_status == 1}
                                    <span class="am-text-success">已通过</span>
                                    {elseif $vo.duplex_status == 2}
                                    <span class="am-text-danger">未通过</span>
                                    {/if}</a>
                                </div>
                                <div style="width:50%;font-size:13px;padding-left:9%;margin:3px 0 10px 0;float:left;">
                                    {:date('Y-m-d H:i:s',$vo.duplex_time)}
                                </div>
                                <div style="width:50%;float:left;font-size:10px;margin:4px 0 10px 0;padding-left:10px;text-align:left;">
                                    <span style="cursor:pointer;" onclick="delDiscuss(0,'{$vo.id}')">删除</span>
                                </div>
                                <div style="clear:both;"></div>
                                {/volist}
                            </td>
                        </tr>
                        {/notempty}
                    </table>
                    {if $list.reply_status==0}
                    <div class="am-form-group" style="text-align:center;margin-top:10px;">
                        <div class="am-u-sm-12">
                            <button type="button" class="am-btn am-btn-success am-btn-sm  am-round" style="margin-right: 120px;" onclick="judgment('1');">
                                通过
                            </button>
                            <button type="button" class="am-btn am-btn-warning am-btn-sm  am-round" onclick="judgment('2');">
                                拒绝
                            </button>
                        </div>
                    </div>
                    {/if}
                </div>
            </div>
        </div>
    </div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel-2">
        <div class="am-modal-dialog" style="background:#fefffe;">
            <div class="am-modal-hd">
                <span style="font-size: 14px;position: absolute;left:12px;top:7px;">修改点赞人数</span>
                <a href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd am-form tpl-form-line-form">
                <div class="am-form-group" style="margin-top:35px;">
                    <label class="am-u-sm-4 am-form-label" style="font-size:14px;margin:5px 0 0 -5px;">点赞人数</label>
                    <div class="am-u-sm-6">
                        <input type="number" id="newStudyPraise" oninput="digitalCheck(this);" class="tpl-form-input" style="margin:3px 0 0 -10px;" placeholder="请输入点赞人数">
                    </div>
                    <div class="am-u-sm-2 am-form-label" style="margin-top:5px;">
                        <span style="position:relative;left:-40px;font-size:14px;">人</span>
                    </div>
                </div>
                <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top:10px;">
                    <button type="button" class="am-btn am-btn-sm" class="am-btn" onclick="sendScamperFrequency();">保存</button>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    var judgment = function (code) {
        if (code == 1) {
            layer.confirm("您确定同意通过审核吗？", {
                btn: ['确定', '取消'], title: '提示'
            }, function () {
                transmitDoom(code);
            }, function (index) {
                layer.close(index);
            });
        } else {
            layer.prompt({'title': "请您输入拒绝原因："}, function (rejectValue, index) {
                if ($.trim(rejectValue) == '') {
                    return false;
                }
                transmitDoom(code, rejectValue);
                layer.close(index);
            });
        }
    }

    var sendScamperFrequencySpecifyDefaultValue = function () {
        $('#newStudyLaud').val('{$list.praise}');
    }

    var sendScamperFrequency = function (type) {
        var sendData = {};
        sendData['paid'] = '{$list.id}';
        sendData['oUnit'] = 1;
        sendData['newStudyPraise'] = $('#newStudyPraise').val();
        $.post("{:url('essay/updateScamperFrequency')}", sendData, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                    location.reload();
                });
            }
        }, 'json');
    }

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
    }

    var transmitDoom = function (code, caption) {
        $.ajaxSettings.async = false;
        $.post("{:url('essay/adjudicationReply')}", {'reid': '{$list.id}', code: code, caption: caption}, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                    location.reload();
                });
            }
        }, 'json');
        $.ajaxSettings.async = true;
    }

    var restoreReply = function (repId) {
        layer.confirm('您确定要恢复这条回复吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("{:url('essay/regainReply')}", {repId: repId}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.href = data.url + '&uplid={$list.id}';
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }


    var satypical = function () {
        var uata = {};
        uata['uplid'] = '{$list.id}';
        layer.prompt({'title': "请您输入删帖原因："}, function (rea_value, index) {
            if (rea_value == '' || rea_value == 'undefined' || rea_value == null) {
                return false;
            }
            uata['reason'] = rea_value;
            equitable(uata);
            layer.close(index);
        });
    }

    var delDiscuss = function (process, acid) {
        var confimDel = function (acid) {
            $.ajax({
                type: "post",
                url: "{:url('tissue/delDiscuss')}",
                data: {acid: acid},
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }
            });
        }
        if (process === 1) {
            confimDel(acid);
        } else {
            layer.confirm("您确定要删除这条评论吗？", {
                btn: ['确定', '取消'], title: '提示 <span style=\"color:red;\">( 删除后数据不可恢复 )</span>'
            }, function () {
                confimDel([acid]);
            }, function (index) {
                layer.close(index);
            });
        }
    }

    var equitable = function (uata) {
        $.ajax({
            type: "post",
            url: "{:url('essay/repas')}",
            data: uata,
            dataType: 'json',
            traditional: true,
            success: function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.href = "{:url('essay/reply')}";
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }
        });
    }
</script>
{/block}