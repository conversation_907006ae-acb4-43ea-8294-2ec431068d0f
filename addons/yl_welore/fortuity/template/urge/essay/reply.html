{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-comment-o {margin-right: 5px;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}

    .filter-btn-group .am-btn {margin-right: 10px; border-radius: 3px;}
    .filter-btn-group .am-btn.active {background-color: #23b7e5; color: white; border-color: #23b7e5;}
    .batch-actions-group > .am-btn { margin-left: 8px; border-radius: 3px;}
    
    .action-btn {display: inline-block; padding: 4px 10px; background: #fff; border: 1px solid #ddd; color: #666; border-radius: 3px; font-size: 12px; cursor: pointer; transition: all 0.3s;}
    .action-btn:hover {border-color: #23b7e5; color: #23b7e5; background-color: #f5fafd;}
    
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-top: 15px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}

    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a, .am-pagination > .am-active > a:hover {background-color: #23b7e5;border-color: #23b7e5;color:#fff;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-comment-o"></span> 回复列表
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索回复或用户...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-sm filter-btn-group">
                        <a href="{:url('essay/reply')}&egon=0" type="button" class="am-btn am-btn-default {if $egon==0}active{/if}">正常显示</a>
                        <a href="{:url('essay/reply')}&egon=1" type="button" class="am-btn am-btn-default {if $egon==1}active{/if}">回收站</a>
                    </div>
                </div>
            </div>
            {if $egon!=1}
            <div class="am-u-sm-12 am-u-md-6" style="text-align: right;">
                <div class="am-btn-group am-btn-group-sm batch-actions-group">
                    <button type="button" class="am-btn am-btn-success" onclick="rebatch('1');">批量通过</button>
                    <button type="button" class="am-btn am-btn-secondary" style="margin-left: 8px;" onclick="rebatch('2');">批量拒绝</button>
                    <button type="button" class="am-btn am-btn-danger" style="margin-left: 8px;" onclick="rebatch('3');">批量删除</button>
                </div>
            </div>
            {/if}
        </div>

        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            {if $egon!=1}
                            <th width="5%">
                                <input id="withole" type="checkbox" class="tpl-table-fz-check">全选
                            </th>
                            {/if}
                            <th width="8%">ID</th>
                            <th width="10%">用户昵称</th>
                            <th width="13%">回复内容</th>
                            <th width="12%">帖子 标题 / 内容</th>
                            <th width="12%">所属圈子</th>
                            <th width="10%">回复时间</th>
                            <th width="10%">审核时间</th>
                            <th width="10%">审核状态</th>
                            <th width="10%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            {if $egon!=1}
                            <td>
                                <input type="checkbox" class="tpl-table-fz-check elctive" value="{$vo.id}">
                            </td>
                            {/if}
                            <td>{$vo.id}</td>
                            <td>
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode|subtext=6}
                                </a>
                            </td>
                            <td>
                                {if $vo.reply_type==0}
                                {if $vo.reply_content!=''}
                                {$vo.reply_content|emoji_decode|strip_tags|subtext=6|$expressionHtml}
                                {else}
                                <span class="am-text-muted">[图片]</span>
                                {/if}
                                {elseif $vo.reply_type==1}
                                <span class="am-text-muted">[语音]</span>
                                {/if}
                            </td>
                            <td>
                                <a href="{:url('essay/setails')}&uplid={$vo.paper_id}" target="_blank">
                                    {if $vo.study_title!=''}
                                        {$vo.study_title|strip_tags|emoji_decode|subtext=6}
                                    {elseif $vo.study_content!=''}
                                        {$vo.study_content|strip_tags|emoji_decode|subtext=6}
                                    {else}
                                        无
                                    {/if}
                                </a>
                            </td>
                            <td>{$vo.tory.realm_name}</td>
                            <td>
                                {:date('Y-m-d H:i:s',$vo.apter_time)}
                            </td>
                            <td>
                                {if $vo.prove_time} {:date('Y-m-d H:i:s',$vo.prove_time)}{else}-{/if}
                            </td>
                            <td>
                                {if $vo.reply_status == 0}
                                <span class="am-text-warning">待审核</span>
                                {elseif $vo.reply_status == 1}
                                <span class="am-text-success">已通过</span>
                                {elseif $vo.reply_status == 2}
                                <span class="am-text-danger">未通过</span>
                                {/if}
                            </td>
                            <td>
                                <button type="button" class="action-btn" onclick="uploof('{$vo.id}','{$vo.token}');">
                                    <span class="am-icon-search"></span> 查看
                                </button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    !function () {
        $('#withole').click(function () {
            $('.elctive').prop('checked', $(this).prop('checked'));
        });

        $('.elctive').click(function () {
            var allChecked = true;
            $('.elctive').each(function () {
                if (!$(this).prop('checked')) {
                    allChecked = false;
                    return false; // break loop
                }
            });
            $('#withole').prop('checked', allChecked);
        });
    }();

    var rebatch = function (guid) {
        var selectedIds = $('.elctive:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            layer.alert('很抱歉，当前没有选中要操作的数据', {'title': '系统提示'});
            return;
        }
        
        var actionText = '', promptTitle = '';
        switch (parseInt(guid)) {
            case 1: actionText = '通过'; break;
            case 2: actionText = '拒绝'; promptTitle = '请您输入拒绝原因：'; break;
            case 3: actionText = '删除'; promptTitle = '请您输入删除原因：'; break;
        }

        var performAction = function(reason) {
            var loadIndex = layer.load(1, { shade: [0.1,'#fff'] });
            var successCount = 0;
            var promises = [];

            selectedIds.forEach(function(id) {
                var postData = {};
                var url = '';

                switch (parseInt(guid)) {
                    case 1:
                    case 2:
                        url = "{:url('essay/adjudicationReply')}";
                        postData = {'reid': id, 'code': guid, 'caption': reason};
                        break;
                    case 3:
                         url = "{:url('essay/repas')}";
                         postData = {'uplid': id, 'reason': reason};
                         break;
                }
                
                promises.push(
                    $.post(url, postData, function (data) {
                        if (data.code > 0) {
                            successCount++;
                        }
                    }, 'json')
                );
            });
            
            $.when.apply($, promises).then(function() {
                layer.close(loadIndex);
                 layer.msg('批量操作完成！共处理 ' + selectedIds.length + ' 条，成功 ' + successCount + ' 条。', {
                    icon: 1,
                    time: 1600
                }, function () {
                    location.reload();
                });
            });
        };

        if (guid === '1') {
            layer.confirm('您确定要' + actionText + '已选中的回复吗？', {
                btn: ['确定', '取消'], title: '提示'
            }, function (index) {
                layer.close(index);
                performAction('');
            });
        } else {
            layer.prompt({'title': promptTitle, formType: 2}, function (value, index) {
                layer.close(index);
                performAction(value);
            });
        }
    }

    var uploof = function (id, token) {
        location.href = "{:url('essay/repas')}&uplid=" + id + '&token=' + token;
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        var url = "{:url('essay/reply')}&egon={$egon}&page={$page}";
        if (fz_name) {
            url += "&hazy_name=" + encodeURIComponent(fz_name);
        }
        location.href = url;
    }

</script>
{/block}