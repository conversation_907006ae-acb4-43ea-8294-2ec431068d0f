{extend name="/base"/}
{block name="main"}
<style>
    /* Modern Dashboard Styles */
    :root {
        --body-bg: #f8f9fa;
        --widget-bg: #ffffff;
        --text-color: #495057;
        --heading-color: #343a40;
        --border-color: #e9ecef;
        --shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        --border-radius: 8px;
    }

    .modern-dashboard {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        background-color: var(--body-bg);
        padding: 24px;
    }

    .stat-cards-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 24px;
        margin-bottom: 24px;
    }

    .stat-card {
        background-color: var(--widget-bg);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        padding: 20px;
        display: flex;
        align-items: center;
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 12px rgba(0, 0, 0, 0.08);
    }

    .stat-card .icon-wrapper {
        flex-shrink: 0;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
    }

    .stat-card .icon-wrapper i {
        font-size: 24px;
        color: #fff;
    }
    
    .stat-card .details .number {
        font-size: 28px;
        font-weight: 600;
        color: var(--heading-color);
        line-height: 1.2;
    }
    
    .stat-card .details .number span {
        font-size: 14px;
        font-weight: 400;
        color: var(--text-color);
    }

    .stat-card .details .desc {
        font-size: 14px;
        color: var(--text-color);
        margin-top: 4px;
    }
    
    /* Icon Colors */
    .icon-users { background-color: #3B82F6; }
    .icon-circle { background-color: #10B981; }
    .icon-chart { background-color: #F59E0B; }
    .icon-comments { background-color: #8B5CF6; }
    .icon-cart { background-color: #EF4444; }
    .icon-rmb { background-color: #14B8A6; }
    .icon-gift { background-color: #EC4899; }
    .icon-stack { background-color: #6366F1; }

    .widgets-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 24px;
    }

    .widget {
        background-color: var(--widget-bg);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        overflow: hidden;
    }

    .widget-header {
        padding: 16px 24px;
        border-bottom: 1px solid var(--border-color);
    }

    .widget-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--heading-color);
        display: flex;
        align-items: center;
    }
    
    .widget-title i {
        margin-right: 10px;
        font-size: 20px;
    }
    
    .font-green { color: #10B981; }
    .font-red { color: #EF4444; }
    
    .widget-content {
        padding: 24px;
    }
    
    #tpl-echarts-A {
        width: 100%;
        height: 350px; /* Ensure chart has height */
    }
    
    .active-users-widget .summary {
        text-align: center;
        margin-bottom: 20px;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: var(--border-radius);
    }
    
    .active-users-widget .summary .title {
        font-size: 14px;
        color: var(--text-color);
    }
    
    .active-users-widget .summary .number {
        font-size: 24px;
        font-weight: 700;
        color: #F59E0B;
        margin-top: 5px;
    }
    
    .widget-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .widget-table th, .widget-table td {
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }
    
    .widget-table thead th {
        font-size: 13px;
        font-weight: 600;
        color: var(--text-color);
        background-color: #f8f9fa;
        text-transform: uppercase;
    }
    
    .widget-table tbody tr:last-child td {
        border-bottom: none;
    }

    .widget-table tbody tr:hover {
        background-color: #f8f9fa;
    }
    
    .widget-table .user-info {
        display: flex;
        align-items: center;
    }

    .widget-table .user-pic {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 12px;
        object-fit: cover;
    }
    
    .widget-table .user-name {
        font-weight: 500;
        color: var(--heading-color);
        text-decoration: none;
    }
    
    .widget-table .user-name:hover {
        text-decoration: underline;
    }
    
    .widget-table .percentage {
        font-weight: 700;
        color: #10B981;
    }

</style>

<div class="modern-dashboard">
    <!-- Stat Cards Section -->
    <div class="stat-cards-grid">
        <div class="stat-card">
            <div class="icon-wrapper icon-users"><i class="am-icon-users"></i></div>
            <div class="details">
                <div class="number">{$new_user_today}</div>
                <div class="desc">今日新增用户</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="icon-wrapper icon-circle"><i class="am-icon-circle-o"></i></div>
            <div class="details">
                <div class="number">{$new_toryon_today}</div>
                <div class="desc">今日申请创建圈子</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="icon-wrapper icon-chart"><i class="am-icon-bar-chart-o"></i></div>
            <div class="details">
                <div class="number">{$new_paper_today}</div>
                <div class="desc">今日发帖数量</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="icon-wrapper icon-comments"><i class="am-icon-comments-o"></i></div>
            <div class="details">
                <div class="number">{$new_punch_today}</div>
                <div class="desc">今日签到人数</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="icon-wrapper icon-cart"><i class="am-icon-shopping-cart"></i></div>
            <div class="details">
                <div class="number">{$new_sorder_today}</div>
                <div class="desc">今日商品订单</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="icon-wrapper icon-rmb"><i class="am-icon-rmb"></i></div>
            <div class="details">
                <div class="number">{$new_userial_today}</div>
                <div class="desc">今日用户充值收益</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="icon-wrapper icon-gift"><i class="am-icon-gift"></i></div>
            <div class="details">
                <div class="number">{$new_subsicont_today} <span>({$defaultNavigate.currency})</span></div>
                <div class="desc">今日送礼总金额</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="icon-wrapper icon-stack"><i class="am-icon-stack-overflow"></i></div>
            <div class="details">
                <div class="number">{$new_subsidy_today} <span>({$defaultNavigate.currency})</span></div>
                <div class="desc">今日平台总扣税额</div>
            </div>
        </div>
    </div>

    <!-- Widgets Section -->
    <div class="widgets-grid">
        <!-- Monthly Stats Chart -->
        <div class="widget">
            <div class="widget-header">
                <div class="widget-title font-green">
                    <i class="am-icon-line-chart"></i>
                    <span>本月用户统计</span>
                </div>
            </div>
            <div class="widget-content">
                <div class="tpl-echarts" id="tpl-echarts-A"></div>
            </div>
        </div>

        <!-- Active Users Table -->
        <div class="widget active-users-widget">
            <div class="widget-header">
                <div class="widget-title font-red">
                    <i class="am-icon-bar-chart"></i>
                    <span>本周发帖活跃用户</span>
                </div>
            </div>
            <div class="widget-content">
                <div class="summary">
                    <div class="title">共计 (单位：条)</div>
                    <div class="number">{$large_total}</div>
                </div>
                <table class="widget-table">
                    <thead>
                        <tr>
                            <th>用户昵称</th>
                            <th style="text-align: center;">发帖次数</th>
                            <th style="text-align: center;">统计百分比</th>
                        </tr>
                    </thead>
                    <tbody>
                        {volist name="large_user" id="vo"}
                        <tr>
                            <td>
                                <a class="user-info" href="{:url('essay/index')}&egon=0&hazy_name={$vo.user_wechat_open_id}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    <img src="{$vo.user_head_sculpture}" class="user-pic" alt="avatar">
                                    <span class="user-name">{$vo.user_nick_name|emoji_decode|subtext=10}</span>
                                </a>
                            </td>
                            <td style="text-align: center;">{$vo.hasty}</td>
                            <td style="text-align: center;" class="percentage">{$vo.percentage}%</td>
                        </tr>
                        {/volist}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{/block}