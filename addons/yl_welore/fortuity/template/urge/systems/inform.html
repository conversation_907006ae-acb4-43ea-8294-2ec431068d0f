{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-envelope"></span> 订阅消息
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">YL0001</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="YL0001" value="{$list.parallelism_data.YL0001}" >
                            <small>新的评论提醒 （文章标题、评论用户、评论内容、评论时间）</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">YL0002</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="YL0002" value="{$list.parallelism_data.YL0002}" >
                            <small>动态点赞通知（帖子标题、点赞用户、点赞时间）</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">YL0003</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="YL0003" value="{$list.parallelism_data.YL0003}" >
                            <small>帖子被收藏通知（收藏帖子、收藏用户、收藏时间）</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">YL0004</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="YL0004" value="{$list.parallelism_data.YL0004}" >
                            <small>新的回复提醒（留言主题、用户、回复内容、回复时间）</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">YL0005</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="YL0005" value="{$list.parallelism_data.YL0005}" >
                            <small>申请加入通知（圈子名称、申请人、温馨提示、申请时间）</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">YL0006</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="YL0006" value="{$list.parallelism_data.YL0006}" >
                            <small>留言提醒 （留言内容、时间、留言者）</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">YL0007</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="YL0007" value="{$list.parallelism_data.YL0007}" >
                            <small>收到赞赏通知（赞赏详情、赞赏时间）</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">YL0008</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="YL0008" value="{$list.parallelism_data.YL0008}" >
                            <small>审核结果通知（审核内容、审核结果、审核时间）</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">YL0009</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="YL0009" value="{$list.parallelism_data.YL0009}" >
                            <small>站内信提醒 （详情内容、时间）</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">YL0010</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="YL0010" value="{$list.parallelism_data.YL0010}" >
                            <small>事件处理进度通知（事件内容、处理意见）</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">YL0011</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="YL0011" value="{$list.parallelism_data.YL0011}" >
                            <small>认证审核通知（申请时间、认证内容、审核结果、备注）</small>
                        </div>
                    </div>

                    <div class="am-form-group" style="display: flex; justify-content: center;margin: 60px 0 40px 0;">
                        <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>
    var holdSave = function () {
        var parallelism_data = {};
        parallelism_data['YL0001'] = $.trim($('#YL0001').val());
        parallelism_data['YL0002'] = $.trim($('#YL0002').val());
        parallelism_data['YL0003'] = $.trim($('#YL0003').val());
        parallelism_data['YL0004'] = $.trim($('#YL0004').val());
        parallelism_data['YL0005'] = $.trim($('#YL0005').val());
        parallelism_data['YL0006'] = $.trim($('#YL0006').val());
        parallelism_data['YL0007'] = $.trim($('#YL0007').val());
        parallelism_data['YL0008'] = $.trim($('#YL0008').val());
        parallelism_data['YL0009'] = $.trim($('#YL0009').val());
        parallelism_data['YL0010'] = $.trim($('#YL0010').val());
        parallelism_data['YL0011'] = $.trim($('#YL0011').val());
        $.post("{:url('systems/inform')}", parallelism_data, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000});
            }
        }, 'json');
    }
</script>
{/block}