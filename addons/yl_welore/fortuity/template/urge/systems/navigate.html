{extend name="/base"/}
{block name="main"}
<style>.img-select{box-shadow:0px 0px 5px 5px rgb(102,153,255) !important;}@media screen and (min-width:1920px){.topmost{white-space:nowrap!important;}}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-life-bouy"></span> 自定义设置
        </div>
    </div>
    <div class="tpl-block am-form am-form-horizontal">
        <div class="topmost">
            <div class="am-g" style="margin: 20px 0px;">
                <div class="am-u-sm-4">
                    <label class="am-u-sm-5 am-form-label" style="padding-top:5px;">自定义 "首页标题" 名称</label>
                    <div class="am-u-sm-7">
                        <input id="sHomeTitle" style="width: 200px;" class="am-form-field am-round" type="text" value="{$list.home_title}" placeholder="首页名称">
                    </div>
                </div>
                <div class="am-u-sm-4">
                    <label class="am-u-sm-7 am-form-label" style="padding-top: 4px;">底部导航 "站内信"替换"小商品"</label>
                    <div class="am-u-sm-5">
                        <div class="tpl-switch">
                            <input id="electSheathe" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $list.elect_sheathe==1}checked{/if}>
                            <div class="tpl-switch-btn-view">
                                <div></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="am-u-sm-4">
                    <div class="am-form-group">
                        <label class="am-u-sm-5 am-form-label" style="padding-top:10px;">自定义 "贝壳商城" 名称</label>
                        <div class="am-u-sm-7">
                            <input id="mall" style="width: 200px;padding: 5px;margin-top: 10px;height: 30px;" type="text" value="{if $list.mall}{$list.mall}{else}贝壳商城{/if}">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g" style="margin: 50px 0px;">
            <div class="am-u-sm-4">
                <div class="am-form-group">
                    <label class="am-u-sm-5 am-form-label" style="padding-top: 10px;">自定义 "圈子" 名称</label>
                    <div class="am-u-sm-7">
                        <input id="landgrave" style="width: 200px;padding: 5px;margin-top: 10px;height: 30px;" type="text" value="{$list.landgrave}">
                    </div>
                </div>
            </div>
            <div class="am-u-sm-4">
                <div class="am-form-group">
                    <label class="am-u-sm-5 am-form-label" style="padding-top: 10px;">自定义 "贝壳" 名称</label>
                    <div class="am-u-sm-7">
                        <input id="currency" style="width: 200px;padding: 5px;margin-top: 10px;height: 30px;" type="text" value="{$list.currency}">
                    </div>
                </div>
            </div>
            <div class="am-u-sm-4">
                <div class="am-form-group">
                    <label class="am-u-sm-5 am-form-label" style="padding-top: 10px;">自定义 "积分" 名称</label>
                    <div class="am-u-sm-7">
                        <input id="confer" style="width: 200px;padding: 5px;margin-top: 10px;height: 30px;" type="text" value="{$list.confer}">
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g" style="margin: 50px 0px;">
            <div class="am-u-sm-4">
                <div class="am-form-group">
                    <label class="am-u-sm-5 am-form-label" style="padding-top: 5px;">导航背景颜色</label>
                    <div class="am-u-sm-7">
                        <input style="width: 200px;padding: 2px;border-radius: 10px;" type="color" value="{$list.pattern_data.style.backcolor}" onchange="transmutationNext(this);">
                        <input id="backcolor" style="width: 200px;padding: 2px;border-radius: 10px;margin-top: 10px;height: 30px;" type="text" value="{$list.pattern_data.style.backcolor}" oninput="transmutationPrev(this);">
                    </div>
                </div>
            </div>
            <div class="am-u-sm-4">
                <div class="am-form-group">
                    <label class="am-u-sm-5 am-form-label" style="padding-top: 5px;">导航字体颜色</label>
                    <div class="am-u-sm-7">
                        <input style="width: 200px;padding: 2px;border-radius: 10px;" type="color" value="{$list.pattern_data.style.font_color}" onchange="transmutationNext(this);">
                        <input id="fontColor" style="width: 200px;padding: 2px;border-radius: 10px;margin-top: 10px;height: 30px;" type="text" value="{$list.pattern_data.style.font_color}" oninput="transmutationPrev(this);">
                    </div>
                </div>
            </div>
            <div class="am-u-sm-4">
                <div class="am-form-group">
                    <label class="am-u-sm-5 am-form-label" style="padding-top: 5px;">导航字体选中颜色</label>
                    <div class="am-u-sm-7">
                        <input style="width: 200px;padding: 2px;border-radius: 10px;" type="color" value="{$list.pattern_data.style.font_color_active}" onchange="transmutationNext(this);">
                        <input id="fontColorActive" style="width: 200px;padding: 2px;border-radius: 10px;margin-top: 10px;height: 30px;" type="text" value="{$list.pattern_data.style.font_color_active}" oninput="transmutationPrev(this);">
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g" style="display: flex; flex-wrap: wrap;justify-content: center;">
            <div style="margin:calc(2% - 10px);text-align: center;color: #ffffff;box-sizing:border-box;">
                <div style="background-image: linear-gradient(to right, #434343 0%, black 100%);line-height: 80px;width: 270px;height: 200px;text-align: center;margin: 0 auto;">
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">名称</p>
                    <input id="writingTitle" type="text" style="border-radius: 3px;margin-left: 10px;width: 150px;display: inline;" value="{if $list.pattern_data.release.list.writing.title}{$list.pattern_data.release.list.writing.title}{else}发图文{/if}" placeholder="发图文">
                    <br>
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">图片</p>
                    <img class="release_collection" src="{if $list.pattern_data.release.list.writing.images}{$list.pattern_data.release.list.writing.images}{else}{:urlBridging('static/wechat/writing.png')}{/if}" style="width: 80px;height: 80px;margin: 10px;display: inline;background-color: #ffffff;cursor: pointer;" onclick="cuonice(this);">
                </div>
            </div>
            <div style="margin:calc(2% - 10px);text-align: center;color: #ffffff;box-sizing:border-box;">
                <div style="background-image: linear-gradient(to right, #434343 0%, black 100%);line-height: 80px;width: 270px;height: 200px;text-align: center;margin: 0 auto;">
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">名称</p>
                    <input id="audioTitle" type="text" style="border-radius: 3px;margin-left: 10px;width: 150px;display: inline;" value="{if $list.pattern_data.release.list.audio.title}{$list.pattern_data.release.list.audio.title}{else}发语音{/if}" placeholder="发语音">
                    <br>
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">图片</p>
                    <img class="release_collection" src="{if $list.pattern_data.release.list.audio.images}{$list.pattern_data.release.list.audio.images}{else}{:urlBridging('static/wechat/audio.png')}{/if}" style="width: 80px;height: 80px;margin: 10px;display: inline;background-color: #ffffff;cursor: pointer;" onclick="cuonice(this);">
                </div>
            </div>
            <div style="margin:calc(2% - 10px);text-align: center;color: #ffffff;box-sizing:border-box;">
                <div style="background-image: linear-gradient(to right, #434343 0%, black 100%);line-height: 80px;width: 270px;height: 200px;text-align: center;margin: 0 auto;">
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">名称</p>
                    <input id="graffitoTitle" type="text" style="border-radius: 3px;margin-left: 10px;width: 150px;display: inline;" value="{if $list.pattern_data.release.list.graffito.title}{$list.pattern_data.release.list.graffito.title}{else}发投票{/if}" placeholder="发投票">
                    <br>
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">图片</p>
                    <img class="release_collection" src="{if $list.pattern_data.release.list.graffito.images}{$list.pattern_data.release.list.graffito.images}{else}{:urlBridging('static/wechat/graffito.png')}{/if}" style="width: 80px;height: 80px;margin: 10px;display: inline;background-color: #ffffff;cursor: pointer;" onclick="cuonice(this);">
                </div>
            </div>
            <div style="margin:calc(2% - 10px);text-align: center;color: #ffffff;box-sizing:border-box;">
                <div style="background-image: linear-gradient(to right, #434343 0%, black 100%);line-height: 80px;width: 270px;height: 200px;text-align: center;margin: 0 auto;">
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">名称</p>
                    <input id="videoTitle" type="text" style="border-radius: 3px;margin-left: 10px;width: 150px;display: inline;" value="{if $list.pattern_data.release.list.video.title}{$list.pattern_data.release.list.video.title}{else}发视频{/if}" placeholder="发视频">
                    <br>
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">图片</p>
                    <img class="release_collection" src="{if $list.pattern_data.release.list.video.images}{$list.pattern_data.release.list.video.images}{else}{:urlBridging('static/wechat/video.png')}{/if}" style="width: 80px;height: 80px;margin: 10px;display: inline;background-color: #ffffff;cursor: pointer;" onclick="cuonice(this);">
                </div>
            </div>
            <div style="margin:calc(2% - 10px);text-align: center;color: #ffffff;box-sizing:border-box;">
                <div style="background-image: linear-gradient(to right, #434343 0%, black 100%);line-height: 80px;width: 270px;height: 200px;text-align: center;margin: 0 auto;">
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">名称</p>
                    <input id="briskTitle" type="text" style="border-radius: 3px;margin-left: 10px;width: 150px;display: inline;" value="{if $list.pattern_data.release.list.brisk.title}{$list.pattern_data.release.list.brisk.title}{else}发活动{/if}" placeholder="发活动">
                    <br>
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">图片</p>
                    <img class="release_collection" src="{if $list.pattern_data.release.list.brisk.images}{$list.pattern_data.release.list.brisk.images}{else}{:urlBridging('static/wechat/brisk.png')}{/if}" style="width: 80px;height: 80px;margin: 10px;display: inline;background-color: #ffffff;cursor: pointer;" onclick="cuonice(this);">
                </div>
            </div>
        </div>

        <div class="am-g" style="display: flex; flex-wrap: wrap;justify-content: center;">
            <div style="margin:calc(2% - 10px);text-align: center;color: #ffffff;box-sizing: border-box;">
                <div style="background-image: linear-gradient(to right, #434343 0%, black 100%);line-height: 80px;width: 270px;height: 200px;text-align: center;margin: 0 auto;">
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">名称</p>
                    <input id="homeTitle" type="text" style="border-radius: 3px;margin-left: 10px;width: 150px;display: inline;" value="{$list.pattern_data.home.title}" placeholder="首页">
                    <br>
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">图片</p>
                    <img class="picture_collection" src="{$list.pattern_data.home.images.img}" style="width: 80px;height: 80px;margin: 10px;display: inline;background-color: #ffffff;cursor: pointer;" onclick="cuonice(this);">
                    <img class="picture_collection" src="{$list.pattern_data.home.images.img_active}" style="width: 80px;height: 80px;margin: 10px;display: inline;background-color: #ffffff;cursor: pointer;" onclick="cuonice(this);">
                </div>
            </div>
            <div style="margin:calc(2% - 10px);text-align: center;color: #ffffff;box-sizing: border-box;">
                <div style="background-image: linear-gradient(to right, #434343 0%, black 100%);line-height: 80px;width: 270px;height: 200px;text-align: center;margin: 0 auto;">
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">名称</p>
                    <input id="plazaTitle" type="text" style="border-radius: 3px;margin-left: 10px;width: 150px;display: inline;" value="{$list.pattern_data.plaza.title}" placeholder="广场">
                    <br>
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">图片</p>
                    <img class="picture_collection" src="{$list.pattern_data.plaza.images.img}" style="width: 80px;height: 80px;margin: 10px;display: inline;background-color: #ffffff;cursor: pointer;" onclick="cuonice(this);">
                    <img class="picture_collection" src="{$list.pattern_data.plaza.images.img_active}" style="width: 80px;height: 80px;margin: 10px;display: inline;background-color: #ffffff;cursor: pointer;" onclick="cuonice(this);">
                </div>
            </div>
            <div style="margin:calc(2% - 10px);text-align: center;color: #ffffff;box-sizing: border-box;">
                <div style="background-image: linear-gradient(to right, #434343 0%, black 100%);line-height: 80px;width: 270px;height: 200px;text-align: center;margin: 0 auto;">
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">名称</p>
                    <input id="releaseTitle" type="text" style="border-radius: 3px;margin-left: 10px;width: 150px;display: inline;" value="{if $list.pattern_data.release.title}{$list.pattern_data.release.title}{else}发布{/if}" placeholder="发布">
                    <br>
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">图片</p>
                    <img class="picture_collection" src="{if $list.pattern_data.release.images.img}{$list.pattern_data.release.images.img}{else}{:urlBridging('static/wechat/release.png')}{/if}" style="width: 80px;height: 80px;margin: 10px;display: inline;background-color: #ffffff;cursor: pointer;" onclick="cuonice(this);">
                </div>
            </div>
            <div style="margin:calc(2% - 10px);text-align: center;color: #ffffff;box-sizing: border-box;">
                <div style="background-image: linear-gradient(to right, #434343 0%, black 100%);line-height: 80px;width: 270px;height: 200px;text-align: center;margin: 0 auto;">
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">名称</p>
                    <input id="goodsTitle" type="text" style="border-radius: 3px;margin-left: 10px;width: 150px;display: inline;" value="{$list.pattern_data.goods.title}" placeholder="小商品 或 站内信">
                    <br>
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">图片</p>
                    <img class="picture_collection" src="{$list.pattern_data.goods.images.img}" style="width: 80px;height: 80px;margin: 10px;display: inline;background-color: #ffffff;cursor: pointer;" onclick="cuonice(this);">
                    <img class="picture_collection" src="{$list.pattern_data.goods.images.img_active}" style="width: 80px;height: 80px;margin: 10px;display: inline;background-color: #ffffff;cursor: pointer;" onclick="cuonice(this);">
                </div>
            </div>
            <div style="margin:calc(2% - 10px);text-align: center;color: #ffffff;box-sizing: border-box;">
                <div style="background-image: linear-gradient(to right, #434343 0%, black 100%);line-height: 80px;width: 270px;height: 200px;text-align: center;margin: 0 auto;">
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">名称</p>
                    <input id="userTitle" type="text" style="border-radius: 3px;margin-left: 10px;width: 150px;display: inline;" value="{$list.pattern_data.user.title}" placeholder="我的">
                    <br>
                    <p style="display: inline;margin: 0;margin-top: 4px;font-size: 14px;">图片</p>
                    <img class="picture_collection" src="{$list.pattern_data.user.images.img}" style="width: 80px;height: 80px;margin: 10px;display: inline;background-color: #ffffff;cursor: pointer;" onclick="cuonice(this);">
                    <img class="picture_collection" src="{$list.pattern_data.user.images.img_active}" style="width: 80px;height: 80px;margin: 10px;display: inline;background-color: #ffffff;cursor: pointer;" onclick="cuonice(this);">
                </div>
            </div>
        </div>
    </div>
    <div>
        <div style="cursor:pointer;margin: 50px auto;color:#ffffff;background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);height: 38px;width: 220px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;" onclick="holdSave();">
            保存
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    !function () {
        $('.tpl-switch').find('.tpl-switch-btn-view').on('click', function () {
            $(this).prev('.tpl-switch-btn').prop("checked", function () {
                if ($(this).is(':checked')) {
                    return false;
                } else {
                    return true;
                }
            });
        });
    }();

    function transmutationNext(obj) {
        $(obj).next('input').val(obj.value);
    }

    function transmutationPrev(obj) {
        $(obj).prev('input').val(obj.value);
    }

    function sutake(eurl) {
        $('.img-select').attr('src', eurl);
    }

    function cuonice(obj) {
        $(obj).addClass('img-select');
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no'],
            end: function (index, layer) {
                $('.img-select').removeClass('img-select');
            }
        });
    }


    function holdSave() {
        var sHomeTitle = $.trim($('#sHomeTitle').val());
        var electSheathe = $('#electSheathe').prop('checked') ? 1 : 0;
        var mall = $.trim($('#mall').val());
        var landgrave = $.trim($('#landgrave').val());
        var currency = $.trim($('#currency').val());
        var confer = $.trim($('#confer').val());
        var gateData = {};
        gateData['style'] = {};
        gateData['home'] = {};
        gateData['plaza'] = {};
        gateData['release'] = {};
        gateData['goods'] = {};
        gateData['user'] = {};
        gateData['style']['backcolor'] = $.trim($('#backcolor').val());
        gateData['style']['font_color'] = $.trim($('#fontColor').val());
        gateData['style']['font_color_active'] = $.trim($('#fontColorActive').val());
        gateData['home']['title'] = $.trim($('#homeTitle').val());
        gateData['plaza']['title'] = $.trim($('#plazaTitle').val());
        gateData['release']['title'] = $.trim($('#releaseTitle').val());
        gateData['goods']['title'] = $.trim($('#goodsTitle').val());
        gateData['user']['title'] = $.trim($('#userTitle').val());

        if (sHomeTitle == "") {
            layer.msg('自定义 "首页标题" 名称不能为空');
            return false;
        }
        if (landgrave == "") {
            layer.msg('自定义 "圈子" 名称不能为空');
            return false;
        }
        if (currency == "") {
            layer.msg('自定义 "贝壳" 名称不能为空');
            return false;
        }
        if (confer == "") {
            layer.msg('自定义 "积分" 名称不能为空');
            return false;
        }
        if (gateData['style']['backcolor']  == "") {
            layer.msg('导航背景颜色不能为空');
            return false;
        }
        if (gateData['style']['font_color'] == "") {
            layer.msg('导航字体颜色不能为空');
            return false;
        }
        if (gateData['style']['font_color_active'] == "") {
            layer.msg('导航字体选中颜色不能为空');
            return false;
        }
        if (gateData['home']['title'] == "") {
            layer.msg('"首页" 导航名称 不能为空');
            return false;
        }
        if (gateData['plaza']['title'] == "") {
            layer.msg('"广场" 导航名称 不能为空');
            return false;
        }
        if (gateData['release']['title'] == "") {
            layer.msg('"发布" 导航名称 不能为空');
            return false;
        }
        if (gateData['goods']['title'] == "") {
            layer.msg('"小商品" 或 "站内信" 导航名称 不能为空');
            return false;
        }
        if (gateData['user']['title'] == "") {
            layer.msg('"我的"导航名称不能为空');
            return false;
        }
        gateData['home']['images'] = {};
        gateData['plaza']['images'] = {};
        gateData['release']['images'] = {};
        gateData['goods']['images'] = {};
        gateData['user']['images'] = {};
        $('.picture_collection').each(function (i) {
            switch (i) {
                case 0:
                    gateData['home']['images']['img'] = $(this).attr('src');
                    break;
                case 1:
                    gateData['home']['images']['img_active'] = $(this).attr('src');
                    break;
                case 2:
                    gateData['plaza']['images']['img'] = $(this).attr('src');
                    break;
                case 3:
                    gateData['plaza']['images']['img_active'] = $(this).attr('src');
                    break;
                case 4:
                    gateData['release']['images']['img'] = $(this).attr('src');
                    break;
                case 5:
                    gateData['goods']['images']['img'] = $(this).attr('src');
                    break;
                case 6:
                    gateData['goods']['images']['img_active'] = $(this).attr('src');
                    break;
                case 7:
                    gateData['user']['images']['img'] = $(this).attr('src');
                    break;
                case 8:
                    gateData['user']['images']['img_active'] = $(this).attr('src');
                    break;
            }
        });

        gateData['release']['list'] = {};
        gateData['release']['list']['writing'] = {};
        gateData['release']['list']['audio'] = {};
        gateData['release']['list']['graffito'] = {};
        gateData['release']['list']['video'] = {};
        gateData['release']['list']['brisk'] = {};
        gateData['release']['list']['writing']['title'] = $.trim($('#writingTitle').val());
        gateData['release']['list']['audio']['title'] = $.trim($('#audioTitle').val());
        gateData['release']['list']['graffito']['title'] = $.trim($('#graffitoTitle').val());
        gateData['release']['list']['video']['title'] = $.trim($('#videoTitle').val());
        gateData['release']['list']['brisk']['title'] = $.trim($('#briskTitle').val());

        if (gateData['release']['list']['writing']['title'] == "") {
            layer.msg('"发图文" 名称 不能为空');
            return false;
        }
        if (gateData['release']['list']['audio']['title'] == "") {
            layer.msg('"发语音" 名称 不能为空');
            return false;
        }
        if (gateData['release']['list']['graffito']['title'] == "") {
            layer.msg('"发投票" 名称 不能为空');
            return false;
        }
        if (gateData['release']['list']['video']['title'] == "") {
            layer.msg('"发视频" 名称 不能为空');
            return false;
        }
        if (gateData['release']['list']['brisk']['title'] == "") {
            layer.msg('"发活动" 名称 不能为空');
            return false;
        }

        $('.release_collection').each(function (i) {
            switch (i) {
                case 0:
                    gateData['release']['list']['writing']['images'] = $(this).attr('src');
                    break;
                case 1:
                    gateData['release']['list']['audio']['images'] = $(this).attr('src');
                    break;
                case 2:
                    gateData['release']['list']['graffito']['images'] = $(this).attr('src');
                    break;
                case 3:
                    gateData['release']['list']['video']['images'] = $(this).attr('src');
                    break;
                case 4:
                    gateData['release']['list']['brisk']['images'] = $(this).attr('src');
                    break;
            }
        });

        $.post("{:url('systems/navigate')}", {
            "confer": confer,
            "currency": currency,
            "landgrave": landgrave,
            "mall": mall,
            "elect_sheathe": electSheathe,
            "home_title": sHomeTitle,
            "pattern_data": gateData
        }, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 1000});
            }
        }, 'json');
    }
</script>
{/block}