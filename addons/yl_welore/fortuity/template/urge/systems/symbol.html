{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:45px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}.customize-span{padding:3px 10px;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-adn"></span> 广告列表
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-xs">
                        <div class="am-btn-group am-btn-group-xs" style="margin-left:-2px;">
                        <span class="customize-span" onclick="saloof();">
                            <span class="am-icon-adn"></span> 新增广告
                        </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover table-main">
                        <thead>
                        <tr>
                            <th width="16.66%" style="text-align:center;">排序</th>
                            <th width="16.66%" style="text-align:center;">广告图片</th>
                            <th width="16.66%" style="text-align:center;">广告类型</th>
                            <th width="16.66%" style="text-align:center;">跳转类型</th>
                            <th width="16.66%" style="text-align:center;">状态</th>
                            <th width="16.67%" style="text-align:center;">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle" style="text-align:center;">
                                <div style="width: 50px;margin: 0 auto;">
                                    <input type="text" class="scfixed" id="sfixed-{$vo.id}" value="{$vo.scores}" data-score="{$vo.scores}" style="width: 50px;margin-top: 8px;" onblur="supre('{$vo.id}','#sfixed-{$vo.id}');">
                                </div>
                            </td>
                            <td class="am-text-middle" style="text-align:center;">
                                <a href="{$vo.playbill_url}" target="_blank">
                                    <img src="{$vo.playbill_url}" onerror="this.src='static/wechat/image_vip_top.jpg'" style="width:auto;height:80px;"/>
                                </a>
                            </td>
                            <td class="am-text-middle" style="text-align:center;">
                                {switch $vo.ad_type} {case 0}轮播广告{/case} {case 1}弹窗广告{/case} {/switch}
                            </td>
                            <td class="am-text-middle" style="text-align:center;">
                                {if $vo.practice_type==0}
                                    内部页面
                                {elseif $vo.practice_type==1}
                                    外部页面
                                {elseif $vo.practice_type==2}
                                    小程序跳转
                                {/if}
                            </td>
                            <td class="am-text-middle" style="text-align:center;">
                                {if $vo.status == 0}
                                <span style="color: red;">隐藏</span>
                                {else}
                                <span style="color: lightgreen;">显示</span>
                                {/if}
                            </td>
                            <td class="am-text-middle" style="text-align:center;">
                                <div style="width:130px;margin: -10px auto 0 auto;">
                                    <div class="am-btn-toolbar">
                                        <div class="am-btn-group am-btn-group-xs">
                                            <button type="button" class="am-btn am-btn-default am-btn-xs am-text-secondary"
                                                    onclick="uploof('{$vo.id}');">
                                                <span class="am-icon-pencil-square-o"></span> 编辑
                                            </button>
                                            <button type="button"
                                                    class="am-btn am-btn-default am-btn-xs am-text-danger am-hide-sm-only"
                                                    onclick="symbolDel('{$vo.id}')">
                                                <span class="am-icon-trash-o"></span>
                                                删除
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    function supre(asyId, domId) {
        var dalue = $(domId).val();
        var daioe = $(domId).attr('data-score');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(domId).val(daioe);
            return false;
        }
        if (dalue != daioe) {
            var repair = exalter(asyId, dalue);
            if (repair.code > 0) {
                layer.msg(repair.msg, {icon: 1, time: 800});
                $(domId).attr('data-score', dalue);
            } else {
                layer.msg(repair.msg, {icon: 5, time: 1600});
            }
        }
    }

    function exalter(asyId, dalue) {
        var straw = [];
        $.ajax({
            type: "post",
            url: "{:url('systems/slymben')}",
            data: {
                asyId: asyId,
                dalue: dalue
            },
            async: false,
            success: function (data) {
                straw = data;
            }
        });
        return straw;
    }


    function saloof() {
        location.href = "{:url('systems/rusymbol')}";
    }

    function uploof(uplid) {
        location.href = "{:url('systems/upsymbol')}&uplid=" + uplid;
    }


    function symbolDel(mid) {
        layer.confirm('您确定要删除这条广告吗', {
            btn: ['确定', '取消']
        }, function () {
            $.post(
                "{:url('systems/symlint')}",
                {'ecid': mid},
                function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }, 'json');
        });
    }

</script>
{/block}