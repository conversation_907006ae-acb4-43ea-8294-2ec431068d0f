{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-copyright"></span> 站点配置
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">站点名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="title" value="{$knight.title}" placeholder="请输入站点名称">
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">Logo</label>
                        <div class="am-u-sm-9">
                            <img src="{$knight.sgraph}" id="shion" onerror="this.src='static/disappear/default.png'" onclick="cuonice();" style="width: 70px;height: 70px;cursor: pointer;"/>
                            <button type="button" style="margin-left:10px;font-size: 12px;" onclick="cuonice();">选择图片</button>
                            <small style="margin-left: 10px;">建议图片尺寸：200*200px</small>
                            <input type="hidden" value="{$knight.sgraph}" name="sngimg">
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">客服电话</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="phone" value="{$knight.cust_phone}" placeholder="请输入客服电话">
                        </div>
                    </div>


                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">版权信息</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="copyright" value="{$knight.copyright}" placeholder="请输入版权信息">
                            {if $autonomy == 0}
                            <small style="color:red;">仅在小程序中展示</small>
                            {/if}
                        </div>
                    </div>


                    <div class="am-form-group">
                        <div class="am-u-sm-6 am-u-sm-push-6">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>
    !function () {
        $('.tpl-switch').find('.tpl-switch-btn-view').on('click', function () {
            $(this).prev('.tpl-switch-btn').prop("checked", function () {
                if ($(this).is(':checked')) {
                    return false;
                } else {
                    return true;
                }
            });
        });
    }();


    function cuonice() {
        layer.open({
            type: 2,
            anim:2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    function sutake(eurl) {
        $('#shion').attr('src', eurl);
        $("[name='sngimg']").val(eurl);
        layer.closeAll();
    }
    function excheck(title, sngimg, phone, copyright) {
        if (title == '' || title == 'undefined' || title == null) {
            layer.msg('站点名称不能为空');
            return false;
        }
        if (sngimg == '' || sngimg == 'undefined' || sngimg == null) {
            layer.msg('请上传Logo图标');
            return false;
        }
        if (phone == '' || phone == 'undefined' || phone == null) {
            layer.msg('客服电话不能为空');
            return false;
        }
        if (copyright == '' || copyright == 'undefined' || copyright == null) {
            layer.msg('版权信息不能为空');
            return false;
        }
        return true;
    }

    var slock = false;

    function holdSave() {
        if (!slock) {
            var title = $.trim($('#title').val());
            var sngimg = $.trim($("[name='sngimg']").val());
            var phone = $.trim($('#phone').val());
            var copyright = $.trim($('#copyright').val());
            if (excheck(title, sngimg, phone, copyright)) {
                slock = true;
                $.ajax({
                    type: "post",
                    url: "{:url('warrior')}",
                    data: {
                        'id': '{$knight.id}',
                        'title': title,
                        'phone': phone,
                        'sngimg': sngimg,
                        'copyright': copyright
                    },
                    dataType: 'json',
                    success: function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                slock = false;
                            });
                        }
                    }
                });
            }
        }
    }
</script>
{/block}