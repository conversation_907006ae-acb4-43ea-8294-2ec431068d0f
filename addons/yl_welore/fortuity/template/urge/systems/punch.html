{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-rub"></span> 货币设置
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-12">


                <div class="am-form am-form-horizontal" style="width: 700px;height:650px;margin: 0 auto;box-shadow: 0 0 10px 0 black;">

                    <div class="am-form-group" style="padding-top: 70px;margin-left: 15px;">
                        <label class="am-u-sm-3 am-form-label"> 货币图片</label>
                        <div class="am-u-sm-9">
                            <img src="{if $list.currency_icon}{$list.currency_icon}{else}{:urlBridging('static/wechat/jinbi.png')}{/if}" id="shion" onclick="cuonice();" style="width: 30px;height: 30px;cursor: pointer;"/>
                            <button type="button" style="margin-left:10px;margin-right:10px; font-size: 12px;" onclick="cuonice();">
                                选择图片
                            </button>
                            <small>建议图片尺寸：100*100px</small>
                            <input type="hidden" value="{if $list.currency_icon}{$list.currency_icon}{else}{:urlBridging('static/wechat/jinbi.png')}{/if}" name="sngimg">
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 30px 0 0 0;margin-left: 15px;">
                        <label class="am-u-sm-4 am-form-label">邀请好友获得{$defaultNavigate.confer}</label>
                        <div class="am-u-sm-3">
                            <input type="number" id="inviteMin" value="{$list.invite_min}">
                        </div>
                        <div class="am-u-sm-1">—</div>
                        <div class="am-u-sm-3 am-u-end">
                            <input type="number" id="inviteMax" value="{$list.invite_max}">
                        </div>
                    </div>

                    <div class="am-form-group" style="padding-top: 30px;margin-left: 15px;">
                        <label class="am-u-sm-4 am-form-label">{$defaultNavigate.currency}兑换{$defaultNavigate.confer}开关</label>
                        <div class="am-u-sm-7 am-u-end">
                            <select id="currencyRedemptionChannel">
                                <option value="1" {if $list.currency_redemption_channel==1}selected{/if}>开启</option>
                                <option value="0" {if $list.currency_redemption_channel==0}selected{/if}>关闭</option>
                            </select>
                            <small style="color: red;">开启后用户可在钱包内使用{$defaultNavigate.currency}兑换{$defaultNavigate.confer}</small>
                        </div>
                    </div>


                    <div class="am-form-group" style="padding: 15px 0;margin-left: 15px;">
                        <label class="am-u-sm-4 am-form-label">{$defaultNavigate.confer}兑换{$defaultNavigate.currency}开关</label>
                        <div class="am-u-sm-7 am-u-end">
                            <select id="fractionRedemptionChannel">
                                <option value="1" {if $list.fraction_redemption_channel==1}selected{/if}>开启</option>
                                <option value="0" {if $list.fraction_redemption_channel==0}selected{/if}>关闭</option>
                            </select>
                            <small style="color: red;">开启后用户可在钱包内使用{$defaultNavigate.confer}兑换{$defaultNavigate.currency}</small>
                        </div>
                    </div>

                    <div title="{$defaultNavigate.currency}兑换{$defaultNavigate.confer}比例 更改后原数据保留不变，新数据产生变化 ( 更改后造成的用户货币损失需自己承担 )">
                        <div class="am-form-group" style="padding-bottom: 30px;margin-left: 15px;">
                            <label class="am-u-sm-4 am-form-label">{$defaultNavigate.currency}兑换{$defaultNavigate.confer}比例</label>
                            <div class="am-u-sm-8" style="display:flex;">
                                <div style="display:flex;width:40%;">
                                    <div style="width:30px;margin-top:4px;">1 :</div>
                                    <div style="width:100px;margin-top:3px;">
                                        <input type="number" id="fracScale" value="{$list.fraction_scale}" oninput="digitalCheck(this);">
                                    </div>
                                </div>
                                <div style="width:60%;margin-top:8px;font-size:14px;color:#71a6ca;">
                                    默认兑换比例为1:10 ( 谨慎更改 )
                                </div>
                            </div>
                        </div>
                        <div class="am-form-group" style="font-size:12px;text-align:center;color:red;">
                            {$defaultNavigate.currency}兑换{$defaultNavigate.confer}比例 更改后原数据保留不变，新数据产生变化 (
                            更改后造成的用户货币损失需自己承担 )
                        </div>
                    </div>

                    <div class="am-form-group" style="margin-top:45px;">
                        <div class="am-u-sm-7 am-u-sm-push-5">
                            <button type="button" class="am-btn am-btn-secondary" style="border-radius: 5px;" onclick="holdSave();">保存设置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    var cuonice = function () {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    var sutake = function (eurl) {
        $("[name='sngimg']").val(eurl);
        $('#shion').attr('src', eurl);
        layer.closeAll();
    }

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
    }

    var holdSave = function () {
        var setData = {};
        setData['puid'] = "{$list.id}";
        setData['sngimg'] = $.trim($("[name='sngimg']").val());
        setData['currencyRedemptionChannel'] = $("#currencyRedemptionChannel").val();
        setData['fractionRedemptionChannel'] = $("#fractionRedemptionChannel").val();
        setData['fracScale'] = Number($('#fracScale').val().match(/^\d+(?:\.\d{0})?/));
        if (setData['fracScale'] < 1) {
            layer.msg('请输入正常兑换比例');
            return;
        }
        setData['inviteMin'] = Number($('#inviteMin').val().match(/^\d+(?:\.\d{0,2})?/));
        setData['inviteMax'] = Number($('#inviteMax').val().match(/^\d+(?:\.\d{0,2})?/));
        $.ajax({
            type: "post",
            url: "{:url('systems/punch')}",
            data: setData,
            dataType: 'json',
            success: function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }
        });
    }
</script>
{/block}