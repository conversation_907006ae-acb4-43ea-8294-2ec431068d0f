{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-spinner"></span> 远程附件
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group am-u-sm-offset-3" style="margin-bottom:30px;">
                        <label class="am-radio-inline">
                            <input type="radio" value="0" name="quickType" {if $list.quicken_type==0}checked{/if}>
                            本地存储
                        </label>
                        <label class="am-radio-inline">
                            <input type="radio" value="1" name="quickType" {if $list.quicken_type==1}checked{/if}>
                            阿里云OSS
                        </label>
                        <label class="am-radio-inline">
                            <input type="radio" value="2" name="quickType" {if $list.quicken_type==2}checked{/if}>
                            七牛云存储
                        </label>
                        <label class="am-radio-inline">
                            <input type="radio" value="3" name="quickType" {if $list.quicken_type==3}checked{/if}>
                            腾讯云COS
                        </label>
                        <label class="am-radio-inline">
                            <input type="radio" value="4" name="quickType" {if $list.quicken_type==4}checked{/if}>
                            又拍云存储
                        </label>
                        <label class="am-radio-inline">
                            <input type="radio" value="5" name="quickType" {if $list.quicken_type==5}checked{/if}>
                            FTP存储
                        </label>
                    </div>
                    <div id="pattern-1" class="pdraft" style="display: none;">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">AccessKey ID</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="oss_access_key_id" placeholder="{if $list.oss_follow.oss_access_key_id}{$list.oss_follow.oss_access_key_id|ciphertext}{else}请输入阿里云AccessKey ID{/if}">
                                <small>Access Key ID是您访问阿里云API的密钥，具有该账户完全的权限，请您妥善保管。</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Access Key Secret</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="oss_access_key_secret" placeholder="{if $list.oss_follow.oss_access_key_secret}{$list.oss_follow.oss_access_key_secret|ciphertext}{else}请输入阿里云Access Key Secret{/if}">
                                <small>Access Key Secret是您访问阿里云API的密钥，具有该账户完全的权限，请您妥善保管。</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Bucket</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="oss_bucket" value="{$list.oss_follow.oss_bucket}" placeholder="请输入阿里云存储空间的名字">
                                <small>请保证Bucket为可公共读取的</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Bucket所在区域</label>
                            <div class="am-u-sm-9">
                                <select id="oss_endpoint">
                                    <option value="0">请选择区域</option>
                                    {volist name="ossEndpoint" id="vo"}
                                    <option value="{$vo.value}" {if $list.oss_follow.oss_endpoint==$vo.value}selected{/if}>{$vo.key}
                                    </option>
                                    {/volist}
                                </select>
                                <small>请选择Bucket对应的地域节点</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="oss_url" value="{$list.oss_follow.oss_url}" placeholder="请输入阿里云绑定的外链域名或自定义外链域名">
                                <small>阿里云支持用户自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com
                                </small>
                            </div>
                        </div>
                    </div>
                    <div id="pattern-2" class="pdraft" style="display: none;">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">AccessKey</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="qiniu_access_key" placeholder="{if $list.qiniu_follow.qiniu_access_key}{$list.qiniu_follow.qiniu_access_key|ciphertext}{else}请输入七牛云用于签名的公钥{/if}">
                                <small>用于签名的公钥</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">SecretKey</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="qiniu_secret_key" placeholder="{if $list.qiniu_follow.qiniu_secret_key}{$list.qiniu_follow.qiniu_secret_key|ciphertext}{else}请输入七牛云用于签名的私钥{/if}">
                                <small>用于签名的私钥</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Bucket</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="qiniu_bucket" value="{$list.qiniu_follow.qiniu_bucket}" placeholder="请输入七牛云存储空间的名字">
                                <small>请保证Bucket为可公共读取的</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="qiniu_url" value="{$list.qiniu_follow.qiniu_url}" placeholder="请输入七牛云绑定的外链域名或自定义外链域名">
                                <small>七牛云支持用户自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com
                                </small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">水印代码</label>
                            <div class="am-u-sm-9">
                                <textarea id="qiniu_watermark" style="height:150px;resize:none;">{$list.qiniu_follow.qiniu_watermark}</textarea>
                            </div>
                        </div>
                    </div>
                    <div id="pattern-3" class="pdraft" style="display: none;">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">APPID</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="cos_app_id" value="{$list.cos_follow.cos_app_id}" placeholder="请输入腾讯云APPID">
                                <small>APPID 是您项目的唯一标识号</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">SecretId</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="cos_secret_id" placeholder="{if $list.cos_follow.cos_secret_id}{$list.cos_follow.cos_secret_id|ciphertext}{else}请输入腾讯云SecretId{/if}">
                                <small>SecretID 是您项目的安全秘钥，具有该账户完全的权限，请妥善保管</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">SecretKey</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="cos_secret_key" placeholder="{if $list.cos_follow.cos_secret_key}{$list.cos_follow.cos_secret_key|ciphertext}{else}请输入腾讯云SecretKey{/if}">
                                <small>SecretKey 是您项目的安全秘钥，具有该账户完全的权限，请妥善保管</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Bucket</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="cos_bucket" value="{$list.cos_follow.cos_bucket}" placeholder="请输入腾讯云存储空间的名字">
                                <small>请保证Bucket为可公共读取的</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">Bucket所在区域</label>
                            <div class="am-u-sm-9">
                                <select id="cos_region">
                                    <option value="0">请选择区域</option>
                                    {volist name="ossRegion" id="vo"}
                                    <option value="{$vo.value}" {if $list.cos_follow.cos_region==$vo.value}selected{/if}>{$vo.key}
                                    </option>
                                    {/volist}
                                </select>
                                <small>请选择Bucket对应的区域</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="cos_url" value="{$list.cos_follow.cos_url}" placeholder="请输入腾讯云绑定的外链域名或自定义外链域名">
                                <small>腾讯云支持用户自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com
                                </small>
                            </div>
                        </div>
                    </div>
                    <div id="pattern-4" class="pdraft" style="display: none;">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">服务名</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="upyun_service_name" value="{$list.upyun_follow.upyun_service_name}" placeholder="请输入又拍云存储的服务名称">
                                <small>云存储服务名称</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">操作员名</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="upyun_operator_name" placeholder="{if $list.upyun_follow.upyun_operator_name}{$list.upyun_follow.upyun_operator_name|ciphertext}{else}请输入又拍云的操作员名{/if}">
                                <small>账户管理 -> 操作员 [不是登录用户名]</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">操作员密码</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="upyun_operator_password" placeholder="{if $list.upyun_follow.upyun_operator_password}{$list.upyun_follow.upyun_operator_password|ciphertext}{else}请输入又拍云的操作员密码{/if}">
                                <small>账户管理 -> 操作员密码 [不是登录密码]</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="upyun_url" value="{$list.upyun_follow.upyun_url}" placeholder="请输入又拍云绑定的外链域名或自定义外链域名">
                                <small>又拍云支持用户自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com
                                </small>
                            </div>
                        </div>
                    </div>
                    <div id="pattern-5" class="pdraft" style="display: none;">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">FTP服务器地址</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="ftp_host" value="{$list.ftp_follow.ftp_host}" placeholder="请输入FTP服务器地址">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">FTP用户名</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="ftp_username" placeholder="{if $list.ftp_follow.ftp_username}{$list.ftp_follow.ftp_username|ciphertext}{else}请输入FTP用户名{/if}">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">FTP密码</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="ftp_password" placeholder="{if $list.ftp_follow.ftp_password}{$list.ftp_follow.ftp_password|ciphertext}{else}请输入FTP密码{/if}">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">FTP被动模式</label>
                            <div class="am-u-sm-9">
                                <select id="ftp_pasv">
                                    <option value="1" {if $list.ftp_follow.ftp_pasv==1}selected{/if}>开启</option>
                                    <option value="0" {if $list.ftp_follow.ftp_pasv==0}selected{/if}>关闭</option>
                                </select>
                                <small>服务器安全组需开启 TCP 39000-40000 FTP被动模端口范围，关闭FTP被动模式可能会造成上传页面卡死或上传失败等情况。</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">FTP端口号</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="ftp_port" value="{$list.ftp_follow.ftp_port}" placeholder="FTP端口号">
                                <small>FTP端口号，默认21</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="ftp_url" value="{$list.ftp_follow.ftp_url}" placeholder="请输入FTP自定义访问域名">
                                <small>FTP自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group am-u-sm-offset-3" style="font-size: 14px;">
                        注意事项：<span style="color:  red;">保存之前请检查您的配置是否正确，保存之后所更改的内容将会立即生效！</span>
                    </div>
                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-push-6">
                            <button type="button" class="am-btn am-btn-primary" onclick="setLoad();">
                                保存配置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    $(function () {
        var quickType = $("[name='quickType']:checked").val();
        $('#pattern-' + quickType).show();

        $("[name='quickType']").change(function () {
            var qucke = $("[name='quickType']:checked").val();
            if (qucke == 0) {
                $('.pdraft').hide();
            } else {
                $('.pdraft').hide();
                $('#pattern-' + qucke).show();
            }
        });
    });


    var tokep = false;
    function setLoad() {
        if (!tokep) {
            tokep = true;
            var sngeing = $("[name='quickType']:checked").val();
            var fabric_data = {'quicken_type': 0};
            if (sngeing == 1) {
                var accesskeyId = $.trim($("#oss_access_key_id").val());
                var accessKeySecret = $.trim($("#oss_access_key_secret").val());
                var endpoint = $.trim($("#oss_endpoint").val());
                var bucket = $.trim($("#oss_bucket").val());
                var url = $.trim($("#oss_url").val());
                var salting = $.trim('{if $list.oss_follow.oss_access_key_id}1{/if}');
                if (accesskeyId == '' && salting != '1') {
                    layer.msg('请输入阿里云AccessKey ID');
                    tokep = false;
                    return;
                }
                if (accessKeySecret == '' && salting != '1') {
                    layer.msg('请输入阿里云Access Key Secret');
                    tokep = false;
                    return;
                }
                if (bucket == '') {
                    layer.msg('请输入阿里云存储空间的名字');
                    tokep = false;
                    return;
                }
                if (endpoint == '0') {
                    layer.msg('请选择Bucket对应的地域节点');
                    tokep = false;
                    return;
                }
                if (url == '') {
                    layer.msg('请输入阿里云绑定的外链域名或自定义外链域名');
                    tokep = false;
                    return;
                }
                fabric_data = {
                    'quicken_type': 1,
                    'oss_access_key_id': accesskeyId,
                    'oss_access_key_secret': accessKeySecret,
                    'oss_endpoint': endpoint,
                    'oss_bucket': bucket,
                    'oss_url': url
                };
            } else if (sngeing == 2) {
                var accessKey = $.trim($("#qiniu_access_key").val());
                var secretKey = $.trim($("#qiniu_secret_key").val());
                var bucket = $.trim($("#qiniu_bucket").val());
                var url = $.trim($("#qiniu_url").val());
                var watermark = $.trim($("#qiniu_watermark").val());
                var salting = $.trim('{if $list.qiniu_follow.qiniu_access_key}1{/if}');
                if (accessKey == '' && salting != '1') {
                    layer.msg('请输入七牛云用于签名的公钥');
                    tokep = false;
                    return;
                }
                if (secretKey == '' && salting != '1') {
                    layer.msg('请输入七牛云用于签名的私钥');
                    tokep = false;
                    return;
                }
                if (bucket == '') {
                    layer.msg('请输入七牛云存储空间的名字');
                    tokep = false;
                    return;
                }
                if (url == '') {
                    layer.msg('请输入七牛云绑定的外链域名或自定义外链域名');
                    tokep = false;
                    return;
                }
                fabric_data = {
                    'quicken_type': 2,
                    'qiniu_access_key': accessKey,
                    'qiniu_secret_key': secretKey,
                    'qiniu_bucket': bucket,
                    'qiniu_url': url,
                    'qiniu_watermark': watermark,
                };
            } else if (sngeing == 3) {
                var appId = $.trim($("#cos_app_id").val());
                var secretId = $.trim($("#cos_secret_id").val());
                var secretKey = $.trim($("#cos_secret_key").val());
                var bucket = $.trim($("#cos_bucket").val());
                var region = $.trim($("#cos_region").val());
                var url = $.trim($("#cos_url").val());
                var salting = $.trim('{if $list.cos_follow.cos_secret_id}1{/if}');
                if (appId == '') {
                    layer.msg('请输入腾讯云APPID');
                    tokep = false;
                    return;
                }
                if (secretId == '' && salting != '1') {
                    layer.msg('请输入腾讯云SecretId');
                    tokep = false;
                    return;
                }
                if (secretKey == '' && salting != '1') {
                    layer.msg('请输入腾讯云SecretKey');
                    tokep = false;
                    return;
                }
                if (bucket == '') {
                    layer.msg('请输入腾讯云存储空间的名字');
                    tokep = false;
                    return;
                }
                if (region == '0') {
                    layer.msg('请选择Bucket对应的区域');
                    tokep = false;
                    return;
                }
                if (url == '') {
                    layer.msg('请输入腾讯云绑定的外链域名或自定义外链域名');
                    tokep = false;
                    return;
                }
                fabric_data = {
                    'quicken_type': 3,
                    'cos_app_id': appId,
                    'cos_secret_id': secretId,
                    'cos_secret_key': secretKey,
                    'cos_bucket': bucket,
                    'cos_region': region,
                    'cos_url': url
                };
            } else if (sngeing == 4) {
                var serviceName = $.trim($("#upyun_service_name").val());
                var operatorName = $.trim($("#upyun_operator_name").val());
                var operatorPassword = $.trim($("#upyun_operator_password").val());
                var url = $.trim($("#upyun_url").val());
                fabric_data = {
                    'quicken_type': 4,
                    'upyun_service_name': serviceName,
                    'upyun_operator_name': operatorName,
                    'upyun_operator_password': operatorPassword,
                    'upyun_url': url,
                };
            } else if (sngeing == 5) {
                var ftpHost = $.trim($("#ftp_host").val());
                var ftpUsername = $.trim($("#ftp_username").val());
                var ftpPassword = $.trim($("#ftp_password").val());
                var ftpPort = $.trim($("#ftp_port").val());
                var ftpPasv = $.trim($("#ftp_pasv").val());
                var url = $.trim($("#ftp_url").val());
                fabric_data = {
                    'quicken_type': 5,
                    'ftp_host': ftpHost,
                    'ftp_username': ftpUsername,
                    'ftp_password': ftpPassword,
                    'ftp_port': ftpPort,
                    'ftp_pasv': ftpPasv,
                    'ftp_url': url,
                };
            }
            $.post("{:url('systems/annex')}", fabric_data, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        tokep = false;
                    });
                }
            }, 'json');
        }
    }
</script>
{/block}