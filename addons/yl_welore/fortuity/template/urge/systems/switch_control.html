{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 功能开关
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-8 am-u-sm-push-2">
                <div class="am-form am-form-horizontal">
                    <div class="am-panel am-panel-default">
                        <div class="am-panel-hd">常用功能</div>
                        <div class="am-panel-bd">
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="用户名称防重复，开启后新增加的用户的昵称会变成随机名称，防止重复">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">用户名称防重复</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="preventDuplication" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.prevent_duplication==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="关闭后用户将不能更改个人信息">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">用户信息更改开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="userInfoUpdateArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.user_info_update_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="收货地址开关，关闭后将隐藏收货地址">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">收货地址开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="receiptArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.receipt_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="首页内容随机推荐开关，开启后首页将随机推荐任意帖子">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">首页内容随机推荐开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="homeRandomArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.home_random_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="视频宽度自适应开关，开启后帖子内的视频宽高自适应">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">视频宽度自适应开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="videoAutoArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.video_auto_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="内容状态监测开关，开启后将检测付费帖的内容是否失效">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">内容状态监测开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="preContentArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.pre_content_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="首页我的圈子开关，开启后会在首页显示我关注的圈子">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">首页我的圈子开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="homeMyToryArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.home_my_tory_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="允许用户创建圈子，关闭后用户不可以申请创建圈子">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">允许用户创建圈子</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="toryArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.tory_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="允许用户创建话题，关闭后用户不可以创建话题">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">允许用户创建话题</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="allowUserTopic" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.allow_user_topic==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="帖子浏览量显示开关，关闭后将不再显示浏览数量">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">帖子浏览量显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="paperBrowseNumHide" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.paper_browse_num_hide==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="允许用户下载视频，开启后用户可以下载视频">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">允许用户下载视频</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="videoDownloadArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.video_download_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-panel am-panel-default">
                        <div class="am-panel-hd">发布功能</div>
                        <div class="am-panel-bd">
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="首页发帖开关，关闭后用户将不能在首页发帖">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">首页发帖开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="homeReleaseArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.home_release_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="发涂鸦显示开关，关闭后将无法发布涂鸦帖子">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">发涂鸦显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="hairGraffitiArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.hair_graffiti_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="发视频显示开关，关闭后将无法发布视频帖子">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">发视频显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="hairVideoArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.hair_video_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="发语音显示开关，关闭后将无法发布语音帖子">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">发语音显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="hairAudioArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.hair_audio_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="发投票显示开关，关闭后将无法发布投票帖子">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">发投票显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="hairVoteArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.hair_vote_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="发活动显示开关，关闭后将无法发布活动帖子">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">发活动显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="hairBriskArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.hair_brisk_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="帖子标题输入框开关，关闭后发帖时将不再显示帖子标题输入框">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">帖子标题输入框开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="titleInputArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.title_input_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="帖子标题强制输入，开启后将强制用户输入帖子标题，否则无法发帖">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">帖子标题强制输入</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="titleArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.title_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="发帖禁止转发显示开关，关闭后发帖将隐藏禁止转发功能">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">发帖禁止转发显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="reprintArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.reprint_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="发帖图片九宫格开关，开启后发帖时将默认开启九宫格样式">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">发帖图片九宫格开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="relPaperImgStyle" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.rel_paper_img_style==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="发帖位置显示开关，关闭后发帖将隐藏位置功能">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">发帖位置显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="relPaperLocationHide" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.rel_paper_location_hide==0}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="发帖话题显示开关，关闭后发帖将隐藏话题功能">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">发帖话题显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="relPaperTopicsdHide" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.rel_paper_topicsd_hide==0}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="发帖图片显示开关，关闭后发帖将隐藏图片功能">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">发帖图片显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="relPaperImageHide" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.rel_paper_image_hide==0}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-panel am-panel-default">
                        <div class="am-panel-hd">增值功能</div>
                        <div class="am-panel-bd">
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="充值开关，关闭后将隐藏充值功能">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">充值开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="rechargeArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.recharge_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="ios充值开关，关闭后ios用户访问小程序将无法进行充值">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">ios充值开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="iosPayArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.ios_pay_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="钱包显示开关，关闭后将隐藏钱包功能">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">钱包显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="walletArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.wallet_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="会员显示开关，关闭后将隐藏开通VIP功能">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">会员显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="nobleArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.noble_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="红包帖子开关，关闭后将无法发布红包帖子">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">福利帖子开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="welfareArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.welfare_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="付费帖子功能开关，关闭后将无法发布付费帖子">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">付费帖子功能开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="buyPaperArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.buy_paper_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="送礼显示开关，关闭后用户将无法赠送礼物">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">送礼显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="tributeArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.tribute_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="守护榜显示开关，关闭后将隐藏个人主页里的守护榜">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">守护榜显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="guardArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.guard_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="小商品显示开关，关闭后将隐藏小商品功能">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">小商品显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="shopArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.shop_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-panel am-panel-default">
                        <div class="am-panel-hd">会员功能</div>
                        <div class="am-panel-bd">
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="只允许会员发布语音，开启后将会只允许VIP用户发布语音">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">只允许会员发布语音</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="voiceMember" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.voice_member==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="只允许会员发布涂鸦，开启后将会只允许VIP用户发布涂鸦">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">只允许会员发布涂鸦</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="graffitiMember" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.graffiti_member==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="只允许会员发布视频，开启后将会只允许VIP用户发布视频">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">只允许会员发布视频</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="videoMember" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.video_member==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="只允许会员发布付费帖子，开启后只有VIP用户可以发布付费帖子">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">只允许会员发布付费帖子</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="buyPaperMember" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.buy_paper_member==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="只允许会员发布投票，开启后将会只允许VIP用户发布投票">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">只允许会员发布投票</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="voteMember" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.vote_member==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="只允许会员发布活动，开启后将会只允许VIP用户发布活动">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">只允许会员发布活动</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="briskMember" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.brisk_member==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-panel am-panel-default">
                        <div class="am-panel-hd">内容安全</div>
                        <div class="am-panel-bd">
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="游客浏览帖子强制授权开关，开启后游客用户浏览帖子内容会强制弹出授权界面">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">游客浏览帖子强制授权开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="warrantArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.warrant_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="强制用户发帖时绑定手机号，开启后用户必须绑定手机号才能发帖">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">强制用户发帖时绑定手机号</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="forcePhoneArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.force_phone_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="强制用户回帖时绑定手机号，开启后用户必须绑定手机号才能回帖">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">强制用户回帖时绑定手机号</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="reForcePhoneArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.re_force_phone_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="留言板显示开关，关闭后将关闭用户留言功能">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">留言板开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="speechArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.speech_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="功能权限开关 ，开启后部分功能需要强制绑定手机号才能显示">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">功能权限开关 </label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="overallArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.overall_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-panel am-panel-default">
                        <div class="am-panel-hd">附加功能</div>
                        <div class="am-panel-bd">
                            <div class="am-form-group" style="margin: 30px 0;">
                                <div title="小秘密显示开关，开启后用户可以发布小秘密">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">小秘密显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="whisperArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.whisper_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="身份铭牌显示开关，开启后用户可以用身份铭牌来发布帖子">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">身份铭牌显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="engraveArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.engrave_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="用户身份认证开关，开启后用户可以进行身份认证">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">用户认证开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="travelArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.travel_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {if $autonomy == 1}
                            <div class="am-form-group" style="margin: 45px 0;">
                                <div title="纸条显示开关，开启后用户可以发布纸条">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">纸条显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="feelingArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.feeling_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div title="短剧视频显示开关，开启后用户可以浏览短剧视频">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">短剧显示开关</label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="shortDramaArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.short_drama_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {/if}
                            <div class="am-form-group hide" style="margin: 30px 0;">
                                <div class="hide" title="圈子推荐置前开关 ，开启后推荐列表将显示在首位">
                                    <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">圈子推荐置前开关 </label>
                                    <div class="am-u-sm-1 am-u-end">
                                        <div class="tpl-switch">
                                            <input id="torySortArbor" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $knight.tory_sort_arbor==1}checked{/if}>
                                            <div class="tpl-switch-btn-view">
                                                <div></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group" style="margin-top:50px;">
                        <div class="am-u-sm-6 am-u-sm-push-6">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>
    !function () {
        $('.tpl-switch').find('.tpl-switch-btn-view').on('click', function () {
            $(this).prev('.tpl-switch-btn').prop("checked", function () {
                if ($(this).is(':checked')) {
                    return false;
                } else {
                    return true;
                }
            });
        });
    }();

    var holdSave = function () {
        var setData = {};
        setData['suid'] = '{$knight.id}';
        setData['preventDuplication'] = $('#preventDuplication').prop('checked') ? 1 : 0;
        setData['toryArbor'] = $('#toryArbor').prop('checked') ? 1 : 0;
        setData['welfareArbor'] = $('#welfareArbor').prop('checked') ? 1 : 0;
        setData['nobleArbor'] = $('#nobleArbor').prop('checked') ? 1 : 0;
        setData['walletArbor'] = $('#walletArbor').prop('checked') ? 1 : 0;
        setData['rechargeArbor'] = $('#rechargeArbor').prop('checked') ? 1 : 0;
        setData['receiptArbor'] = $('#receiptArbor').prop('checked') ? 1 : 0;
        setData['titleArbor'] = $('#titleArbor').prop('checked') ? 1 : 0;
        setData['hairAudioArbor'] = $('#hairAudioArbor').prop('checked') ? 1 : 0;
        setData['hairGraffitiArbor'] = $('#hairGraffitiArbor').prop('checked') ? 1 : 0;
        setData['hairVideoArbor'] = $('#hairVideoArbor').prop('checked') ? 1 : 0;
        setData['voiceMember'] = $('#voiceMember').prop('checked') ? 1 : 0;
        setData['graffitiMember'] = $('#graffitiMember').prop('checked') ? 1 : 0;
        setData['videoMember'] = $('#videoMember').prop('checked') ? 1 : 0;
        setData['titleInputArbor'] = $('#titleInputArbor').prop('checked') ? 1 : 0;
        setData['forcePhoneArbor'] = $('#forcePhoneArbor').prop('checked') ? 1 : 0;
        setData['reForcePhoneArbor'] = $('#reForcePhoneArbor').prop('checked') ? 1 : 0;
        setData['buyPaperArbor'] = $('#buyPaperArbor').prop('checked') ? 1 : 0;
        setData['buyPaperMember'] = $('#buyPaperMember').prop('checked') ? 1 : 0;
        setData['shopArbor'] = $('#shopArbor').prop('checked') ? 1 : 0;
        setData['tributeArbor'] = $('#tributeArbor').prop('checked') ? 1 : 0;
        setData['guardArbor'] = $('#guardArbor').prop('checked') ? 1 : 0;
        setData['reprintArbor'] =$('#reprintArbor').prop('checked') ? 1 : 0;
        setData['iosPayArbor'] = $('#iosPayArbor').prop('checked') ? 1 : 0;
        setData['speechArbor'] = $('#speechArbor').prop('checked') ? 1 : 0;
        setData['homeReleaseArbor'] = $('#homeReleaseArbor').prop('checked') ? 1 : 0;
        setData['homeRandomArbor'] = $('#homeRandomArbor').prop('checked') ? 1 : 0;
        setData['hairBriskArbor'] = $('#hairBriskArbor').prop('checked') ? 1 : 0;
        setData['briskMember'] = $('#briskMember').prop('checked') ? 1 : 0;
        setData['userInfoUpdateArbor'] = $('#userInfoUpdateArbor').prop('checked') ? 1 : 0;
        setData['warrantArbor'] = $('#warrantArbor').prop('checked') ? 1 : 0;
        setData['whisperArbor'] = $('#whisperArbor').prop('checked') ? 1 : 0;
        setData['videoAutoArbor'] = $('#videoAutoArbor').prop('checked') ? 1 : 0;
        setData['hairVoteArbor'] = $('#hairVoteArbor').prop('checked') ? 1 : 0;
        setData['voteMember'] = $('#voteMember').prop('checked') ? 1 : 0;
        setData['engraveArbor'] = $('#engraveArbor').prop('checked') ? 1 : 0;
        setData['travelArbor'] = $('#travelArbor').prop('checked') ? 1 : 0;
        setData['torySortArbor'] = $('#torySortArbor').prop('checked') ? 1 : 0;
        setData['feelingArbor'] = $('#feelingArbor').prop('checked') ? 1 : 0;
        setData['overallArbor'] = $('#overallArbor').prop('checked') ? 1 : 0;
        setData['allowUserTopic'] = $('#allowUserTopic').prop('checked') ? 1 : 0;
        setData['preContentArbor'] = $('#preContentArbor').prop('checked') ? 1 : 0;
        setData['homeMyToryArbor'] = $('#homeMyToryArbor').prop('checked') ? 1 : 0;
        setData['relPaperImgStyle'] = $('#relPaperImgStyle').prop('checked') ? 1 : 0;
        setData['paperBrowseNumHide'] = $('#paperBrowseNumHide').prop('checked') ? 1 : 0;
        setData['relPaperLocationHide'] = $('#relPaperLocationHide').prop('checked') ? 0 : 1;
        setData['relPaperTopicsdHide'] = $('#relPaperTopicsdHide').prop('checked') ? 0 : 1;
        setData['relPaperImageHide'] = $('#relPaperImageHide').prop('checked') ? 0 : 1;
        setData['videoDownloadArbor'] = $('#videoDownloadArbor').prop('checked') ? 1 :0;
        setData['shortDramaArbor'] = $('#shortDramaArbor').prop('checked') ? 1 :0;

        if (setData['titleInputArbor'] === 0 && setData['titleArbor'] !== 0) {
            layer.msg('关闭帖子标题输入框需同时关闭帖子标题强制输入', {time: 2000});
            return;
        }
        $.ajaxSettings.async = false;
        $.post("{:url('systems/switch_control')}", setData, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }
        );
        $.ajaxSettings.async = true;
    }
</script>
{/block}