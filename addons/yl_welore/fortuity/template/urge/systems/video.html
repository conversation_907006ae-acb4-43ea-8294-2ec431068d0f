{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-video-camera"></span> 视频设置
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-12">
                <div class="am-form am-form-horizontal" style="width: 650px;height:300px;margin: 0 auto;box-shadow: 0 0 10px 0 black;">
                    <div class="am-form-group" style="padding: 60px 0 30px 0;margin-left: 30px;">
                        <label class="am-u-sm-5 am-form-label">视频上传压缩开关</label>
                        <div class="am-u-sm-3 am-u-end">
                            <select id="videoCompressionSetting">
                                <option value="1" {if $list.video_compression_setting==1}selected{/if}>开启</option>
                                <option value="0" {if $list.video_compression_setting==0}selected{/if}>关闭</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group" style="padding: 0 0 30px 0;margin-left: 30px;">
                        <label class="am-u-sm-5 am-form-label">视频上传时间限制</label>
                        <div class="am-u-sm-3">
                            <input type="number" id="videoSetting" value="{$list.video_setting}" oninput="strange(this);">
                        </div>
                        <label class="am-u-sm-1 am-form-label am-u-end">秒</label>
                    </div>
                    <div class="am-form-group">
                        <div class="am-u-sm-7 am-u-sm-push-5">
                            <button type="button" class="am-btn am-btn-secondary" style="border-radius: 5px;" onclick="holdSave();">保存设置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>
    var strange = function (obj) {
        if (obj.value.indexOf('.')) {
            obj.value = obj.value.replace('.', '');
        }
    }
    function holdSave() {
        var videoSetting = $.trim($('#videoSetting').val());
        var videoCompressionSetting = $.trim($('#videoCompressionSetting').val());
        if (videoSetting == '' || videoSetting == 0) {
            layer.msg('视频上传时间不能低于1秒');
            return;
        }
        if (videoSetting > 2147483647) {
            layer.msg('输入的秒数超出正常范围，请重新输入');
            return;
        }
        $.post("{:url('systems/video')}", {
            'videoSetting': videoSetting,
            'videoCompressionSetting': videoCompressionSetting
        }, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000});
            }
        }, 'json');
    }
</script>
{/block}