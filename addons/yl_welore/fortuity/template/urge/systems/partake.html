{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-adn"></span> 转发帖子
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-12">
                <div class="am-form am-form-horizontal" style="width: 700px;height:400px;margin: 0px auto;box-shadow: 0px 0px 10px 0px black;">
                    <div class="am-form-group" style="padding: 30px 0px 0px 0px;margin-left: 30px;">
                        <label class="am-u-sm-3 am-form-label">固定转发显示</label>
                        <div class="am-u-sm-8 am-u-end">
                            <div class="tpl-switch">
                                <input id="wetopen" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $list.whether_open==1}checked{/if}>
                                <div class="tpl-switch-btn-view">
                                    <div></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group" style="padding: 10px 0px 0px 0px;margin-left: 30px;">
                        <label class="am-u-sm-3 am-form-label">转发时显示的标题</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="title" value="{$list.title}" placeholder="请输入标题">
                        </div>
                    </div>

                    <div class="am-form-group" style="padding: 10px 0px 30px 0px;margin-left: 30px;">
                        <label class="am-u-sm-3 am-form-label"> 转发时显示的图片</label>
                        <div class="am-u-sm-9">
                            <img src="{$list.reis_img}" id="shion" onerror="this.src='static/wechat/image_vip_top.jpg'" onclick="cuonice();" style="width: 170px;height: 136px;cursor: pointer;"/>
                            <button type="button" style="margin-left:10px;margin-right:10px; font-size: 12px;" onclick="cuonice();">
                                选择图片
                            </button>
                            <small>建议图片尺寸：500*400px</small>
                            <input type="hidden" value="{$list.reis_img}" name="sngimg">
                        </div>
                    </div>

                    <div class="am-form-group">
                        <div class="am-u-sm-7 am-u-sm-push-5">
                            <button type="button" class="am-btn am-btn-secondary" style="border-radius: 5px;" onclick="holdSave();">保存设置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>


    !function () {
        $('.tpl-switch').find('.tpl-switch-btn-view').on('click', function () {
            $(this).prev('.tpl-switch-btn').prop("checked", function () {
                if ($(this).is(':checked')) {
                    return false;
                } else {
                    return true;
                }
            });
        });
    }();

    function cuonice() {
        layer.open({
            type: 2,
            anim:2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    function sutake(eurl) {
        $('#shion').attr('src', eurl);
        $("[name='sngimg']").val(eurl);
        layer.closeAll();
    }




    function holdSave() {
        var wetopen = $('#wetopen').prop('checked') ? 1 : 0;
        var title = $.trim($('#title').val());
        if (title == '' || title == 'undefined' || title == null) {
            layer.msg('转发时显示的标题不能为空');
            return;
        }
        var sngimg = $("[name='sngimg']").val();
        if (sngimg == '' || sngimg == 'undefined' || sngimg == null) {
            layer.msg('请上传转发时显示的图片');
            return;
        }
        $.ajax({
            type: "post",
            url: "{:url('partake')}",
            data: {
                'usid': '{$list.id}',
                'wetopen': wetopen,
                'title': title,
                'sngimg': sngimg
            },
            dataType: 'json',
            success: function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }
        });
    }
</script>
{/block}