{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-cog"></span> 小程序设置
        </div>
        <div class="tpl-portlet-input tpl-fz-ml right">
            <span style="font-size: 12px;font-weight: bold;">版本号 : {$signCode}</span>
            <span style="margin-left:10px;font-size: 12px;font-weight: bold;">应用ID : {$much_id}</span>
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top: 20px;">
                <div class="am-form am-form-horizontal">

                    <div class="am-panel am-panel-default">
                        <div class="am-panel-hd">小程序信息</div>
                        <div class="am-panel-bd">

                            <div class="am-form-group" style="display: none;">
                                <label class="am-u-sm-3 am-form-label">小程序名称</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="appName" placeholder="{if $configList.app_name}{$configList.app_name}{else}请输入你的小程序名称{/if}">
                                </div>
                            </div>

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">小程序AppID</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="appId" placeholder="{if $configList.app_id}{$configList.app_id|ciphertext}{else}请输入你的小程序标识号{/if}">
                                </div>
                            </div>

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">小程序AppSecret</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="appSecret" placeholder="{if $configList.app_secret}{$configList.app_secret|ciphertext}{else}请输入你的小程序密钥{/if}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="am-panel am-panel-default">
                        <div class="am-panel-hd">微信支付配置</div>
                        <div class="am-panel-bd">

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">微信支付商户号</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="appMchid" placeholder="{if $configList.app_mchid}{$configList.app_mchid|ciphertext}{else}请输入你的微信支付商户号{/if}">
                                    <small>
                                        商户号 APP_MCHID
                                        <a href="https://pay.weixin.qq.com/" target="_blank">登录微信支付商户平台</a>，
                                        在【账户中心-账户设置-商户信息】中查看
                                    </small>
                                </div>
                            </div>

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">企业付款到零钱 API</label>
                                <div class="am-u-sm-9">
                                    <select id="versionType">
                                        <option value="0" {if $configList.version_type==0}selected{/if}>API v2</option>
                                        <option value="1" {if $configList.version_type==1}selected{/if}>API v3</option>
                                    </select>
                                    <small>[ 新注册商户建议选择 API v3 ]</small>
                                </div>
                            </div>

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">证书序列号</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="certificateSerial" placeholder="{if $configList.certificate_serial_number}{$configList.certificate_serial_number|ciphertext}{else}请输入你的微信支付证书序列号{/if}">
                                    <small>
                                        [ 选择 API v2 接口时 此处无需填写 ]
                                    </small>
                                </div>
                            </div>

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">微信支付密钥 v2</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="appKey" placeholder="{if $configList.app_key}{$configList.app_key|ciphertext}{else}请输入你的微信支付 v2 密钥{/if}">
                                    <small>
                                        微信支付密钥 APP_KEY
                                        <a href="https://pay.weixin.qq.com/" target="_blank">登录微信支付商户平台</a>，
                                        在【账户中心-账户设置-API安全】中设置
                                    </small>
                                </div>
                            </div>

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">微信支付密钥 v3</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="appKeyV3" placeholder="{if $configList.app_key_v3}{$configList.app_key_v3|ciphertext}{else}请输入你的微信支付 v3 密钥{/if}">
                                    <small>
                                        微信支付密钥 APP_KEY
                                        <a href="https://pay.weixin.qq.com/" target="_blank">登录微信支付商户平台</a>，
                                        在【账户中心-账户设置-API安全】中设置
                                    </small>
                                </div>
                            </div>

                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">微信支付回调地址</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="payReact" value="{$configList.pay_react}" placeholder="系统默认生成，非必要不建议修改">
                                    <small>
                                        <i style="color: crimson;margin-right: 10px;">系统默认生成，如非必要不建议修改</i>
                                        <span>生成规则：</span><strong>https://站点域名/addons/yl_welore/web/payReact.php</strong>
                                    </small>
                                </div>
                            </div>

                        </div>
                    </div>

                    <div class="am-panel am-panel-default">
                        <div class="am-panel-hd">微信支付证书信息</div>
                        <div class="am-panel-bd">
                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">微信支付 apiclient_cert.pem</label>
                                <div class="am-u-sm-9">
                                    <textarea id="apiclientCert" style="height: 280px;resize: none;margin-top: 5px;" placeholder="为保证安全性, 不显示证书内容. 若要修改, 请直接输入"></textarea>
                                    <small style="color: red;">如果不需要提现功能无需填写</small>
                                </div>
                            </div>
                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">微信支付 apiclient_key.pem</label>
                                <div class="am-u-sm-9">
                                    <textarea id="apiclientKey" style="height: 280px;resize: none;margin-top: 5px;" placeholder="为保证安全性, 不显示证书内容. 若要修改, 请直接输入"></textarea>
                                    <small style="color: red;">如果不需要提现功能无需填写</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group" style="display: flex;justify-content: center;">
                        <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                    </div>

                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>


    var slock = false;
    var holdSave = function () {
        if (!slock) {
            var appName = $.trim($('#appName').val());
            var appId = $.trim($("#appId").val());
            var appSecret = $.trim($('#appSecret').val());
            var appMchid = $.trim($('#appMchid').val());
            var appKey = $.trim($('#appKey').val());
            var appKeyV3 = $.trim($('#appKeyV3').val());
            var payReact = $.trim($('#payReact').val());
            var certificateSerial = $.trim($('#certificateSerial').val());
            var versionType = $.trim($('#versionType').val());
            var apiclientCert = $.trim($('#apiclientCert').val());
            var apiclientKey = $.trim($('#apiclientKey').val());
            slock = true;
            $.ajax({
                type: "post",
                url: "{:url('applets')}",
                data: {
                    'usid': '{$configList.id}',
                    'appName': appName,
                    'appId': appId,
                    'appSecret': appSecret,
                    'appMchid': appMchid,
                    'appKey': appKey,
                    'appKeyV3': appKeyV3,
                    'payReact': payReact,
                    'certificateSerial': certificateSerial,
                    'versionType': versionType,
                    'apiclientCert': apiclientCert,
                    'apiclientKey': apiclientKey
                },
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                            slock = false;
                        });
                    }
                }
            });
        }
    }
</script>
{/block}