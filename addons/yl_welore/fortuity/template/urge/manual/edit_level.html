{extend name="/base"/}
{block name="main"}
<style>.am-form input{padding-left:5px!important;margin-left:-5px;}small{margin-left:-5px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-edit"></span> 编辑等级
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">等级级别</label>
                        <div class="am-u-sm-9">
                            <input type="text" value="Lv.{$list.level_hierarchy}" disabled style="background: #f5f5f5;">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">等级名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="name" value="{$list.level_name}" placeholder="请输入等级名称">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">等级图标</label>
                        <div class="am-u-sm-9">
                            <img src="{$list.level_icon}" id="shion" onerror="this.src='static/disappear/level.png'" onclick="cuonice();" style="width: 64px;height: 36px;cursor: pointer;"/>
                            <button type="button" style="margin-left:10px;font-size: 12px;" onclick="cuonice();">
                                选择图片
                            </button>
                            <small style="margin-left: 5px;">建议图片尺寸：160*90px 或 比例为：16:9
                                <span style="margin-left:30px;font-weight:bold;">注 : </span>
                                <span style="color:red;font-weight:bold;">图片内容需占满整个画布</span>
                            </small>
                            <input type="hidden" value="{$list.level_icon}" name="sngimg">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">所需经验</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="experience" value="{$list.need_experience}" {if $list.level_hierarchy==0}disabled{/if} placeholder="请输入达到该等级所需要的经验" oninput="digitalCheck(this);">
                            {if $list.level_hierarchy==0}
                            <small>
                                <span style="color: red;">达到该等级所需要的经验</span>
                                <span style="font-weight: bold;">当前等级级别为 Lv.0 时不允许修改</span>
                            </small>
                            {/if}
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">赠送荣誉点</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="point" value="{$list.honor_point}" {if $list.level_hierarchy==0}disabled{/if} placeholder="请输入达到该等级赠送给用户的荣誉点数" oninput="digitalCheck(this);">
                            {if $list.level_hierarchy==0}
                            <small>
                                <span style="color: red;">达到该等级赠送给用户的荣誉点数</span>
                                <span style="font-weight: bold;">当前等级级别为 Lv.0 时不允许修改</span>
                            </small>
                            {/if}
                        </div>
                    </div>
                    <div class="am-form-group" style="display: flex;justify-content: center;">
                        <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    var cuonice = function () {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    var sutake = function (eurl) {
        $('#shion').attr('src', eurl);
        $("[name='sngimg']").val(eurl);
        layer.closeAll();
    }

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
        return obj.value;
    }

    var onlock = false;
    var holdSave = function () {
        var setData = {};
        setData['level_name'] = $.trim($('#name').val());
        if (setData['level_name'] == '') {
            layer.msg('请输入等级名称');
            return;
        }
        setData['level_icon'] = $.trim($('[name=\'sngimg\']').val());
        if (setData['level_icon'] == '') {
            layer.msg('请选择等级图标');
            return;
        }
        setData['need_experience'] = digitalCheck($('#experience')[0]);
        setData['honor_point'] = digitalCheck($('#point')[0]);
        if (!onlock) {
            onlock = true;
            $.post("{:url('manual/editLevel')}", {leid: '{$list.id}', setData}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1600}, function () {
                        onlock = false;
                    });
                }
            }, 'json');
        }
    }
</script>
{/block}