{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .am-form {position: relative;overflow: visible;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-trophy {margin-right: 5px;color: #23b7e5;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .filter-btn-group {margin: 0 0 15px 0;}
    .filter-btn-group .am-btn {margin-right: 10px;border-radius: 3px;padding: 6px 12px;font-size: 13px;border: 1px solid #ddd;background: #fff;color: #666;transition: all 0.3s;text-decoration: none;}
    .filter-btn-group .am-btn:hover {border-color: #23b7e5;color: #23b7e5;background-color: #f5fafd;text-decoration: none;}
    .filter-btn-group .am-btn.active {background-color: #23b7e5;color: white;border-color: #23b7e5;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 12px 8px;}
    .am-table > tbody > tr > td {padding: 15px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .user-avatar {width: 50px;height: 50px;border-radius: 50%;border: 2px solid #e8e8e8;box-shadow: 0 2px 4px rgba(0,0,0,0.1);transition: all 0.3s;}
    .user-avatar:hover {border-color: #23b7e5;box-shadow: 0 2px 8px rgba(35,183,229,0.3);}
    .user-link {color: #23b7e5;text-decoration: none;font-weight: 500;}
    .user-link:hover {color: #1a9bc0;text-decoration: none;}
    .task-count {font-weight: 600;color: #333;}
    .rank-badge {display: inline-block;width: 20px;height: 20px;border-radius: 50%;color: white;font-size: 11px;font-weight: bold;line-height: 20px;text-align: center;}
    .rank-1 {background: linear-gradient(45deg, #ffd700, #ffed4e);}
    .rank-2 {background: linear-gradient(45deg, #c0c0c0, #e8e8e8);}
    .rank-3 {background: linear-gradient(45deg, #cd7f32, #daa520);}
    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-trophy"></span> 任务完成排行榜
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索用户昵称...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-btn-toolbar filter-btn-group">
                    <a href="{:url('manual/taskLeaderboard')}&egon=0" class="am-btn {if $egon==0}active{/if}">全部</a>
                    <a href="{:url('manual/taskLeaderboard')}&egon=1" class="am-btn {if $egon==1}active{/if}">每日任务排行</a>
                    <a href="{:url('manual/taskLeaderboard')}&egon=2" class="am-btn {if $egon==2}active{/if}">每周任务排行</a>
                    <a href="{:url('manual/taskLeaderboard')}&egon=3" class="am-btn {if $egon==3}active{/if}">每月任务排行</a>
                    <a href="{:url('manual/taskLeaderboard')}&egon=4" class="am-btn {if $egon==4}active{/if}">今年任务排行</a>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="15%">排名</th>
                            <th width="25%">用户头像</th>
                            <th width="35%">用户昵称</th>
                            <th width="25%">完成任务次数</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo" key="rank"}
                        <tr>
                            <td class="am-text-middle">
                                {if $rank <= 3}
                                <span class="rank-badge rank-{$rank}">{$rank}</span>
                                {else}
                                <span style="font-weight: 600; color: #666;">{$rank}</span>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.user_head_sculpture && $vo.user_head_sculpture !='/yl_welore/style/icon/default.png'}
                                <img src="{$vo.user_head_sculpture}" class="user-avatar">
                                {else}
                                <img src="{:urlBridging('static/disappear/tourist.png')}" class="user-avatar">
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}" class="user-link">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                            </td>
                            <td class="am-text-middle">
                                <span class="task-count">共{$vo.fulfill}次</span>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>
    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('manual/taskLeaderboard')}&egon={$egon}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('manual/taskLeaderboard')}&egon={$egon}&page={$page}";
        }
    }
</script>
{/block}