{extend name="/base"/}
{block name="main"}
<style>.resth button{margin-right:5px;}.cust-btn{font-size:14px;padding:5px 10px;}.cust-btn-one{margin-left:-20px;}.cust-btn-activate{border-bottom:solid 2px cornflowerblue;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption bold" style="margin-left:14px;">
            <a href="{:url('user/material')}&usid={$userInfo.id}" target="_blank" title="{$userInfo.user_nick_name|emoji_decode}">
                <span style="color: black;margin: 0px 3px;">{$userInfo.user_nick_name|emoji_decode}</span>
            </a>
            任务完成详情
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="searchDetail" value="{$searchDetail}" placeholder="搜索任务说明...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div style="width:100%;margin: 0px 0px 20px 20px;">
                    <div class="am-btn-group am-btn-group-xs resth">
                        <a href="{:url('manual/dailytask')}&uid={$userInfo.id}&egon=0" class="cust-btn cust-btn-one {if $egon==0}cust-btn-activate{/if}">全部</a>
                        <a href="{:url('manual/dailytask')}&uid={$userInfo.id}&egon=1" class="cust-btn {if $egon==1}cust-btn-activate{/if}">发布帖子</a>
                        <a href="{:url('manual/dailytask')}&uid={$userInfo.id}&egon=2" class="cust-btn {if $egon==2}cust-btn-activate{/if}">回复帖子</a>
                        <a href="{:url('manual/dailytask')}&uid={$userInfo.id}&egon=3" class="cust-btn {if $egon==3}cust-btn-activate{/if}">帖子点赞</a>
                        <a href="{:url('manual/dailytask')}&uid={$userInfo.id}&egon=4" class="cust-btn {if $egon==4}cust-btn-activate{/if}">帖子收藏</a>
                        <a href="{:url('manual/dailytask')}&uid={$userInfo.id}&egon=5" class="cust-btn {if $egon==5}cust-btn-activate{/if}">帖子转发</a>
                        <a href="{:url('manual/dailytask')}&uid={$userInfo.id}&egon=6" class="cust-btn {if $egon==6}cust-btn-activate{/if}">赠送礼物</a>
                        <a href="{:url('manual/dailytask')}&uid={$userInfo.id}&egon=7" class="cust-btn {if $egon==7}cust-btn-activate{/if}">充值贝壳</a>
                        <a href="{:url('manual/dailytask')}&uid={$userInfo.id}&egon=8" class="cust-btn {if $egon==8}cust-btn-activate{/if}">关注用户</a>
                        <a href="{:url('manual/dailytask')}&uid={$userInfo.id}&egon=9" class="cust-btn {if $egon==9}cust-btn-activate{/if}">购买商品</a>
                        <a href="{:url('manual/dailytask')}&uid={$userInfo.id}&egon=10" class="cust-btn {if $egon==10}cust-btn-activate{/if}">邀请好友</a>
                        <a href="{:url('manual/dailytask')}&uid={$userInfo.id}&egon=11" class="cust-btn {if $egon==11}cust-btn-activate{/if}">每日签到</a>
                        <a href="{:url('manual/dailytask')}&uid={$userInfo.id}&egon=12" class="cust-btn {if $egon==12}cust-btn-activate{/if}">购买会员</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main">
                        <thead>
                        <tr style="text-align: center;">
                            <th width="12.5%">任务名称</th>
                            <th width="12.5%">任务类型</th>
                            <th width="12.5%">任务周期</th>
                            <th width="12.5%">奖励类型</th>
                            <th width="12.5%">所需次数</th>
                            <th width="12.5%">奖励分数</th>
                            <th width="12.5%">完成说明</th>
                            <th width="12.5%">完成时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">{$vo.task_name}</td>
                            <td class="am-text-middle">
                                {switch $vo.task_type}
                                {case 0}发布帖子{/case}
                                {case 1}回复帖子{/case}
                                {case 2}帖子点赞{/case}
                                {case 3}帖子收藏{/case}
                                {case 4}帖子转发{/case}
                                {case 5}赠送礼物{/case}
                                {case 6}充值贝壳{/case}
                                {case 7}关注用户{/case}
                                {case 7}购买商品{/case}
                                {case 9}邀请好友{/case}
                                {case 10}每日签到{/case}
                                {case 11}购买会员{/case}
                                {case 12}观看广告{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">
                                {switch $vo.task_cycle}
                                {case 0}每日任务{/case}
                                {case 1}每周任务{/case}
                                {case 2}每月任务{/case}
                                {case 3}每年任务{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">
                                {switch $vo.task_reward_type}
                                {case 0}积分奖励{/case}
                                {case 1}经验奖励{/case}
                                {case 2}荣誉奖励{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">{$vo.task_frequency}次</td>
                            <td class="am-text-middle">
                                {$vo.task_salary}
                                {switch $vo.task_reward_type}
                                {case 0}积分{/case}
                                {case 1}经验{/case}
                                {case 2}荣誉{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">
                                {$vo.complete_description}
                            </td>
                            <td class="am-text-middle">{:date('Y-m-d H:i:s',$vo.complete_time)}</td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var fuzzy = function () {
        var searchDetail = $.trim($('#searchDetail').val());
        if (searchDetail) {
            location.href = "{:url('manual/dailytask')}&uid={$userInfo.id}&egon={$egon}&searchDetail=" + searchDetail + "&page={$page}";
        } else {
            location.href = "{:url('manual/dailytask')}&uid={$userInfo.id}&egon={$egon}&page={$page}";
        }
    }
</script>
{/block}