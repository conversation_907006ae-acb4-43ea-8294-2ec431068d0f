{extend name="/base"/}
{block name="main"}
<link rel="stylesheet" href="assets/css/colorful-font.css?v={:time()}">
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 新增装扮
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">装扮名称</label>
                        <div class="am-u-sm-9">
                            <select id="specialNumber">
                                <option value="-1">请选择</option>
                                <option value="1">扫描</option>
                                <option value="2">抖动</option>
                                <option value="3">抖音</option>
                                <option value="4">霓虹</option>
                                <option value="5">模糊</option>
                                <option value="6">彩虹</option>
                                <option value="7">闪烁</option>
                                <option value="8">条刷</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">装扮预览</label>
                        <div class="am-u-sm-9">
                            <div id="specialStyle" style="width:130px;font-size:20px;font-weight:bold;white-space:nowrap;">
                                文字文字文字
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">所需荣誉点</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="outlay" placeholder="请输入解锁装扮所需要的荣誉点数" oninput="digitalCheck(this);">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">状态</label>
                        <div class="am-u-sm-9">
                            <select id="status">
                                <option value="1">显示</option>
                                <option value="0">隐藏</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">排序</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="scores" value="0" placeholder="请输入排序数字" oninput="digitalCheck(this);">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-push-3">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    !function () {
        $('#specialNumber').change(function () {
            $('#specialStyle').removeClass().addClass('yl_style' + this.value);
        });
    }();

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
        return obj.value;
    }

    var onlock = false;
    var holdSave = function () {
        var setData = {};
        setData['special_number'] = $('#specialNumber').val();
        setData['unlock_outlay'] = digitalCheck($('#outlay')[0]);
        setData['status'] = $('#status').val();
        setData['scores'] = digitalCheck($('#scores')[0]);
        if (!onlock) {
            onlock = true;
            $.post("{:url('manual/newDeck')}", setData, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.href = "{:url('manual/deck')}";
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1000}, function () {
                        onlock = false;
                    });
                }
            }, 'json');
        }
    }
</script>
{/block}