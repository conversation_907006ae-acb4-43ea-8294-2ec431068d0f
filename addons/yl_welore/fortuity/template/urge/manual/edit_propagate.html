{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-edit"></span> 编辑任务
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-9 am-u-sm-push-1">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">任务名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="name" value="{$list.task_name}" disabled placeholder="请输入任务名称">
                            <small style="color:red;font-weight:bold;">为防止已做任务的用户看到任务名字不同时造成疑惑，不可修改</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">任务类型</label>
                        <div class="am-u-sm-9">
                            <select id="type" onchange="selectType(this);">
                                <option value="-1">请选择任务类型</option>
                                <option {if $list.task_type==0}selected{/if} value="0">发布帖子</option>
                                <option {if $list.task_type==1}selected{/if} value="1">回复帖子</option>
                                <option {if $list.task_type==2}selected{/if} value="2">帖子点赞</option>
                                <option {if $list.task_type==3}selected{/if} value="3">帖子收藏</option>
                                <option {if $list.task_type==4}selected{/if} value="4">帖子转发</option>
                                <option {if $list.task_type==5}selected{/if} value="5">赠送礼物</option>
                                <option {if $list.task_type==6}selected{/if} value="6">充值贝壳</option>
                                <option {if $list.task_type==7}selected{/if} value="7">关注用户</option>
                                <option {if $list.task_type==8}selected{/if} value="8">购买商品</option>
                                <option {if $list.task_type==9}selected{/if} value="9">邀请好友</option>
                                <option {if $list.task_type==10}selected{/if} value="10">每日签到</option>
                                <option {if $list.task_type==11}selected{/if} value="11">购买会员</option>
                                <option {if $list.task_type==12}selected{/if} value="12">观看广告</option>
                            </select>
                            <small id="reminder" style="color:red;font-weight:bold;"></small>
                        </div>
                    </div>
                    <div id="tory-hide" class="am-form-group" style="display: none;">
                        <label class="am-u-sm-3 am-form-label">指定圈子</label>
                        <div class="am-u-sm-9">
                            <select id="toryId">
                                <option value="0">不选择 ( 任务在全部圈子都可以完成 )</option>
                                {volist name="toryList" id="vo"}
                                <option {if $list.tory_id==$vo.id}selected{/if} value="{$vo.id}">{$vo.realm_name}</option>
                                {/volist}
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">任务周期</label>
                        <div class="am-u-sm-9">
                            <select id="cycle">
                                <option value="-1">请选择任务周期</option>
                                <option {if $list.task_cycle==0}selected{/if} value="0">每日任务</option>
                                <option {if $list.task_cycle==1}selected{/if} value="1">每周任务</option>
                                <option {if $list.task_cycle==2}selected{/if} value="2">每月任务</option>
                                <option {if $list.task_cycle==3}selected{/if} value="3">每年任务</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">奖励类型</label>
                        <div class="am-u-sm-9">
                            <select id="rewardType">
                                <option value="-1">请选择奖励类型</option>
                                <option {if $list.task_reward_type==0}selected{/if} value="0">
                                    {$defaultNavigate.confer}奖励
                                </option>
                                <option {if $list.task_reward_type==1}selected{/if} value="1">经验奖励</option>
                                <option {if $list.task_reward_type==2}selected{/if} value="2">荣誉点奖励</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">所需次数</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="frequency" value="{$list.task_frequency}"
                                   placeholder="请输入完成任务所需要达成的次数" oninput="digitalCheck(this);">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">普通用户奖励</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="poorSalary" value="{$list.poor_task_salary}" placeholder="请输入普通用户奖励分数" oninput="digitalCheck(this);">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">会员用户奖励</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="richSalary" value="{$list.rich_task_salary}"
                                   placeholder="请输入会员用户奖励分数" oninput="digitalCheck(this);">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">排序</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="scores" value="{$list.scores}" placeholder="请输入排序数字" oninput="digitalCheck(this);">
                        </div>
                    </div>

                    <div class="am-form-group" style="display: flex; justify-content: center;margin: 60px 0 40px 0;">
                        <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
        return obj.value;
    }

    !function () {
        var type = $('#type');
        if (type.val() >= 0 && type.val() <= 4) {
            $('#tory-hide').show();
        } else {
            $('#tory-hide').hide();
        }
        type.change(function () {
            if (parseInt($(this).val()) === 12) {
                $('#reminder').text('观看广告任务仅支持激励式广告 广告收益请自行测试 合理安排用户奖励');
            } else {
                $('#reminder').text('');
            }
        });
    }();

    var selectType = function (obj) {
        if (obj.value >= 0 && obj.value <= 4) {
            $('#tory-hide').show();
        } else {
            $('#tory-hide').hide();
        }
    }


    var onlock = false;
    var holdSave = function () {
        var setData = {};
        setData['task_name'] = $.trim($('#name').val());
        if (setData['task_name'] == '') {
            layer.msg('任务名称不能为空');
            return;
        }
        setData['task_type'] = $.trim($('#type').val());
        if (setData['task_type'] == -1) {
            layer.msg('请选择任务类型');
            return;
        }
        setData['tory_id'] = $.trim($('#toryId').val());
        setData['task_cycle'] = $.trim($('#cycle').val());
        if (setData['task_cycle'] == -1) {
            layer.msg('请选择任务周期');
            return;
        }
        setData['task_reward_type'] = $.trim($('#rewardType').val());
        if (setData['task_reward_type'] == -1) {
            layer.msg('请选择奖励类型');
            return;
        }
        setData['task_frequency'] = digitalCheck($('#frequency')[0]);
        if (setData['task_frequency'] <= 0) {
            layer.msg('完成任务所需要达成的次数不能小于或等于零次');
            return;
        }
        setData['poor_task_salary'] = digitalCheck($('#poorSalary')[0]);
        if (setData['poor_task_salary'] <= 0) {
            layer.msg('普通用户奖励分数不能小于或等于零分');
            return;
        }
        setData['rich_task_salary'] = digitalCheck($('#richSalary')[0]);
        if (setData['rich_task_salary'] <= 0) {
            layer.msg('会员用户奖励分数不能小于或等于零分');
            return;
        }
        setData['scores'] = digitalCheck($('#scores')[0]);
        if (setData['scores'] < 0) {
            layer.msg('排序不能小于零');
            return;
        }
        if (!onlock) {
            onlock = true;
            $.post("{:url('manual/editPropagate')}", {meid: '{$list.id}', setData}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.href = "{:url('manual/propagate')}";
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1600}, function () {
                        onlock = false;
                    });
                }
            }, 'json');
        }
    }
</script>
{/block}