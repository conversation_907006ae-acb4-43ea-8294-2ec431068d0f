{extend name="/base"/}
{block name="main"}
<style>.am-table>thead:first-child>tr:first-child>th,.am-table>tbody>tr>td{text-align:center;}.span-ranking{background:#477a98;border-radius:3px;padding:6px 10px;font-size:12px;cursor:pointer;color:#f1f4f5;margin-left:5px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption bold" style="margin-left:14px;">
            <a href="{:url('user/material')}&usid={$userInfo.id}" target="_blank" title="{$userInfo.user_nick_name|emoji_decode}">
                <span style="color: black;margin: 0px 3px;">{$userInfo.user_nick_name|emoji_decode}</span>
            </a>
            经验荣誉获得记录
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="searchDetail" value="{$searchDetail}" placeholder="搜索记录说明...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom:20px;">
            <div class="am-u-sm-6 am-u-md-6">
            <span style="font-size:14px;font-weight:bold;">
                经验值 : <span style="color:green;">{$userInfo.experience}</span>
            </span>
                <span style="font-size:14px;font-weight:bold;margin-left:20px;">
                荣誉点 : <span style="color:green;">{$userInfo.honor_point}</span>
            </span>
            </div>
            <div class="am-u-sm-6 am-u-md-6" style="text-align:right;">
            <span class="span-ranking"
                  data-am-modal="{target: '#rechargeExperienceA', closeViaDimmer: 0, width: 550, height: 335}">
                增加经验值
            </span>
                <span class="span-ranking"
                      data-am-modal="{target: '#rechargeExperienceB', closeViaDimmer: 0, width: 550, height: 335}">
                扣除经验值
            </span>
                <span class="span-ranking"
                      data-am-modal="{target: '#rechargeHonorPointA', closeViaDimmer: 0, width: 550, height: 335}">
                增加荣誉点
            </span>
                <span class="span-ranking"
                      data-am-modal="{target: '#rechargeHonorPointB', closeViaDimmer: 0, width: 550, height: 335}">
                扣除荣誉点
            </span>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main">
                        <thead>
                        <tr style="text-align: center;">
                            <th width="16%">所得前</th>
                            <th width="16%">所得点数</th>
                            <th width="16%">所得后</th>
                            <th width="16%">获得类型</th>
                            <th width="24%">记录说明</th>
                            <th width="12%">记录时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                <span style="color:#328cb8;">{$vo.dot_before}</span>
                            </td>
                            <td class="am-text-middle">
                                {switch $vo.cypher}
                                {case 0}<span style="color:green;">+{$vo.points}</span>{/case}
                                {case 1}<span style="color:red;">-{$vo.points}</span>{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">
                                <span style="color:#328cb8;">{$vo.dot_after}</span>
                            </td>
                            <td class="am-text-middle">{switch $vo.type}{case 0}经验值{/case}{case 1}荣誉点{/case}{/switch}</td>
                            <td class="am-text-middle">{$vo.dot_cap}</td>
                            <td class="am-text-middle">{:date('Y-m-d H:i:s',$vo.receive_time)}</td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="rechargeExperienceA">
        <div class="am-modal-dialog">
            <div class="am-modal-hd" style="text-align: left;">
                <a href="javascript: void(0)" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd" style="margin-top: 30px;">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">增加经验值</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <input type="number" id="modifyExperienceA" value="0" placeholder="请输入要增加的经验值数量" data-Experience="0">
                            <input type="hidden" id="uExperienceA" value="{$userInfo.experience}">
                            <small id="cEcipherA"><span style="color: red;font-size: 14px;"> {$userInfo.experience} + 0 = {$userInfo.experience} </span></small><br>
                            <small><strong>计算方式：经验值数量 + 增加经验值数量 = 即将要保存的经验值</strong></small><br>
                            <small><span style="color: #2D93CA;">计算结果将会自动保留两位小数</span></small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">备注</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <textarea id="solutionA" rows="3" style="resize: none;">系统赠送经验值</textarea>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <button type="button" class="am-btn am-btn-default am-btn-sm" onclick="holdSave('0');">
                            确认增加
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="rechargeExperienceB">
        <div class="am-modal-dialog">
            <div class="am-modal-hd" style="text-align: left;">
                <a href="javascript: void(0)" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd" style="margin-top: 30px;">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">扣除经验值</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <input type="number" id="modifyExperienceB" value="0" placeholder="请输入要扣除的经验值数量" data-Experience="0">
                            <input type="hidden" id="uExperienceB" value="{$userInfo.experience}">
                            <small id="cEcipherB"><span style="color: red;font-size: 14px;"> {$userInfo.experience} - 0 = {$userInfo.experience} </span></small><br>
                            <small><strong>计算方式：经验值数量 - 扣除经验值数量 = 即将要保存的经验值</strong></small><br>
                            <small><span style="color: #2D93CA;">计算结果将会自动保留两位小数</span></small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">备注</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <textarea id="solutionB" rows="3" style="resize: none;">系统扣除经验值</textarea>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <button type="button" class="am-btn am-btn-default am-btn-sm" onclick="holdSave('1');">
                            确认扣除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="rechargeHonorPointA">
        <div class="am-modal-dialog">
            <div class="am-modal-hd" style="text-align: left;">
                <a href="javascript: void(0)" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd" style="margin-top: 30px;">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">增加荣誉点</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <input type="number" id="modifyHonorPointA" value="0" placeholder="请输入要增加的荣誉点数量" data-HonorPoint="0">
                            <input type="hidden" id="uHonorPointA" value="{$userInfo.honor_point}">
                            <small id="fEcipherA"><span style="color: red;font-size: 14px;"> {$userInfo.honor_point} + 0 = {$userInfo.honor_point} </span></small><br>
                            <small><strong>计算方式：荣誉点数量 + 增加荣誉点数量 = 即将要保存的荣誉点</strong></small><br>
                            <small><span style="color: #2D93CA;">计算结果将会自动保留两位小数</span></small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">备注</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <textarea id="solutionC" rows="3" style="resize: none;">系统赠送荣誉点</textarea>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <button type="button" class="am-btn am-btn-default am-btn-sm" onclick="holdSave('2');">
                            确认增加
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="rechargeHonorPointB">
        <div class="am-modal-dialog">
            <div class="am-modal-hd" style="text-align: left;">
                <a href="javascript: void(0)" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd" style="margin-top: 30px;">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">扣除荣誉点</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <input type="number" id="modifyHonorPointB" value="0" placeholder="请输入要扣除的荣誉点数量" data-HonorPoint="0">
                            <input type="hidden" id="uHonorPointB" value="{$userInfo.honor_point}">
                            <small id="fEcipherB"><span style="color: red;font-size: 14px;"> {$userInfo.honor_point} - 0 = {$userInfo.honor_point} </span></small><br>
                            <small><strong>计算方式：荣誉点数量 - 扣除荣誉点数量 = 即将要保存的荣誉点</strong></small><br>
                            <small><span style="color: #2D93CA;">计算结果将会自动保留两位小数</span></small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:-4px;">备注</label>
                        <div class="am-u-sm-9" style="text-align: left;">
                            <textarea id="solutionD" rows="3" style="resize: none;">系统扣除荣誉点</textarea>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <button type="button" class="am-btn am-btn-default am-btn-sm" onclick="holdSave('3');">
                            确认扣除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    $(function () {
        $('#modifyExperienceA').keyup(function () {
            var getUExperience = Number($('#uExperienceA').val().match(/^\d+(?:\.\d{0,2})?/));
            var getExperience = Number($(this).val().match(/^\d+(?:\.\d{0,2})?/));
            if (isNaN(getExperience)) {
                $(this).val(getExperience = 0);
            }
            var epayoff = Number((getUExperience + getExperience).toString().match(/^\d+(?:\.\d{0,2})?/));
            if (epayoff >= 9999999999999999) {
                var dataExperience = Number($(this).attr('data-Experience'));
                $(this).val(dataExperience);
                return false;
            }
            $('#cEcipherA').html('<span style="color: red;font-size: 14px;"> ' + getUExperience + ' + ' + getExperience + ' = ' + epayoff + ' </span>');
            $(this).attr('data-Experience', getExperience.toString());
        });
        $('#modifyExperienceB').keyup(function () {
            var getUExperience = Number($('#uExperienceB').val().match(/^\d+(?:\.\d{0,2})?/));
            var getExperience = Number($(this).val().match(/^\d+(?:\.\d{0,2})?/));
            if (isNaN(getExperience)) {
                $(this).val(getExperience = 0);
            }
            var epayoff = Number((getUExperience - getExperience).toString().match(/^\d+(?:\.\d{0,2})?/));
            if (epayoff < 0) {
                var dataExperience = Number($(this).attr('data-Experience'));
                $(this).val(dataExperience);
                return false;
            }
            $('#cEcipherB').html('<span style="color: red;font-size: 14px;"> ' + getUExperience + ' - ' + getExperience + ' = ' + epayoff + ' </span>');
            $(this).attr('data-Experience', getExperience.toString());
        });
        $('#modifyHonorPointA').keyup(function () {
            var getUHonorPoint = Number($('#uHonorPointA').val().match(/^\d+(?:\.\d{0,2})?/));
            var getHonorPoint = Number($(this).val().match(/^\d+(?:\.\d{0,2})?/));
            if (isNaN(getHonorPoint)) {
                $(this).val(getHonorPoint = 0);
            }
            var epayoff = Number((getUHonorPoint + getHonorPoint).toString().match(/^\d+(?:\.\d{0,2})?/));
            if (epayoff >= 9999999999999999) {
                var dataHonorPoint = Number($(this).attr('data-HonorPoint'));
                $(this).val(dataHonorPoint);
                return false;
            }
            $('#fEcipherA').html('<span style="color: red;font-size: 14px;"> ' + getUHonorPoint + ' + ' + getHonorPoint + ' = ' + epayoff + ' </span>');
            $(this).attr('data-HonorPoint', getHonorPoint.toString());
        });
        $('#modifyHonorPointB').keyup(function () {
            var getUHonorPoint = Number($('#uHonorPointB').val().match(/^\d+(?:\.\d{0,2})?/));
            var getHonorPoint = Number($(this).val().match(/^\d+(?:\.\d{0,2})?/));
            if (isNaN(getHonorPoint)) {
                $(this).val(getHonorPoint = 0);
            }
            var epayoff = Number((getUHonorPoint - getHonorPoint).toString().match(/^\d+(?:\.\d{0,2})?/));
            if (epayoff < 0) {
                var dataHonorPoint = Number($(this).attr('data-HonorPoint'));
                $(this).val(dataHonorPoint);
                return false;
            }
            $('#fEcipherB').html('<span style="color: red;font-size: 14px;"> ' + getUHonorPoint + ' - ' + getHonorPoint + ' = ' + epayoff + ' </span>');
            $(this).attr('data-HonorPoint', getHonorPoint.toString());
        });
    });

    var islock = false
    var holdSave = function (genus) {
        if (!islock) {
            islock = true;
            var cipher = 0;
            var solution = '';
            switch (parseInt(genus)) {
                case 0:
                    cipher = Number($('#modifyExperienceA').val().toString().match(/^\d+(?:\.\d{0,2})?/));
                    solution = $.trim($('#solutionA').val());
                    break;
                case 1:
                    cipher = Number($('#modifyExperienceB').val().toString().match(/^\d+(?:\.\d{0,2})?/));
                    solution = $.trim($('#solutionB').val());
                    break;
                case 2:
                    cipher = Number($('#modifyHonorPointA').val().toString().match(/^\d+(?:\.\d{0,2})?/));
                    solution = $.trim($('#solutionC').val());
                    break;
                case 3:
                    cipher = Number($('#modifyHonorPointB').val().toString().match(/^\d+(?:\.\d{0,2})?/));
                    solution = $.trim($('#solutionD').val());
                    break;
            }
            $.post("{:url('user/update_experience_and_honor_point')}", {
                'usid': '{$userInfo.id}',
                'genus': genus,
                'cipher': cipher,
                'solution': solution
            }, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                    islock = false;
                }
            }, 'json');
        }
    }

    var fuzzy = function () {
        var searchDetail = $.trim($('#searchDetail').val());
        if (searchDetail) {
            location.href = "{:url('manual/expgloryrecord')}&uid={$userInfo.id}&searchDetail=" + searchDetail + "&page={$page}";
        } else {
            location.href = "{:url('manual/expgloryrecord')}&uid={$userInfo.id}&page={$page}";
        }
    }
</script>
{/block}