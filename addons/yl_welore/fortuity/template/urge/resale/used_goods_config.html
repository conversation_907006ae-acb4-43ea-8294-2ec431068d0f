{extend name="/base"/}
{block name="main"}
<style>.am-modal-hd .am-close{top:-5px;right:-3px;}.w-e-menu{font-size:12px}.w-e-text,.w-e-text-container{height:500px !important}</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 二手交易配置
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-8 am-u-sm-push-1" style="margin-top: 30px;">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">标题名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.customTitle" placeholder="请输入标题名称">
                            <small>自定义标题名称</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">发布开启自动审核</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.isAutoAudit">
                                <option value="0">关闭</option>
                                <option value="1">开启</option>
                            </select>
                            <small>开启后用户发布二手交易信息时将自动通过审核</small>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">回复开启自动审核</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.replyIsAutoAudit">
                                <option value="0">关闭</option>
                                <option value="1">开启</option>
                            </select>
                            <small>开启后用户回复二手交易信息时将自动通过审核</small>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">付费置顶开关</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.topTwig">
                                <option value="0">关闭</option>
                                <option value="1">开启</option>
                            </select>
                            <small>开启后用户可付费置顶</small>
                        </div>
                    </div>
                    <div v-if="item.topTwig == 1" class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">置顶价格类型</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.priceType">
                                <option value="0">贝壳支付</option>
                                <option value="1">积分支付</option>
                                <option value="2">微信支付</option>
                            </select>
                            <small>选择置顶的价格类型</small>
                        </div>
                    </div>
                    <div v-if="item.topTwig == 1" class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">置顶价格</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.topPrice" placeholder="请输入置顶价格" oninput="grender(this,2);">
                            <small>置顶价格 单价/天</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">交易说明</label>
                        <div class="am-u-sm-9 am-u-end">
                            <div id="detail" style="min-height:600px;">{$list.help_document|$richTextCompatible}</div>
                            <span id="customizeGallery" style="display:none;" onclick="cuonice();"></span>
                        </div>
                    </div>
                    <div class="am-form-group" style="display: flex;justify-content: center;margin: 50px 0 30px 0;">
                        <button type="button" class="am-btn am-btn-primary" @click="holdSave">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script type="text/javascript" src="static/wangEditor/wangEditor.min.js?time={:time()}"></script>
<link rel="stylesheet" href="static/wangEditor/wangEditor-fullscreen-plugin.css?time={:time()}">
<script type="text/javascript" src="static/wangEditor/wangEditor-fullscreen-plugin.js?time={:time()}"></script>
<script>

    var vm = new Vue({
        el: '#app',
        data() {
            return {
                item: {
                    customTitle: '',
                    isAutoAudit: '0',
                    topTwig: '0',
                    priceType: '0',
                    topPrice: '0.00',
                    helpDocument: '',
                },
                E: [],
                editor: []

            }
        }, created() {
            this.item.customTitle = '{$list.custom_title}';
            this.item.isAutoAudit = '{$list.is_auto_audit}';
            this.item.replyIsAutoAudit = '{$list.reply_is_auto_audit}';
            this.item.topTwig = '{$list.top_twig}';
            this.item.priceType = '{$list.price_type}';
            this.item.topPrice = '{$list.top_price}';
        }, mounted: function () {
            this.E = window.wangEditor;
            this.editor = new this.E('#detail');
            this.editor.customConfig.uploadImgServer = true;
            this.editor.create();
            this.E.fullscreen.init('#detail');
        },
        methods: {
            holdSave() {
                this.item.helpDocument = $.trim(this.editor.txt.html());
                $.post("{:url('resale/used_goods_config')}", {'item': this.item}, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }, 'json');
            }
        }
    });

    var grender = function (obj, limit) {
        if (limit === 2) {
            obj.value = obj.value.replace(/[^\d.]/g, "");
            obj.value = obj.value.replace(/^\./g, "");
            obj.value = obj.value.replace(/\.{2,}/g, ".");
            obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
            obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
        } else {
            obj.value = Number((obj.value.match(/^\d+(?:\.\d{0})?/)));
        }
    }

    var cuonice = function () {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    var sutake = function (eurl) {
        vm.editor.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
    }
</script>
{/block}