{extend name="/base"/}
{block name="main"}
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 微信公众号配置
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top: 20px;">
                <div class="am-form am-form-horizontal">

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">公众号二维码</label>
                        <div class="am-u-sm-9">
                            <img :src="appQrcode" onerror="this.src='static/disappear/default.png'" @click="cuoNice" style="width: 150px;height: 150px;cursor: pointer;"/>
                            <button type="button" style="margin-left: 5px;font-size: 12px;" @click="cuoNice">选择图片</button>
                            <input type="hidden" v-model="appQrcode">
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">公众号名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="appName" placeholder="请输入你的公众号名称">
                            <small style="color: #8c8c8c;">
                                公众号名称 登录<a href="https://mp.weixin.qq.com/" target="_blank" style="color: #8c8c8c;">微信公众平台</a>， 在【开发者中心】中查看
                            </small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">公众号 AppID</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="appId" placeholder="{if $list.wx_app_id}{$list.wx_app_id|ciphertext}{else}请输入你的公众号标识号{/if}">
                            <small style="color: #8c8c8c;">
                                公众号 AppID 登录<a href="https://mp.weixin.qq.com/" target="_blank" style="color: #8c8c8c;">微信公众平台</a>， 在【开发者中心】中查看
                            </small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">公众号 AppSecret</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="appSecret" placeholder="{if $list.wx_app_secret}{$list.wx_app_secret|ciphertext}{else}请输入你的公众号密钥{/if}">
                            <small style="color: #8c8c8c;">
                                公众号 AppSecret 登录<a href="https://mp.weixin.qq.com/" target="_blank" style="color: #8c8c8c;">微信公众平台</a>， 在【开发者中心】中查看
                            </small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">外部绑定URL</label>
                        <div class="am-u-sm-9">
                            <input type="text" style="background:#eee;" value="https://open.weixin.qq.com/connect/oauth2/authorize?appid={$list.wx_app_id}&redirect_uri={$domain}/addons/yl_welore/web/index.php?s=/api/wechat/wx_open_id&response_type=code&scope=snsapi_userinfo&state={$much_id}#wechat_redirect" disabled>
                            <small style="color: #8c8c8c;">
                                用于获取公众号UnionID ( 需要在开放平台同时绑定小程序和公众号才可使用 否则则无效果)
                            </small>
                        </div>
                    </div>


                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-offset-2" style="font-size: 14px;display: flex;flex-direction:column;justify-content: center;">
                            <div style="color: #5594ca;margin: 20px 0 15px 0;">
                                1.请前往
                                <a href="https://mp.weixin.qq.com/" target="_blank" style="color: #999;">
                                    [ 微信公众平台 ]
                                </a>
                                登录小程序 [ 左侧菜单 -> 开发管理 -> 开发设置 -> 业务域名 ] 配置：业务域名
                            </div>
                            <div style="color: #5594ca;margin: 10px 0;">
                                2.请前往
                                <a href="https://mp.weixin.qq.com/" target="_blank" style="color: #999;">
                                    [ 微信公众平台 ]
                                </a>
                                登录公众号 [ 左侧菜单 -> 设置与开发 -> 公众号设置 -> 功能设置 ] 配置：JS接口安全域名、网页授权域名
                            </div>
                            <div style="color: #5594ca;margin: 10px 0 5px 0;">
                                3.请前往
                                <a href="https://mp.weixin.qq.com/" target="_blank" style="color: #999;">
                                    [ 微信公众平台 ]
                                </a>
                                登录公众号 [ 左侧菜单 -> 设置与开发 -> 基本配置 ] 配置：IP白名单
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group" style="display: flex;justify-content: center;margin: 30px 0 30px 0;">
                        <button type="button" class="am-btn am-btn-primary" @click="holdSave">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    var vm = new Vue({
        el: '#app',
        data() {
            return {
                'appQrcode': '{$list.wx_app_qrcode}',
                'appName': '{$list.wx_app_name}',
                'appId': '',
                'appSecret': '',
            }
        },
        methods: {
            cuoNice() {
                layer.open({
                    type: 2,
                    anim: 2,
                    scrollbar: true,
                    area: ['900px', '600px'],
                    title: false,
                    closeBtn: 0,
                    shadeClose: true,
                    content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
                });
            },
            holdSave() {
                var setData = {};
                setData['appQrcode'] = this.appQrcode;
                setData['appName'] = this.appName;
                setData['appId'] = this.appId;
                setData['appSecret'] = this.appSecret;
                $.post("{:url('people/config')}", setData, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }, 'json');
            }
        }
    });

    var sutake = function (url) {
        vm.appQrcode = url;
        layer.closeAll();
    }
</script>
{/block}