{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-edit"></span> 编辑物品分类
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">分类名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="name" value="{$list.name}" placeholder="请输入分类名称">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">排序</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="sort" value="{$list.sort}" placeholder="请输入排序数字">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">显示状态</label>
                        <div class="am-u-sm-9">
                            <select id="status">
                                <option value="1" {if $list.status==1}selected{/if}>正常</option>
                                <option value="0" {if $list.status==0}selected{/if}>隐藏</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <div class="am-u-sm-12" style="display: flex;justify-content: center;margin-left: 30px;">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>
    var holdSave = function () {
        var setData = {};
        setData['fid'] = '{$list.id}';
        setData['name'] = $('#name').val();
        setData['sort'] = $('#sort').val();
        setData['status'] = $('#status').val();
        $.post("{:url('people/edit_lost_type')}", setData, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.href = "{:url('people/lost_type')}";
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2200});
            }
        }, 'json');
    }
</script>
{/block}