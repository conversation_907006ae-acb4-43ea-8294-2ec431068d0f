{extend name="/base"/}
{block name="main"}
<style>.span-ranking{background:#477a98;border-radius:3px;padding:6px 10px;font-size:12px;cursor:pointer;color:#f1f4f5;margin-left:5px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 失物招领列表
        </div>
        <div class="tpl-portlet-input tpl-fz-ml" style="display: flex;align-items: center;">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="hazy_name" value="{$hazy_name}" placeholder="搜索招领内容...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main">
                        <thead>
                        <tr>
                            <th class="text-center" width="11.11%">用户昵称</th>
                            <th class="text-center" width="11.11%">物品名称</th>
                            <th class="text-center" width="11.11%">物品类型</th>
                            <th class="text-center" width="11.11%">发布类型</th>
                            <th class="text-center" width="11.11%">遗失 ( 捡到 ) 时间</th>
                            <th class="text-center" width="11.11%">物品状态</th>
                            <th class="text-center" width="11.11%">审核状态</th>
                            <th class="text-center" width="11.11%">发布时间</th>
                            <th class="text-center" width="11.11%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle text-center">
                                {if $vo.user.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user.user_wechat_open_id}&page=1" title="{$vo.user.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user.user_nick_name|emoji_decode}">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td class="am-text-middle text-center">
                                {$vo.item_name|emoji_decode}
                            </td>
                            <td class="am-text-middle text-center">
                                {$vo.type_name}
                            </td>
                            <td class="am-text-middle text-center">
                                {switch $vo.release_type}
                                {case 0}遗失{/case}
                                {case 1}捡到{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle text-center">
                                {:date('Y-m-d H:i:s',$vo.lost_time)}
                            </td>
                            <td class="am-text-middle text-center">
                                {switch $vo.item_status}
                                {case 1}正常{/case}
                                {case 2}
                                    {switch $vo.release_type}
                                    {case 0}已找到{/case}
                                    {case 1}已归还{/case}
                                    {/switch}
                                {/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle text-center">
                                {switch $vo.audit_status}
                                {case 0}
                                <span style="color: orange;">待审核</span>
                                {/case}
                                {case 1}
                                <span style="color: green;">已通过</span>
                                {/case}
                                {case 2}
                                <span style="color: red;">已拒绝</span>
                                {/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle text-center">
                                {:date('Y-m-d H:i:s',$vo.create_time)}
                            </td>
                            <td class="am-text-middle text-center">
                                <div class="am-btn-group am-btn-group-sm" style="display: flex;justify-content: center;margin-top: -5px;">
                                    <button class="am-btn" style="padding: 0.5em 0.5em; color:#000;height: 30px;border-left:1px solid #9c9c9c;border-top:1px solid #9c9c9c;border-bottom:1px solid #9c9c9c;">
                                        功能列表
                                    </button>
                                    <div class="am-dropdown" data-am-dropdown style="height: 30px;line-height: 0;border-right:1px solid #9c9c9c;border-top:1px solid #9c9c9c;border-bottom:1px solid #9c9c9c;">
                                        <button class="am-btn am-dropdown-toggle" data-am-dropdown-toggle style="padding: 0.2em 0.5em;">
                                            <span class="am-icon-caret-down"></span>
                                        </button>
                                        <ul class="am-dropdown-content" style="width: 120px;min-width: initial;">
                                            <li style="display: flex;justify-content: center;">
                                                <a href="javascript:void(0);" onclick="reviewDetail('{$vo.id}')" style="padding: 5px;color: #000;">
                                                    查看详情
                                                </a>
                                            </li>
                                            {if $vo.audit_status==0}
                                            <li style="display: flex;justify-content: center;">
                                                <a href="javascript:void(0);" onclick="auditCorrect('{$vo.id}',1)" style="padding: 5px;color: #000;">
                                                    审核通过
                                                </a>
                                            </li>
                                            <li style="display: flex;justify-content: center;">
                                                <a href="javascript:void(0);" onclick="auditCorrect('{$vo.id}',2)" style="padding: 5px;color: #000;">
                                                    审核拒绝
                                                </a>
                                            </li>
                                            {/if}
                                            <li style="display: flex;justify-content: center;">
                                                <a href="javascript:void(0);" onclick="delCorrect('{$vo.id}')" style="padding: 5px;color: #000;">
                                                    删除信息
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var reviewDetail = function (fid) {
        layer.open({
            type: 2,
            anim: 2,
            title: false,
            area: ['520px', '550px'],
            scrollbar: true,
            closeBtn: true,
            shadeClose: true,
            content: ["{:url('people/lost_found_detail')}&fid=" + fid],
        });
    }

    var auditCorrect = function (fid, process) {
        var twoCheck = function (fid, process, reaValue) {
            $.ajax({
                type: "post",
                url: "{:url('people/trial_lost_found')}",
                data: {'fid': fid, 'process': process, 'inject': reaValue},
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }
            });
        }
        switch (process) {
            case 1:
                layer.confirm('您确定要审核通过这条数据吗？', {
                    btn: ['确定', '取消'], title: '提示'
                }, function () {
                    twoCheck(fid, process);
                }, function (index) {
                    layer.close(index);
                });
                break;
            case 2:
                layer.prompt({
                    title: '请输入这条数据未通过审核的原因：',
                    formType: 2,
                    area: ['300px', '100px'],
                    btn: ['确定', '取消'],
                }, function (reaValue, index) {
                    if (reaValue.trim() === '') {
                        return false;
                    }
                    twoCheck(fid, process, reaValue);
                    layer.close(index);
                });
                break;
        }
    }

    var delCorrect = function (fid) {
        layer.confirm('您确定要删除这条数据吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('people/del_lost_found')}", {'fid': fid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

    var fuzzy = function () {
        var hazy_name = $.trim($('#hazy_name').val());
        if (hazy_name) {
            location.href = "{:url('people/lost_found')}&hazy_name=" + hazy_name + "&page={$page}";
        } else {
            location.href = "{:url('people/lost_found')}&page={$page}";
        }
    }
</script>
{/block}