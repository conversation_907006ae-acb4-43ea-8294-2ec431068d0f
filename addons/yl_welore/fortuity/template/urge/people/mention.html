{extend name="/base"/}
{block name="main"}
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 公众号模板消息
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top: 50px;">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">模板ID</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.YL0001" placeholder="请输入模板ID">
                            <small style="font-weight: bold;">
                                可以任意选择服务类目 - 根据下方参数完整的复制模版ID
                            </small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">模板类型</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.type">
                                <option value="0">历史模版</option>
                                <option value="1">类目模版</option>
                            </select>
                            <small style="font-weight: bold;">
                                新版模板消息请选择类目模板
                            </small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">模板参数1</label>
                        <div class="am-u-sm-9">
                            <div class="flex-center">
                                <input class="text-center w-25" type="text" value="标题" disabled>
                                <input type="text" v-model="item.param1" placeholder="请输入模板参数1">
                            </div>
                            <small style="font-weight: bold;" v-pre>
                                参数填写举例：{{ thing10.DATA }} 为模版其中一个参数，只复制其中的 thing10 ( .DATA前面 )
                            </small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">模板参数2</label>
                        <div class="am-u-sm-9">
                            <div class="flex-center">
                                <input class="text-center w-25" type="text" value="类型" disabled>
                                <input type="text" v-model="item.param2" placeholder="请输入模板参数2">
                            </div>
                            <small style="font-weight: bold;" v-pre>
                                参数填写举例：{{ thing11.DATA }} 为模版其中一个参数，只复制其中的 thing11 ( .DATA前面 )
                            </small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">模板参数3</label>
                        <div class="am-u-sm-9">
                            <div class="flex-center">
                                <input class="text-center w-25" type="text" value="内容" disabled>
                                <input type="text" v-model="item.param3" placeholder="请输入模板参数3">
                            </div>
                            <small style="font-weight: bold;" v-pre>
                                参数填写举例：{{ thing12.DATA }} 为模版其中一个参数，只复制其中的 thing12 ( .DATA前面 )
                            </small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">模板参数4</label>
                        <div class="am-u-sm-9">
                            <div class="flex-center">
                                <input class="text-center w-25" type="text" value="备注" disabled>
                                <input type="text" v-model="item.param4" placeholder="请输入模板参数4">
                            </div>
                            <small style="font-weight: bold;" v-pre>
                                参数填写举例：{{ phrase13.DATA }} 为模版其中一个参数，只复制其中的 phrase13 ( .DATA前面 )
                            </small>
                        </div>
                    </div>
                    <div class="am-form-group" style="display: flex;justify-content: center;margin: 50px 0 30px 0;">
                        <button type="button" class="am-btn am-btn-primary" @click="holdSave">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>
    new Vue({
        el: '#app',
        data() {
            return {
                item: {}
            }
        }, created() {
            this.item = JSON.parse('{$list|trim|str_replace=PHP_EOL,\'\',###}');
            if (this.item.type === undefined) {
                this.item.type = 0;
            }
        },
        methods: {
            holdSave() {
                var setData = {};
                setData['YL0001'] = $.trim(this.item.YL0001);
                setData['type'] = $.trim(this.item.type);
                setData['param1'] = $.trim(this.item.param1);
                setData['param2'] = $.trim(this.item.param2);
                setData['param3'] = $.trim(this.item.param3);
                setData['param4'] = $.trim(this.item.param4);
                $.post("{:url('people/mention')}", {'item': setData}, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }, 'json');
            }
        }
    });
</script>
{/block}