{extend name="/base"/}
{block name="main"}
<style>.span-ranking{background:#477a98;border-radius:3px;padding:6px 10px;font-size:12px;cursor:pointer;color:#f1f4f5;margin-left:5px;}img{max-width: 100px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 失物招领回复列表
        </div>
        <div class="tpl-portlet-input tpl-fz-ml" style="display: flex;align-items: center;">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="hazy_name" value="{$hazy_name}" placeholder="搜索回复...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main">
                        <thead>
                        <tr>
                            <th class="text-center" width="15%">用户信息</th>
                            <th class="text-center" width="10%">物品名称</th>
                            <th class="text-center" width="25%">回复内容</th>
                            <th class="text-center" width="14%">回复时间</th>
                            <th class="text-center" width="8%">是否隐私回复</th>
                            <th class="text-center" width="10%">审核状态</th>
                            <th class="text-center" width="8%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle text-center">
                                {if $vo.user.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user.user_wechat_open_id}&page=1" title="{$vo.user.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user.user_nick_name|emoji_decode}">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td class="am-text-middle text-center">
                                <a href="{:url('people/lost_found')}&fid={$vo.li_id}" target="_self">
                                    {$vo.li_name|emoji_decode}
                                </a>
                            </td>
                            <td class="am-text-middle text-center">
                                <div style="display: flex;flex-direction: column;justify-content: center;align-items: center;">
                                    {$vo.content|emoji_decode|$expressionHtml}
                                </div>
                            </td>
                            <td class="am-text-middle text-center">
                                {:date('Y-m-d H:i:s',$vo.reply_time)}
                            </td>
                            <td class="am-text-middle text-center">
                                {if $vo.is_show}是{else}否{/if}
                            </td>
                            <td class="am-text-middle text-center">
                                {switch $vo.audit_status}
                                {case 0}
                                <span style="color: orange;">待审核</span>
                                {/case}
                                {case 1}
                                <span style="color: green;">已通过</span>
                                {/case}
                                {case 2}
                                <span style="color: red;">已拒绝</span>
                                {/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle text-center">
                                <div class="am-btn-group am-btn-group-sm" style="display: flex;justify-content: center;margin-top: -5px;">
                                    <button class="am-btn" style="padding: 0.5em 0.5em; color:#000;height: 30px;border-left:1px solid #9c9c9c;border-top:1px solid #9c9c9c;border-bottom:1px solid #9c9c9c;">
                                        功能列表
                                    </button>
                                    <div class="am-dropdown" data-am-dropdown style="height: 30px;line-height: 0;border-right:1px solid #9c9c9c;border-top:1px solid #9c9c9c;border-bottom:1px solid #9c9c9c;">
                                        <button class="am-btn am-dropdown-toggle" data-am-dropdown-toggle style="padding: 0.2em 0.5em;">
                                            <span class="am-icon-caret-down"></span>
                                        </button>
                                        <ul class="am-dropdown-content" style="width: 120px;min-width: initial;">
                                            {if $vo.audit_status==0}
                                            <li style="display: flex;justify-content: center;">
                                                <a href="javascript:void(0);" onclick="auditCorrect('{$vo.id}',1)" style="padding: 5px;color: #000;">
                                                    审核通过
                                                </a>
                                            </li>
                                            <li style="display: flex;justify-content: center;">
                                                <a href="javascript:void(0);" onclick="auditCorrect('{$vo.id}',2)" style="padding: 5px;color: #000;">
                                                    审核拒绝
                                                </a>
                                            </li>
                                            {/if}
                                            <li style="display: flex;justify-content: center;">
                                                <a href="javascript:void(0);" onclick="delCorrect('{$vo.id}')" style="padding: 5px;color: #000;">
                                                    删除回复
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>


    var auditCorrect = function (fid, process) {
        var twoCheck = function (fid, process, reaValue) {
            $.ajax({
                type: "post",
                url: "{:url('people/trial_lost_reply')}",
                data: {'fid': fid, 'process': process, 'inject': reaValue},
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }
            });
        }
        switch (process) {
            case 1:
                layer.confirm('您确定要审核通过这条回复吗？', {
                    btn: ['确定', '取消'], title: '提示'
                }, function () {
                    twoCheck(fid, process);
                }, function (index) {
                    layer.close(index);
                });
                break;
            case 2:
                layer.prompt({
                    title: '请输入用户未通过审核的原因：',
                    formType: 2,
                    area: ['300px', '100px'],
                    btn: ['确定', '取消'],
                }, function (reaValue, index) {
                    if (reaValue.trim() === '') {
                        return false;
                    }
                    twoCheck(fid, process, reaValue);
                    layer.close(index);
                });
                break;
        }
    }

    var delCorrect = function (fid) {
        layer.confirm('您确定要删除这条数据吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('people/del_lost_reply')}", {'fid': fid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

    var fuzzy = function () {
        var hazy_name = $.trim($('#hazy_name').val());
        if (hazy_name) {
            location.href = "{:url('people/lost_reply')}&hazy_name=" + hazy_name + "&page={$page}";
        } else {
            location.href = "{:url('people/lost_reply')}&page={$page}";
        }
    }
</script>
{/block}