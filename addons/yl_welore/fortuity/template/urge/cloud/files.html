{extend name="/base"/}
{block name="main"}
<style>.span-ranking{background:#477a98;border-radius:3px;padding:6px 10px;font-size:12px;cursor:pointer;color:#f1f4f5;margin-left:5px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 文件列表
        </div>
        <div class="tpl-portlet-input tpl-fz-ml" style="display: flex;align-items: center;">
            <div style="margin-right: 20px;">
                 <span style="font-size:14px;font-weight: bold;">
                    已用空间：{:$setupSize($ncSum)}
                </span>
            </div>
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="hazy_name" value="{$hazy_name}" placeholder="搜索文件MD5...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main">
                        <thead>
                        <tr>
                            <th class="text-center" width="25%">文件MD5</th>
                            <th class="text-center" width="7.5%">文件类型</th>
                            <th class="text-center" width="7.5%">文件大小</th>
                            <th class="text-center" width="15%">上传用户</th>
                            <th class="text-center" width="10%">上传来源 ( IP )</th>
                            <th class="text-center" width="15%">上传时间</th>
                            <th class="text-center" width="10%">文件状态</th>
                            <th class="text-center" width="10%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle text-center">
                                <span title="点击预览文件">
                                    <a href="{$vo.file_address}" target="_blank">
                                        {$vo.file_md5}
                                    </a>
                                </span>
                            </td>
                            <td class="am-text-middle text-center">
                                {$vo.file_suffix}
                            </td>
                            <td class="am-text-middle text-center">
                                {:$setupSize($vo.file_size)}
                            </td>
                            <td class="am-text-middle text-center">
                                {if $vo.user.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user.user_wechat_open_id}&page=1" title="{$vo.user.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&openid={$vo.user.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user.user_nick_name|emoji_decode}">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td class="am-text-middle text-center">
                                {$vo.up_user_ip}
                            </td>
                            <td class="am-text-middle text-center">
                                {:date('Y-m-d H:i:s',$vo.add_time)}
                            </td>
                            <td class="am-text-middle text-center">
                                {if $vo.file_status==1}
                                <span style="color:green;">正常</span>
                                {else}
                                <span style="color:red;">已屏蔽</span>
                                {/if}
                            </td>
                            <td class="am-text-middle text-center">
                                <div class="am-btn-group am-btn-group-sm" style="display: flex;justify-content: center;margin-top: -5px;">
                                    <button class="am-btn" style="padding: 0.5em 0.5em; color:#000;height: 30px;border-left:1px solid #9c9c9c;border-top:1px solid #9c9c9c;border-bottom:1px solid #9c9c9c;">
                                        功能列表
                                    </button>
                                    <div class="am-dropdown" data-am-dropdown style="height: 30px;line-height: 0;border-right:1px solid #9c9c9c;border-top:1px solid #9c9c9c;border-bottom:1px solid #9c9c9c;">
                                        <button class="am-btn am-dropdown-toggle" data-am-dropdown-toggle style="padding: 0.2em 0.5em;">
                                            <span class="am-icon-caret-down"></span>
                                        </button>
                                        <ul class="am-dropdown-content" style="width: 120px;min-width: initial;">
                                            <li style="display: flex;justify-content: center;">
                                                <a href="#" onclick="userFileSaves('{$vo.id}')" style="padding: 5px;color: #000;">
                                                    文件信息
                                                </a>
                                            </li>
                                            {if $vo.file_status == 0}
                                            <li style="display: flex;justify-content: center;">
                                                <a href="#" onclick="cloudForUpdate('{$vo.id}',1)" style="padding: 5px; color: #000;">
                                                    取消屏蔽
                                                </a>
                                            </li>
                                            {else}
                                            <li style="display: flex;justify-content: center;">
                                                <a href="#" onclick="cloudForUpdate('{$vo.id}',0)" style="padding: 5px;color: #000;">
                                                    屏蔽文件
                                                </a>
                                            </li>
                                            {/if}
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var userFileSaves = function (fid) {
        layer.open({
            type: 2,
            anim: 2,
            title: false,
            area: ['900px', '550px'],
            scrollbar: false,
            closeBtn: true,
            shadeClose: true,
            content: ["{:url('cloud/fileSaves')}&fid=" + fid, 'no'],
        });
    }

    var cloudForUpdate = function (fid, status) {
        var title = '您确定要';
        switch (status) {
            case 0:
                title += '屏蔽';
                break;
            case 1:
                title += '取消屏蔽';
                break;
        }
        title += '这条数据吗？';
        layer.confirm(title, {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('cloud/fileUpdateStatus')}", {'fid': fid, 'status': status}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

    var fuzzy = function () {
        var hazy_name = $.trim($('#hazy_name').val());
        if (hazy_name) {
            location.href = "{:url('cloud/files')}&hazy_name=" + hazy_name + "&page={$page}";
        } else {
            location.href = "{:url('cloud/files')}&page={$page}";
        }
    }
</script>
{/block}