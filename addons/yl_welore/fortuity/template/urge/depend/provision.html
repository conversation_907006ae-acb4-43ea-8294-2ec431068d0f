{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:86px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}.resth button{margin-right:5px;}.cust-btn{font-size:14px;padding:5px 10px;}.cust-btn-one{margin-left:-20px;}.cust-btn-activate{border-bottom:solid 2px cornflowerblue;}.cust-aver-img{width:82px;height:82px;position:absolute;top:11px;border:1px solid #cccccc;border-radius:3px;}.cust-noble-img{background-image:url('static/disappear/icon_coupon.png');background-size:40px;background-repeat:no-repeat;width:82px;height:82px;position:absolute;top:11px;border:1px solid #cccccc;border-radius:3px;}.customize-span{padding:3px 10px;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-th-list"></span> 认证表单
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="turtle('all');"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索表单名称...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-6 am-u-md-7">
                <div class="am-btn-toolbar" style="margin-top: -10px;">
                    <div class="am-btn-group am-btn-group-xs" style="margin-left:-2px;">
                        <span class="customize-span" onclick="saloof();">
                            <span class="am-icon-adn"></span> 新增认证表单
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form" style="overflow-x:auto;">
                    <table class="am-table am-table-striped am-table-hover table-main">
                        <thead>
                        <tr>
                            <th class="no-wrap" width="14.28%">排序</th>
                            <th class="no-wrap" width="14.28%">表单名称</th>
                            <th class="no-wrap" width="14.28%">认证图标</th>
                            <th class="no-wrap" width="14.28%">赠送会员天数</th>
                            <th class="no-wrap" width="14.28%">表单状态</th>
                            <th class="no-wrap" width="14.28%">创建时间</th>
                            <th class="no-wrap" width="14.28%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="no-wrap">
                                <input type="text" class="scfixed" id="sfixed-{$vo.id}" value="{$vo.scores}" data-score="{$vo.scores}" style="width: 50px;margin-top: 24px;" onblur="supre('{$vo.id}','#sfixed-{$vo.id}');">
                            </td>
                            <td class="no-wrap">
                                <span title="{$vo.at_name}">
                                    {$vo.at_name}
                                </span>
                            </td>
                            <td class="no-wrap">
                                <a href="{$vo.at_icon}" target="_blank">
                                    <img src="{$vo.at_icon}" onerror="this.src='static/disappear/default.png'" style="width: 50px;height:50px;"/>
                                </a>
                            </td>
                            <td class="no-wrap">
                                <span title="{$vo.handsel_day}天">
                                    {$vo.handsel_day}天
                                </span>
                            </td>
                            <td class="no-wrap">
                                {if $vo.status == 0}
                                <span class="tpl-badge-danger" style="color:white;padding:5px;border-radius:3px;">关闭认证</span>
                                {elseif $vo.status == 1}
                                <span  class="tpl-badge-success" style="color:white;padding:5px;border-radius:3px;">正常</span>
                                {/if}
                            </td>
                            <td class="no-wrap">
                                {:date('Y-m-d H:i:s',$vo.create_time)}
                            </td>
                            <td class="no-wrap">
                                <button type="button" class="am-btn am-btn-default am-btn-xs am-text-secondary" onclick="unute('{$vo.id}');">
                                    <span class="am-icon-pencil-square-o"></span>编辑
                                </button>
                                <button type="button" class="am-btn am-btn-default am-btn-xs am-text-danger" onclick="provisionDel('{$vo.id}','{$vo.at_name}');">
                                    <span class="am-icon-trash-o"></span>删除
                                </button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                </div>
                <div class="am-u-sm-12 no-wrap" style="text-align:center;">
                    {$list->render()}
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>


    function exalter(asyId, dalue) {
        var straw = {};
        $.ajax({
            type: "post",
            url: "{:url('depend/provisionSort')}",
            data: {
                asyId: asyId,
                dalue: dalue
            },
            async: false,
            success: function (data) {
                straw = data;
            }
        });
        return straw;
    }

    function supre(asyId, domId) {
        var dalue = $(domId).val();
        var daioe = $(domId).attr('data-score');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(domId).val(daioe);
            return false;
        }
        if (dalue != daioe) {
            var repair = exalter(asyId, dalue);
            if (repair.code > 0) {
                layer.msg(repair.msg, {icon: 1, time: 800});
                $(domId).attr('data-score', dalue);
            } else {
                layer.msg(repair.msg, {icon: 5, time: 1600});
            }
        }
    }

    function saloof() {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('depend/muProvision')}");
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }

    function unute(usid) {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('depend/muProvision')}&acid=" + usid);
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }

    var lock = false;
    function provisionDel(mid, vname) {
        if (!lock) {
            lock = true;
            var shint = '您确定要 <span style="color: red">删除</span> <span style="color: blue;">' + vname + '</span> 吗？';
            layer.confirm(shint, {
                btn: ['确定', '取消'], 'title': '删除提示'
            }, function () {
                $.post("{:url('depend/delProvision')}", {'ecid': mid},
                    function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                lock = false;
                            });
                        }
                    }, 'json');
            }, function () {
                lock = false;
            });
        }
    }


    function turtle() {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('depend/provision')}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('depend/provision')}&page={$page}";
        }
    }

</script>
{/block}