<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <title>表单详情</title>
    <link rel="stylesheet" href="assets/css/amazeui.min.css"/>
</head>
<body>
<table class="am-table am-table-bordered">
    <tbody>
    {volist name="list.postback_data" id="vo"}
    <tr>
        <td width="30%" style="text-align: right">
            {$vo.text}
        </td>
        {if $vo.dataType == 'text' || $vo.dataType == 'textarea' || $vo.dataType == 'select' || $vo.dataType == 'radio'}
        <td width="70%">
            {$vo.value}
        </td>
        {/if}
        {if $vo.dataType == 'checkbox'}
        <td width="70%">
            {volist name="$vo.value" id="val"}
            {$val}
            {/volist}
        </td>
        {/if}
        {if $vo.dataType == 'image'}
        <td width="70%">
            {volist name="$vo.value" id="src"}
            <a href="{$src}" target="_blank">
                <img src="{$src}" style="width:100px;height: auto">
            </a>
            {/volist}
        </td>
        {/if}
    </tr>
    {/volist}
    <tr>
        <td width="30%" style="text-align: right">审核状态</td>
        <td width="70%">
            {switch $list.adopt_status}{case 0}待审核{/case}{case 1}已通过{/case}{case 2}已拒绝{/case}{/switch}
        </td>
    </tr>
    <tr>
        <td width="30%" style="text-align: right">审核时间</td>
        <td width="70%">
            {if $list.refuse_time}{:date('Y-m-d H:i:s',$list.refuse_time)}{else}无{/if}
        </td>
    </tr>
    <tr>
        <td width="30%" style="text-align: right">
            {if $list.adopt_status == 2} 拒绝原因 {else} 信息备注 {/if}
        </td>
        <td width="70%">
            {if $list.ut_inject}{$list.ut_inject}{else}无{/if}
        </td>
    </tr>
    </tbody>
</table>
</body>
</html>