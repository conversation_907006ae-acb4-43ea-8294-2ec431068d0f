{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:86px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}.resth button{margin-right:5px;}.cust-btn{font-size:14px;padding:5px 10px;}.cust-btn-one{margin-left:-20px;}.cust-btn-activate{border-bottom:solid 2px cornflowerblue;}.cust-aver-img{width:82px;height:82px;position:absolute;top:11px;border:1px solid #cccccc;border-radius:3px;}.cust-noble-img{background-image:url('static/disappear/icon_coupon.png');background-size:40px;background-repeat:no-repeat;width:82px;height:82px;position:absolute;top:11px;border:1px solid #cccccc;border-radius:3px;}.customize-span{padding:3px 10px;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-bar-chart-o"></span> 认证列表
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="turtle('all');"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-6 am-u-md-7">
                <div class="am-btn-toolbar" style="margin: 10px 0 5px 20px;">
                    <div class="am-btn-group am-btn-group-xs resth no-wrap">
                        <a href="{:url('depend/acquire')}&egon=0" class="cust-btn cust-btn-one {if $egon==0}cust-btn-activate{/if}">全部</a>
                        <a href="{:url('depend/acquire')}&egon=1" class="cust-btn {if $egon==1}cust-btn-activate{/if}">待审核</a>
                        <a href="{:url('depend/acquire')}&egon=2" class="cust-btn {if $egon==2}cust-btn-activate{/if}">已通过</a>
                        <a href="{:url('depend/acquire')}&egon=3" class="cust-btn {if $egon==3}cust-btn-activate{/if}">已拒绝</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover" style="font-size: 1.4rem;padding: .5rem;">
                        <thead>
                        <tr>
                            <th class="no-wrap" width="12%">用户头像</th>
                            <th class="no-wrap" width="12%">用户名称</th>
                            <th class="no-wrap" width="22%">openid</th>
                            <th class="no-wrap" width="14%">表单名称</th>
                            <th class="no-wrap" width="14%">提交时间</th>
                            <th class="no-wrap" width="12%">审核状态</th>
                            <th class="no-wrap" width="14%" style="text-align: center">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="no-wrap">
                                <img src="{$vo.user_head_sculpture}" style="width:70px;height:70px;border-radius:50%;">
                            </td>
                            <td class="no-wrap">
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td class="no-wrap">
                                {if $vo.user_wechat_open_id}
                                {$vo.user_wechat_open_id}
                                {else}
                                ( 虚拟用户 )
                                {/if}
                            </td>
                            <td class="no-wrap">
                                <a href="{:url('depend/provision')}&hazy_name={$vo.at_name}&page=1" target="_blank">
                                    {$vo.at_name}
                                </a>
                            </td>
                            <td class="no-wrap">
                                {:date('Y-m-d H:i:s',$vo.refer_time)}
                            </td>
                            <td class="no-wrap">
                                {if $vo.adopt_status == 0}
                                <span class="am-text-warning">待审核</span>
                                {elseif $vo.adopt_status == 1}
                                <span class="am-text-success">已通过</span>
                                {elseif $vo.adopt_status == 2}
                                <span class="am-text-secondary">已拒绝</span>
                                {/if}
                            </td>
                            <td class="no-wrap am-text-middle">
                                <div class="am-btn-group am-btn-group-sm" style="display: flex;justify-content: center;margin-top: -5px;">
                                    <button class="am-btn" style="color:#000;height: 39px;border-left:1px solid #9c9c9c;border-top:1px solid #9c9c9c;border-bottom:1px solid #9c9c9c;">
                                        功能列表
                                    </button>
                                    <div class="am-dropdown" data-am-dropdown style="margin-top: 8px;line-height: 0;border-right:1px solid #9c9c9c;border-top:1px solid #9c9c9c;border-bottom:1px solid #9c9c9c;">
                                        <button class="am-btn am-dropdown-toggle" data-am-dropdown-toggle>
                                            <span class="am-icon-caret-down"></span>
                                        </button>
                                        <ul class="am-dropdown-content">
                                            <li>
                                                <a href="#" onclick="acquireInfo('{$vo.id}')" style="color:#000;">
                                                    查看详情
                                                </a>
                                            </li>
                                            {if $vo.adopt_status == 0}
                                            <li>
                                                <a href="#" onclick="reviewProve('{$vo.id}',1)" style="color:#000;">
                                                    审核通过
                                                </a>
                                            </li>
                                            <li>
                                                <a href="#" onclick="reviewProve('{$vo.id}',2)" style="color:#000;">
                                                    审核拒绝
                                                </a>
                                            </li>
                                            {/if}
                                            <li>
                                                <a href="#" onclick="reviewDel('{$vo.id}','{$vo.user_nick_name|emoji_decode}')" style="color:#000;">
                                                    删除认证
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                </div>
                <div class="am-u-sm-12 no-wrap" style="text-align:center;">
                    {$list->render()}
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var acquireInfo = function (acid) {
        layer.open({
            type: 2,
            title: false,
            shadeClose: true,
            shade: 0.8,
            resize: false,
            scrollbar: false,
            area: ['600px', '70%'],
            content: "{:url('depend/acquireInfo')}&acid=" + acid
        });
    }

    var reviewProve = function (acid, process) {
        var prompt = [];
        prompt['title'] = '请输入用户认证成功后的备注信息：';
        prompt['area'] = ['500px', '80px'];
        if (process === 2) {
            prompt['title'] = '请输入用户未通过审核的原因：';
            prompt['area'] = ['500px', '150px'];
        }
        layer.prompt({
            title: prompt['title'],
            formType: 2,
            area: prompt['area'],
            btn: ['确定', '取消'],
        }, function (reaValue, index) {
            if (reaValue.trim() === '') {
                return false;
            }
            $.ajax({
                type: "post",
                url: "{:url('depend/trialAcquire')}",
                data: {acid: acid, process: process, inject: reaValue},
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }
            });
            layer.close(index);
        });
    }

    var lock = false;
    var reviewDel = function (mid, vname) {
        if (!lock) {
            lock = true;
            var shint = '您确定要 <span style="color: red">删除</span> 用户 <span style="color: blue;">' + vname + '</span> 的认证数据吗？';
            layer.confirm(shint, {
                btn: ['确定', '取消'], 'title': '删除提示 ( 不可恢复 )'
            }, function () {
                $.post("{:url('depend/acquireDel')}", {'ecid': mid},
                    function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                lock = false;
                            });
                        }
                    }, 'json');
            }, function () {
                lock = false;
            });
        }
    }

    function turtle() {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('depend/acquire')}&egon={$egon}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('depend/acquire')}&egon={$egon}&page={$page}";
        }
    }

</script>
{/block}