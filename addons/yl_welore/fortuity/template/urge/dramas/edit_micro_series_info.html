{extend name="/base"/}
{block name="main"}
<style>
    .el-input__inner {
        background: #fff !important;
    }
</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 编辑短剧信息
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧上传用户UID</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.uploadUserId" placeholder="请输入短剧上传用户UID" @input="getUserName">
                            <small> 输入 0 为管理员上传 <span style="margin-left: 10px;" v-html="tempUserName"></span> </small>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.title" placeholder="请输入短剧名称">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧海报图片</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.posterUrl" placeholder="请输入短剧海报图片地址">
                            <div style="margin-top: 5px;">
                                <img :src="item.posterUrl" onerror="this.src='static/disappear/default.png'" @click="cuonice(0)" style="width: auto;height: 150px;cursor: pointer;"/>
                                <button type="button" style="margin-left:10px;font-size: 12px;" @click="cuonice(0)">
                                    选择图片
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧类型</label>
                        <div class="am-u-sm-9">
                            <el-select class="w-100" v-model="item.type" multiple placeholder="请选择短剧类型">
                                <el-option v-for="item in typeOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
                            </el-select>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧导演名</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.director" placeholder="请输入短剧导演名">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧编剧名</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.screenwriter" placeholder="请输入短剧编剧名">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧主演名</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.leadActors" placeholder="请输入短剧主演名">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧制片国家或地区</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.productionCountry" placeholder="请输入短剧制片国家或地区">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧语言</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.language" placeholder="请输入短剧语言">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧上映日期</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.releaseDate" placeholder="请输入短剧上映日期">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧片长（分钟）</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.durationMinutes" placeholder="请输入短剧片长（分钟）">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧别名</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.alias" placeholder="请输入短剧别名">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧剧情简介</label>
                        <div class="am-u-sm-9">
                            <textarea style="resize: none;height: 150px;" v-model="item.plotSummary" placeholder="请输入短剧剧情简介"></textarea>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧总集数</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.totalEpisodes" placeholder="请输入短剧总集数">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;" v-if="item.uploadUserId != 0">
                        <label class="am-u-sm-3 am-form-label">用户版权或授权证书</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.userCopyrightImg" placeholder="请输入用户版权或授权证书">
                            <div style="margin-top: 5px;">
                                <img :src="item.userCopyrightImg" onerror="this.src='static/disappear/default.png'" @click="cuonice(1)" style="width: auto;height: 150px;cursor: pointer;"/>
                                <button type="button" style="margin-left:10px;font-size: 12px;" @click="cuonice(1)">
                                    选择图片
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">审核状态</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.status">
                                <option value="0">待审核</option>
                                <option value="1">已通过</option>
                                <option value="2">未通过</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">显示状态</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.displayStatus">
                                <option value="0">已下架</option>
                                <option value="1">正常</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <div class="am-u-sm-12" style="display: flex;justify-content: center;margin-left: 30px;">
                            <button type="button" class="am-btn am-btn-primary" @click="holdSave">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    var vm = new Vue({
        el: '#app',
        data: {
            onLock: false,
            tempUserName: '',
            typeOptions: [],
            item: {
                uploadUserId: '',
                title: '',
                posterUrl: '',
                type: [],
                director: '',
                screenwriter: '',
                leadActors: '',
                productionCountry: '',
                language: '',
                releaseDate: '',
                durationMinutes: '',
                alias: '',
                plotSummary: '',
                totalEpisodes: '',
                userCopyrightImg: '',
                status: '1',
                displayStatus: '1',
            }
        },
        created() {
            this.typeOptions = JSON.parse(decodeURIComponent(atob('{$mstList}')));
            var data = JSON.parse('{:$list}');
            this.item = Object.assign(this.item, {
                uploadUserId: parseInt(data.upload_user_id),
                title: data.title,
                posterUrl: data.poster_url,
                director: data.director,
                screenwriter: data.screenwriter,
                leadActors: data.lead_actors,
                productionCountry: data.production_country,
                language: data.language,
                releaseDate: data.release_date,
                durationMinutes: data.duration_minutes,
                alias: data.alias,
                plotSummary: data.plot_summary,
                totalEpisodes: data.total_episodes,
                userCopyrightImg: data.user_copyright_img,
                status: data.status,
                displayStatus: data.display_status,
            });
            
            var itemType = data.type;
            if (itemType.indexOf(',') !== -1) {
                this.item.type = itemType.split(',').map(Number);
            } else {
                this.item.type = [Number(itemType)];
            }
            if (this.item.uploadUserId !== 0) {
                this.getUserName();
            }
        },
        methods: {
            getUserName() {
                if (parseInt(this.item.uploadUserId) !== 0) {
                    $.post("{:url('dramas/user_id_to_user_name')}", {uid: this.item.uploadUserId}, data => {
                        if (data.code > 0) {
                            this.tempUserName = '<span>用户昵称：' + data.userName + '</span>';
                        } else {
                            this.tempUserName = '<span style="color: red;">用户不存在或UID输入错误</span>';
                        }
                    })
                } else {
                    this.tempUserName = '<span>管理员上传</span>';
                }
            },
            cuonice(index) {
                layer.open({
                    type: 2,
                    anim: 2,
                    scrollbar: true,
                    area: ['900px', '600px'],
                    title: false,
                    closeBtn: 0,
                    shadeClose: true,
                    content: ["{:url('images/dialogImages')}&gclasid=0&pictureIndex=" + index, 'no']
                });
            }, holdSave: function () {

                var setData = JSON.parse(JSON.stringify(this.item));
                setData['fid'] = '{$Request.get.fid}';
                if ($.trim(setData.uploadUserId) === '') {
                    layer.msg('请输入短剧上传用户UID', {icon: 5, time: 2200});
                    return;
                }

                if ($.trim(setData.title) === '') {
                    layer.msg('请输入短剧名称', {icon: 5, time: 2200});
                    return;
                }

                if ($.trim(setData.posterUrl) === '') {
                    layer.msg('请上传短剧海报图片', {icon: 5, time: 2200});
                    return;
                }

                if (setData.type.length === 0) {
                    layer.msg('请选择短剧类型', {icon: 5, time: 2200});
                    return;
                }

                setData.type = setData.type.join(',');

                if ($.trim(setData.director) === '') {
                    setData.director = '无';
                }

                if ($.trim(setData.screenwriter) === '') {
                    setData.screenwriter = '无';
                }

                if ($.trim(setData.leadActors) === '') {
                    setData.leadActors = '无';
                }

                if ($.trim(setData.productionCountry) === '') {
                    setData.productionCountry = '无';
                }

                if ($.trim(setData.language) === '') {
                    setData.language = '无';
                }

                if ($.trim(setData.releaseDate) === '') {
                    setData.releaseDate = '无';
                }

                if ($.trim(setData.durationMinutes) === '' || isNaN(setData.durationMinutes)) {
                    setData.durationMinutes = '0';
                }

                if ($.trim(setData.alias) === '') {
                    setData.alias = '无';
                }

                if ($.trim(setData.plotSummary) === '') {
                    setData.plotSummary = '无';
                }

                if ($.trim(setData.totalEpisodes) === '' || isNaN(setData.totalEpisodes)) {
                    setData.totalEpisodes = '无';
                }
                this.onlock = true;
                $.post("{:url('dramas/edit_micro_series_info')}", setData, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.href = "{:url('dramas/micro_series_info')}";
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2200}, function () {
                            this.onLock = false;
                        });
                    }
                }, 'json');
            }
        }
    })

    var sutake = function (eurl, type) {
        switch (type) {
            case 0:
                vm.item.posterUrl = eurl;
                break;
            case 1:
                vm.item.userCopyrightImg = eurl;
                break;
        }
        layer.closeAll();
    }
</script>
{/block}