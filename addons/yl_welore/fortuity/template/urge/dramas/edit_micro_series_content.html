{extend name="/base"/}
{block name="main"}
<style>
    .el-input__inner {
        background: #fff !important;
    }
</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 编辑短剧内容
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" value="{$msiTitle}" disabled placeholder="请输入短剧内容集数">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">内容上传用户UID</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.uploadUserId" placeholder="请输入内容上传用户UID" @input="getUserName">
                            <small> 输入 0 为管理员上传 <span style="margin-left: 10px;" v-html="tempUserName"></span> </small>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧内容集数</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.msiEpisodeNumber" placeholder="请输入短剧内容集数">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">短剧内容视频地址</label>
                        <div class="am-u-sm-9">
                            <div style="display: flex;">
                                <input type="text" v-model="item.msiEpisodeUrl" placeholder="请输入短剧集数视频地址">
                                <input id="uploadFile" type="file" accept="video/*" style="display: none" @change="uploadVideoFile">
                                <el-button type="primary" plain @click="selectUploadVideo">上传视频</el-button>
                            </div>
                            <div style="margin-top: 5px;">
                                <video id="video" :src="item.msiEpisodeUrl" controls="controls" style="width: 100%;height: 400px;"></video>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">仅允许VIP观看</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.isAllowOnlyVip">
                                <option value="0">否</option>
                                <option value="1">是</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">付费解锁类型</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.paidUnlockingType">
                                <option value="0">免费</option>
                                <option value="1">贝壳</option>
                                <option value="2">积分</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;" v-if="item.paidUnlockingType > 0">
                        <label class="am-u-sm-3 am-form-label">付费解锁价格</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.paidUnlockingPrice" placeholder="请输入付费解锁价格" oninput="gRender(this,2)">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">审核状态</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.status">
                                <option value="0">待审核</option>
                                <option value="1">已通过</option>
                                <option value="2">未通过</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">显示状态</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.displayStatus">
                                <option value="1">正常</option>
                                <option value="0">已下架</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <label class="am-u-sm-3 am-form-label">排序数值</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.sort" placeholder="请输入付费解锁价格" oninput="gRender(this)">
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0;">
                        <div class="am-u-sm-12" style="display: flex;justify-content: center;margin-left: 30px;">
                            <button type="button" class="am-btn am-btn-primary" @click="holdSave">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    var vm = new Vue({
        el: '#app',
        data: {
            onLock: false,
            tempUserName: '',
            typeOptions: [],
            item: {
                msiId: '',
                uploadUserId: '0',
                msiEpisodeNumber: '',
                msiEpisodeUrl: '',
                isAllowOnlyVip: '0',
                paidUnlockingType: '0',
                paidUnlockingPrice: '0',
                status: '1',
                displayStatus: '1',
                sort: '0'
            }
        },
        created() {
            this.item.uploadUserId = '{$list.upload_user_id}';
            this.item.msiEpisodeNumber = '{$list.msi_episode_number}';
            this.item.msiEpisodeUrl = '{$list.msi_episode_url}';
            this.item.isAllowOnlyVip = '{$list.is_allow_only_vip}';
            this.item.paidUnlockingType = '{$list.paid_unlocking_type}';
            this.item.paidUnlockingPrice = '{$list.paid_unlocking_price}';
            this.item.status = '{$list.status}';
            this.item.displayStatus = '{$list.display_status}';
            this.item.sort = '{$list.sort}';
            if (this.item.uploadUserId !== 0) {
                this.getUserName();
            }
        },
        methods: {
            getUserName() {
                if (parseInt(this.item.uploadUserId) !== 0) {
                    $.post("{:url('dramas/user_id_to_user_name')}", {uid: this.item.uploadUserId}, data => {
                        if (data.code > 0) {
                            this.tempUserName = '<span>用户昵称：' + data.userName + '</span>';
                        } else {
                            this.tempUserName = '<span style="color: red;">用户不存在或UID输入错误</span>';
                        }
                    })
                } else {
                    this.tempUserName = '<span>管理员上传</span>';
                }
            },
            selectUploadVideo: function () {
                $('#uploadFile').click();
            },
            uploadVideoFile: function (e) {
                layer.load();
                var formData = new FormData();
                formData.append('sngpic', e.target.files[0]);
                $.ajax({
                    type: "post",
                    url: "{:url('upload/operate')}",
                    async: false,
                    data: formData,
                    processData: false,
                    contentType: false,
                    dataType: 'json',
                    success: function (data) {
                        parent.layer.closeAll('loading');
                        if (data.status === 'success') {
                            vm.item.msiEpisodeUrl = data.url;
                            $('#uploadFile').val('');
                        } else {
                            layer.msg('上传失败，请检查上传配置，Msg：' + data.msg);
                            $('#uploadFile').val('');
                        }
                    }
                });
            },
            holdSave: function () {
                var setData = this.item;
                setData.fid = '{$Request.get.fid}';
                setData.msiId = '{$msiId}';
                if ($.trim(setData.uploadUserId) === '') {
                    layer.msg('请输入短剧上传用户UID', {icon: 5, time: 2200});
                    return;
                }
                if ($.trim(setData.msiEpisodeNumber) === '') {
                    layer.msg('请输入短剧内容集数', {icon: 5, time: 2200});
                    return;
                }
                if ($.trim(setData.msiEpisodeUrl) === '') {
                    layer.msg('请输入短剧集数视频地址', {icon: 5, time: 2200});
                    return;
                }
                if (setData.paidUnlockingType > 0 && setData.paidUnlockingPrice <= 0) {
                    layer.msg('付费解锁价格不能为0', {icon: 5, time: 2200});
                    return;
                }
                this.onlock = true;
                $.post("{:url('dramas/edit_micro_series_content')}", setData, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.href = "{:url('dramas/micro_series_content')}";
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2200}, function () {
                            this.onLock = false;
                        });
                    }
                }, 'json');
            }
        }
    });

    var gRender = function (obj, limit) {
        if (limit === 2) {
            obj.value = obj.value.replace(/[^\d.]/g, "");
            obj.value = obj.value.replace(/^\./g, "");
            obj.value = obj.value.replace(/\.{2,}/g, ".");
            obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
            obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
        } else {
            obj.value = Number((obj.value.match(/^\d+(?:\.\d{0})?/)));
        }
    }
</script>
{/block}