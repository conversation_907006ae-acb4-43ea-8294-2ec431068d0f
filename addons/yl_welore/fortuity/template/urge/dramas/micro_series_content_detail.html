<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <title>短剧视频内容详情</title>
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <style>
        img {
            max-width: 100%;
        }
        a, a:hover, a:focus {
            color: #000;
        }
        .am-table {
            text-align: center; /* 水平居中 */
        }
        .am-table td {
            vertical-align: middle; /* 垂直居中 */
        }
    </style>
</head>
<body>
<div class="am-g">
    <div class="am-form" style="min-height: 465px;">
        <table class="am-table am-table-compact am-table-bordered am-table-radius am-table-striped">
            <tbody>
            {if $list.upload_user_id != 0}
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    用户昵称
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.user.user.uvirtual == 0}
                    <a href="{:url('user/index')}&openid={$list.user.user_wechat_open_id}&page=1" title="{$list.user.user_nick_name|emoji_decode}" target="_blank">
                        {$list.user.user_nick_name|emoji_decode}
                    </a>
                    {else}
                    <a href="{:url('user/theoretic')}&hazy_name={$list.user.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$list.user.user_nick_name|emoji_decode}">
                        {$list.user.user.user_nick_name|emoji_decode}
                    </a>
                    {/if}
                </td>
            </tr>
            {/if}
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    短剧名称
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.title}{$list.title}{else}无{/if}
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    短剧内容集数
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {$list.msi_episode_number}
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    短剧内容视频
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    <video width="100%" height="300px" controls="controls">
                        <source src="{$list.msi_episode_url}">
                    </video>
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    仅允许VIP观看
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {switch $list.is_allow_only_vip}
                    {case 0}
                    否
                    {/case}
                    {case 1}
                    是
                    {/case}
                    {/switch}
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    付费解锁价格
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.paid_unlocking_type > 0}
                    {$list.paid_unlocking_price} {switch $list.paid_unlocking_type} {case 1} ( 贝壳 ) {/case} {case 2} ( 积分 ) {/case} {/switch}
                    {else}
                    免费
                    {/if}
                </td>
            </tr>
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    审核状态
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {switch $list.status}
                    {case 0}
                    <span style="color: orange;">待审核</span>
                    {/case}
                    {case 1}
                    <span style="color: green;">已通过</span>
                    {/case}
                    {case 2}
                    <span style="color: red;">已拒绝</span>
                    {/case}
                    {/switch}
                </td>
            </tr>
            {if $list.audit_status == 2}
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    拒绝原因
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {if $list.audit_reason}{$list.audit_reason}{else}无{/if}
                </td>
            </tr>
            {/if}
            <tr>
                <td style="width: 35%; padding-right: 10px;">
                    显示状态
                </td>
                <td style="width: 65%; padding: 3px 10px 0 10px;">
                    {switch $list.display_status}
                    {case 0}
                    已下架
                    {/case}
                    {case 1}
                    正常
                    {/case}
                    {/switch}
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</div>
</body>
</html>
