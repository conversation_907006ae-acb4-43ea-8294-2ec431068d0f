{extend name="/base"/}
{block name="main"}
<style>.am-modal-hd .am-close{top:-5px;right:-3px;}.w-e-menu{font-size:12px}.w-e-text,.w-e-text-container{height:500px !important}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-cog"></span> 提现设置
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">

                    <div class="am-form-group">
                        <div class="am-u-sm-10 am-u-sm-push-2" style="font-size: 14px;color:#3294bb;text-align: center;padding: 20px;background: #f3f7ed;">
                            微信支付 零钱提现功能 注意事项：使用微信 企业付款到零钱 功能需要上传双向证书。<br>
                            企业付款到零钱功能介绍：
                            <a href="https://pay.weixin.qq.com/wiki/doc/api/tools/mch_pay.php" target="_blank" style="color: #93a2a9;">
                                https://pay.weixin.qq.com/wiki/doc/api/tools/mch_pay.php
                            </a>
                            <br>
                            微信商户平台(
                            <a href="https://pay.weixin.qq.com" target="_blank" style="color: #93a2a9;">
                                pay.weixin.qq.com
                            </a>) -> 账户中心 -> 账户设置 -> API安全 -> 证书下载。<br>
                            只需把 apiclient_cert.pem 和 apiclient_key.pem 这两个证书里面的内容复制粘贴到
                            <span style="color: #6d6314;">小程序管理/小程序设置</span> 里即可。<br>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-4 am-form-label" style="font-size:15px;">是否开启提现功能</label>
                        <div class="am-u-sm-1 am-u-end">
                            <div class="tpl-switch">
                                <input id="openWithdrawals" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $list.open_withdrawals==1}checked{/if}>
                                <div class="tpl-switch-btn-view">
                                    <div></div>
                                </div>
                            </div>
                        </div>
                        <label class="am-u-sm-2 am-form-label" style="font-size:15px;">是否开启线下转账</label>
                        <div class="am-u-sm-1 am-u-end">
                            <div class="tpl-switch">
                                <input id="openOfflinePayment" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $list.open_offline_payment==1}checked{/if}>
                                <div class="tpl-switch-btn-view">
                                    <div></div>
                                </div>
                            </div>
                        </div>
                        <label class="am-u-sm-2 am-form-label" style="font-size:15px;">是否开启提现自动审核</label>
                        <div class="am-u-sm-1 am-u-end">
                            <div class="tpl-switch">
                                <input id="autoReviewPayment" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn" {if $list.auto_review_payment==1}checked{/if}>
                                <div class="tpl-switch-btn-view">
                                    <div></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0px;">
                        <label class="am-u-sm-3 am-form-label">最低提现金额</label>
                        <div class="am-u-sm-8">
                            <input id="lowestMoney" type="number" value="{$list.lowest_money}" placeholder="请输入最低提现金额">
                        </div>
                        <label style="color: #999;font-weight: normal;font-size: 16px;line-height: 35px;width: auto;">元</label>
                    </div>
                    <div class="am-form-group" style="margin: 40px 0px;">
                        <label class="am-u-sm-3 am-form-label">提现费率</label>
                        <div class="am-u-sm-8">
                            <input id="paymentTariff" type="number" value="{$list.payment_tariff*100}" placeholder="请输入提现费率">
                            <small>
                                <span style="color: lightseagreen;">用户申请提现时，每笔申请提现扣除的费用，默认为 0，即提现不扣费。</span><br>
                                提现费率仅保留一位小数点，如设置成 0.67% 将会默认保存为 0.6%<br>
                                例如提现手续费为 0.6% <span style="color: red;">计算方式 [ 实际金额 = 提现金额 - ( 提现金额 * 0.6% ) ]</span><br>
                                <span style="color: #3b97d7;">注：单笔手续费小于0.1元的，按照0.1元收取。</span>
                            </small>
                        </div>
                        <label style="color: #999;font-weight: normal;font-size: 16px;line-height: 35px;width: auto;">%</label>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">提现须知</label>
                        <div class="am-u-sm-8 am-u-end">
                            <div id="detail" style="min-height:600px;">{$list.notice}</div>
                            <span id="customizeGallery" style="display:none;" onclick="cuonice();"></span>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <div class="am-u-sm-8 am-u-end am-u-sm-push-3">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script type="text/javascript" src="static/wangEditor/wangEditor.min.js?v=1.0"></script>
<link rel="stylesheet" href="static/wangEditor/wangEditor-fullscreen-plugin.css?v=1.0">
<script type="text/javascript" src="static/wangEditor/wangEditor-fullscreen-plugin.js?v=1.0"></script>
<script>

    var E = window.wangEditor;
    var editor = new E('#detail');
    editor.customConfig.uploadImgServer = true;
    editor.create();
    E.fullscreen.init('#detail');

    !function () {
        $('.tpl-switch').find('.tpl-switch-btn-view').on('click', function () {
            $(this).prev('.tpl-switch-btn').prop("checked", function () {
                if ($(this).is(':checked')) {
                    return false;
                } else {
                    return true;
                }
            });
        });
    }();

    function excheck(lowestMoney, paymentTariff) {
        if (lowestMoney <= 0) {
            layer.msg('很抱歉，最低提现金额不能小于或等于 0 元');
            return false;
        }
        if (paymentTariff > 100) {
            layer.msg('很抱歉，提现费率最大为100%');
            return false;
        }
        return true;
    }

    var cuonice = function () {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    var sutake = function (eurl) {
        editor.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
    }

    function holdSave() {
        var openWithdrawals = $('#openWithdrawals').prop('checked') ? 1 : 0;
        var openOfflinePayment = $('#openOfflinePayment').prop('checked') ? 1 : 0;
        var autoReviewPayment = $('#autoReviewPayment').prop('checked') ? 1 : 0;
        var lowestMoney = Number($('#lowestMoney').val().match(/^\d+(?:\.\d{0,2})?/));
        var paymentTariff = Number($('#paymentTariff').val().match(/^\d+(?:\.\d{0,1})?/));
        var notice = $.trim(editor.txt.html());
        if (excheck(lowestMoney, paymentTariff)) {
            $.ajax({
                type: "post",
                url: "{:url('rawls/setting')}",
                data: {
                    'openWithdrawals': openWithdrawals,
                    'openOfflinePayment': openOfflinePayment,
                    'autoReviewPayment': autoReviewPayment,
                    'lowestMoney': lowestMoney,
                    'paymentTariff': paymentTariff,
                    'notice': notice
                },
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }
            });
        }
    }
</script>
{/block}