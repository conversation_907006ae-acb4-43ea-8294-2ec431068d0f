{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-server"></span> 提现详情
        </div>
        <div style="text-align: right">
            <a href="javascript:history.go(-1);" style="color: black;">
                <span style="background: #e6e6e6;padding: 6px;border-radius: 3px;line-height: 10px;font-size: 12px;">返回上一页</span>
            </a>
            {if $list.status==0}
            <button type="button" class="am-btn am-btn-default am-btn-xs" onclick="holdMinor('1');">同意提现申请</button>
            <button type="button" class="am-btn am-btn-default am-btn-xs" onclick="holdMinor('0');">拒绝提现申请</button>
            {/if}
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g am-u-md-6 am-u-md-push-3 am-u-md-end"
             style="margin-top:20px;padding-left: 50px;border: 1px solid;box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 1);">
            <div class="tpl-form-body tpl-form-line">
                <div class="am-form tpl-form-line-form">
                    <div class="am-form-group">
                        <div data-am-widget="titlebar" class="am-titlebar am-titlebar-default am-u-md-11 am-u-md-push-1"
                             style="margin-left: -45px;">
                            <h2 class="am-titlebar-title" style="margin-top: 10px;">
                                用户信息
                            </h2>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">
                            <img src="{$list.user_head_sculpture}" style="width: 100px;height: 100px;border-radius: 50%;">
                        </label>
                        <div class="am-u-sm-4" style="margin-top: 28px;">
                            <small>用户昵称 :
                                <a href="{:url('user/index')}&openid={$list.user_wechat_open_id}&page=1"
                                   title="{$list.user_nick_name|emoji_decode}"
                                   target="_blank">
                                    {$list.user_nick_name|emoji_decode}
                                </a>
                                <br>
                                用户性别：{if $list.gender==1}男{else}女{/if}
                                <br>
                                用户状态：{if $list.uats==1}<span style="color: green;">正常</span>{else}<span style="color: red;">封禁中</span>{/if}
                            </small>
                        </div>
                        <div class="am-u-sm-5" style="margin-top: 28px;">
                            <small>
                                所剩{$defaultNavigate.currency} : {$list.conch}<br>
                                所剩{$defaultNavigate.confer} : {$list.fraction}
                            </small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">提现税率 :</label>
                        <div class="am-u-sm-9" style="margin-top: 3px;">
                            <small>{$list.tariff*100}%</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">提现金额 ( 税前 ) :</label>
                        <div class="am-u-sm-9" style="margin-top: 3px;">
                            <small>{$list.display_money}</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">实际金额 ( 税后 ) :</label>
                        <div class="am-u-sm-9" style="margin-top: 3px;">
                            <small>{$list.actual_amount}</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">提现类型 :</label>
                        <div class="am-u-sm-9" style="margin-top: 3px;">
                            <small>
                                {if $list.withdraw_type==0}
                                    微信支付
                                {else}
                                    线下转账
                                {/if}
                            </small>
                        </div>
                    </div>
                    {if $list.withdraw_type==1}
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">支付宝账号 :</label>
                        <div class="am-u-sm-9" style="margin-top: 3px;">
                            <small>{$list.user_account}</small>
                        </div>
                    </div>
                    {/if}
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">提现状态 :</label>
                        <div class="am-u-sm-9" style="margin-top: 3px;">
                            <small>
                                {if $list.status==0}
                                <span style="color: darkorange;padding: 5px;border-radius: 3px;">待提现</span>
                                {elseif $list.status==1}
                                <span style="color: darkseagreen;padding: 5px;border-radius: 3px;">提现通过</span>
                                {elseif $list.status==2}
                                <span style="color: indianred;padding: 5px;border-radius: 3px;">提现拒绝</span>
                                {/if}
                            </small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">提现申请时间 :</label>
                        <div class="am-u-sm-9" style="margin-top: 3px;">
                            <small>{:date('Y-m-d H:i:s',$list.seek_time)}</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">提现审核时间 :</label>
                        <div class="am-u-sm-9" style="margin-top: 3px;">
                            <small>{if $list.verify_time}{:date('Y-m-d H:i:s',$list.verify_time)}{/if}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    function holdMinor(uentreat) {
        switch (uentreat) {
            case '0':
                layer.prompt({title: "请填写拒绝提现理由：",}, function (pass, index) {
                    if ($.trim(pass) == '') {
                        return false;
                    }
                    layer.close(index);
                    $.ajax({
                        type: "post",
                        url: "{:url('rawls/austive')}",
                        data: {"suid": "{$list.id}", "thesis": 2, "argument": pass},
                        async: false,
                        dataType: "json",
                        success: function (data) {
                            if (data.code > 0) {
                                layer.msg(data.msg, {icon: 1, time: 2600}, function () {
                                    location.reload();
                                });
                            } else {
                                layer.msg(data.msg, {icon: 5, time: 2600});
                            }
                        }
                    });
                });
                break;
            case '1':
                layer.confirm("您确定要同意该用户的提现申请吗？", {
                    btn: ['确定', '取消'], title: '系统提示'
                }, function () {
                    $.ajax({
                        type: "post",
                        url: "{:url('rawls/austive')}",
                        data: {"suid": "{$list.id}", "thesis": 1},
                        async: false,
                        dataType: "json",
                        success: function (data) {
                            if (data.code > 0) {
                                layer.msg(data.msg, {icon: 1, time: 2600}, function () {
                                    location.reload();
                                });
                            } else {
                                layer.msg(data.msg, {icon: 5, time: 2600});
                            }
                        }
                    });
                }, function (index) {
                    layer.close(index);
                });
                break;
        }
    }
</script>
{/block}