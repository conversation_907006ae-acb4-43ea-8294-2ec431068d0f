{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:86px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}.resth button{margin-right:5px;}.cust-btn{font-size:14px;padding:5px 10px;}.cust-btn-one{margin-left:-20px;}.cust-btn-activate{border-bottom:solid 2px cornflowerblue;}.cust-aver-img{width:82px;height:82px;position:absolute;top:11px;border:1px solid #cccccc;border-radius:3px;}</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-reorder"></span> 提现列表
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right" style="width: 200px;margin-left: -80px;">
                    <i class="am-icon-search" onclick="turtle('all');"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}"
                           placeholder="搜索用户名称...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div style="display: flex;justify-content: space-between;">
                    <div class="am-btn-toolbar" style="margin: 10px 0 5px 20px;">
                        <div class="am-btn-group am-btn-group-xs resth">
                            <a href="{:url('rawls/stand')}&egon=0"
                               class="cust-btn cust-btn-one {if $egon==0}cust-btn-activate{/if}">全部</a>
                            <a href="{:url('rawls/stand')}&egon=1" class="cust-btn {if $egon==1}cust-btn-activate{/if}">待提现</a>
                            <a href="{:url('rawls/stand')}&egon=2" class="cust-btn {if $egon==2}cust-btn-activate{/if}">提现通过</a>
                            <a href="{:url('rawls/stand')}&egon=3" class="cust-btn {if $egon==3}cust-btn-activate{/if}">提现拒绝</a>
                        </div>
                    </div>
                    <div style="justify-content: flex-end;cursor: pointer;">
                        <el-button type="primary" size="mini" icon="el-icon-download" onclick="exportTheFile();">
                            导出全部信息
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g" style="margin-top: 10px;">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th width="11.75%">提现用户</th>
                            <th width="11.75%">提现金额</th>
                            <th width="11.75%">实际金额</th>
                            <th width="11.75%">提现类型</th>
                            <th width="11.75%">提现申请时间</th>
                            <th width="11.75%">提现审核时间</th>
                            <th width="11.75%">提现状态</th>
                            <th width="11.75%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td>{$vo.display_money}</td>
                            <td style="position: relative;">{$vo.actual_amount}</td>
                            <td>
                                {if $vo.withdraw_type==0}
                                    微信支付
                                {else}
                                    线下转账
                                {/if}
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.seek_time)}</td>
                            <td>{if $vo.verify_time}{:date('Y-m-d H:i:s',$vo.verify_time)}{/if}</td>
                            <td>
                                {if $vo.status==0}
                                <span style="background: darkorange; color: white;padding: 5px;border-radius: 3px;">待提现</span>
                                {elseif $vo.status==1}
                                <span style="background: darkseagreen; color: white;padding: 5px;border-radius: 3px;">提现通过</span>
                                {elseif $vo.status==2}
                                <span style="background: indianred; color: white;padding: 5px;border-radius: 3px;">提现拒绝</span>
                                {/if}
                            </td>
                            <td>
                                <button type="button" class="am-btn am-btn-default am-btn-xs am-text-secondary"
                                        onclick="unute('{$vo.id}');">提现详情
                                </button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    new Vue({
        el: '#app'
    });

    function exportTheFile() {
        let fileName = '.csv';
        if ('{$egon}' === '0') {
            fileName = '全部提现列表' + fileName;
        }
        if ('{$egon}' === '1') {
            fileName = '待提现列表' + fileName;
        }
        if ('{$egon}' === '2') {
            fileName = '提现通过列表' + fileName;
        }
        if ('{$egon}' === '3') {
            fileName = '提现拒绝列表' + fileName;
        }
        let url = "{:url('rawls/export_payout_information')}&egon={$egon}";
        const a = document.createElement('a');
        a.style.display = 'none';
        a.setAttribute('target', '_blank');
        a.setAttribute('download', fileName);
        a.href = url;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    function unute(renum) {
        location.href = "{:url('rawls/austive')}&renum=" + renum;
    }

    function turtle() {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('rawls/stand')}&egon={$egon}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('rawls/stand')}&egon={$egon}&page={$page}";
        }
    }

</script>
{/block}