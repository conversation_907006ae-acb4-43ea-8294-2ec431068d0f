{extend name="/base"/}
{block name="main"}
<style>.am-form-group{margin-bottom:0;}.w-e-menu{font-size:14px;}.w-e-text,.w-e-text-container{height:650px !important;}</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 信息设置
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-12 am-u-md-12">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">自定义标题</label>
                        <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                            <input type="text" v-model="list.title">
                        </div>
                    </div>
                    <div class="am-form-group am-margin-top-lg">
                        <label class="am-u-sm-3 am-form-label">入驻按钮</label>
                        <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                            <select v-model="list.showBtn">
                                <option value="1">显示</option>
                                <option value="0">隐藏</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group am-margin-top-lg">
                        <label class="am-u-sm-3 am-form-label">按钮图标</label>
                        <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                            <img :src="list.images[0]" @click="changePicture(0,1)" style="width:150px;height:150px;cursor:pointer;border: 1px dashed #ccc;">
                            <button type="button" style="margin-left:10px;font-size: 12px;" @click="changePicture(0,1)">
                                选择图片
                            </button>
                            <small>建议选择图片大小尺寸为：150*150px 以内</small>
                        </div>
                    </div>
                    <div class="am-form-group am-margin-top-lg">
                        <label class="am-u-sm-3 am-form-label">客服二维码</label>
                        <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                            <img :src="list.images[1]" @click="changePicture(1,1)" style="width:150px;height:150px;cursor:pointer;border: 1px dashed #ccc;">
                            <button type="button" style="margin-left:10px;font-size: 12px;" @click="changePicture(1,1)">
                                选择图片
                            </button>
                            <small>建议选择图片大小尺寸为：150*150px 以内</small>
                        </div>
                    </div>

                    <div class="am-form-group" style="margin-top:40px;">
                        <label class="am-u-sm-3 am-form-label">入驻须知</label>
                        <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                            <div id="detail" style="min-height:680px;"></div>
                            <span id="customizeGallery" style="display:none;" @click="cuonice"></span>
                        </div>
                    </div>
                    <div class="am-form-group" style="display: flex;justify-content: center;margin-top: 50px;">
                        <button type="button" class="am-btn am-btn-primary" @click="holdSave">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script src="static/datetime/laydate.js"></script>
<script type="text/javascript" src="static/wangEditor/wangEditor.min.js"></script>
<link rel="stylesheet" href="static/wangEditor/wangEditor-fullscreen-plugin.css">
<script type="text/javascript" src="static/wangEditor/wangEditor-fullscreen-plugin.js"></script>
<script>

    var vm = new Vue({
        el: '#app',
        data: {
            list: {
                title: '{$list.custom_title}',
                images: ['{$list.btn_icon}', '{$list.waiter_qrcode}'],
                showBtn: '{$list.is_show_btn}',
                content: decodeURIComponent(atob('{$list.precautions}'))
            },
            E: [],
            editor: [],
        }, mounted: function () {
            this.E = window.wangEditor;
            this.editor = new this.E('#detail');
            this.editor.customConfig.uploadImgServer = true;
            this.editor.create();
            this.E.fullscreen.init('#detail');
            this.editor.txt.html(this.list.content);
        },
        methods: {
            changePicture: function (index, openPicture,picturePath) {
                switch (openPicture) {
                    case 0:
                        this.list.images[index] = picturePath;
                        this.$forceUpdate();
                        break;
                    case 1:
                        layer.open({
                            type: 2,
                            anim: 2,
                            scrollbar: true,
                            area: ['900px', '600px'],
                            title: false,
                            closeBtn: 0,
                            shadeClose: true,
                            content: ["{:url('images/dialogimages')}&gclasid=0&pictureIndex=" + index, 'no']
                        });
                        break;
                }
            },
            holdSave: function () {
                this.list.content = this.editor.txt.html();
                $.post("{:url('pluto/merchant_config')}", this.list, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }, 'json');
            }, cuonice: function () {
                layer.open({
                    type: 2,
                    anim: 2,
                    scrollbar: true,
                    area: ['900px', '600px'],
                    title: false,
                    closeBtn: 0,
                    shadeClose: true,
                    content: ["{:url('images/dialogImages')}&gclasid=0&pictureIndex=-1", 'no']
                });
            }
        }
    });

    var sutake = function (eurl, pictureIndex, type) {
        console.log(eurl, pictureIndex, type);
        switch (type) {
            case 0:
                vm.changePicture(pictureIndex, 0, eurl);
                break;
            case 1:
                vm.editor.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
                break;
        }
        layer.closeAll();
    }

</script>
{/block}