{extend name="/base"/}
{block name="main"}
<style>.customize-span,.customize-span:hover{padding:3px 10px;color:black !important;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}.am-table > thead:first-child > tr:first-child > th{text-align:center;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 商家列表
        </div>
        <div style="text-align: right;">
            <div class="am-btn-toolbar" style="padding-top: 5px;">
                <a href="{:url('pluto/new_merchant_list')}" class="customize-span">
                    <span class="am-icon-adn"></span> 新增商家
                </a>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div id="search-list" style="display: flex;flex-wrap: wrap;width: 100%;min-height: 38px;">
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">ID</label>
                <div class="search-input-inline">
                    <input type="text" name="fid" value="{$fid}" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">商家名称</label>
                <div class="search-input-inline">
                    <input type="text" name="name" value="{$name}" class="search-input">
                </div>
            </div>
            <button class="search-wide-pitch search-btn" onclick="turtle();">
                <i class="am-icon-search"></i> 搜 索
            </button>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover table-main" style="text-align:center;">
                        <thead>
                        <tr>
                            <th width="12%">ID</th>
                            <th width="12%">排序</th>
                            <th width="12%">商家图标</th>
                            <th width="12%">商家名称</th>
                            <th width="12%">商家类型</th>
                            <th width="12%">状态</th>
                            <th width="18%">创建时间</th>
                            <th width="18%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                {$vo.id}
                            </td>
                            <td class="am-text-middle">
                                <div style="width: 100%;display:flex; justify-content:center;">
                                    <input type="text" class="scfixed" id="sfixed-{$vo.id}" value="{$vo.sort}" data-score="{$vo.sort}" style="width: 50px;margin-top: 8px;" onblur="supre('{$vo.id}','#sfixed-{$vo.id}');">
                                </div>
                            </td>
                            <td class="am-text-middle">
                                <img src="{$vo.merchant_icon_carousel|athumbnail}" style="width:100px;height:100px;">
                            </td>
                            <td class="am-text-middle">
                                <span title="{$vo.merchant_name}">{$vo.merchant_name}</span>
                            </td>
                            <td class="am-text-middle">
                                <span title="{$vo.name}">{$vo.name}</span>
                            </td>
                            <td class="am-text-middle">
                                {if $vo.status==1}
                                <span style="color: green;">正常</span>
                                {else}
                                <span style="color: red;">隐藏</span>
                                {/if}
                            </td>
                            <td class="am-text-middle">{:date('Y-m-d H:i:s',$vo.create_time)}</td>
                            <td class="am-text-middle">
                                <a href="{:url('pluto/edit_merchant_list')}&layid={$vo.id}" target="_blank">
                                    <span style="color:#43675c;padding:4px 12px;background:white;border: 1px solid #ccc;margin-right: 5px;">
                                        编辑
                                    </span>
                                </a>
                                <span onclick="erase_merchant_list('{$vo.id}');">
                                    <span style="color:red;padding:4px 12px;background:white;cursor: pointer;border: 1px solid #ccc;">
                                        删除
                                    </span>
                                </span>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var supre = function (asyId, domId) {
        var dalue = $(domId).val();
        var daioe = $(domId).attr('data-score');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(domId).val(daioe);
            return false;
        }
        if (dalue != daioe) {
            $.post("{:url('pluto/merchant_list_sort')}", {asyId, dalue}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 800});
                    $(domId).attr('data-score', dalue);
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1600});
                }
            });
        }
    }

    var erase_merchant_list = function (meid) {
        layer.confirm('您确定要删除这条数据吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('pluto/del_merchant_list')}", {fid: meid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 800}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1600}, function () {
                        location.reload();
                    });
                }
            });
        }, function (index) {
            layer.close(index);
        });
    }

    function turtle() {
        var searchInput = $('#search-list .search-input');
        var n = 0;
        for (var i = 0; i < searchInput.length; i++) {
            if (searchInput[i].value.trim() !== '') {
                n++;
            }
        }
        if (n > 0) {
            location.href = `{:url('pluto/merchant_list')}&${searchInput.serialize()}&page={$page}`;
        } else {
            location.href = "{:url('pluto/merchant_list')}&page={$page}";
        }
    }

</script>
{/block}