{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 新增店员
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">

                    <div class="am-form-group" style="display: none;margin-top: 15px;">
                        <label class="am-u-sm-3 am-form-label">用户名称</label>
                        <div class="am-u-sm-9">
                            <input id="username" type="text" class="am-form-field am-radius" disabled>
                        </div>
                    </div>

                    <div class="am-form-group" style="display: none;margin-top: 15px">
                        <label class="am-u-sm-3 am-form-label">用户头像</label>
                        <div class="am-u-sm-9">
                            <img id="userhead" src=""  onerror="this.src='static/disappear/default.png'" style="width: 100px;height: 100px;">
                        </div>
                    </div>

                    <div class="am-form-group" style="margin-top: 15px;">
                        <label class="am-u-sm-3 am-form-label">openid</label>
                        <div class="am-u-sm-9">
                            <input id="openid" type="text" class="am-form-field am-radius" placeholder="请输入openid" onblur="extolled(this);">
                            <small id="hobby">[ 用户管理 -> 用户信息 -> 用户列表 -> openid ]</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">商家名称</label>
                        <div class="am-u-sm-9">
                            <select id="eilId">
                                <option value="-1">请选择</option>
                                {volist name="list" id="vo"}
                                <option value="{$vo.id}">{$vo.merchant_name}</option>
                                {/volist}
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group" style="margin-top: 15px;">
                        <label for="status" class="am-u-sm-3 am-form-label">状态</label>
                        <div class="am-u-sm-9">
                            <select id="status">
                                <option value="0">禁用</option>
                                <option value="1">正常</option>
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group" style="margin-top: 15px;">
                        <div class="am-u-sm-9 am-u-sm-push-3" style="display: flex;justify-content: center;">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>


    function extolled(obg) {
        obg.value = $.trim(obg.value);
        if (obg.value != '') {
            $.getJSON("{:url('compass/getopenid')}&virtual=1", {"openid": obg.value}, function (data) {
                if (data.name != '') {
                    $('#hobby').html('');
                    $('#username').val(data.name).parent().parent().show();
                    $('#userhead').attr('src',data.userhead).parent().parent().show();
                } else {
                    $('#username').val('').parent().parent().hide();
                    $('#userhead').attr('src','static/disappear/default.png').parent().parent().hide();
                    var gropt = "<span id='sgropt' style='color: red;'>openid填写错误</span>";
                    $('#hobby').html(gropt);
                    obg.value = '';
                }
            });
        }
    }


    var slock = false;

    function holdSave() {
        if (!slock) {
            var eilId = $.trim($('#eilId').val());
            var openid = $.trim($('#openid').val());
            var status = $.trim($('#status').val());

            if (openid === '') {
                layer.msg('openId不能为空');
                return false;
            }

            slock = true;
            $.ajax({
                type: "post",
                url: "{:url('pluto/new_merchant_sales_clerk')}",
                data: {
                    'eilId': eilId,
                    'openId': openid,
                    'status': status
                },
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.href = "{:url('pluto/merchant_sales_clerk')}";
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                            slock = false;
                        });
                    }
                }
            });
        }
    }
</script>
{/block}