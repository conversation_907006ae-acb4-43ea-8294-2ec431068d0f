{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:45px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 订单列表
        </div>
    </div>
    <div class="tpl-block">
        <div id="search-list" style="display: flex;flex-wrap: wrap;width: 100%;min-height: 38px;">
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">ID</label>
                <div class="search-input-inline">
                    <input type="text" name="fid" value="{$fid}" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">订单号</label>
                <div class="search-input-inline">
                    <input type="text" name="orderNumber" value="{$orderNumber}" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">商家ID</label>
                <div class="search-input-inline">
                    <input type="text" name="eid" value="{$eid}" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">商品兑换码</label>
                <div class="search-input-inline">
                    <input type="text" name="code" value="{$code}" class="search-input">
                </div>
            </div>
            <button class="search-wide-pitch search-btn" onclick="turtle();">
                <i class="am-icon-search"></i> 搜 索
            </button>
        </div>
        <div class="am-g" style="margin-top: 15px;">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th width="10%">ID</th>
                            <th width="10%">用户昵称</th>
                            <th width="16%">订单名称</th>
                            <th width="10%">商家名称</th>
                            <th width="12%">商城订单号</th>
                            <th width="12%">商品兑换码</th>
                            <th width="10%">订单状态</th>
                            <th width="10%">使用状态</th>
                            <th width="10%">购买时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                {$vo.id}
                            </td>
                            <td>
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td>
                                <a href="{:url('marketing/shop')}&egon=0&sid={$vo.product_id}" target="_blank">
                                {$vo.product_name}
                                </a>
                            </td>
                            <td>
                                <a href="{:url('pluto/merchant_list')}&fid={$vo.eil_id}" target="_blank">
                                    {$vo.merchant_name}
                                </a>
                            </td>
                            <td>
                                <a href="{:url('marketing/sorder')}&egon=0&orderNumber={$vo.order_number}" target="_blank">
                                    {$vo.order_number}
                                </a>
                            </td>
                            <td>{$vo.redemption_code}</td>
                            <td>
                                {if $vo.order_status}
                                    <span style="color: green;">正常</span>
                                {else}
                                    <span style="color: red;">取消</span>
                                {/if}
                            </td>
                            <td>
                                {if $vo.use_status}
                                <span style="color: gray;">已使用</span>
                                {else}
                                <span style="color: coral;">未使用</span>
                                {/if}
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.create_time)}</td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    function turtle() {
        var searchInput = $('#search-list .search-input');
        var n = 0;
        for (var i = 0; i < searchInput.length; i++) {
            if (searchInput[i].value.trim() !== '') {
                n++;
            }
        }
        if (n > 0) {
            location.href = `{:url('pluto/merchant_commodity_order')}&${searchInput.serialize()}&page={$page}`;
        } else {
            location.href = "{:url('pluto/merchant_commodity_order')}&page={$page}";
        }
    }

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
    }

    var isLock = false;
    var sendGifts = function () {
        if (!isLock) {
            isLock = true;
            var eid = $('.am-modal-dialog [name=\'eid\']').val();
            var pid = $('.am-modal-dialog [name=\'pid\']').val();
            $.post("{:url('pluto/new_merchant_commodity')}", {'eid': eid, 'pid': pid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        isLock = false;
                    });
                }
            }, 'json');
        }
    }

    function symbolDel(mid) {
        layer.confirm('您确定要删除当前订单吗', {
            btn: ['确定', '取消']
        }, function () {
            $.post("{:url('pluto/del_merchant_commodity')}", {'fid': mid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

</script>
{/block}