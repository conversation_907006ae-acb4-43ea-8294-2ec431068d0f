{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:45px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 店员列表
        </div>
        <div style="text-align: right;">
            <div class="am-btn-toolbar" style="padding-top: 5px;">
                <a href="{:url('pluto/new_merchant_sales_clerk')}" class="customize-span">
                    <span class="am-icon-adn"></span> 新增店员
                </a>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-top: 15px;">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th width="14.28%">ID</th>
                            <th width="14.28%">店员头像</th>
                            <th width="14.28%">店员名称</th>
                            <th width="14.28%">所属店铺</th>
                            <th width="14.28%">状态</th>
                            <th width="14.28%">创建时间</th>
                            <th width="14.28%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                {$vo.id}
                            </td>
                            <td>
                                <img src="{$vo.user_head_sculpture}" onerror="this.src='static/disappear/default.png'" style="width: 50px;height:50px;border-radius: 50%;"/>
                            </td>
                            <td>
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td>{$vo.merchant_name}</td>
                            <td>
                                {if $vo.status == 0}
                                <span style="color: red;cursor: pointer;" title="点击更改状态" onclick="outward('1','{$vo.id}');">禁用</span>
                                {else}
                                <span style="color: lightgreen;cursor: pointer;" title="点击更改状态" onclick="outward('0','{$vo.id}');">正常</span>
                                {/if}
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.create_time)}</td>
                            <td>
                                <div class="am-btn-toolbar">
                                    <div class="am-btn-group am-btn-group-xs">
                                        <button type="button" class="am-btn am-btn-default am-btn-xs am-text-danger am-hide-sm-only" onclick="symbolDel('{$vo.id}')">
                                            <span class="am-icon-trash-o"></span>
                                            删除
                                        </button>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>


    function outward(oue, usid) {
        $.ajax({
            type: "post",
            url: "{:url('pluto/update_merchant_sales_clerk_status')}",
            data: {
                'usid': usid,
                'status': oue
            },
            async: false,
            success: function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }
        });
    }

    function symbolDel(mid) {
        layer.confirm('您确定要删除当前店员吗', {
            btn: ['确定', '取消']
        }, function () {
            $.post("{:url('pluto/del_merchant_sales_clerk')}", {'fid': mid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        lock = false;
                    });
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

</script>
{/block}