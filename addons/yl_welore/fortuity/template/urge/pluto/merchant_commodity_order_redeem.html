{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:45px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 核销记录
        </div>
    </div>
    <div class="tpl-block">
        <div id="search-list" style="display: flex;flex-wrap: wrap;width: 100%;min-height: 38px;">
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">ID</label>
                <div class="search-input-inline">
                    <input type="text" name="fid" value="{$fid}" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline" >
                <label class="search-label">商城订单号</label>
                <div class="search-input-inline">
                    <input type="text" name="orderNumber" value="{$orderNumber}" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">商家商品订单号</label>
                <div class="search-input-inline">
                    <input type="text" name="pid" value="{$pid}" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline">
                <label class="search-label">商家ID</label>
                <div class="search-input-inline">
                    <input type="text" name="eid" value="{$eid}" class="search-input">
                </div>
            </div>
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">核销用户ID</label>
                <div class="search-input-inline">
                    <input type="text" name="uid" value="{$uid}" class="search-input">
                </div>
            </div>
            <button class="search-wide-pitch search-btn" onclick="turtle();">
                <i class="am-icon-search"></i> 搜 索
            </button>
        </div>
        <div class="am-g" style="margin-top: 15px;">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th width="10%">ID</th>
                            <th width="10%">商城订单号</th>
                            <th width="10%">商家名称</th>
                            <th width="16%">商品名称</th>
                            <th width="10%">核销用户昵称</th>
                            <th width="10%">核销时间</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                {$vo.id}
                            </td>
                            <td>
                                <a href="{:url('marketing/sorder')}&egon=0&orderNumber={$vo.order_number}" target="_blank">
                                    {$vo.order_number}
                                </a>
                            </td>
                            <td>
                                <a href="{:url('pluto/merchant_list')}&fid={$vo.eil_id}" target="_blank">
                                    {$vo.merchant_name}
                                </a>
                            </td>
                            <td>
                                <a href="{:url('pluto/merchant_commodity_order')}&fid={$vo.eiso_id}" target="_blank">
                                {$vo.product_name}
                                </a>
                            </td>
                            <td>
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.verify_time)}</td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel">
        <div class="am-modal-dialog" style="background:#fefefe;">
            <div class="am-modal-hd">
                <span style="font-size: 14px;position: absolute;left:12px;top:7px;">新增商品</span>
                <a id="euModalClose" href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd am-form tpl-form-line-form">
                <div class="am-form-group" style="margin-top:35px;">
                    <label class="am-u-sm-4 am-form-label" style="font-size: 15px;margin:2px 0 0 -5px;">商家ID</label>
                    <div class="am-u-sm-8">
                        <input type="number" name="eid" class="tpl-form-input" style="margin:3px 0 0 -20px;" placeholder="请输入商家编号" oninput="digitalCheck(this);">
                    </div>
                </div>
                <div class="am-form-group" style="margin-top:15px;">
                    <label class="am-u-sm-4 am-form-label" style="font-size: 15px;margin:2px 0 0 -5px;">商品ID</label>
                    <div class="am-u-sm-8">
                        <input type="number" name="pid" class="tpl-form-input" style="margin:3px 0 0 -20px;" placeholder="请输入商品编号" oninput="digitalCheck(this);">
                    </div>
                </div>
                <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top:10px;">
                    <button type="button" class="am-btn am-btn-sm" style="border:1px solid #ccc;" onclick="sendGifts();">
                        确定保存
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    function turtle() {
        var searchInput = $('#search-list .search-input');
        var n = 0;
        for (var i = 0; i < searchInput.length; i++) {
            if (searchInput[i].value.trim() !== '') {
                n++;
            }
        }
        if (n > 0) {
            location.href = `{:url('pluto/merchant_commodity_order_redeem')}&${searchInput.serialize()}&page={$page}`;
        } else {
            location.href = "{:url('pluto/merchant_commodity_order_redeem')}&page={$page}";
        }
    }

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
    }

    var isLock = false;
    var sendGifts = function () {
        if (!isLock) {
            isLock = true;
            var eid = $('.am-modal-dialog [name=\'eid\']').val();
            var pid = $('.am-modal-dialog [name=\'pid\']').val();
            $.post("{:url('pluto/new_merchant_commodity')}", {'eid': eid, 'pid': pid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        isLock = false;
                    });
                }
            }, 'json');
        }
    }


</script>
{/block}