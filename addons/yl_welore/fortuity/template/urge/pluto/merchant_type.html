{extend name="/base"/}
{block name="main"}
<style>.customize-span,.customize-span:hover{padding:3px 10px;color:black !important;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}.am-table > thead:first-child > tr:first-child > th{text-align:center;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 商家分类
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索分类名称...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-xs">
                        <a href="{:url('pluto/new_merchant_type')}" class="customize-span">
                            <span class="am-icon-adn"></span> 新增商家分类
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover table-main" style="text-align:center;">
                        <thead>
                        <tr>
                            <th width="14.28%">ID</th>
                            <th width="14.28%">排序</th>
                            <th width="14.28%">分类图标</th>
                            <th width="14.28%">分类名称</th>
                            <th width="14.28%">创建时间</th>
                            <th width="14.28%">状态</th>
                            <th width="14.28%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">{$vo.id}</td>
                            <td class="am-text-middle">
                                <div style="width: 100%;display:flex; justify-content:center;">
                                    <input type="text" class="scfixed" id="sfixed-{$vo.id}" value="{$vo.sort}" data-score="{$vo.sort}" style="width: 50px;margin-top: 8px;" onblur="supre('{$vo.id}','#sfixed-{$vo.id}');">
                                </div>
                            </td>
                            <td class="am-text-middle">
                                <img src="{$vo.icon}" style="width:70px;height:70px;">
                            </td>
                            <td class="am-text-middle">
                                <span title="{$vo.name}">{$vo.name}</span>
                            </td>
                            <td class="am-text-middle">{:date('Y-m-d H:i:s',$vo.create_time)}</td>
                            <td class="am-text-middle">
                                {if $vo.status==1}
                                <span style="color: green;">正常</span>
                                {else}
                                <span style="color: red;">隐藏</span>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                <a href="{:url('pluto/edit_merchant_type')}&layid={$vo.id}" target="_blank">
                                    <span style="color:#43675c;padding:4px 12px;background:white;border: 1px solid #ccc;margin-right: 5px;">
                                        编辑
                                    </span>
                                </a>
                                <span onclick="erase_merchant_type('{$vo.id}');">
                                    <span style="color:red;padding:4px 12px;background:white;cursor: pointer;border: 1px solid #ccc;">
                                        删除
                                    </span>
                                </span>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var supre = function (asyId, domId) {
        var dalue = $(domId).val();
        var daioe = $(domId).attr('data-score');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(domId).val(daioe);
            return false;
        }
        if (dalue != daioe) {
            $.post("{:url('pluto/merchant_type_sort')}", {asyId, dalue}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 800});
                    $(domId).attr('data-score', dalue);
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1600});
                }
            });
        }
    }

    var erase_merchant_type = function (meid) {
        layer.confirm('您确定要删除这条数据吗？<br><strong style="color:red;">此分类下的商家会一并被删除（不可恢复）</strong>', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('pluto/del_merchant_type')}", {fid: meid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 800}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1600}, function () {
                        location.reload();
                    });
                }
            });
        }, function (index) {
            layer.close(index);
        });
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('pluto/merchant_type')}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('pluto/merchant_type')}&page={$page}";
        }
    }

</script>
{/block}