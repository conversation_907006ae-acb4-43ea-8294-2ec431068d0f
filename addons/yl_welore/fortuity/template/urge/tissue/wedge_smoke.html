<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <title>抽取详情</title>
    <meta name="referrer" content="never">
    <link rel="stylesheet" href="assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/amazeui.min.css"/>
    <style>.am-table {width: 96%;margin: 3% auto;}</style>
</head>
<body>
<table class="am-table am-table-bordered">
    <thead>
    <tr>
        <th width="30%">UID</th>
        <th width="40%">用户昵称</th>
        <th width="40%">抽取时间</th>
    </tr>
    </thead>
    <tbody>
    {volist name="list" id="vo"}
    <tr>
        <td>
            {$vo.user_id}
        </td>
        <td>
            {if $vo.uvirtual == 0}
            <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                {$vo.user_nick_name|emoji_decode}
            </a>
            {else}
            <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                {$vo.user_nick_name|emoji_decode}
            </a>
            {/if}
        </td>
        <td>{:date('Y-m-d H:i:s',$vo.pull_time)}</td>
    </tr>
    {/volist}
    </tbody>
</table>
<div class="am-g text-center">
    {$list->render()}
</div>
</body>
</html>