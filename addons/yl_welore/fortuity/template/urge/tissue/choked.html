{extend name="/base"/}
{block name="main"}
<style>.am-modal-hd .am-close{top:-5px;right:-3px;}.w-e-menu{font-size:12px;}.w-e-text,.w-e-text-container{height:500px !important;}.content-wrapper{width:100%;min-height:100vh;background-color:#f5f7fa;padding:20px;margin-bottom:20px;}.tpl-portlet-components{max-width:900px;width:100%;margin:0 auto;background:#fff;padding:25px;border-radius:8px;box-shadow:0 2px 12px rgba(0,0,0,0.05);}.portlet-title{margin-bottom:20px;border-bottom:1px solid #eee;padding-bottom:12px;}.portlet-title .caption{font-size:18px;color:#333;}.portlet-title .am-icon-cog{color:#3bb4f2;}.am-form-group{position:relative;margin-bottom:20px;padding-bottom:20px;border-bottom:1px solid #f0f0f0;}.am-form-group:last-child{border-bottom:none;margin-bottom:0;padding-bottom:0;}.am-form-label{font-weight:500;color:#333;padding-top:6px;font-size:13px;}.am-form input[type="text"],.am-form input[type="number"],.am-form select{height:36px;border-radius:4px;border:1px solid #ddd;padding:0 10px;width:100%;}.radio-group{display:flex;gap:15px;margin-bottom:4px;height:36px;align-items:center;}.radio-group label{display:flex;align-items:center;cursor:pointer;font-weight:normal;color:#666;font-size:14px;line-height:36px;height:36px;}.radio-group input[type="radio"]{margin:0 4px 0 0;vertical-align:middle;}.am-form small{margin-top:4px;font-size:12px;color:#666;display:block;}.am-form small[style*="color:red"]{color:#ff5252 !important;}.image-selector{display:inline-block;margin-bottom:10px;}.image-selector img{border-radius:4px;border:1px solid #eee;transition:all 0.3s;}.image-selector button{margin-top:8px;padding:4px 12px;border:1px solid #ddd;background:#fff;border-radius:4px;cursor:pointer;transition:all 0.3s;}.image-selector button:hover{background:#f5f5f5;}#detail{border:1px solid #ddd;border-radius:4px;margin-bottom:10px;min-height:500px !important;background:#fff;}.w-e-toolbar{background:#fafafa !important;border-bottom:1px solid #eee !important;}.w-e-text-container{border-color:#eee !important;}.form-submit{display:flex;justify-content:center;padding:20px 0;}.am-btn-primary{background:#3bb4f2;border-color:#3bb4f2;padding:8px 24px;font-size:14px;border-radius:4px;transition:all 0.3s;min-width:120px;}.am-btn-primary:hover{background:#2798d8;border-color:#2798d8;}@media screen and (max-width:768px){.content-wrapper{padding:10px;}.tpl-portlet-components{padding:15px;}.am-u-sm-3,.am-u-sm-7,.am-u-sm-9{width:100%;}}.qrcode-trigger{cursor:pointer;font-size:20px;color:#3bb4f2;transition:color 0.3s;}.qrcode-trigger:hover{color:#2798d8;}</style>
<div class="content-wrapper">
    <div class="tpl-portlet-components">
        <div class="portlet-title">
            <div class="caption font-green bold">
                <span class="am-icon-cog"></span> 小纸条设置
            </div>
        </div>
        
        <div class="tpl-block">
            <div class="am-g tpl-amazeui-form">
                <div class="am-u-sm-12 am-u-md-12">
                    <div class="am-form am-form-horizontal">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">纸条二维码</label>
                            <div class="am-u-sm-9">
                                <span title="点击生成二维码" class="qrcode-trigger" onclick="showQrCode()">
                                    <span class="am-icon-qrcode"></span>
                                </span>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">自定义纸条名称</label>
                            <div class="am-u-sm-9">
                                <input type="text" id="customTitle" value="{if !$list.custom_title}小纸条{else}{$list.custom_title}{/if}">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">纸条显示位置</label>
                            <div class="am-u-sm-9">
                                <select id="bareLocation">
                                    <option value="0" {if $list.bare_location==0}selected{/if}>个人中心</option>
                                    <option value="1" {if $list.bare_location==1}selected{/if}>首页展示</option>
                                </select>
                            </div>
                        </div>
                        <div id="bareType" style="display: none">
                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">纸条展示方向</label>
                                <div class="am-u-sm-9">
                                    <select id="bareDirection">
                                        <option value="0" {if $list.bare_direction==0}selected{/if}>首页左方</option>
                                        <option value="1" {if $list.bare_direction==1}selected{/if}>首页右方</option>
                                    </select>
                                </div>
                            </div>
                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">纸条底部距离</label>
                                <div class="am-u-sm-9">
                                    <input type="text" id="directionBottom" value="{$list.direction_bottom}" oninput="grender(this);">
                                    <small>首页小纸条距离底部的距离 百分比显示(%)</small>
                                </div>
                            </div>
                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">纸条首页入口图标</label>
                                <div class="am-u-sm-9">
                                    <span class="image-selector" onclick="cuonice(this);">
                                        <img src="{$list.bare_img_url}" onerror="this.src='static/disappear/default.png'" width="80"/>
                                        <button type="button">选择图片</button>
                                        <input type="hidden" value="{$list.bare_img_url}" name="bare-img">
                                    </span>
                                    <small>建议图片宽度图片：70px（高度自适应）</small>
                                </div>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">纸条自动审核</label>
                            <div class="am-u-sm-9">
                                <div class="radio-group">
                                    <label>
                                        <input type="radio" name="autoCareful" value="1" {if $list.auto_careful==1}checked{/if}> 开启
                                    </label>
                                    <label>
                                        <input type="radio" name="autoCareful" value="0" {if $list.auto_careful==0}checked{/if}> 关闭
                                    </label>
                                </div>
                                <small style="color:red;">开启后您无需再手动审核 系统将自动通过所有用户发布的纸条</small>
                            </div>
                        </div>
                        {if $autonomy == 1}
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">投放纸条价格（男）</label>
                            <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                                <input type="number" id="throwPriceMale" value="{$list.throw_price_male}" oninput="grender(this,2);">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">投放纸条价格（女）</label>
                            <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                                <input type="text" id="throwPriceFemale" value="{$list.throw_price_female}" oninput="grender(this,2);">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">抽取纸条价格（男）</label>
                            <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                                <input type="text" id="pickPriceMale" value="{$list.pick_price_male}" oninput="grender(this,2);">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">抽取纸条价格（女）</label>
                            <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                                <input type="text" id="pickPriceFemale" value="{$list.pick_price_female}" oninput="grender(this,2);">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">纸条支付方式</label>
                            <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                                <select id="payType">
                                    <option value="0" {if $list.pay_type==0}selected{/if}>积分支付</option>
                                    <option value="1" {if $list.pay_type==1}selected{/if}>贝壳支付</option>
                                    <option value="2" {if $list.pay_type==2}selected{/if}>微信支付</option>
                                </select>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">投放纸条次数限制</label>
                            <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                                <input type="text" id="throwLimit" value="{$list.throw_limit}" oninput="grender(this);">
                                <small>单个用户每日最多投放次数 0 为不限制</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">抽取纸条次数限制</label>
                            <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                                <input type="text" id="pickLimit" value="{$list.pick_limit}" oninput="grender(this);">
                                <small>单个用户每日最多抽取次数 0 为不限制</small>
                            </div>
                        </div>
                        {/if}
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">腾讯地图key</label>
                            <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                                <input type="text" id="tenLocalKey" value="{$list.ten_local_key}">
                                <small>
                                    纸条位置转换时需要使用 申请地址：
                                    <a href="https://lbs.qq.com" target="_blank">
                                        https://lbs.qq.com/
                                    </a>
                                    ( 申请时请先创建应用，勾选应用中的 WebServiceAPI，推荐授权IP的方式 )
                                </small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">用户须知</label>
                            <div class="am-u-sm-9">
                                <div id="detail">{$list.notice}</div>
                                <span id="customizeGallery" style="display:none;" onclick="cuonice();"></span>
                                <small style="color: red;">请填写用户须知 ( 玩家纸条玩法需要遵守的规则 )</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <div class="form-submit">
                                <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script type="text/javascript" src="static/wangEditor/wangEditor.min.js?time={:time()}"></script>
<link rel="stylesheet" href="static/wangEditor/wangEditor-fullscreen-plugin.css?time={:time()}">
<script type="text/javascript" src="static/wangEditor/wangEditor-fullscreen-plugin.js?time={:time()}"></script>
<script src="assets/js/jquery.qrcode.min.js"></script>
<script>

    var E = window.wangEditor;
    var editor = new E('#detail');

    editor.customConfig.showLinkImg = false;
    editor.customConfig.uploadImgShowBase64 = false;
    editor.customConfig.uploadImgServer = false;
    
    // 点击图片菜单时的自定义处理
    editor.customConfig.customUploadImg = function (files, insert) {
        cuonice();
    };

    editor.create();
    E.fullscreen.init('#detail');

    $(function () {
        var bareLocation = Number('{$list.bare_location}');
        if (bareLocation === 1) {
            $('#bareType').show();
        }
        $('#bareLocation').change(function () {
            var selectType = Number($(this).val());
            if (selectType) {
                $('#bareType').show();
            } else {
                $('#bareType').hide();
            }
        });
    });

    var showQrCode = function () {
        $.get("{:url('tissue/createQrCode')}&pagePath=yl_welore/pages/packageE/notes/index@", function (data) {
            try {
                var msg = JSON.parse(data);
                layer.open({
                    title: "errcode：" + msg.errcode,
                    content: msg.errmsg
                });
            } catch (e) {
                layer.open({
                    type: 1,
                    title: false,
                    shadeClose: true,
                    closeBtn: 1,
                    area: ['430px', '430px'],
                    content: "<img src=\"{:url(\'tissue/createQrCode\')}&pagePath=yl_welore/pages/packageE/notes/index@\">",
                });
            }
        });
    }

    var cuonice = function (obj) {
        if(obj) {
            // 处理图片选择器的情况
            $(obj).children('img').addClass('img-select');
        } else {
            // 处理富文本编辑器的情况
            window.editorInstance = editor;
        }
        
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no'],
            end: function (index, layer) {
                $('.img-select').removeClass('img-select');
                window.editorInstance = null;
            }
        });
    }

    var sutake = function (eurl) {
        if($('.img-select').length > 0) {
            // 处理图片选择器的情况
            $('.img-select').attr('src', eurl).parent().children('input').val(eurl);
        } else if(window.editorInstance) {
            // 处理富文本编辑器的情况
            window.editorInstance.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
        }
        layer.closeAll();
    }

    var grender = function (obj, limit) {
        if (limit === 2) {
            obj.value = obj.value.replace(/[^\d.]/g, "");
            obj.value = obj.value.replace(/^\./g, "");
            obj.value = obj.value.replace(/\.{2,}/g, ".");
            obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
            obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
        } else {
            obj.value = Number((obj.value.match(/^\d+(?:\.\d{0})?/)));
        }
    }

    var holdSave = function () {
        var setData = {};
        setData['uplid'] = '{$list.id}';
        setData['customTitle'] = $.trim($("#customTitle").val());
        setData['bareLocation'] = $.trim($("#bareLocation").val());
        setData['bareDirection'] = $.trim($("#bareDirection").val());
        setData['directionBottom'] = $.trim($("#directionBottom").val());
        setData['bareImgUrl'] = $.trim($("[name='bare-img']").val());
        setData['autoCareful'] = $.trim($("[name='autoCareful']:checked").val());
        setData['throwPriceMale'] = $.trim($('#throwPriceMale').val());
        setData['throwPriceFemale'] = $.trim($('#throwPriceFemale').val());
        setData['pickPriceMale'] = $.trim($('#pickPriceMale').val());
        setData['pickPriceFemale'] = $.trim($('#pickPriceFemale').val());
        setData['payType'] = Number($('#payType').val());
        setData['throwLimit'] = Number($('#throwLimit').val());
        setData['pickLimit'] = Number($('#pickLimit').val());
        setData['tenLocalKey'] = $.trim($("#tenLocalKey").val());
        setData['notice'] = $.trim(editor.txt.html());
        $.post("{:url('tissue/choked')}", setData, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000});
            }
        }, 'json');
    }
</script>
{/block}