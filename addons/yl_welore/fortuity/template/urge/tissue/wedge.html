{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .am-form {position: relative;overflow: visible;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-file-text {margin-right: 5px;color: #23b7e5;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .action-btn {display: inline-block;padding: 4px 8px;background: #fff;border: 1px solid #ddd;color: #333;border-radius: 3px;font-size: 12px;cursor: pointer;transition: all 0.3s;margin: 0 2px;}
    .action-btn:hover {border-color: #23b7e5;color: #23b7e5;background-color: #f5fafd;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
    .filter-btn-group .am-btn {
        margin-right: 10px;
        border-radius: 3px;
    }
    .filter-btn-group .am-btn.active {
        background-color: #23b7e5;
        color: white;
        border-color: #23b7e5;
    }
    .batch-actions-group > .am-btn + .am-btn {
        margin-left: 8px;
    }
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-file-text"></span> 小纸条列表
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索内容或用户...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-7">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-sm filter-btn-group">
                        <a href="{:url('tissue/wedge')}&egon=0" type="button" class="am-btn am-btn-default {if $egon==0}active{/if}">全部</a>
                        <a href="{:url('tissue/wedge')}&egon=1" type="button" class="am-btn am-btn-default {if $egon==1}active{/if}">待审核</a>
                        <a href="{:url('tissue/wedge')}&egon=2" type="button" class="am-btn am-btn-default {if $egon==2}active{/if}">已审核</a>
                        <a href="{:url('tissue/wedge')}&egon=3" type="button" class="am-btn am-btn-default {if $egon==3}active{/if}">已拒绝</a>
                    </div>
                </div>
            </div>
            {if $egon!=4}
            <div class="am-u-sm-12 am-u-md-5" style="text-align: right;">
                 <div class="am-btn-group am-btn-group-sm batch-actions-group">
                    <button type="button" class="am-btn am-btn-success" onclick="batchReviewProve(1);">批量通过</button>
                    <button type="button" class="am-btn am-btn-secondary" onclick="batchReviewProve(2);">批量拒绝</button>
                    <button type="button" class="am-btn am-btn-danger" onclick="batchReviewProve(3);">批量删除</button>
                </div>
            </div>
            {/if}
        </div>

        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="5%"><input id="withole" type="checkbox" class="tpl-table-fz-check">全选</th>
                            <th width="5%">ID</th>
                            <th width="15%">发布用户</th>
                            <th width="8%">性别</th>
                            <th width="10%">星座</th>
                            <th width="15%">发布时间</th>
                            <th width="10%">审核状态</th>
                            <th width="32%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td><input type="checkbox" class="tpl-table-fz-check elctive" value="{$vo.id}"></td>
                            <td>{$vo.id}</td>
                            <td>
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td>
                                {if $vo.gender == 0} 女 {else} 男 {/if}
                            </td>
                            <td>
                                {switch $vo.constellation}
                                    {case 0}白羊座{/case}
                                    {case 1}金牛座{/case}
                                    {case 2}双子座{/case}
                                    {case 3}巨蟹座{/case}
                                    {case 4}狮子座{/case}
                                    {case 5}处女座{/case}
                                    {case 6}天秤座{/case}
                                    {case 7}天蝎座{/case}
                                    {case 8}射手座{/case}
                                    {case 9}魔羯座{/case}
                                    {case 10}水瓶座{/case}
                                    {case 11}双鱼座{/case}
                                {/switch}
                            </td>
                            <td> {:date('Y-m-d H:i:s',$vo.create_time)} </td>
                            <td>
                                {if $vo.check_status == 0} <span class="am-text-warning">待审核</span>
                                {elseif $vo.check_status == 1} <span class="am-text-success">已通过</span>
                                {elseif $vo.check_status == 2} <span class="am-text-danger">已拒绝</span>
                                {/if}
                            </td>
                            <td>
                                <button type="button" class="action-btn" onclick="wedgeInfo('{$vo.id}',0)">纸条详情</button>
                                {if $vo.check_status != 0}
                                <button type="button" class="action-btn" onclick="wedgeInfo('{$vo.id}',1)">抽取详情</button>
                                {/if}
                                {if $vo.check_status == 0}
                                <button type="button" class="action-btn" style="color: #4CAF50;" onclick="reviewProve('{$vo.id}',1)">通过</button>
                                <button type="button" class="action-btn" style="color: #f44336;" onclick="reviewProve('{$vo.id}',2)">拒绝</button>
                                {/if}
                                <button type="button" class="action-btn" style="color: #f44336;" onclick="delWedge(0,'{$vo.id}')">删除</button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    !function () {
        $('#withole').click(function () {
            var wecked = $(this).prop('checked');
            if (wecked) {
                $('.elctive').prop('checked', true);
            } else {
                $('.elctive').prop('checked', false);
            }
        });
        $('.elctive').click(function () {
            var jack = 1;
            $('.elctive').each(function () {
                var weck = $(this).prop('checked');
                if (weck === false) {
                    jack = 0;
                    return false;
                }
            });
            if (jack) {
                $('#withole').prop('checked', true);
            } else {
                $('#withole').prop('checked', false);
            }
        });
    }();

    var wedgeInfo = function (acid, type) {
        switch (type) {
            case 0:
                layer.open({
                    type: 2,
                    title: false,
                    shadeClose: true,
                    shade: 0.8,
                    resize: false,
                    scrollbar: false,
                    area: ['600px', '70%'],
                    content: "{:url('tissue/wedgeInfo')}&acid=" + acid
                });
                break;
            case 1:
                layer.open({
                    type: 2,
                    title: false,
                    shadeClose: true,
                    shade: 0.8,
                    resize: false,
                    scrollbar: false,
                    area: ['900px', '70%'],
                    content: "{:url('tissue/wedgeSmoke')}&acid=" + acid
                });
                break;
        }

    }

    var batchReviewProve = function (process) {
        var acid = [];
        $('.elctive').each(function () {
            if ($(this).prop('checked')) {
                acid.push($(this).val());
            }
        });
        if (acid.length === 0) {
            layer.alert('很抱歉，当前没有选中要操作的数据', {'title': '系统提示'});
            return;
        }
        if (process === 3) {
            layer.confirm("您确定批量删除选中的纸条吗？", {
                btn: ['确定', '取消'], title: '提示'
            }, function () {
                delWedge(1, acid);
            }, function (index) {
                layer.close(index);
            });
        } else {
            if (process === 1) {
                layer.confirm("您确定批量同意选中的纸条通过审核吗？", {
                    btn: ['确定', '取消'], title: '提示'
                }, function () {
                    simplifyTrial(acid, process, '');
                }, function (index) {
                    layer.close(index);
                });
            }
            if (process === 2) {
                var prompt = [];
                prompt['title'] = '请输入选中的纸条未通过审核的原因：';
                prompt['area'] = ['300px', '100px'];
                layer.prompt({
                    title: prompt['title'],
                    formType: 2,
                    area: prompt['area'],
                    btn: ['确定', '取消'],
                }, function (reaValue, index) {
                    if (reaValue.trim() === '') {
                        return false;
                    }
                    simplifyTrial(acid, process, reaValue);
                    layer.close(index);
                });
            }
        }
    }

    var reviewProve = function (acid, process) {
        if (process === 1) {
            layer.confirm("您确定同意通过审核吗？", {
                btn: ['确定', '取消'], title: '提示'
            }, function () {
                simplifyTrial([acid], process, '');
            }, function (index) {
                layer.close(index);
            });
        }
        if (process === 2) {
            var prompt = [];
            prompt['title'] = '请输入该纸条未通过审核的原因：';
            prompt['area'] = ['300px', '100px'];
            layer.prompt({
                title: prompt['title'],
                formType: 2,
                area: prompt['area'],
                btn: ['确定', '取消'],
            }, function (reaValue, index) {
                if (reaValue.trim() === '') {
                    return false;
                }
                simplifyTrial([acid], process, reaValue);
                layer.close(index);
            });
        }
    }

    var simplifyTrial = function (acid, process, reaValue) {
        $.ajax({
            type: "post",
            url: "{:url('tissue/trialWedge')}",
            data: {acid: acid, status: process, opinion: reaValue},
            dataType: 'json',
            success: function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }
        });
    }

    var delWedge = function (process, acid) {
        var confimDel = function (acid) {
            $.ajax({
                type: "post",
                url: "{:url('tissue/delWedge')}",
                data: {acid: acid},
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }
            });
        }
        if (process === 1) {
            confimDel(acid);
        } else {
            layer.confirm("您确定要删除这张纸条吗？", {
                btn: ['确定', '取消'], title: '提示'
            }, function () {
                confimDel([acid]);
            }, function (index) {
                layer.close(index);
            });
        }
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('tissue/wedge')}&egon={$egon}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('tissue/wedge')}&egon={$egon}&page={$page}";
        }
    }
</script>
{/block}