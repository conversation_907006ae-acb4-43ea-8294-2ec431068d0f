{extend name="/base"/}
{block name="main"}
<style>.customize-span{padding:3px 10px;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            投票给 <span style="color:black;">{$pvInfo.ballot_name}</span> 的用户
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索用户...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <form class="am-form">
                    <table class="am-table table-main">
                        <thead>
                        <tr>
                            <th width="33%" style="text-align:left">
                                用户信息
                            </th>
                            <th width="33%" style="text-align:center">
                                openid
                            </th>
                            <th width="33%" style="text-align:right">
                                投票时间
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                <div style="width:100%;height:60px;display:flex;justify-content:flex-start;align-items:center;">
                                    <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                        {if $vo.user_head_sculpture && $vo.user_head_sculpture !='/yl_welore/style/icon/default.png'}
                                        <img src="{$vo.user_head_sculpture}" style="width: 50px;height: 50px;border-radius: 50%;">
                                        {else}
                                        <img src="{:urlBridging('static/disappear/tourist.png')}" style="width: 50px;height: 50px;border-radius: 50%;">
                                        {/if}
                                        {$vo.user_nick_name|emoji_decode}
                                    </a>
                                </div>
                            </td>
                            <td>
                                <div style="width:100%;height:60px;display:flex;justify-content:center;align-items:center;">
                                    {$vo.user_wechat_open_id}
                                </div>
                            </td>
                            <td>
                                <div style="width:100%;height:60px;display:flex;justify-content:flex-end;align-items:center;">
                                    {:date('Y-m-d H:i:s',$vo.decide_time)}
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </form>
            </div>

        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('compass/caveat')}&tyid={$tyid}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('compass/caveat')}&tyid={$tyid}&page={$page}";
        }
    }

</script>
{/block}