{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:45px;text-align:center;}.am-table > thead:first-child > tr:first-child > th{text-align:center;}.customize-span{padding:3px 10px;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-location-arrow"></span> {$toryInfo.realm_name|emoji_decode} 禁言列表
        </div>

        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索用户...">
                </div>
            </div>
        </div>

    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <span class="customize-span" data-am-modal="{target: '#shandsel', closeViaDimmer: 0, width: 420, height: 300}">
                        <span class="am-icon-adn"></span> 新增禁言
                    </span>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <form class="am-form">
                    <table class="am-table table-main">
                        <thead>
                        <tr>
                            <th width="20%">用户信息</th>
                            <th width="20%">openid</th>
                            <th width="20%">禁言结束时间</th>
                            <th width="20%">禁言原因</th>
                            <th width="20%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {if $vo.user_head_sculpture && $vo.user_head_sculpture !='/yl_welore/style/icon/default.png'}
                                    <img src="{$vo.user_head_sculpture}" style="width: 50px;height: 50px;border-radius: 50%;">
                                    {else}
                                    <img src="{:urlBridging('static/disappear/tourist.png')}" style="width: 50px;height: 50px;border-radius: 50%;">
                                    {/if}
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {if $vo.user_head_sculpture && $vo.user_head_sculpture !='/yl_welore/style/icon/default.png'}
                                    <img src="{$vo.user_head_sculpture}" style="width: 50px;height: 50px;border-radius: 50%;">
                                    {else}
                                    <img src="{:urlBridging('static/disappear/tourist.png')}" style="width: 50px;height: 50px;border-radius: 50%;">
                                    {/if}
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td class="am-text-middle">{$vo.user_wechat_open_id}</td>
                            <td class="am-text-middle">{:date('Y-m-d H:i:s',$vo.refer_time)}</td>
                            <td class="am-text-middle">
                                <button type="button" class="am-btn am-btn-default am-btn-xs" onclick="seeWhy('{$vo.id}');">点击查看</button>
                            </td>
                            <td class="am-text-middle">
                                <span style="cursor: pointer;" onclick="cancelStrike('{$vo.id}');" >取消禁言</span>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </form>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel">
        <div class="am-modal-dialog" style="background:#fefffe;">
            <div class="am-modal-hd">
                <span style="font-size: 14px;position: absolute;left:12px;top:7px;">禁言用户</span>
                <a id="euModalClose" href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd am-form tpl-form-line-form">
                <div class="am-form-group" style="margin-top:35px;">
                    <label class="am-u-sm-4 am-form-label" style="font-size: 15px;margin:2px 0 0 -5px;">用户openid</label>
                    <div class="am-u-sm-8">
                        <input type="text" id="openid" class="tpl-form-input" style="margin:3px 0 0 -20px;" placeholder="请输入要禁言的用户openid">
                    </div>
                </div>
                <div class="am-form-group" style="margin-top:15px;">
                    <label class="am-u-sm-4 am-form-label" style="font-size: 15px;margin:2px 0 0 -5px;">禁言时间</label>
                    <div class="am-u-sm-8">
                        <select id="containTime" style="margin:3px 0 0 -20px;">
                            <option value="1">1天</option>
                            <option value="3">3天</option>
                            <option value="7">7天</option>
                            <option value="15">15天</option>
                            <option value="30">30天</option>
                            <option value="90">90天</option>
                            <option value="180">180天</option>
                            <option value="365">365天</option>
                        </select>
                    </div>
                </div>
                <div class="am-form-group" style="margin-top:3px;">
                    <label class="am-u-sm-4 am-form-label" style="font-size: 15px;margin:2px 0 0 -5px;">禁言原因</label>
                    <div class="am-u-sm-8">
                        <input type="text" id="tell" class="tpl-form-input" style="margin:3px 0 0 -20px;" placeholder="请输入要禁言的原因">
                    </div>
                </div>
                <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top:10px;">
                    <button type="button" class="am-btn am-btn-sm" class="am-btn" onclick="sendGifts();">禁言</button>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    var seeWhy = function (ubid) {
        $.post("{:url('leading/muted')}", {ubid}, function (data) {
            layer.msg(data);
        }, 'json');
    }

    var sendGifts = function () {
        var exponent = 1;
        var tyid = '{$toryInfo.id}';
        var openid = $.trim($('#openid').val());
        if (openid == '') {
            layer.msg('请输入要禁言用户的openid');
            return;
        }
        var containTime = $('#containTime').val();
        var tell = $.trim($('#tell').val());
        if (tell == '') {
            layer.msg('请输入禁言理由');
            return;
        }
        layer.confirm('您确定要禁言此用户吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("{:url('leading/commute')}", {exponent, openid, tyid, tell, containTime}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1600}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }

    var cancelStrike = function (ubid) {
        var exponent = 2;
        layer.confirm('您确定要取消禁言当前用户吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("{:url('leading/commute')}", {exponent, ubid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1600}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('leading/muted')}&tyid={$toryInfo.id}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('leading/muted')}&tyid={$toryInfo.id}&page={$page}";
        }
    }

</script>
{/block}