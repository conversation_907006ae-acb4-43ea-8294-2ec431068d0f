{extend name="/base"/}
{block name="main"}
<style>#app.tpl-portlet-components{padding:0;}.settings-card{background-color:#fff;border-radius:8px;box-shadow:0 4px 6px -1px rgba(0,0,0,0.1),0 2px 4px -1px rgba(0,0,0,0.06);}.card-header{padding:30px;border-bottom:1px solid #e5e7eb;}.card-title{font-size:20px;font-weight:600;color:#111827;margin:0 0 5px 0;}.card-subtitle{margin-top:0.5px;font-size:14px;color:#6b7280;}.card-body{padding:30px 15%;}.form-group{display:flex;flex-wrap:wrap;margin-bottom:50px;align-items:center;}.form-label{width:200px;padding-top:15px;padding-right:15px;font-weight:500;color:#374151;text-align:right;flex-shrink:0;}.form-control{flex:1;min-width:0;padding:0 !important;border:0 !important;}.form-input{display:block;width:100%;padding:0.75rem 0.85rem;font-size:1.5rem;line-height:1.5;color:#111827;background-color:#fff;background-clip:padding-box;border:1px solid #d1d5db;border-radius:6px;transition:border-color 0.15s ease-in-out,box-shadow 0.15s ease-in-out;}.form-input:focus{color:#111827;background-color:#fff;border-color:#3b82f6;outline:0;box-shadow:0 0 0 3px rgba(59,130,246,0.25);}.form-help-text{margin-top:8px;font-size:15px;color:#6b7280;}.notice-box{margin-top:80px;padding:8px 12px;background-color:#fef3c7;border-radius:6px;font-size:16px;color:#92400e;text-align:center;}.notice-box span{font-weight:600;}.card-footer{padding:25px;background-color:#f9fafb;text-align:center;border-top:1px solid #e5e7eb;}.btn-primary{width:100px;height:40px;display:inline-block;font-weight:500;color:#fff;text-align:center;vertical-align:middle;cursor:pointer;background-color:#2563eb;border:1px solid transparent;padding:1px 1.25px;font-size:16px;line-height:1.5;border-radius:6px;transition:color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;}.btn-primary:hover{background-color:#1d4ed8;}</style>
<div id="app" class="tpl-portlet-components">
    <div class="settings-card">
        <div class="card-header">
            <h1 class="card-title">123云盘</h1>
            <p class="card-subtitle">配置您的123云盘，所有更改将立即生效。</p>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label class="form-label">Client ID</label>
                <div class="form-control">
                    <input type="text" class="form-input" v-model="list.clientId"
                        placeholder="{if $list.pan123_client_id}{$list.pan123_client_id|ciphertext}{else}请输入 Client ID{/if}">
                    <p class="form-help-text">请输入123云盘的 Client ID</p>
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">Client Secret</label>
                <div class="form-control">
                    <input type="text" class="form-input" v-model="list.clientSecret"
                        placeholder="{if $list.pan123_client_secret}{$list.pan123_client_secret|ciphertext}{else}请输入 Client Secret{/if}">
                    <p class="form-help-text">请输入123云盘的 Client Secret</p>
                </div>
            </div>
            <div class="notice-box">
                <strong>注意事项：</strong><span>保存之前请检查您的配置是否正确，保存之后所更改的内容将会立即生效！</span>
            </div>
        </div>
        <div class="card-footer">
            <button type="button" class="btn-primary" @click="holdSave">
                保存配置
            </button>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    new Vue({
        el: '#app',
        data() {
            return {
                list: {
                    'clientId': '',
                    'clientSecret': ''
                }
            }
        }, methods: {
            holdSave() {
                var setData = this.list;
                $.post("{:url('leading/pan123')}", setData, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, { icon: 5, time: 2000 });
                    }
                }, 'json');
            }
        }
    })
</script>
{/block}