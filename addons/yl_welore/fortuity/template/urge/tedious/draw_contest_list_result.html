<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <title>开奖结果</title>
    <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
    <link rel="stylesheet" href="./assets/css/element-ui.min.css">
</head>
<body>
<div id="app" class="am-g">
    <div class="am-form" style="min-height: 465px;">
        <table class="am-table am-table-compact am-table-bordered am-table-radius am-table-striped">
            <tbody>
            <template v-for="(item,index) in list.prizeList">
                <tr>
                    <td style="width: 35%;text-align: center;padding-right: 10px;">
                        抽奖名称
                    </td>
                    <td style="width: 65%;padding: 3px 10px 0 10px;">
                        {{item.prizeName}}
                    </td>
                </tr>
                <tr>
                    <td style="width: 35%;text-align: center;padding-right: 10px;">
                        抽奖类型
                    </td>
                    <td style="width: 65%;padding: 3px 10px 0 10px;">
                        <template v-if="item.prizeType == 0">
                            实物奖励
                        </template>
                        <template v-if="item.prizeType == 1">
                            贝壳奖励
                        </template>
                        <template v-if="item.prizeType == 2">
                            积分奖励
                        </template>
                        <template v-if="item.prizeType == 3">
                            经验值奖励
                        </template>
                        <template v-if="item.prizeType == 4">
                            荣誉点奖励
                        </template>
                    </td>
                </tr>
                <tr>
                    <td style="width: 35%;text-align: center;padding-right: 10px;">
                        奖品图片
                    </td>
                    <td style="width: 65%;padding: 3px 10px 0 10px;">
                        <img :src="item.prizeImage" height="70px" width="70px">
                    </td>
                </tr>
                <tr>
                    <td style="width: 35%;text-align: center;padding-right: 10px;">
                        奖品等级
                    </td>
                    <td style="width: 65%;padding: 3px 10px 0 10px;">
                        {{item.prizeLevel}}等奖
                    </td>
                </tr>
                <tr>
                    <td style="width: 35%;text-align: center;padding-right: 10px;">
                        奖品数量
                    </td>
                    <td style="width: 65%;padding: 3px 10px 0 10px;">
                        {{item.extractQuantity}}
                    </td>
                </tr>
                <tr v-if="item.prizeType > 0">
                    <td style="width: 35%;text-align: center;padding-right: 10px;">
                        奖励分数
                    </td>
                    <td style="width: 65%;padding: 3px 10px 0 10px;">
                        {{item.prizeQuantity}}
                        <template v-if="item.prizeType == 1">
                            贝壳奖励
                        </template>
                        <template v-if="item.prizeType == 2">
                            积分奖励
                        </template>
                        <template v-if="item.prizeType == 3">
                            经验值奖励
                        </template>
                        <template v-if="item.prizeType == 4">
                            荣誉点奖励
                        </template>
                    </td>
                </tr>
                <tr>
                    <td style="width: 35%;text-align: center;padding-right: 10px;">
                        开奖号码
                    </td>
                    <td style="width: 65%;padding: 3px 10px 0 10px;">
                        {{item.luckyNumber}}
                    </td>
                </tr>
                <tr v-if="index != list.prizeList.length - 1">
                    <td colspan="2">
                        <el-divider></el-divider>
                    </td>
                </tr>
            </template>
            </tbody>
        </table>
    </div>
</div>
</body>
<script src="./assets/js/vue.min.js"></script>
<script src="./assets/js/element-ui.min.js"></script>
<script>
    new Vue({
        el: '#app',
        data() {
            return {
                list: {
                    prizeList: []
                }
            }
        }, created() {
            this.list.prizeList = JSON.parse(decodeURIComponent(atob('{$list.prize_outcome}')));
        }
    });
</script>
</html>