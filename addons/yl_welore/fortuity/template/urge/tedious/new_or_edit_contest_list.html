{extend name="/base"/}
{block name="main"}
<style>
    .am-form-group {
        margin-bottom: 0;
    }

    .w-e-menu {
        font-size: 14px;
    }

    .w-e-text, .w-e-text-container {
        height: 650px !important;
    }
</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> {if !$isEdit}新增{else}编辑{/if}抽奖内容
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-12 am-u-md-12">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">抽奖活动名称</label>
                        <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                            <input type="text" v-model="list.lotteryName">
                            <small>抽奖页面显示的活动名称</small>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin-top:40px;">
                        <label class="am-u-sm-3 am-form-label">参与抽奖人数限制</label>
                        <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                            <input type="number" v-model="list.participantNumLimit">
                            <small>活动期间参与抽奖人数限制 设置为 0 则无限制</small>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin-top:40px;">
                        <label class="am-u-sm-3 am-form-label">免费参与次数</label>
                        <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                            <input type="number" v-model="list.freeEntryCount">
                            <small>活动期间免费参与次数 设置为 0 则不免费
                                <span style="color:red;font-weight:bold;">
                                    注：此功能和观看广告免费抽奖相互独立
                                </span>
                            </small>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin-top:40px;">
                        <label class="am-u-sm-3 am-form-label">看广告免费抽奖次数</label>
                        <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                            <input type="number" v-model="list.videoEntryCount">
                            <small>活动期间观看广告免费抽奖次数 设置为 0 则不免费</small>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin-top:40px;">
                        <label class="am-u-sm-3 am-form-label">是否分组</label>
                        <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                            <select v-model="list.isGroup">
                                <option value="0">否</option>
                                <option value="1">是</option>
                            </select>
                            <small>参与抽奖时系统会随机分配一个奖号例如：A0001-A9999、B0001-B9999或C0001-B9999，A组达到指定人数则启用其他组的号码</small>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin-top:40px;">
                        <label class="am-u-sm-3 am-form-label">抽取类型</label>
                        <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                            <select v-model="list.extractType">
                                <option value="0">号池抽取</option>
                                <option value="1">随机抽取</option>
                            </select>
                            <small>号池抽取则在用户的奖号中抽取，随机抽取则在设置的抽取范围中随机，有可能存在空奖</small>
                        </div>
                    </div>
                    <template v-if="list.extractType == 1">
                        <div class="am-form-group" style="margin-top:40px;">
                            <label class="am-u-sm-3 am-form-label">随机抽取范围开始</label>
                            <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                                <input type="number" v-model="list.randomExtractRangeStart">
                                <small>随机抽取号码范围开始 例如：0 则从 0 - 9999、50 则从 50 - 9999</small>
                            </div>
                        </div>
                        <div class="am-form-group" style="margin-top:40px;">
                            <label class="am-u-sm-3 am-form-label">随机抽取范围结束</label>
                            <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                                <input type="number" v-model="list.randomExtractRangeEnd">
                                <small>随机抽取号码范围开始 例如：5000 则从 0 - 5000、9999 则从 0 - 9999</small>
                            </div>
                        </div>
                    </template>
                    <div class="am-form-group" style="margin-top:40px;">
                        <label class="am-u-sm-3 am-form-label">活动开始时间</label>
                        <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                            <el-date-picker v-model="list.startTime" type="datetime" value-format="timestamp"
                                            placeholder="选择日期时间"></el-date-picker>
                            <small>抽奖活动开始时间</small>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin-top:40px;">
                        <label class="am-u-sm-3 am-form-label">活动结束时间</label>
                        <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                            <el-date-picker v-model="list.endTime" type="datetime" value-format="timestamp"
                                            placeholder="选择日期时间"></el-date-picker>
                            <small>抽奖活动结束时间</small>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin-top:40px;">
                        <label class="am-u-sm-3 am-form-label">活动开奖时间</label>
                        <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                            <el-date-picker v-model="list.drawTime" type="datetime" value-format="timestamp"
                                            placeholder="选择日期时间"></el-date-picker>
                            <small>抽奖活动开奖时间</small>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin-top:40px;">
                        <label class="am-u-sm-3 am-form-label">奖品列表</label>
                        <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                            <div style="display: flex;">
                                <input type="text" disabled>
                                <el-button type="primary" size="medium" @click="newPrizeData">
                                    新增奖品
                                </el-button>
                            </div>
                            <template v-for="(item,index) in list.prizeList">
                                <div style="display:flex;flex-wrap: wrap;justify-content:center;align-items:center;padding-top:30px;position: relative;">
                                    <div v-if="index != 0"
                                         style="position: absolute;top: 25px;right: 5px;cursor: pointer;z-index: 10;"
                                         title="移除此奖品" @click="removePrizeData(index)">
                                        ×
                                    </div>
                                    <div class="w-100"
                                         style="display: flex;justify-content: center;align-items: center;">
                                        <div class="am-u-sm-12 am-u-end"
                                             style="border-top:solid 1px;border-left:solid 1px;border-right:solid 1px;padding: 15px 0;">
                                            <label class="am-u-sm-2 am-form-label"
                                                   style="height: 70px;display: flex;justify-content: flex-end;align-items: center;">
                                                奖品名称
                                            </label>
                                            <div class="am-u-sm-2"
                                                 style="height: 70px;display: flex;justify-content: flex-end;align-items: center;">
                                                <input type="text" v-model="item.prizeName">
                                            </div>
                                            <label class="am-u-sm-2 am-form-label"
                                                   style="height: 70px;display: flex;justify-content: flex-end;align-items: center;">
                                                奖品类型
                                            </label>
                                            <div class="am-u-sm-2"
                                                 style="height: 70px;display: flex;justify-content: flex-end;align-items: center;">
                                                <select v-model="item.prizeType" @change="changeChoose(index);">
                                                    <option value="0">实物奖励</option>
                                                    <option value="1">贝壳奖励</option>
                                                    <option value="2">积分奖励</option>
                                                    <option value="3">经验值奖励</option>
                                                    <option value="4">荣誉点奖励</option>
                                                </select>
                                            </div>
                                            <label class="am-u-sm-2 am-form-label"
                                                   style="height: 70px;display: flex;justify-content: flex-end;align-items: center;">
                                                奖品等级
                                            </label>
                                            <div class="am-u-sm-2"
                                                 style="height: 70px;display: flex;justify-content: flex-end;align-items: center;">
                                                <input type="number" v-model="item.prizeLevel">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="w-100"
                                         style="display: flex;justify-content: center;align-items: center;">
                                        <div class="am-u-sm-12 am-u-end"
                                             style="border-bottom:solid 1px;border-left:solid 1px;border-right:solid 1px;padding: 15px 0;">
                                            <label class="am-u-sm-2 am-form-label"
                                                   style="height: 70px;display: flex;justify-content: flex-end;align-items: center;">
                                                奖品图片
                                            </label>
                                            <div class="am-u-sm-2" style="margin-top: 3px;">
                                                <img :src="item.prizeImage"
                                                     onerror="this.src='static/disappear/default.png'"
                                                     @click="changePicture(index,1);"
                                                     style="width:70px;height:70px;cursor:pointer;border: #ccc dashed 1px;">
                                            </div>
                                            <template v-if="item.prizeType < 1">
                                                <label class="am-u-sm-2 am-form-label"
                                                       style="height: 70px;display: flex;justify-content: flex-end;align-items: center;">
                                                    奖品数量
                                                </label>
                                                <div class="am-u-sm-2 am-u-end"
                                                     style="height: 70px;display: flex;justify-content: flex-end;align-items: center;">
                                                    <input type="number" v-model="item.extractQuantity">
                                                </div>
                                            </template>
                                            <template v-if="item.prizeType > 0">
                                                <label class="am-u-sm-2 am-form-label"
                                                       style="height: 70px;display: flex;justify-content: flex-end;align-items: center;">
                                                    奖品数量
                                                </label>
                                                <div class="am-u-sm-2"
                                                     style="height: 70px;display: flex;justify-content: flex-end;align-items: center;">
                                                    <input type="number" v-model="item.extractQuantity">
                                                </div>
                                                <label class="am-u-sm-2 am-form-label"
                                                       style="height: 70px;display: flex;justify-content: flex-end;align-items: center;">
                                                    奖励分数
                                                </label>
                                                <div class="am-u-sm-2"
                                                     style="height: 70px;display: flex;justify-content: flex-end;align-items: center;">
                                                    <input type="number" v-model="item.prizeQuantity">
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin-top:40px;">
                        <label class="am-u-sm-3 am-form-label">抽奖规则说明</label>
                        <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                            <div id="detail" style="min-height:680px;"></div>
                            <span id="customizeGallery" style="display:none;" @click="cuonice();"></span>
                        </div>
                    </div>
                    <div class="am-form-group" style="display: flex;justify-content: center;margin-top: 50px;">
                        <button type="button" class="am-btn am-btn-primary" @click="holdSave();">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script type="text/javascript" src="static/wangEditor/wangEditor.min.js"></script>
<link rel="stylesheet" href="static/wangEditor/wangEditor-fullscreen-plugin.css">
<script type="text/javascript" src="static/wangEditor/wangEditor-fullscreen-plugin.js"></script>
<script>

    var vm = new Vue({
        el: '#app',
        data: {
            list: {
                lotteryName: '',
                prizeList: [{
                    "prizeType": "0",
                    "prizeName": "",
                    "prizeImage": "",
                    "prizeLevel": "1",
                    "prizeQuantity": "1",
                    "extractQuantity": "1"
                }],
                freeEntryCount: '1',
                videoEntryCount: '1',
                participantNumLimit: '0',
                isGroup: '0',
                extractType: '0',
                randomExtractRangeStart: '0',
                randomExtractRangeEnd: '9999',
                startTime: '',
                endTime: '',
                drawTime: '',
                campaignDesc: '',
            },
            E: [],
            editor: [],
            onLock: false
        },
        created: function () {
            if ('{$isEdit}' === '1') {
                this.list = JSON.parse(decodeURIComponent(atob('{$list}')));
            }
        }, mounted: function () {
            this.E = window.wangEditor;
            this.editor = new this.E('#detail');
            this.editor.customConfig.uploadImgServer = true;
            this.editor.create();
            this.E.fullscreen.init('#detail');
            this.editor.txt.html(this.list.campaignDesc);
        },
        methods: {
            newPrizeData: function () {
                var prizeLevel = this.list.prizeList.length;
                this.list.prizeList.push({
                    "prizeType": "0",
                    "prizeName": "",
                    "prizeImage": "",
                    "prizeLevel": ++prizeLevel,
                    "prizeQuantity": "1",
                    "extractQuantity": "1"
                });
            },
            removePrizeData: function (index) {
                this.list.prizeList.splice(index, 1);
            },
            changePicture: function (index, openPicture, picturePath) {
                switch (openPicture) {
                    case 0:
                        this.list.prizeList[index].prizeImage = picturePath;
                        break;
                    case 1:
                        layer.open({
                            type: 2,
                            anim: 2,
                            scrollbar: true,
                            area: ['900px', '600px'],
                            title: false,
                            closeBtn: 0,
                            shadeClose: true,
                            content: ["{:url('images/dialogimages')}&gclasid=0&pictureIndex=" + index, 'no']
                        });
                        break;
                }
            },
            holdSave: function () {
                var setData = {};
                setData['fid'] = '{$Request.get.fid}';
                setData['lotteryName'] = this.list.lotteryName;
                if ($.trim(setData['lotteryName']) === '') {
                    layer.msg('抽奖活动名称不能为空');
                    return;
                }
                setData['freeEntryCount'] = this.list.freeEntryCount;
                setData['videoEntryCount'] = this.list.videoEntryCount;
                setData['participantNumLimit'] = this.list.participantNumLimit;
                setData['isGroup'] = this.list.isGroup;
                setData['extractType'] = this.list.extractType;
                setData['randomExtractRangeStart'] = this.list.randomExtractRangeStart;
                setData['randomExtractRangeEnd'] = this.list.randomExtractRangeEnd;
                setData['startTime'] = this.list.startTime;
                if ($.trim(setData['startTime']) === '') {
                    layer.msg('请选择活动开始时间');
                    return;
                }
                setData['endTime'] = this.list.endTime;
                if ($.trim(setData['endTime']) === '') {
                    layer.msg('请选择活动结束时间');
                    return;
                }
                setData['drawTime'] = this.list.drawTime;
                if ($.trim(setData['drawTime']) === '') {
                    layer.msg('请选择活动开奖时间');
                    return;
                }
                var prizeListIsReturn = false;
                setData['prizeList'] = this.list.prizeList;
                for (var i = 0; i < setData['prizeList'].length; i++) {
                    if ($.trim(setData['prizeList'][i]['prizeName']) === '') {
                        prizeListIsReturn = true;
                        layer.msg('奖品-' + (i + 1) + '名称不能为空');
                        break;
                    }
                    if ($.trim(setData['prizeList'][i]['prizeImage']) === '' || $.trim(setData['prizeList'][i]['prizeImage']) === 'static/disappear/default.png') {
                        prizeListIsReturn = true;
                        layer.msg('请选择奖品-' + (i + 1) + '的图片');
                        break;
                    }
                }
                if (prizeListIsReturn) {
                    return;
                }
                setData['campaignDesc'] = this.editor.txt.html();
                if (!this.onLock) {
                    this.onLock = true;
                    var _this = this;
                    $.post("{:url('tedious/new_or_edit_contest_list')}", setData, function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                if ('{$isEdit}' === '1') {
                                    location.reload();
                                } else {
                                    location.href = "{:url('tedious/contest_list')}";
                                }
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                _this.onLock = false;
                            });
                        }
                    }, 'json');
                }
            }, cuonice: function () {
                layer.open({
                    type: 2,
                    anim: 2,
                    scrollbar: true,
                    area: ['900px', '600px'],
                    title: false,
                    closeBtn: 0,
                    shadeClose: true,
                    content: ["{:url('images/dialogImages')}&gclasid=0&pictureIndex=-1", 'no']
                });
            }
        }
    });

    var sutake = function (eurl, pictureIndex, type) {
        switch (type) {
            case 0:
                vm.changePicture(pictureIndex, 0, eurl);
                break;
            case 1:
                vm.editor.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
                break;
        }
        layer.closeAll();
    }

</script>
{/block}