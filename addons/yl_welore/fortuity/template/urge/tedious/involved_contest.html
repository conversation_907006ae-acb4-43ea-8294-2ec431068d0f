{extend name="/base"/}
{block name="main"}
<style>
    .am-table-striped > tbody > tr:nth-child(odd) > td, .am-table > tbody > tr > td {
        line-height: 45px;
        text-align: center;
    }

    .am-table > thead:first-child > tr:first-child > th {
        text-align: center;
    }
</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 参与列表
        </div>
    </div>
    <div class="tpl-block">
        <div id="search-list" style="display: flex;flex-wrap: wrap;width: 100%;min-height: 38px;">
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">UID</label>
                <div class="search-input-inline w-100">
                    <el-input class="search-input" v-model="item.uid" clearable></el-input>
                </div>
            </div>
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">抽奖名称</label>
                <div class="search-input-inline w-100">
                    <el-input class="search-input" v-model="item.lotteryName" clearable></el-input>
                </div>
            </div>
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">奖券号码</label>
                <div class="search-input-inline w-100">
                    <el-input class="search-input" v-model="item.luckyNumber" clearable></el-input>
                </div>
            </div>
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">是否中奖</label>
                <div class="search-input-inline w-100">
                    <el-select class="search-input" v-model="item.isAward" placeholder="请选择">
                        <el-option v-for="item in selectOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </div>
            </div>
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">是否派奖</label>
                <div class="search-input-inline w-100">
                    <el-select class="search-input" v-model="item.isPayoutPrizes" placeholder="请选择">
                        <el-option v-for="item in selectOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </div>
            </div>
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">抽奖开始时间</label>
                <div class="search-input-inline">
                    <el-date-picker class="search-input" v-model="item.startTime" type="datetime" value-format="timestamp"
                                    placeholder="选择日期时间"></el-date-picker>
                </div>
            </div>
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">抽奖结束时间</label>
                <div class="search-input-inline">
                    <el-date-picker class="search-input" v-model="item.endTime" type="datetime" value-format="timestamp"
                                    placeholder="选择日期时间"></el-date-picker>
                </div>
            </div>
            <button class="search-wide-pitch search-btn" onclick="turtle();">
                <i class="am-icon-search"></i> 搜 索
            </button>
        </div>
        <div class="am-g" style="margin-top: 10px;">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main-style-2 am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th width="11.11%">
                                用户昵称
                            </th>
                            <th width="11.11%">
                                参与抽奖名称
                            </th>
                            <th width="11.11%">
                                奖券号码
                            </th>
                            <th width="11.11%">
                                抽奖开始时间
                            </th>
                            <th width="11.11%">
                                抽奖结束时间
                            </th>
                            <th width="11.11%">
                                开奖时间
                            </th>
                            <th width="11.11%">
                                是否中奖
                            </th>
                            <th width="11.11%">
                                是否派奖
                            </th>
                            <th width="11.11%">
                                操作
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                {if $vo.user.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user.user_wechat_open_id}&page=1" title="{$vo.user.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&openid={$vo.user.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user.user_nick_name|emoji_decode}">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                {$vo.slInfo.lottery_name}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.slInfo.is_group == 1}{:chr(65+intval($vo.award_categories))}{/if}{$vo.lucky_number}
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$vo.slInfo.start_time)}
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$vo.slInfo.end_time)}
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$vo.slInfo.draw_time)}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.is_award}
                                <span style="color: green;">是</span>
                                {else}
                                <span style="color: red;">否</span>
                                {/if}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.is_payout_prizes}是{else}否{/if}
                            </td>
                            <td class="am-text-middle no-wrap">
                                <el-dropdown size="small" split-button type="primary" trigger="click">
                                    功能菜单
                                    <el-dropdown-menu slot="dropdown">
                                        {if $vo.is_award}
                                        {if !$vo.is_payout_prizes}
                                        <el-dropdown-item @click.native="handleClick(0,'{$vo.id}')">
                                            设置派奖
                                        </el-dropdown-item>
                                        {else}
                                        <el-dropdown-item @click.native="handleClick(1,'{$vo.id}')">
                                            取消派奖
                                        </el-dropdown-item>
                                        {/if}
                                        {/if}
                                        <el-dropdown-item @click.native="handleClick(2,'{$vo.id}')">
                                            查看备注
                                        </el-dropdown-item>
                                        <el-dropdown-item @click.native="handleClick(3,'{$vo.id}')">
                                            修改备注
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var vm = new Vue({
        el: '#app',
        data() {
            return {
                selectOptions: [{
                    value: '-1',
                    label: '请选择'
                }, {
                    value: '1',
                    label: '是'
                }, {
                    value: '0',
                    label: '否'
                }],
                item: {
                    uid: '{$uid}',
                    lotteryName: '{$lotteryName}',
                    luckyNumber: '{$luckyNumber}',
                    isAward: "{if $isAward !== '' && intval($isAward) !== -1}{:intval($isAward)}{/if}",
                    isPayoutPrizes: "{if $isPayoutPrizes !== '' && intval($isPayoutPrizes) !== -1}{:intval($isPayoutPrizes)}{/if}",
                    startTime: '{if $startTime}{$startTime}{/if}',
                    endTime: '{if $endTime}{$endTime}{/if}'
                }
            }
        },
        methods: {
            handleClick(e, fid) {
                switch (e) {
                    case 0:
                    case 1:
                        var delivery = '已';
                        if (e === 1) {
                            delivery = '未';
                        }
                        layer.confirm('您确定要设置为' + delivery + '派奖吗？', {
                            btn: ['确定', '取消'], title: '提示'
                        }, function () {
                            $.post("{:url('tedious/deliver_rewards_contest_involved')}", {'fid': fid, 'action': e}, function (data) {
                                if (data.code > 0) {
                                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                                        location.reload();
                                    });
                                } else {
                                    layer.msg(data.msg, {icon: 5, time: 2200});
                                }
                            });
                        }, function (index) {
                            layer.close(index);
                        });
                        break;
                    case 2:
                        $.get("{:url('tedious/get_contest_involved_remark')}", {'fid': fid}, function (data) {
                            layer.alert(data.info ? data.info : '无备注', {'title': '备注信息'});
                        });
                        break;
                    case 3:
                        layer.prompt({title: '请输入备注内容 : '}, function (content) {
                            if ($.trim(content) === '') {
                                return;
                            }
                            $.post("{:url('tedious/update_contest_involved_remark')}", {
                                'fid': fid,
                                'content': content
                            }, function (data) {
                                if (data.code > 0) {
                                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                        location.reload();
                                    });
                                } else {
                                    layer.msg(data.msg, {icon: 5, time: 2000});
                                }
                            }, 'json');
                        });
                        break;
                }
            }
        }
    });

    function turtle() {
        function arrayToUrlParams(data) {
            return Object.keys(data)
                .map(key => {
                    const encodedKey = encodeURIComponent(key);
                    const encodedValue = data[key] !== null ? encodeURIComponent(data[key]) : '';
                    return encodedKey + '=' + encodedValue;
                })
                .join('&');
        }
        var data = arrayToUrlParams(vm.item);
        location.href = `{:url('tedious/involved_contest')}&${data}&page={$page}`;
    }

</script>
{/block}