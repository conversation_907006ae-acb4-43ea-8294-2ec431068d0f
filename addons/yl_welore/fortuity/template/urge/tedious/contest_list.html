{extend name="/base"/}
{block name="main"}
<style>
    .am-table-striped > tbody > tr:nth-child(odd) > td, .am-table > tbody > tr > td {
        line-height: 45px;
        text-align: center;
    }

    .am-table > thead:first-child > tr:first-child > th {
        text-align: center;
    }
</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 抽奖列表
        </div>
        <div style="text-align: right;">
            <div class="am-btn-toolbar" style="padding-top: 5px;">
                <a href="{:url('tedious/new_or_edit_contest_list')}" class="customize-span">
                    <span class="am-icon-adn"></span> 新增抽奖内容
                </a>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div id="search-list" style="display: flex;flex-wrap: wrap;width: 100%;min-height: 38px;">
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">ID</label>
                <div class="search-input-inline w-100">
                    <el-input class="search-input" v-model="item.fid" clearable></el-input>
                </div>
            </div>
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">抽奖名称</label>
                <div class="search-input-inline w-100">
                    <el-input class="search-input" v-model="item.lotteryName" clearable></el-input>
                </div>
            </div>
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">抽奖开始时间</label>
                <div class="search-input-inline">
                    <el-date-picker class="search-input" v-model="item.startTime" type="datetime" value-format="timestamp"
                                    placeholder="选择日期时间"></el-date-picker>
                </div>
            </div>
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">抽奖结束时间</label>
                <div class="search-input-inline">
                    <el-date-picker class="search-input" v-model="item.endTime" type="datetime" value-format="timestamp"
                                    placeholder="选择日期时间"></el-date-picker>
                </div>
            </div>
            <div class="search-wide-pitch search-inline" style="margin-left: 15px !important;">
                <label class="search-label">是否开奖</label>
                <div class="search-input-inline w-100">
                    <el-select class="search-input" v-model="item.isWinning" placeholder="请选择">
                        <el-option v-for="item in selectOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </div>
            </div>
            <button class="search-wide-pitch search-btn" onclick="turtle();">
                <i class="am-icon-search"></i> 搜 索
            </button>
        </div>
        <div class="am-g" style="margin-top: 10px;">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main-style-2 am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th width="11.11%">
                                ID
                            </th>
                            <th width="11.11%">
                                抽奖名称
                            </th>
                            <th width="11.11%">
                                抽取类型
                            </th>
                            <th width="11.11%">
                                已参与抽奖数量
                            </th>
                            <th width="11.11%">
                                抽奖开始时间
                            </th>
                            <th width="11.11%">
                                抽奖结束时间
                            </th>
                            <th width="11.11%">
                                开奖时间
                            </th>
                            <th width="11.11%">
                                是否开奖
                            </th>
                            <th width="11.11%">
                                操作
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                {$vo.id}
                            </td>
                            <td class="am-text-middle">
                                {$vo.lottery_name}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.extract_type==0}号池抽取{/if}
                                {if $vo.extract_type==1}随机抽取{/if}
                            </td>
                            <td class="am-text-middle">
                                {$vo.participant_num}
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$vo.start_time)}
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$vo.end_time)}
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$vo.draw_time)}
                            </td>
                            <td class="am-text-middle">
                                {if $vo.is_winning}已开奖{else}待开奖{/if}
                            </td>
                            <td class="am-text-middle no-wrap">
                                <el-dropdown size="small" split-button type="primary" trigger="click">
                                    功能菜单
                                    <el-dropdown-menu slot="dropdown">
                                        {if !$vo.is_winning}
                                        <el-dropdown-item @click.native="handleClick(0,'{$vo.id}')">
                                            立即开奖
                                        </el-dropdown-item>
                                        {else}
                                        <el-dropdown-item @click.native="handleClick(1,'{$vo.id}')">
                                            开奖结果
                                        </el-dropdown-item>
                                        {/if}
                                        <el-dropdown-item @click.native="handleClick(2,'{$vo.id}')">
                                            编辑抽奖
                                        </el-dropdown-item>
                                        <el-dropdown-item @click.native="handleClick(3,'{$vo.id}')">
                                            删除抽奖
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var vm = new Vue({
        el: '#app',
        data() {
            return {
                selectOptions: [{
                    value: '-1',
                    label: '请选择'
                }, {
                    value: '1',
                    label: '是'
                }, {
                    value: '0',
                    label: '否'
                }],
                item: {
                    fid: '{$fid}',
                    lotteryName: '{$lotteryName}',
                    startTime: '{if $startTime}{$startTime}{/if}',
                    endTime: '{if $endTime}{$endTime}{/if}',
                    isWinning: "{if $isWinning !== '' && intval($isWinning) !== -1}{:intval($isWinning)}{/if}"
                }
            }
        }, methods: {
            handleClick(e, fid) {
                switch (e) {
                    case 0:
                        layer.confirm('您确定要立即开奖吗？', {
                            btn: ['确定', '取消'], title: '提示'
                        }, function () {
                            $.post("{:url('tedious/now_draw_contest_list')}", {'fid': fid}, function (data) {
                                if (typeof data === 'string') {
                                    data = JSON.parse(data);
                                }
                                if (data.code > 0) {
                                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                                        location.reload();
                                    });
                                } else {
                                    layer.msg(data.msg, {icon: 5, time: 2200});
                                }
                            });
                        }, function (index) {
                            layer.close(index);
                        });
                        break;
                    case 1:
                        layer.open({
                            type: 2,
                            anim: 2,
                            title: false,
                            area: ['580px', '600px'],
                            scrollbar: false,
                            closeBtn: true,
                            shadeClose: true,
                            content: ["{:url('tedious/draw_contest_list_result')}&fid=" + fid],
                        });
                        break;
                    case 2:
                        location.href = "{:url('tedious/new_or_edit_contest_list')}&fid=" + fid;
                        break;
                    case 3:
                        layer.confirm('您确定要删除当前选择的数据吗？', {
                            btn: ['确定', '取消'], title: '提示'
                        }, function () {
                            $.post("{:url('tedious/delete_contest_list')}", {'fid': fid}, function (data) {
                                if (data.code > 0) {
                                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                                        location.reload();
                                    });
                                } else {
                                    layer.msg(data.msg, {icon: 5, time: 2200});
                                }
                            }, 'json');
                        }, function (index) {
                            layer.close(index);
                        });
                        break
                }
            }
        }
    });

    function turtle() {
        function arrayToUrlParams(data) {
            return Object.keys(data)
                .map(key => {
                    const encodedKey = encodeURIComponent(key);
                    const encodedValue = data[key] !== null ? encodeURIComponent(data[key]) : '';
                    return encodedKey + '=' + encodedValue;
                })
                .join('&');
        }

        var data = arrayToUrlParams(vm.item);
        location.href = `{:url('tedious/contest_list')}&${data}&page={$page}`;
    }

</script>
{/block}