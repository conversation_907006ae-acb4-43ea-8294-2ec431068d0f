{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:86px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}.resth button{margin-right:5px;}.cust-btn{font-size:14px;padding:5px 10px;}.cust-btn-one{margin-left:-20px;}.cust-btn-activate{border-bottom:solid 2px cornflowerblue;}.cust-aver-img{width:82px;height:82px;position:absolute;top:11px;border:1px solid #cccccc;border-radius:3px;}.cust-noble-img{background-image:url('static/disappear/icon_coupon.png');background-size:40px;background-repeat:no-repeat;width:82px;height:82px;position:absolute;top:11px;border:1px solid #cccccc;border-radius:3px;}.customize-span{padding:3px 10px;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 商品规格
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="turtle('all');"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索规格名称...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-6 am-u-md-7">
                <div class="am-btn-toolbar" style="margin-top: -10px;">
                    <div class="am-btn-group am-btn-group-xs" style="margin-left:-2px;">
                        <span class="customize-span" data-am-modal="{target: '#shandsel', closeViaDimmer: 0, width: 400, height: 200}">
                            <span class="am-icon-adn"></span> 新增规格
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form" style="overflow-x:auto;">
                    <table class="am-table am-table-striped am-table-hover table-main">
                        <thead>
                        <tr>
                            <th class="no-wrap text-center" width="25%">ID</th>
                            <th class="no-wrap text-center" width="25%">规格名称</th>
                            <th class="no-wrap text-center" width="25%">创建时间</th>
                            <th class="no-wrap text-center" width="25%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="no-wrap text-center">
                               {$vo.id}
                            </td>
                            <td class="no-wrap text-center">
                                <span title="{$vo.at_name}">
                                    {$vo.at_name}
                                </span>
                            </td>
                            <td class="no-wrap text-center">
                                {:date('Y-m-d H:i:s',$vo.create_time)}
                            </td>
                            <td class="no-wrap text-center">
                                <span data-am-modal="{target: '#shandsel', closeViaDimmer: 0, width: 400, height: 200}" onclick="editContent('{$vo.id}','{$vo.at_name}');">
                                    <button type="button" class="am-btn am-btn-default am-btn-xs am-text-secondary">
                                        <span class="am-icon-pencil-square-o"></span>编辑
                                    </button>
                                </span>
                                <button type="button" class="am-btn am-btn-default am-btn-xs am-text-danger" onclick="synthesisDel('{$vo.id}','{$vo.ranking_name}');">
                                    <span class="am-icon-trash-o"></span>删除
                                </button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                </div>
                <div class="am-u-sm-12 no-wrap" style="text-align:center;">
                    {$list->render()}
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel">
        <div class="am-modal-dialog" style="background:#fefefe;">
            <div class="am-modal-hd">
                <span class="am-modal-custom-name" style="font-size: 14px;position: absolute;left:12px;top:7px;">新增规格</span>
                <a id="euModalClose" href="javascript: void(0);" class="am-close am-close-spin" data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd am-form tpl-form-line-form">
                <div class="am-form-group" style="margin-top:35px;">
                    <label class="am-u-sm-4 am-form-label" style="font-size: 15px;margin:2px 0 0 -5px;">规格名称</label>
                    <div class="am-u-sm-8">
                        <input type="hidden" name="fid" value="0">
                        <input type="text" name="atName" class="tpl-form-input" style="margin:3px 0 0 -20px;" placeholder="请输入规格名称">
                    </div>
                </div>
                <div class="am-u-sm-9 am-u-sm-push-1" style="margin-top:10px;">
                    <button type="button" class="am-btn am-btn-sm hold-save" style="border:1px solid #ccc;" onclick="sendGifts(0);">
                        确定保存
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    $(function () {
        $('#euModalClose').click(function () {
            $('[name=\'fid\']').val('0');
            $('[name=\'atName\']').val('');
            $('.am-modal-custom-name').text('新增规格');
            $('.hold-save').attr('onclick', 'sendGifts(0);');
        });
    });

    function editContent(fid, atName) {
        $('.am-modal-dialog [name=\'fid\']').val(fid);
        $('.am-modal-dialog [name=\'atName\']').val(atName);
        $('.am-modal-dialog .am-modal-custom-name').text('编辑规格');
        $('.am-modal-dialog .hold-save').attr('onclick', 'sendGifts(1);');
    }


    var isLock = false;
    var sendGifts = function (type) {
        if (!isLock) {
            isLock = true;
            var postUrl = "{:url('tedious/add_shop_specs')}";
            var setData = {};
            if (type === 1) {
                postUrl = "{:url('tedious/update_shop_specs')}";
                setData['fid'] = $('.am-modal-dialog [name=\'fid\']').val();
            }
            setData['atName'] = $('.am-modal-dialog [name=\'atName\']').val();
            $.post(postUrl, setData, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        isLock = false;
                    });
                }
            }, 'json');
        }
    }

    function synthesisDel(mid, vname) {
        var shint = '您确定要 <span style="color: red">删除</span> <span style="color: blue;">' + vname + '</span> 吗？';
        layer.confirm(shint, {
            btn: ['确定', '取消'], 'title': '删除提示 (<span style="color: #0066ee;">数据将不可恢复</span>)'
        }, function () {
            $.post("{:url('tedious/del_shop_specs')}", {'fid': mid},
                function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

    function turtle() {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('tedious/shop_specs')}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('tedious/shop_specs')}&page={$page}";
        }
    }

</script>
{/block}