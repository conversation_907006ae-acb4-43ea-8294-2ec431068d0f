<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <title>表情包</title>
  <link rel="stylesheet" href="./assets/css/amazeui.min.css"/>
  <script src="./assets/js/jquery.min.js"></script>
</head>
<body style="background: rgb(243,243,243);">
<div class="tpl-content-wrapper">
  <div class="am-g tpl-amazeui-form" style="width: 100%;height: 360px;overflow-y: auto;">
    <div class="am-form am-form-horizontal">
      <div style="display: flex;flex-direction: column;align-items: center;padding-top: 3px;">
        <div style="width: 100%;height: 100%;display: flex;flex-wrap: wrap;">
          {volist name="emojiList" id="vo"}
            <span style="cursor: pointer;" onclick="callFunc('{$vo}')">
              <div style="width: 50px;height: 50px;display: flex;justify-content: center;align-items: center;">
                <img src="{:urlBridging('static/expression/'.$vo)}" style="margin:0 3px;height: 30px;width: 30px;vertical-align: sub">
              </div>
            </span>
          {/volist}
        </div>
      </div>
    </div>
  </div>
</div>
</body>
<script>
  var callFunc = function (val) {
    var data = {};
    data['content'] = '[#:' + val.substring(0, val.lastIndexOf('.')) + ']';
    parent.complicatedCallFunc(Number('{$type}'), data);
  }
</script>
</html>