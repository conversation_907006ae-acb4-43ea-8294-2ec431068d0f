{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:45px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}.resth button{margin-right:5px;}.cust-btn{font-size:14px;padding:5px 10px;}.cust-btn-one{margin-left:-20px;}.cust-btn-activate{border-bottom:solid 2px cornflowerblue;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-bar-chart"></span> 赠礼排行榜
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索用户名...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-6 am-u-md-7">
                <div class="am-btn-toolbar" style="margin: 0px 0px 10px 20px;">
                    <div class="am-btn-group am-btn-group-xs resth">
                        <a href="{:url('user/gift_ranking')}&egon=0" class="cust-btn cust-btn-one {if $egon==0}cust-btn-activate{/if}">全部排行</a>
                        <a href="{:url('user/gift_ranking')}&egon=1" class="cust-btn {if $egon==1}cust-btn-activate{/if}">本周排行</a>
                        <a href="{:url('user/gift_ranking')}&egon=2" class="cust-btn {if $egon==2}cust-btn-activate{/if}">本月排行</a>
                        <a href="{:url('user/gift_ranking')}&egon=3" class="cust-btn {if $egon==3}cust-btn-activate{/if}">今年排行</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <form class="am-form">
                    <table class="am-table table-main am-table-bordered am-table-radius am-table-striped">
                        <thead>
                        <tr>
                            <th width="16.66%">用户名称</th>
                            <th width="16.66%">用户头像</th>
                            <th width="16.66%">openid</th>
                            <th width="16.66%">赠送次数</th>
                            <th width="16.66%">赠送详情</th>
                            <th width="16.67%">金额</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td>
                                <img src="{$vo.user_head_sculpture}" style="width:70px;height:70px;border-radius:50%;">
                            </td>
                            <td>
                                {if $vo.user_wechat_open_id}
                                {$vo.user_wechat_open_id}
                                {else}
                                ( 虚拟用户 )
                                {/if}
                            </td>
                            <td>{$vo.cid} 次</td>
                            <td>
                                <span style="background: #75adbf;border-radius: 3px;color: white;padding: 5px 10px;cursor: pointer;"
                                      onclick="tance('{$vo.id}');">
                                    查 看
                                </span>
                            </td>
                            <td>
                                {if $vo.sprice}{$vo.sprice}{else}0.00{/if} ( {$defaultNavigate.currency} )
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </form>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var tance = function (reid) {
        var page = 1;
        layer.open({
            type: 2,
            id: 'erpUp',
            title: '赠送详情',
            shadeClose: true,
            shade: 0.8,
            area: ['600px', '80%'],
            scrollbar: true,
            content: "{:url('user/giftUnderstand')}&type=0&carton={$egon}&reid=" + reid,
            success: function () {
                $($('#erpUp iframe')[0].contentWindow).scroll(function () {
                    var iframeScrollHeight = $("#erpUp iframe").get(0).contentWindow.document.body.scrollHeight;
                    var iframeScrollTop = $($('#erpUp iframe')[0].contentWindow).scrollTop();
                    var iframeClientHeight = $($('#erpUp iframe')[0].contentWindow).height();
                    if (iframeScrollHeight - iframeClientHeight == iframeScrollTop) {
                        $.ajaxSettings.async = false;
                        $.post("{:url('user/giftUnderstand')}", {
                            type: 0,
                            carton: '{$egon}',
                            reid: reid,
                            jetty: 1,
                            page: ++page
                        }, function (data) {
                            if (data.length != 0) {
                                for (var i = 0; i < data.length; i++) {
                                    var html = '<tr>';
                                    html += '<td class="am-table-centered am-text-middle" title="' + data[i].user_nick_name + '">';
                                    html += '    <img src="' + data[i].user_head_sculpture + '" style="width:50px;height:50px;border-radius:50%;">';
                                    html += '    <span style="position:relative;top:5px;font-size:12px;">';
                                    if ($.trim(data[i].user_wechat_open_id) != '') {
                                        html += '    <a class="user-name" href="{:url(\'user/index\')}&egon=0&openid=' + data[i].user_wechat_open_id + '&page=1" target="_blank">' + data[i].user_nick_name + '</a>';
                                    } else {
                                        html += '    <a class="user-name" href="{:url(\'user/theoretic\')}&hazy_name=' + $.trim(data[i].user_nick_name) + '&page=1" target="_blank">' + data[i].user_nick_name + '</a>';
                                    }
                                    html += '</span>';
                                    html += '</td>';
                                    html += '<td class="am-table-centered am-text-middle">';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">' + '物品名称</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">' + data[i].bute_name + '</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">物品单价</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">' + data[i].bute_price + ' ( {$defaultNavigate.currency} )</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">收益利率</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">' + data[i].allow_scale * 100 + '%</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">结算汇率</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;"> 1 : ' + data[i].exchange_rate + '</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;border-right:#dddddd solid 1px;">结算金额</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-bottom:#dddddd solid 1px;">';
                                    html += '    ' + (data[i].bute_price * data[i].num * data[i].allow_scale) + ' ( {$defaultNavigate.currency} )';
                                    html += '    </div>';
                                    html += '    <div style="float:left;width:50%;height:50%;border-right:#dddddd solid 1px;">受赠时间</div>';
                                    html += '    <div style="float:left;width:50%;height:50%;">' + data[i].bute_time + '</div>';
                                    html += '</td>';
                                    html += '</tr>';
                                    $($("#erpUp iframe").get(0).contentWindow.document).find('tbody').append(html);
                                }
                            } else {
                                --page;
                            }
                        }, 'json');
                        $.ajaxSettings.async = true;
                    }
                });
            }
        });
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('user/gift_ranking')}&egon={$egon}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('user/gift_ranking')}&egon={$egon}&page={$page}";
        }
    }

</script>
{/block}