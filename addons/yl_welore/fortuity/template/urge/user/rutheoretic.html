{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 新增虚拟用户
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-8 am-u-sm-push-1">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label for="name" class="am-u-sm-3 am-form-label">虚拟用户名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="name" placeholder="请输入虚拟用户名称" maxlength="14">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">虚拟用户头像</label>
                        <div class="am-u-sm-9">
                            <img src="" id="shion" onerror="this.src='static/disappear/default.png'" onclick="cuonice();" style="width: 100px;height: 100px;cursor: pointer;border-radius: 50%;"/>
                            <button type="button" style="margin-left:10px;font-size: 12px;" onclick="cuonice();">
                                选择图片
                            </button>
                            <small>建议图片尺寸：132*132px</small>
                            <input type="hidden" name="sngimg">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">虚拟用户性别</label>
                        <div class="am-u-sm-9">
                            <select id="gender">
                                <option value="1">男</option>
                                <option value="2">女</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">虚拟用户等级</label>
                        <div class="am-u-sm-9">
                            <select id="level">
                                {if $userLevel}
                                {volist name="userLevel" id="vo"}
                                <option value="{$vo.level_hierarchy}" {if $list.level==$vo.level_hierarchy}selected{/if}>
                                    Lv.{$vo.level_hierarchy} - {$vo.level_name}
                                </option>
                                {/volist}
                                {else}
                                <option value="0">初始等级</option>
                                {/if}
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">会员到期时间</label>
                        <div class="am-u-sm-9">
                            <div style="margin:3px 0 0 -10px;" class="am-input-group am-datepicker-date"data-am-datepicker="{format: 'yyyy-mm-dd', viewMode: 'day'}">
                                <input id="vipEndTime" type="text" style="padding-left:10px;margin-left: 10px;" class="am-form-field" placeholder="请选择会员到期时间" readonly>
                                <span class="am-input-group-btn am-datepicker-add-on">
                                    <button class="am-btn am-btn-default" type="button">
                                        <span class="am-icon-calendar"></span>
                                    </button>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">虚拟粉丝数量</label>
                        <div class="am-u-sm-9">
                            <input id="virtualFansNum" type="number" value="{$list.virtual_fans_num}" placeholder="请输入用户虚拟粉丝数量">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">虚拟用户个性签名</label>
                        <div class="am-u-sm-9">
                            <textarea id="autograph" style="margin-top:4px;height: 300px;resize: none;" placeholder="请填写虚拟用户个性签名"></textarea>
                        </div>
                    </div>
                    <div class="am-form-group" style="display: flex;justify-content: center;margin: 80px 0 50px 0;">
                        <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    var cuonice = function () {
        layer.open({
            type: 2,
            anim:2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    var sutake = function (eurl) {
        $('#shion').attr('src', eurl);
        $("[name='sngimg']").val(eurl);
        layer.closeAll();
    }


    var unlock = false;
    var holdSave = function () {
        var setDate = {};
        setDate['name'] = $.trim($('#name').val());
        if (setDate['name'] == '') {
            layer.msg('请输入虚拟用户名称');
            return;
        }
        setDate['avatar'] = $.trim($('[name=\'sngimg\']').val());
        if (setDate['avatar'] == '') {
            layer.msg('请选择虚拟用户头像');
            return;
        }
        setDate['level'] = $.trim($('#level').val());
        setDate['gender'] = $.trim($('#gender').val());
        setDate['phone'] = $.trim($('#phone').val());
        setDate['vipEndTime'] = $.trim($('#vipEndTime').val());
        setDate['resetVipEndTime'] = $('#resetVipEndTime').prop('checked') ? 1 : 0;
        setDate['virtualFansNum'] = $.trim($('#virtualFansNum').val());
        setDate['autograph'] = $.trim($('#autograph').val());
        if(!unlock){
            unlock = true;
            $.post("{:url('user/rutheoretic')}",setDate,function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.href = "{:url('user/theoretic')}";
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000},function () {
                        unlock = false;
                    });
                }
            });
        }
    }
</script>
{/block}