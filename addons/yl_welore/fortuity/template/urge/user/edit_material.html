{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-edit"></span> 编辑{if $list.uvirtual==0}用户资料{else}虚拟用户{/if}
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-8 am-u-sm-push-1">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">{if $list.uvirtual==1}虚拟{/if}用户名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="name" value="{$list.user_nick_name|emoji_decode}" placeholder="请输入用户名称" maxlength="14">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">{if $list.uvirtual==1}虚拟{/if}用户头像</label>
                        <div class="am-u-sm-9">
                            <img src="{$list.user_head_sculpture}" id="shion" onerror="this.src='static/disappear/default.png'" onclick="cuonice();" style="width: 100px;height: 100px;cursor: pointer;border-radius: 50%;"/>
                            <button type="button" style="margin-left:10px;font-size: 12px;" onclick="cuonice();">
                                选择图片
                            </button>
                            <small>建议图片尺寸：132*132px</small>
                            <input type="hidden" value="{$list.user_head_sculpture}" name="sngimg">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">{if $list.uvirtual==1}虚拟{/if}用户性别</label>
                        <div class="am-u-sm-9">
                            <select id="gender">
                                <option value="1" {if $list.gender==1}selected{/if}>男</option>
                                <option value="2" {if $list.gender==2}selected{/if}>女</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">{if $list.uvirtual==1}虚拟{/if}用户等级</label>
                        <div class="am-u-sm-9">
                            <select id="level">
                                {if $userLevel}
                                {volist name="userLevel" id="vo"}
                                <option value="{$vo.level_hierarchy}" {if $list.level==$vo.level_hierarchy}selected{/if}>
                                    Lv.{$vo.level_hierarchy} - {$vo.level_name}
                                </option>
                                {/volist}
                                {else}
                                <option value="0">初始等级</option>
                                {/if}
                            </select>
                            {if $list.level > $userLevel[count($userLevel)-1]['level_hierarchy']}
                            <small style="color:red;">该用户的等级超出了设定范围，请重新选择</small>
                            {/if}
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">佩戴勋章</label>
                        <div class="am-u-sm-9">
                            <select id="medal">
                                <option value="0">请选择</option>
                                {if $medalInfo}
                                {volist name="medalInfo" id="vo"}
                                <option value="{$vo.id}" {if $list.wear_merit==$vo.id}selected{/if}>
                                    {$vo.merit_name}
                                </option>
                                {/volist}
                                {/if}
                            </select>
                        </div>
                    </div>
                    <!--
                    <div class="am-form-group" {if $list.uvirtual==1}style="display:none;"{/if}>
                        <label class="am-u-sm-3 am-form-label">当前经验值</label>
                        <div class="am-u-sm-9">
                            <input id="exp" type="number" value="{$list.experience}" oninput="if(value.length>22)value=value.slice(0,22)" placeholder="请输入用户经验值">
                        </div>
                    </div>
                    <div class="am-form-group" {if $list.uvirtual==1}style="display:none;"{/if}>
                        <label class="am-u-sm-3 am-form-label">当前荣誉点</label>
                        <div class="am-u-sm-9">
                            <input id="glory" type="number" value="{$list.honor_point}" oninput="if(value.length>22)value=value.slice(0,22)" placeholder="请输入用户荣誉点">
                        </div>
                    </div>
                    -->
                    <div class="am-form-group" {if $list.uvirtual==1}style="display:none;"{/if}>
                        <label class="am-u-sm-3 am-form-label">手机号</label>
                        <div class="am-u-sm-9">
                            <input id="phone" type="number" value="{$list.user_phone}" oninput="if(value.length>11)value=value.slice(0,11)" placeholder="请输入用户手机号">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">会员到期时间</label>
                        <div class="am-u-sm-9">
                            <div style="margin:3px 0 0 0;" class="am-input-group am-datepicker-date"data-am-datepicker="{format: 'yyyy-mm-dd', viewMode: 'day'}">
                                <input id="vipEndTime" type="text" style="padding-left:10px;" class="am-form-field" placeholder="请选择会员到期时间" value="{:date('Y-m-d',$list.vip_end_time)}" readonly>
                                <span class="am-input-group-btn am-datepicker-add-on">
                                    <button class="am-btn am-btn-default" type="button">
                                        <span class="am-icon-calendar"></span>
                                    </button>
                                </span>
                            </div>
                            <div {if $list.uvirtual==1}style="display:none;"{/if}>
                                <div style="display: flex;width: 100%;margin: 3px 0 0 -8px;">
                                    <input id="resetVipEndTime" type="checkbox">
                                    <span style="font-size: 12px;margin-left: 5px;font-weight: 500;">会员到期时间重置 ( 重置后再次开通可享受首次折扣 )</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">{if $list.uvirtual==1}虚拟{/if}用户关注隐私权限</label>
                        <div class="am-u-sm-9">
                            <select id="isEnableConcernPrivacy">
                                <option value="1" {if $list.is_enable_concern_privacy==1}selected{/if}>开启</option>
                                <option value="0" {if $list.is_enable_concern_privacy==0}selected{/if}>关闭</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">{if $list.uvirtual==1}虚拟{/if}用户粉丝隐私权限</label>
                        <div class="am-u-sm-9">
                            <select id="isEnableFansPrivacy">
                                <option value="1" {if $list.is_enable_fans_privacy==1}selected{/if}>开启</option>
                                <option value="0" {if $list.is_enable_fans_privacy==0}selected{/if}>关闭</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">虚拟粉丝数量</label>
                        <div class="am-u-sm-9">
                            <input id="virtualFansNum" type="number" value="{$list.virtual_fans_num}" placeholder="请输入用户虚拟粉丝数量">
                            <small>需要开启{if $list.uvirtual==1}虚拟{/if}用户粉丝隐私权限后生效</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">{if $list.uvirtual==1}虚拟{/if}用户个性签名</label>
                        <div class="am-u-sm-9">
                            <textarea id="autograph" style="margin-top:4px;height: 300px;resize: none;" placeholder="请填写个性签名">{$list.autograph}</textarea>
                        </div>
                    </div>
                    <div class="am-form-group" style="display: flex;justify-content: center;margin: 80px 0 50px 0;">
                        <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    var cuonice = function () {
        layer.open({
            type: 2,
            anim:2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }
    
    var sutake = function (eurl) {
        $('#shion').attr('src', eurl);
        $("[name='sngimg']").val(eurl);
        layer.closeAll();
    }


    var holdSave = function () {
        var setDate = {};
        setDate['uid'] = '{$list.id}';
        setDate['name'] = $.trim($('#name').val());
        if (setDate['name'] == '') {
            layer.msg('请输入{if $list.uvirtual==1}虚拟{/if}用户名称');
            return;
        }
        setDate['avatar'] = $.trim($('[name=\'sngimg\']').val());
        setDate['gender'] = $.trim($('#gender').val());
        setDate['level'] = $.trim($('#level').val());
        setDate['medal'] = $.trim($('#medal').val());
        setDate['exp'] = $.trim($('#exp').val());
        setDate['glory'] = $.trim($('#glory').val());
        setDate['phone'] = $.trim($('#phone').val());
        setDate['vipEndTime'] = $.trim($('#vipEndTime').val());
        setDate['resetVipEndTime'] = $('#resetVipEndTime').prop('checked') ? 1 : 0;
        setDate['virtualFansNum'] = $.trim($('#virtualFansNum').val());
        setDate['autograph'] = $.trim($('#autograph').val());
        setDate['autograph'] = $.trim($('#autograph').val());
        setDate['isEnableConcernPrivacy'] = $.trim($('#isEnableConcernPrivacy').val());
        setDate['isEnableFansPrivacy'] = $.trim($('#isEnableFansPrivacy').val());
        $.post("{:url('user/editMaterial')}",setDate,function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.href = "{if $list.uvirtual==0}{:url('user/material')}&usid={$list.id}{else}{:url('user/theoretic')}{/if}";
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000});
            }
        });
    }
</script>
{/block}