{extend name="/base"/}
{block name="main"}
<style>[v-cloak]{display:none!important}.am-modal-hd .am-close{top:-5px;right:-3px;}.w-e-menu{font-size:12px;}.w-e-text,.w-e-text-container{height:500px !important;}#word{position:absolute;z-index:10002;height:auto;background-color:white;border:black solid 1px;padding:4px 0.43em;}.click-work{font-size:14px;cursor:pointer;z-index:10003;font-weight:bold;}.click-work:hover{color:black;background-color:#f0f0f0;z-index:10003;}.el-input__inner{width:100% !important;margin-top:-1px !important;padding-left:30px !important;}.el-picker-panel.el-date-picker.el-popper{z-index:10001 !important;}</style>
<div id="app" class="tpl-portlet-components"  v-cloak>
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-edit"></span> 发表帖子
        </div>
        <div class="tpl-portlet-input tpl-fz-ml right">
            <a href="javascript:void(0);" @click="openArticleModal">
                <span style="font-size:12px;font-weight:bold;color:black;padding:4px 9px;background: #98b2b5;">
                    微信公众号文章采集
                </span>
            </a>
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">用户昵称</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input :title="userInfo.nickName" type="text" :value="userInfo.nickName" disabled>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">帖子标题</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" v-model="postData.title">
                            <small>请输入帖子标题</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">标题颜色</label>
                        <div class="am-u-sm-8 am-u-end">
                            <div style="display: flex;">
                                <input type="text" v-model="postData.titleColor">
                                <el-color-picker v-model="postData.titleColor"></el-color-picker>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">帖子类型</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select v-model="postData.patype" @change="changePostType">
                                <option value="0">图文帖</option>
                                <option value="1">语音帖</option>
                                <option value="2">视频帖</option>
                                <option value="3">活动帖</option>
                                <option value="4">单选投票帖</option>
                                <option value="5">多选投票帖</option>
                                <option value="6">视频号视频</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label" title="付费帖子">付费帖子</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select v-model="postData.isBuy" @change="changeBuyStatus">
                                <option value="0">关闭</option>
                                <option value="1">内容付费</option>
                                {if $netDiscPluginKey}
                                <option value="2">文件付费</option>
                                <option value="3">整体付费</option>
                                {/if}
                            </select>
                        </div>
                    </div>
                    <div v-show="showPayHidden">
                        {if $netDiscPluginKey}
                        <div v-show="showPayFileHidden">
                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label">
                                    付费文件
                                </label>
                                <div class="am-u-sm-8 am-u-end">
                                    <span @click="selectFiles(userInfo.id)">
                                        <div id="fileNames"
                                             style="width:100%; background: #eee; color: #555; font-size:1.2rem; line-height: 1.2; border: 1px solid #ccc; padding: 1rem; cursor: pointer; display: inline-block;">
                                            {{ fileNames }}
                                        </div>
                                    </span>
                                </div>
                            </div>
                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-form-label" title="二次售卖">二次售卖</label>
                                <div class="am-u-sm-8 am-u-end">
                                    <select v-model="postData.fileIsSell">
                                        <option value="1">允许</option>
                                        <option value="0">禁止</option>
                                    </select>
                                    <small>
                                        <i style="color: #0f9ae0;">
                                            是否允许文件被其他用户二次售卖
                                        </i>
                                    </small>
                                </div>
                            </div>
                        </div>
                        {/if}
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label" title="付费帖子">付费类型</label>
                            <div class="am-u-sm-8 am-u-end">
                                <select v-model="postData.buyPriceType">
                                    <option value="0">{$defaultNavigate.currency}支付</option>
                                    <option value="1">{$defaultNavigate.confer}支付</option>
                                    <!--
                                    <option value="2">微信支付</option>
                                    -->
                                </select>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">
                                付费售价
                            </label>
                            <div class="am-u-sm-8 am-u-end">
                                <input type="text" v-model="postData.buyPrice" @input="postData.buyPrice = digitalCheck(postData.buyPrice, 2)">
                            </div>
                        </div>
                    </div>
                    {if $callPhonePluginKey}
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">联系方式</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" v-model="postData.callPhone" @input="postData.callPhone = digitalCheck(postData.callPhone, 0)">
                            <small>用户可直接在帖子内拨打电话（插件功能 非必填项）</small>
                        </div>
                    </div>
                    {/if}
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">所属圈子</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select v-model="postData.toryid">
                                {volist name="toryInfo" id="vo"}
                                <option value="{$vo.id}">{$vo.realm_name}</option>
                                {/volist}
                            </select>
                        </div>
                    </div>
                    <div v-show="showSelectBrisk">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">活动认证</label>
                            <div class="am-u-sm-8 am-u-end">
                                <select v-model="postData.briskApprove">
                                    <option value="0">未认证</option>
                                    <option value="1">已认证</option>
                                </select>
                                <small>活动真实性是否已经经过认证</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">活动地址</label>
                            <div class="am-u-sm-8 am-u-end">
                                <input type="text" v-model="postData.briskAddress" placeholder="请输入活动地址">
                                <small>
                                    <a href="https://lbs.amap.com/tools/picker" target="_blank" style="margin-right:10px;">点击打开地图获取坐标</a>
                                    例如 北京市天安门广场 坐标为 : ( 116.397724,39.903755 ) 其中 116.xxx 为经度 39.xxx 为纬度
                                </small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">坐标经度</label>
                            <div class="am-u-sm-3">
                                <input type="text" v-model="postData.briskAddressLongitude" placeholder="请输入活动地址坐标经度">
                            </div>
                            <label class="am-u-sm-2 am-form-label" style="white-space:nowrap;">坐标纬度</label>
                            <div class="am-u-sm-3 am-u-end">
                                <input type="text" v-model="postData.briskAddressLatitude" placeholder="请输入活动地址坐标纬度">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label" style="white-space:nowrap;">活动开始时间</label>
                            <div class="am-u-sm-3">
                                <input id="dateStartTime" type="text" placeholder="请选择活动开始时间" readonly style="cursor: pointer;">
                            </div>
                            <label class="am-u-sm-2 am-form-label" style="white-space:nowrap;">活动结束时间</label>
                            <div class="am-u-sm-3 am-u-end">
                                <input id="dateEndTime" type="text" placeholder="请选择活动结束时间" readonly style="cursor: pointer;">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">活动人数</label>
                            <div class="am-u-sm-8 am-u-end">
                                <input type="number" v-model="postData.numberOfPeople" placeholder="请输入活动参加人数">
                                <small>设置数值 0 为不限制报名人数</small>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">话题名称</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" v-model="postData.gambit" @keyup="loadGambitSuggestions" placeholder="输入话题名称前后不要带 # 号 例如 #话题# 直接输入 话题 即可">
                            <div id="word" v-show="wordVisible">
                                <div class="click-work" v-for="item in wordSuggestions" @click="selectGambit(item.s)">{{ item.s }}</div>
                            </div>
                        </div>
                    </div>

                    <div v-show="showSelectBallot" class="am-form-group">
                        <div style="width:auto;height:55px;">
                            <label class="am-u-sm-3 am-form-label">投票选项</label>
                            <label class="am-u-sm-3 am-text-sm" style="padding-top:6px;">
                                至少两项，建议每项不超过二十个字
                            </label>
                            <label class="am-u-sm-5 am-text-sm am-u-end" style="text-align:right;padding-top:6px;">
                                <span style="border:1px solid;padding:2px 5px;color:#bb841e;cursor:pointer;" @click="addVoteOption">
                                    ＋ 添加选项
                                </span>
                            </label>
                        </div>
                        <div style="width:auto;height:60px">
                            <label class="am-u-sm-3 am-form-label">投票截止时间</label>
                            <div class="am-u-sm-8 am-u-end">
                                <input id="voteDeadline" type="text" placeholder="请选择投票截止时间" readonly>
                            </div>
                        </div>
                        <div v-for="(option, index) in postData.voteOptions" :key="index" style="width:auto;height:80px;">
                            <div class="am-form-group">
                                <label class="am-u-sm-3 am-text-sm" style="text-align:right;padding-top:8px;">选项 {{ index + 1 }}.</label>
                                <div class="am-u-sm-3">
                                    <input type="text" v-model="option.option" placeholder="请填写投票选项">
                                    <small style="color:#ef5f5f;">此项为必填项</small>
                                </div>
                                <label class="am-u-sm-2 am-text-sm" style="text-align:right;padding-top:8px;">虚假票数</label>
                                <div class="am-u-sm-3 am-u-end">
                                    <input type="number" v-model="option.fakeVotes" placeholder="请填写虚假票数">
                                    <small style="color:#b56b6b;">可设置虚假选票数量 如不需要可不填写</small>
                                </div>
                                <div class="am-u-sm-1" v-if="index >= 2">
                                    <span style="font-size:24px;margin-left:-10px;cursor:pointer;" title="点击删除此选项" @click="removeVoteOption(index)">
                                        ×
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-show="showSelectAudio" class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">音频文件</label>
                        <div class="am-u-sm-9" style="margin-top:4px;font-size:12px;">
                            <audio id="audioFile" controls="controls" style="width:88%;">
                                <source src="" type="audio/mpeg">
                                您的浏览器不支持标签。
                            </audio>
                            <input type="file" width="20%" @change="fileUpload(0, $event)">
                        </div>
                    </div>

                    <div v-show="showSelectVideo">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">视频类型</label>
                            <div class="am-u-sm-8 am-u-end">
                                <select v-model="postData.videoType" @change="changeVideoType">
                                    <option value="0">本地视频</option>
                                    <option value="1">腾讯视频</option>
                                    {if $videoParsePluginKey}
                                    <option value="2">视频解析</option>
                                    {/if}
                                </select>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">{{ thirdPartyVideoName }}</label>
                            <div class="am-u-sm-8 am-u-end" style="display: flex;">
                                <input type="text" v-model="postData.tencentVideoVid">
                                <button type="button" class="am-btn am-btn-default am-btn-xs" @click="loadThirdPartyVideo">加载视频</button>
                            </div>
                        </div>

                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">视频文件</label>
                            <div class="am-u-sm-9" style="margin-top:4px;font-size:12px;">
                                <video id="videoFile" controls="controls" crossorigin="anonymous" style="width:88%;height:362px;background:#9db3bf;" @loadeddata="captureImage($event.target)">
                                    您的浏览器不支持视频标签。
                                </video>
                                <div id="videoTypeSelectOne" v-show="postData.videoType == 0">
                                    <input type="file" width="20%" accept="video/mp4" @change="fileUpload(1, $event)">
                                </div>
                                <div style="width: 88%;height: 362px;display: flex;justify-content: center;border:1px dashed #ccc;background: url('./static/wechat/image_vip_top.jpg') no-repeat;background-size:cover;margin-top: 10px;">
                                    <div style="width: 100%;height: 100%;cursor: pointer;text-align: center;" title="点击选择视频封面图" @click="openImageDialog(0)">
                                        <img id="videoCoverImage" style="width: 100%;height: 362px;object-fit: contain;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">图片类型</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select v-model="postData.imgShowType">
                                <option value="0">默认样式</option>
                                <option value="1">九宫格</option>
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group" v-if="postData.patype == 6">
                        <label class="am-u-sm-3 am-form-label">feed-token</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" v-model="postData.feedToken">
                            <small style="color: indianred;font-weight: bold;">登陆MP平台，在「设置-基本设置-隐私与安全」找到「获取视频号视频ID权限」，并将开关打开<br>在移动端找到想要内嵌的视频号视频，并复制该视频的feed-token</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">发布时间</label>
                        <div class="am-u-sm-8 am-u-end">
                            <el-date-picker style="width: 100%;" v-model="adapterTime" type="datetime" value-format="timestamp" placeholder="选择日期时间"></el-date-picker>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">帖子内容</label>
                        <div class="am-u-sm-8 am-u-end">
                            <div id="detail" style="min-height:600px;"></div>
                            <span id="customizeGallery" style="display:none;" @click="openImageDialog(1)"></span>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <div class="am-u-sm-6 am-u-sm-push-6">
                            <span style="padding:10px 20px;background:#479fcd;color:white;cursor:pointer;border-radius:10px;" @click="submitForm">发 布 帖 子</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="am-modal am-modal-prompt" tabindex="-1" id="network-article" style="z-index:10005;">
        <div class="am-modal-dialog">
            <div class="am-modal-hd">微信公众号文章地址</div>
            <div class="am-modal-bd">
                <input v-model="modalArticleUrl" type="text" class="am-modal-prompt-input">
            </div>
            <div class="am-modal-footer">
                <span class="am-modal-btn" data-am-modal-confirm>确定</span>
                <span class="am-modal-btn" data-am-modal-cancel>取消</span>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script src="static/datetime/laydate.js"></script>
<script type="text/javascript" src="static/wangEditor/wangEditor.min.js?time={:time()}"></script>
<link rel="stylesheet" href="static/wangEditor/wangEditor-fullscreen-plugin.css?time={:time()}">
<script type="text/javascript" src="static/wangEditor/wangEditor-fullscreen-plugin.js?time={:time()}"></script>
<script>
    // 全局函数，用于接收文件选择回调
    var filesRefrain = function (nid, fid, fileName) {
        // 调用Vue实例中的方法
        if (vm) {
            vm.setFileInfo(nid, fid, fileName);
        }
    }

    // 全局函数，用于接收图片选择回调
    var sutake = function (eurl, richText) {
        // 调用Vue实例中的方法
        if (vm) {
            if (richText == 1) {
                // 富文本编辑器中插入图片
                editor.cmd.do('insertHTML', '<img src="' + eurl + '" style="max-width:100%;"/>');
            } else {
                // 视频封面图
                var videoCoverImage = $('#videoCoverImage');
                videoCoverImage.attr('src', eurl);
                videoCoverImage.parent().parent().css({'background': 'none'});
                vm.postData.videoImg = eurl;
            }
        }
    }

    var appendTextStealth = function () {
        var textContent = ($.trim(editor.txt.html())).replace(/<p><br><\/p>/ig, '');
        if (textContent === '') {
            editor.txt.html('<div class="stealth_module"></div>');
        } else {
            editor.txt.append('<div class="stealth_module"></div>');
        }
    }

    // 全局函数，用于选择表情
    var selectEmoji = function () {
        layer.open({
            type: 2,
            anim: 2,
            title: false,
            area: ['350px', '360px'],
            scrollbar: true,
            closeBtn: false,
            shadeClose: true,
            content: ["{:url('tedious/emoji')}&type=0", 'no'],
        });
    }

    // 全局函数，用于接收表情选择回调
    var complicatedCallFunc = function (type, data) {
        if (type === 0 && editor) {
            var textContent = ($.trim(editor.txt.html())).replace(/<p><br><\/p>/ig, '');
            if (textContent === '') {
                editor.txt.html('<span>' + data['content'] + '</span>');
            } else {
                editor.txt.append('<span>' + data['content'] + '</span>');
            }
        }
    }

    var E;
    var editor;
    var vm;

    vm = new Vue({
        el: '#app',
        data() {
            return {
                // 基本字段
                userInfo: {
                    id: '{$userInfo.id}',
                    nickName: '{$userInfo.user_nick_name|emoji_decode}'
                },
                // 表单数据
                postData: {
                    title: '',
                    titleColor: '#000000',
                    patype: 0, // 帖子类型
                    isBuy: 0, // 付费状态
                    buyPriceType: 0, // 付费类型
                    buyPrice: '0.00', // 付费价格
                    toryid: '{volist name="toryInfo" id="vo"}{if $i==1}{$vo.id}{/if}{/volist}', // 所属圈子
                    gambit: '', // 话题名称
                    imgShowType: 0, // 图片类型
                    content: '', // 帖子内容
                    callPhone: '', // 联系电话
                    // 音频相关
                    voice: '',
                    voiceTime: '',
                    // 视频相关
                    video: '',
                    videoType: 0,
                    videoImg: '',
                    tencentVideoVid: '',
                    // 活动相关
                    briskApprove: 1,
                    briskAddress: '',
                    briskAddressLatitude: '',
                    briskAddressLongitude: '',
                    dateStartTime: '',
                    dateEndTime: '',
                    numberOfPeople: '',
                    // 付费文件相关
                    ncId: 0,
                    fileId: 0,
                    fileIsSell: 1,
                    // 投票相关
                    voteOptions: [
                        { option: '', fakeVotes: 0 },
                        { option: '', fakeVotes: 0 }
                    ],
                    voteDeadline: '',
                    feedToken: ''
                },
                adapterTime: '', // 发布时间
                fileNames: '点击选择文件信息',
                wordSuggestions: [], // 话题建议
                wordVisible: false, // 话题建议是否显示
                modalArticleUrl: '', // 微信公众号文章地址
                isLock: false,
                thirdPartyVideoName: '视频地址',
                // 显示控制
                showPayHidden: false,
                showPayFileHidden: false,
                showSelectGraphic: true, // 默认显示图文帖
                showSelectAudio: false,
                showSelectVideo: false,
                showSelectBrisk: false,
                showSelectBallot: false
            };
        },
        computed: {
            voteCountingNumber() {
                return this.postData.voteOptions.length + 1;
            }
        },
        methods: {
            // 微信公众号文章采集
            openArticleModal() {
                // 在Vue中使用layer或其他UI组件
                const that = this;
                setTimeout(function () {
                    $('.am-dimmer').css({'z-index': '10004'})
                }, 10);
                $('#network-article').modal({
                    relatedTarget: this,
                    onConfirm: function(e) {
                        if ($.trim(e.data) == '') {
                            layer.msg('微信公众号文章地址输入有误');
                        } else {
                            that.fetchArticle($.trim(e.data));
                        }
                        that.modalArticleUrl = '';
                    },
                    onCancel: function() {
                        that.modalArticleUrl = '';
                    }
                });
            },
            // 获取微信文章内容
            fetchArticle(url) {
                const that = this;
                $.getJSON("{:url('unlawful/gathering')}", {'url': url}, function(data) {
                    if (data.code === 0) {
                        layer.msg(data.msg || '微信公众号文章地址输入有误');
                    } else if (data.code === 1 || (!data.code && data.title)) {
                        that.postData.title = data.title;
                        editor.txt.html(data.content);
                    } else {
                        layer.msg('微信公众号文章地址输入有误');
                    }
                }).fail(function() {
                    layer.msg('请求失败，请检查网络连接');
                });
            },
            // 切换帖子类型
            changePostType() {
                this.showSelectGraphic = false;
                this.showSelectAudio = false;
                this.showSelectVideo = false;
                this.showSelectBrisk = false;
                this.showSelectBallot = false;

                switch(parseInt(this.postData.patype)) {
                    case 0: // 图文帖
                        this.showSelectGraphic = true;
                        break;
                    case 1: // 语音帖
                        this.showSelectAudio = true;
                        break;
                    case 2: // 视频帖
                        this.showSelectVideo = true;
                        break;
                    case 3: // 活动帖
                        this.showSelectBrisk = true;
                        break;
                    case 4: // 单选投票帖
                    case 5: // 多选投票帖
                        this.showSelectBallot = true;
                        break;
                }
            },
            // 变更付费状态
            changeBuyStatus() {
                this.showPayHidden = this.postData.isBuy > 0;
                this.showPayFileHidden = this.postData.isBuy > 1;
            },
            // 变更视频类型
            changeVideoType() {
                this.postData.tencentVideoVid = '';
                if (parseInt(this.postData.videoType) === 0 || parseInt(this.postData.videoType) === 2) {
                    $('#videoTypeSelectOne').show();
                    this.thirdPartyVideoName = '视频地址';
                } else {
                    $('#videoTypeSelectOne').hide();
                    this.thirdPartyVideoName = '视频vid';
                }
            },
            // 加载话题建议
            loadGambitSuggestions() {
                const keywords = this.postData.gambit.trim();
                if (keywords === '') {
                    this.wordSuggestions = [];
                    this.wordVisible = false;
                    return;
                }
                const that = this;
                that.wordVisible = true;
                $('#word').width($('#gambit').width());
                $.getJSON('{:url(\'unlawful/loadGambit\')}', {'wd': keywords}, function(data) {
                    that.wordSuggestions = data;
                    that.wordVisible = data.length > 0;
                });
            },
            // 选择话题
            selectGambit(word) {
                this.postData.gambit = word;
                this.wordVisible = false;
            },
            // 添加投票选项
            addVoteOption() {
                this.postData.voteOptions.push({ option: '', fakeVotes: 0 });
            },
            // 删除投票选项
            removeVoteOption(index) {
                if (index === this.postData.voteOptions.length - 1) {
                    this.postData.voteOptions.splice(index, 1);
                } else {
                    layer.msg("请先移除最后一位投票项！");
                }
            },
            // 数字格式检查
            digitalCheck(value, limit) {
                if (limit === 2) {
                    value = value.replace(/[^\d.]/g, "");
                    value = value.replace(/^\./g, "");
                    value = value.replace(/\.{2,}/g, ".");
                    value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
                    value = value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
                } else {
                    value = Number((value.match(/^\d+(?:\.\d{0})?/)));
                }
                return value;
            },
            // 文件上传
            fileUpload(type, event) {
                const file = event.target.files[0];
                if (!file) return;

                let fileQualified = true;
                const suffix = file.name.lastIndexOf('.');
                const ext = file.name.substring(suffix).toLowerCase();

                if (type === 0) { // 音频文件
                    if (ext !== '.wav' && ext !== '.mp3' && ext !== '.ogg' && ext !== '.mpeg') {
                        layer.msg('文件类型选择错误,请选择音频类型文件进行上传');
                        event.target.value = '';
                        return;
                    }
                } else if (type === 1) { // 视频文件
                    if (ext !== '.mp4') {
                        layer.msg('视频文件类型选择错误,请选择MP4类型文件进行上传');
                        event.target.value = '';
                        return;
                    }
                }

                if (fileQualified) {
                    layer.load();
                    const formData = new FormData();
                    formData.append('sngpic', file);

                    const that = this;
                    $.ajax({
                        type: "post",
                        url: "{:url('upload/operate')}",
                        async: false,
                        data: formData,
                        processData: false,
                        contentType: false,
                        dataType: 'json',
                        success: function(data) {
                            setTimeout(function() {
                                parent.layer.closeAll('loading');
                                if (data.status === 'success') {
                                    if (type === 0) { // 音频
                                        that.postData.voice = data.url;
                                        $('#audioFile').attr({'src': data.url, 'title': data.title});
                                        $('#audioFile').get(0).load();
                                        $('#audioFile').get(0).onloadedmetadata = function() {
                                            that.postData.voiceTime = parseInt($('#audioFile').get(0).duration);
                                        }
                                    } else if (type === 1) { // 视频
                                        that.postData.video = data.url;
                                        that.postData.tencentVideoVid = data.url;
                                        $('#videoFile').attr({'src': data.url, 'title': data.title});
                                        $('#videoFile').get(0).load();
                                    }
                                    event.target.value = '';
                                } else {
                                    layer.msg('上传失败，请检查上传配置');
                                    event.target.value = '';
                                }
                            }, 1600);
                        }
                    });
                }
            },
            // 捕获视频图像
            captureImage(video) {
                if (parseInt(this.postData.videoType) !== 0) {
                    return;
                }

                setTimeout(() => {
                    const canvas = document.createElement("canvas");
                    canvas.width = video.videoWidth * 0.8;
                    canvas.height = video.videoHeight * 0.8;
                    canvas.getContext('2d').drawImage(video, 0, 0, canvas.width, canvas.height);
                    const images = canvas.toDataURL("image/png");

                    const formData = new FormData();
                    formData.append('sngpic', this.base64ToBlob(images));

                    const that = this;
                    $.ajax({
                        type: "post",
                        url: "{:url('upload/operate')}",
                        async: false,
                        data: formData,
                        processData: false,
                        contentType: false,
                        dataType: 'json',
                        success: function(data) {
                            if (data.status === 'success') {
                                that.postData.videoImg = data.url;
                                const videoCoverImage = $('#videoCoverImage');
                                videoCoverImage.attr('src', data.url);
                                videoCoverImage.css({'width':'auto'});
                                videoCoverImage.parent().parent().css({'background': 'none'});
                            }
                        }
                    });
                }, 500);
            },
            // Base64转Blob
            base64ToBlob(Base64) {
                const bytes = window.atob(Base64.split(',')[1]);
                const ab = new ArrayBuffer(bytes.length);
                const ia = new Uint8Array(ab);
                for (let i = 0; i < bytes.length; i++) {
                    ia[i] = bytes.charCodeAt(i);
                }
                return new Blob([ab], {type: 'image/png'});
            },
            // 加载第三方视频
            loadThirdPartyVideo() {
                const thirdPartyVideoUrl = this.postData.tencentVideoVid.trim();
                const videoType = parseInt(this.postData.videoType);

                if (thirdPartyVideoUrl === '') {
                    switch (videoType) {
                        case 0:
                        case 2:
                            layer.msg('视频地址不能为空');
                            break;
                        case 1:
                            layer.msg('视频vid不能为空');
                            break;
                    }
                    return;
                }

                switch (videoType) {
                    case 0:
                        this.postData.video = thirdPartyVideoUrl;
                        $('#videoFile').attr({'src': thirdPartyVideoUrl});
                        break;
                    case 1:
                        const urlPath = 'https://vv.video.qq.com/getinfo?platform=101001&charge=0&otype=json&defn=shd&vids=' + thirdPartyVideoUrl;
                        $.ajax({
                            url: urlPath,
                            dataType: "jsonp",
                            jsonp: "callback",
                            success: (data) => {
                                try {
                                    const fileName = data['vl']['vi'][0]['fn'];
                                    const fvkey = data['vl']['vi'][0]['fvkey'];
                                    const host = data['vl']['vi'][0]['ul']['ui'][0]['url'];
                                    $('#videoFile').attr({'src': host + fileName + '?vkey=' + fvkey});
                                    this.postData.videoImg = 'http://shp.qpic.cn/qqvideo/0/' + data['vl']['vi'][0]['vid'] + '/0';
                                } catch (e) {
                                    layer.msg('视频vid填写错误');
                                }
                            }
                        });
                        break;
                    case 2:
                        /*{if $videoParsePluginKey}*/
                        const that = this;
                        $.post("{:url('resolve/parse_url')}", {'url': thirdPartyVideoUrl, 'isCover': 1}, function(data) {
                            if (data.code > 0) {
                                that.postData.title = data.title;
                                $('#videoFile').attr({'src': data.video});
                                that.postData.videoImg = data.cover;
                                $('#videoCoverImage').attr({'src': data.cover});
                            } else {
                                layer.msg(data.msg);
                            }
                        });
                        /*{/if}*/
                        break;
                }
            },
            // 图片选择
            openImageDialog(richText) {
                let dynamicUrl = "{:url('images/dialogImages')}&gclasid=0";
                if (richText === 1) {
                    dynamicUrl += "&dynamicStyle=richText";
                }
                layer.open({
                    type: 2,
                    anim: 2,
                    scrollbar: true,
                    area: ['900px', '600px'],
                    title: false,
                    closeBtn: 0,
                    shadeClose: true,
                    content: [dynamicUrl, 'no']
                });
            },
            // 选择文件
            selectFiles(uid) {
                layer.open({
                    type: 2,
                    anim: 2,
                    title: false,
                    area: ['650px', '640px'],
                    scrollbar: false,
                    closeBtn: true,
                    shadeClose: true,
                    content: ["{:url('cloud/steal')}&uid=" + uid + '&pid=0&type=1', 'no'],
                });
            },
            // 设置文件信息
            setFileInfo(nid, fid, fileName) {
                this.postData.ncId = nid;
                this.postData.fileId = fid;
                this.fileNames = fileName;
            },
            // 表单提交
            submitForm() {
                const postData = {
                    userid: this.userInfo.id,
                    title: this.postData.title.trim(),
                    titleColor: this.postData.titleColor,
                    patype: Number(this.postData.patype),
                    toryid: this.postData.toryid,
                    gambit: this.postData.gambit,
                    imgShowType: Number(this.postData.imgShowType),
                    content: ($.trim(editor.txt.html())).replace(/<p><\/p>/ig, '').replace(/<p><br><\/p>/ig, ''),
                    isBuy: Number(this.postData.isBuy),
                    buyPriceType: Number(this.postData.buyPriceType),
                    buyPrice: this.postData.buyPrice,
                    callPhone: this.postData.callPhone,
                    voice: this.postData.voice,
                    voiceTime: this.postData.voiceTime,
                    video: this.postData.video,
                    videoType: Number(this.postData.videoType),
                    videoImg: this.postData.videoImg,
                    tencentVideoVid: this.postData.tencentVideoVid,
                    adapterTime: this.adapterTime,
                    multipleImg: [],
                    feedToken: this.postData.feedToken
                };

                // 处理话题格式
                if (postData.gambit !== '') {
                    postData.gambit = postData.gambit.replace('#', '');
                    postData.gambit = '#' + postData.gambit + '#';
                }

                // 活动帖特殊处理
                if (postData.patype === 3) {
                    postData.briskApprove = this.postData.briskApprove;
                    postData.briskAddress = this.postData.briskAddress.trim();
                    postData.briskAddressLatitude = this.postData.briskAddressLatitude.trim();
                    postData.briskAddressLongitude = this.postData.briskAddressLongitude.trim();
                    postData.dateStartTime = this.postData.dateStartTime;
                    postData.dateEndTime = this.postData.dateEndTime;
                    postData.numberOfPeople = this.postData.numberOfPeople;

                    // 活动帖验证
                    if (postData.briskAddress === '') {
                        layer.msg('活动地址不能为空');
                        return;
                    }
                    if (postData.briskAddressLatitude === '') {
                        layer.msg('坐标纬度不能为空');
                        return;
                    }
                    if (postData.briskAddressLongitude === '') {
                        layer.msg('坐标经度不能为空');
                        return;
                    }
                    if (postData.dateStartTime === '') {
                        layer.msg('活动开始时间不能为空');
                        return;
                    }
                    if (postData.dateEndTime === '') {
                        layer.msg('活动结束时间不能为空');
                        return;
                    }
                    if (postData.numberOfPeople === '') {
                        layer.msg('活动人数不能为空');
                        return;
                    }
                }

                // 付费文件处理
                if (postData.isBuy > 1) {
                    postData.ncId = Number(this.postData.ncId);
                    postData.fileId = Number(this.postData.fileId);
                    postData.fileIsSell = Number(this.postData.fileIsSell);

                    if (postData.fileId === 0) {
                        layer.msg('请选择付费文件！');
                        return;
                    }
                }

                // 基本验证
                if (postData.title === '' && postData.content === '') {
                    layer.msg('帖子标题或内容必须填写一项');
                    return;
                }

                if (postData.isBuy > 0 && postData.buyPrice <= 0) {
                    layer.msg('付费帖子金额不能小于0.01');
                    return;
                }

                if (postData.patype === 1 && postData.voice === '') {
                    layer.msg('语音帖必须上传音频文件');
                    return;
                }

                // 视频帖验证
                if ((postData.patype === 2 && postData.videoType === 0 && postData.video === '') || (postData.patype === 2 && (postData.videoType === 1 || postData.videoType === 2) && postData.tencentVideoVid === '')) {
                    if (postData.videoImg === '' && postData.tencentVideoVid !== '') {
                        layer.msg('视频vid未加载，请点击"加载视频"');
                        return;
                    }

                    switch (postData.videoType) {
                        case 0:
                            layer.msg('请上传本地视频');
                            break;
                        case 1:
                            layer.msg('视频vid不能为空');
                            break;
                        case 2:
                            layer.msg('视频地址不能为空');
                            break;
                    }
                    return;
                }

                // 收集多图
                $('.multiple-img').each(function() {
                    postData.multipleImg.push($.trim($(this).attr('data-multiple-img')));
                });

                // 投票帖特殊处理
                let shocked = {};
                if (postData.patype === 4 || postData.patype === 5) {
                    shocked.votes = this.postData.voteOptions.map(item => item.option.trim());
                    shocked.pretends = this.postData.voteOptions.map(item => Number(item.fakeVotes));

                    if (shocked.votes.includes('')) {
                        layer.msg("请填写投票选项！");
                        return;
                    }

                    postData.voteDeadline = this.postData.voteDeadline.trim();
                }

                postData['feedToken'] = $.trim(this.postData.feedToken);
                if (postData.patype === 6 && postData['feedToken'] === '') {
                    layer.msg('请填写feed-token');
                    return;
                }

                postData['adapterTime'] = $.trim(this.adapterTime);
                if (postData['adapterTime'] === '') {
                    layer.msg('请选择发布时间');
                    return;
                }

                // 提交表单
                if (!this.isLock) {
                    this.isLock = true;
                    const loadIndex = layer.load(2, {
                        shadeClose: false,
                        shade: [0.5, '#000']
                    });

                    $.post("{:url('user/reticraphic')}", {postData, shocked}, (data) => {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function() {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000}, () => {
                                this.isLock = false;
                                layer.close(loadIndex);
                            });
                        }
                    }, 'json');
                }
            }
        },
        mounted() {
            // 初始化富文本编辑器
            E = window.wangEditor;
            editor = new E('#detail');
            editor.customConfig.uploadImgServer = true;
            editor.create();
            E.secretSpace.init('#detail');
            E.fullscreen.init('#detail');

            // 初始化日期控件
            laydate.render({
                elem: '#dateStartTime',
                type: 'datetime',
                done: (value) => {
                    this.postData.dateStartTime = value;
                }
            });
            laydate.render({
                elem: '#dateEndTime',
                type: 'datetime',
                done: (value) => {
                    this.postData.dateEndTime = value;
                }
            });
            laydate.render({
                elem: '#voteDeadline',
                type: 'datetime',
                done: (value) => {
                    this.postData.voteDeadline = value;
                }
            });
        }
    });

</script>
{/block}