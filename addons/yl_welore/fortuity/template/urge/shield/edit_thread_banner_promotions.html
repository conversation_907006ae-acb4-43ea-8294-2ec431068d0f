{extend name="/base"/}
{block name="main"}
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 编辑广告
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label"> 广告图片</label>
                        <div class="am-u-sm-9">
                            <form id="snup" style="display: none;">
                                <input type="file" id="sngpic" name="sngpic" onchange="snuload();">
                            </form>
                            <img :src="item.imgUrl" onerror="this.src='static/wechat/image_vip_top.jpg'"
                                 onclick="cuonice();"
                                 style="width: 200px;height: 120px;cursor: pointer;box-sizing:border-box;"/>
                            <button type="button" style="margin-left:10px;margin-right:10px; font-size: 12px;"
                                    onclick="cuonice();">
                                选择图片
                            </button>
                            <small>
                                建议图片尺寸：900*500px
                            </small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">选择跳转路径</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.jumpType">
                                <option value="0">内部页面跳转</option>
                                <option value="1">外部网页跳转</option>
                                <option value="2">小程序跳转</option>
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group" v-if="item.jumpType == 0">
                        <label class="am-u-sm-3 am-form-label">内部链接</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.url">
                            <small>
                                *跳转到圈子请按以下格式填写,id值在圈子列表中获取(
                                /yl_welore/pages/packageA/circle_info/index?id=1 )<br>
                                *跳转到帖子详情页面请按以下格式填写,id值在帖子信息中获取(
                                /yl_welore/pages/packageA/article/index?id=1&type=0
                                ) <br>
                                ( type 帖子类型 0.图文 1.语音 2.视频 3.活动 )<br>
                                *跳转到话题页面请按以下格式填写,id值在话题列表中获取(
                                /yl_welore/pages/gambit/index?id=1 )<br>
                            </small>
                        </div>
                    </div>

                    <div class="am-form-group" v-if="item.jumpType == 1">
                        <label class="am-u-sm-3 am-form-label">外部链接</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.url">
                            <small>*此链接为网页外部跳转链接，需要在小程序后台配置业务域名。</small>
                        </div>
                    </div>
                    <template v-if="item.jumpType == 2">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">跳转小程序 APPID</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="item.appid" placeholder="请输入跳转小程序的 APPID">
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">跳转小程序 URL</label>
                            <div class="am-u-sm-9">
                                <input type="text" v-model="item.url" placeholder="请输入跳转小程序的 URL 地址">
                                <small style="color:red;">不填写默认跳转到首页</small>
                            </div>
                        </div>
                    </template>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">排序</label>
                        <div class="am-u-sm-9">
                            <input type="number" v-model="item.sort" placeholder="请输入排序数字">
                        </div>
                    </div>

                    <div class="am-form-group flex-center">
                        <button type="button" class="am-btn am-btn-primary" @click="holdSave">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    var vm = new Vue({
        el: '#app',
        data() {
            return {
                item: {
                    fid: '{$Request.get.fid}',
                    imgUrl: '{$list.img_url}',
                    jumpType: '{$list.jump_type}',
                    appid: '{$list.appid}',
                    url: '{$list.url}',
                    sort: '{$list.sort}'
                },
                onLock: false,
            }
        }, methods: {
            holdSave() {
                if (this.item.imgUrl === '') {
                    layer.msg('请选择广告图片');
                    return;
                }
                if (!this.onLock) {
                    this.onLock = true;
                    var setData = this.item;
                    $.ajax({
                        type: "post",
                        url: "{:url('shield/edit_thread_banner_promotions')}",
                        data: setData,
                        success: function (data) {
                            if (data.code > 0) {
                                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                    location.href = "{:url('shield/thread_banner_promotions')}";
                                });
                            } else {
                                layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                    this.onLock = false;
                                });
                            }
                        }
                    });
                }
            }
        }
    })

    var cuonice = function () {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    var sutake = function (eurl) {
        vm.item.imgUrl = eurl;
        vm.$forceUpdate();
        layer.closeAll();
    }
</script>
{/block}