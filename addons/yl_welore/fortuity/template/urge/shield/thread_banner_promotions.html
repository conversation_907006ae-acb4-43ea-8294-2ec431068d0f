{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-code {margin-right: 5px;}
    
    .am-btn-toolbar .am-btn-primary { background-color: #23b7e5; border-color: #23b7e5; border-radius: 3px; }
    .action-btn {display: inline-block; padding: 4px 10px; background: #fff; border: 1px solid #ddd; color: #666; border-radius: 3px; font-size: 12px; cursor: pointer; transition: all 0.3s;}
    .action-btn:hover {border-color: #23b7e5; color: #23b7e5; background-color: #f5fafd;}
    .action-btn.danger {color: #dd514c;}
    .action-btn.danger:hover {border-color: #dd514c; color: #dd514c; background-color: #fff8f8;}

    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-top: 15px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .am-table .sort-input { width: 50px; text-align: center; border: 1px solid #ddd; border-radius: 3px; padding: 2px 4px; }
    .am-table .sort-input:focus { border-color: #23b7e5; }
    .banner-image { max-width: 200px; max-height: 80px; border-radius: 4px; }

    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a, .am-pagination > .am-active > a:hover {background-color: #23b7e5;border-color: #23b7e5;color:#fff;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-code"></span> 横幅广告
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-btn-toolbar">
                    <button type="button" class="am-btn am-btn-primary am-btn-sm" onclick="saloof();">
                        <span class="am-icon-plus"></span> 新增广告
                    </button>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="15%">排序</th>
                            <th width="40%">广告图片</th>
                            <th width="20%">跳转类型</th>
                            <th width="25%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                <input type="text" class="sort-input" id="sfixed-{$vo.id}" value="{$vo.sort}" data-sort="{$vo.sort}" onblur="supre('{$vo.id}', this);">
                            </td>
                            <td>
                                <a href="{$vo.img_url}" target="_blank">
                                    <img src="{$vo.img_url}" class="banner-image" onerror="this.src='static/wechat/image_vip_top.jpg'"/>
                                </a>
                            </td>
                            <td>
                                {if $vo.jump_type==0}
                                    内部页面
                                {elseif $vo.jump_type==1}
                                    外部页面
                                {elseif $vo.jump_type==2}
                                    小程序跳转
                                {/if}
                            </td>
                            <td>
                                <button type="button" class="action-btn" onclick="uploof('{$vo.id}');">
                                    <span class="am-icon-pencil-square-o"></span> 编辑
                                </button>
                                <button type="button" class="action-btn danger" onclick="symbolDel('{$vo.id}')">
                                    <span class="am-icon-trash-o"></span> 删除
                                </button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    function supre(asyId, dom) {
        var dalue = $(dom).val();
        var daioe = $(dom).attr('data-sort');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(dom).val(daioe);
            return false;
        }
        if (dalue != daioe) {
            var loadIndex = layer.load(1, { shade: [0.1,'#fff'] });
            $.post("{:url('shield/thread_banner_promotions_sort')}", { asyId: asyId, dalue: dalue }, function (data) {
                layer.close(loadIndex);
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 800});
                    $(dom).attr('data-sort', dalue);
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1600});
                    $(dom).val(daioe); // Restore old value on failure
                }
            }, 'json');
        }
    }

    function saloof() {
        location.href = "{:url('shield/new_thread_banner_promotions')}";
    }

    function uploof(fid) {
        location.href = "{:url('shield/edit_thread_banner_promotions')}&fid=" + fid;
    }

    function symbolDel(mid) {
        layer.confirm('您确定要删除这条广告吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function (index) {
            layer.close(index);
            var loadIndex = layer.load(1, { shade: [0.1,'#fff'] });
            $.post("{:url('shield/delete_thread_banner_promotions')}", {'ecid': mid}, function (data) {
                layer.close(loadIndex);
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () { location.reload(); });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000});
                }
            }, 'json');
        });
    }
</script>
{/block}