{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-code {margin-right: 5px;}
    
    .am-form-horizontal .am-form-group { margin-bottom: 20px; }
    .am-form-horizontal .am-form-label { text-align: right; font-weight: 500; padding-top: .5em; }
    .am-form-horizontal .am-form-group .am-u-sm-9 { padding-left: 15px; }
    .am-form-horizontal input[type='text'], .am-form-horizontal select {
        border-radius: 3px;
        border: 1px solid #e8e8e8;
        padding: 6px 10px;
        transition: all 0.3s;
        width: 100%;
        height: 38px;
    }
    .am-form-horizontal input[type='text']:focus, .am-form-horizontal select:focus {
        border-color: #23b7e5;
        box-shadow: 0 0 0 2px rgba(35,183,229,0.1);
    }
    .am-checkbox-inline { margin-right: 20px; }

    .image-uploader-wrapper { display: flex; align-items: center; }
    .image-preview { width: 120px; height: 120px; border: 1px dashed #d9d9d9; border-radius: 4px; cursor: pointer; display: flex; align-items: center; justify-content: center; overflow: hidden; }
    .image-preview:hover { border-color: #23b7e5; }
    .image-preview img { max-width: 100%; max-height: 100%; }
    .image-preview .upload-icon { font-size: 28px; color: #8c939d; }

    .am-btn-primary { background-color: #23b7e5; border-color: #23b7e5; border-radius: 3px; }
    .am-btn-primary:hover { background-color: #49c5ec; border-color: #49c5ec; }

</style>
<div id="app" class="tpl-portlet-components" v-cloak>
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-code"></span> 热帖配置
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-12 am-u-md-8 am-u-md-offset-2">
                <div class="am-form am-form-horizontal">

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">热榜标题</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="item.title" placeholder="请输入榜单名称">
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">样式风格</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.styleType">
                                <option value="0">样式1</option>
                                <option value="1">样式2</option>
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">热榜图片</label>
                        <div class="am-u-sm-9">
                             <div class="image-uploader-wrapper">
                                <div class="image-preview" onclick="cuonice();">
                                    <img v-if="item.headImg" :src="item.headImg" onerror="this.src='static/wechat/image_vip_top.jpg'" alt="热榜图片">
                                    <i v-else class="am-icon-plus upload-icon"></i>
                                </div>
                            </div>
                             <form id="snup" style="display: none;">
                                <input type="file" id="sngpic" name="sngpic" onchange="snuload();">
                            </form>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">统计范围</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.statisticsTime">
                                <option value="0">全部</option>
                                <option value="1">天</option>
                                <option value="2">周</option>
                                <option value="3">月</option>
                                <option value="4">季</option>
                                <option value="5">年</option>
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">排序条件</label>
                        <div class="am-u-sm-9">
                            <label class="am-checkbox-inline">
                                <input v-model="item.sortCondition" type="checkbox" value="liulan"> 浏览
                            </label>
                            <label class="am-checkbox-inline">
                                <input v-model="item.sortCondition" type="checkbox" value="dianzan"> 点赞
                            </label>
                            <label class="am-checkbox-inline">
                                <input v-model="item.sortCondition" type="checkbox" value="shoucang"> 收藏
                            </label>
                            <label class="am-checkbox-inline">
                                <input v-model="item.sortCondition" type="checkbox" value="huifu"> 回复
                            </label>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">显示开关</label>
                        <div class="am-u-sm-9">
                            <select v-model="item.displaySwitch">
                                <option value="1">显示</option>
                                <option value="0">隐藏</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="am-form-group">
                         <div class="am-u-sm-9 am-u-sm-offset-3">
                            <button type="button" class="am-btn am-btn-primary" @click="holdSave">
                                保存
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    var vm = new Vue({
        el: '#app',
        data() {
            return {
                item: {
                    title: '{$list.custom_title}',
                    styleType: '{$list.style_type}',
                    headImg: '{$list.custom_head_img}',
                    statisticsTime: '{$list.statistics_time}',
                    sortCondition: [],
                    displaySwitch: '{$list.display_switch}'
                },
                onLock: false,
            }
        }, created() {
            this.item.sortCondition = JSON.parse('{$list.custom_sort_condition}');
        }, methods: {
            holdSave() {
                if (this.item.title === '') {
                    layer.msg('请输入热榜标题');
                    return;
                }
                if (!this.onLock) {
                    this.onLock = true;
                    var setData = this.item;
                    $.ajax({
                        type: "post",
                        url: "{:url('shield/thread_config')}",
                        data: setData,
                        success: function (data) {
                            if (data.code > 0) {
                                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                    location.reload();
                                });
                            } else {
                                layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                    this.onLock = false;
                                });
                            }
                        }
                    });
                }
            }
        }
    })

    var cuonice = function () {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    var sutake = function (eurl) {
        vm.item.headImg = eurl;
        vm.$forceUpdate();
        layer.closeAll();
    }

</script>
{/block}