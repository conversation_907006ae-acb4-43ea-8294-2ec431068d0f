{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .am-form {position: relative;overflow: visible;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-reply-all {margin-right: 5px;color: #4CAF50;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .action-btn {display: inline-block;padding: 4px 8px;background: #fff;border: 1px solid #ddd;color: #333;border-radius: 3px;font-size: 12px;cursor: pointer;transition: all 0.3s;margin: 2px;}
    .action-btn:hover {border-color: #23b7e5;color: #23b7e5;background-color: #f5fafd;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-reply-all"></span> 帖子申诉
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}"
                       placeholder="搜索申诉用户...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover table-main">
                        <thead>
                        <tr>
                            <th class="text-center" width="10%">申诉用户</th>
                            <th class="text-center" width="10%">申诉类型</th>
                            <th class="text-center" width="10%">申诉楼层</th>
                            <th class="text-center" width="10%">申诉 标题/内容</th>
                            <th class="text-center" width="10%">所属圈子</th>
                            <th class="text-center" width="10%">申诉时间</th>
                            <th class="text-center" width="10%">申诉内容</th>
                            <th class="text-center" width="10%">处理状态</th>
                            <th class="text-center" width="10%">帖子状态</th>
                            <th class="text-center" width="10%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                <a href="{:url('user/index')}&userName={$vo.user_nick_name|emoji_decode|filter_emoji}&page=1"
                                   title="{$vo.user_nick_name|emoji_decode}"
                                   target="_blank">
                                    {$vo.user_nick_name|emoji_decode|subtext=6}
                                </a>
                            </td>
                            <td>
                                {if $vo.tale_type==2}
                                帖子申诉
                                {elseif $vo.tale_type==3}
                                回复申诉
                                {/if}
                            </td>
                            <td>
                                {if $vo.tale_type==2}
                                1
                                {elseif $vo.tale_type==3}
                                {$vo.satisfy.phase}
                                {/if}
                            </td>
                            <td>
                                {if $vo.tale_type==2}
                                <a href="{:url('essay/setails')}&uplid={$vo.paper_id}{if $vo.satisfy.token!=''}&token={$vo.satisfy.token}{/if}"
                                   target="_blank" title="{$vo.satisfy.study_title|strip_tags|subtext=6}">
                                    {if $vo.satisfy.study_title}
                                    {$vo.satisfy.study_title|strip_tags|subtext=6}
                                    {else}
                                        无
                                    {/if}
                                </a>
                                {elseif $vo.tale_type==3}
                                {if $vo.satisfy.reply_type==0}
                                <a href="{:url('essay/repas')}&uplid={$vo.prely_id}{if $vo.satisfy.token!=''}&token={$vo.satisfy.token}{/if}"
                                   target="_blank" title="{$vo.satisfy.reply_content|strip_tags|subtext=6}">
                                    {if $vo.satisfy.reply_content}
                                    {$vo.satisfy.reply_content|strip_tags|subtext=6}
                                    {else}
                                    无
                                    {/if}
                                </a>
                                {elseif $vo.satisfy.reply_type==1}
                                <a href="{:url('essay/repas')}&uplid={$vo.prely_id}{if $vo.satisfy.token!=''}&token={$vo.satisfy.token}{/if}"
                                   target="_blank" title="语音">语音</a>
                                {/if}
                                {/if}
                            </td>
                            <td>
                                <a href="{:url('compass/fence')}&hazy_name={$vo.realm_name}&page=1"
                                   title="{$vo.realm_name}" target="_blank">
                                    {$vo.realm_name|subtext=7}
                                </a>
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.petition_time)}</td>
                            <td>
                                <button type="button" class="action-btn" onclick="karma('{$vo.id}');">查看</button>
                            </td>
                            <td>
                                {if $vo.acceptance_status==0}
                                <span class="am-text-danger">未处理</span>
                                {elseif $vo.acceptance_status==1}
                                <span class="am-text-success">已处理</span>
                                {/if}
                            </td>
                            <td>
                                {if $vo.is_strike==0}
                                <span class="am-text-success">已恢复</span>
                                {else}
                                <span class="am-text-danger">未恢复</span>
                                {/if}
                            </td>
                            <td>
                                {if $vo.acceptance_status==0}
                                <button type="button" class="action-btn"
                                        onclick="ruling('{$vo.user_nick_name|emoji_decode|filter_emoji}','{$vo.tale_type}','{$vo.id}','{$vo.satisfy.id}');">
                                    处理申诉
                                </button>
                                {else}
                                <button type="button" class="action-btn" style="color: #dd514c;" onclick="appDel('{$vo.id}');">
                                    删除
                                </button>
                                {/if}
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>
    function karma(sin) {
        $.getJSON("{:url('journal/appeal')}", {'ksin': sin}, function (data) {
            layer.alert(data, {'title': '申诉内容'});
        });
    }

    function appDel(rid) {
        layer.confirm('您确定要删除这条数据吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("{:url('journal/delsuar')}", {'rid': rid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000},function () {
                        location.reload();
                    });
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }

    function ruling(usname, stale, ngid, vgid) {
        var estale = stale == 2 ? '帖子' : '回复';
        var sitle = "您是否要恢复用户<span style='color: blue'>" + usname + "</span>申诉的" + estale + "？";
        layer.confirm(sitle, {
            btn: ['是', '否'], 'title': '操作确认'
        }, function () {
            layer.closeAll();
            srompt('0', stale, ngid, vgid);
        }, function () {
            layer.closeAll();
            srompt('1', stale, ngid, vgid);
        });
    }

    function srompt(fell, stale, ngid, vgid) {
        layer.prompt({'title': "请输入处理结果，回复给申诉用户"}, function (rea_value, index) {
            if (rea_value == '' || rea_value == 'undefined' || rea_value == null) {
                return false;
            }
            $.post("{:url('rejupeal')}", {
                'sfell': fell,
                "sngid": ngid,
                'setale': stale,
                'nvgid': vgid,
                'nstruct': rea_value
            }, function (data) {
                layer.close(index);
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                        location.reload();
                    });
                }
            });
        });
    }


    function fuzzy() {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name)
            location.href = "{:url('appeal')}&hazy_name=" + fz_name + "&page={$page}";
        else
            location.href = "{:url('appeal')}&page={$page}";
    }
</script>
{/block}