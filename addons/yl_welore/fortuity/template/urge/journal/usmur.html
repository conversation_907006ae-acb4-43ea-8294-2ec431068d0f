{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .am-form {position: relative;overflow: visible;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-user {margin-right: 5px;color: #ed4040;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fafafa;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .action-btn {display: inline-block;padding: 4px 8px;background: #fff;border: 1px solid #ddd;color: #333;border-radius: 3px;font-size: 12px;cursor: pointer;transition: all 0.3s;margin: 2px;}
    .action-btn:hover {border-color: #23b7e5;color: #23b7e5;background-color: #f5fafd;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 10px 8px;}
    .am-table > tbody > tr > td {padding: 10px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;position: relative;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .filter-btn-group .am-btn {
        margin-right: 10px;
        border-radius: 3px;
    }
    .filter-btn-group .am-btn.active {
        background-color: #23b7e5;
        color: white;
        border-color: #23b7e5;
    }
    .batch-actions-group > .am-btn + .am-btn {
        margin-left: 8px;
    }
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-user"></span> 用户投诉
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索投诉用户...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-7">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-sm filter-btn-group">
                        <a href="{:url('journal/usmur')}&egon=0" type="button" class="am-btn am-btn-default {if $egon==0}active{/if}">全部</a>
                        <a href="{:url('journal/usmur')}&egon=1" type="button" class="am-btn am-btn-default {if $egon==1}active{/if}">未查看</a>
                        <a href="{:url('journal/usmur')}&egon=2" type="button" class="am-btn am-btn-default {if $egon==2}active{/if}">已查看</a>
                    </div>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-5" style="text-align: right;">
                 <div class="am-btn-group am-btn-group-sm batch-actions-group">
                    <button type="button" class="am-btn am-btn-secondary" onclick="rebatch('0');">批量标记已查看</button>
                    <button type="button" class="am-btn am-btn-danger" onclick="rebatch('1');">批量删除</button>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover table-main">
                        <thead>
                        <tr>
                            <th class="text-center" width="5%"><input id="withole" type="checkbox" class="tpl-table-fz-check">全选</th>
                            <th class="text-center" width="12%">投诉用户</th>
                            <th class="text-center" width="12%">被投诉用户</th>
                            <th class="text-center" width="12%">投诉原因</th>
                            <th class="text-center" width="15%">提交时间</th>
                            <th class="text-center" width="12%">私信记录</th>
                            <th class="text-center" width="10%">查看状态</th>
                            <th class="text-center" width="22%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td><input type="checkbox" class="tpl-table-fz-check elctive" value="{$vo.id}" data-mvalue="{$vo.mopt_id}"></td>
                            <td>
                                <a href="{:url('user/index')}&userName={$vo.user_nick_name|emoji_decode}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user_nick_name|emoji_decode|subtext=15}
                                </a>
                            </td>
                            <td>
                                {if $vo.userType == 0}
                                <a href="{:url('user/index')}&userName={$vo.username|emoji_decode}&page=1" title="{$vo.username|emoji_decode}" target="_blank">
                                    {$vo.username|emoji_decode|subtext=15}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.username|emoji_decode}&page=1" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                    {$vo.username|emoji_decode|subtext=15}
                                </a>
                                {/if}
                            </td>
                            <td>
                                <button type="button" class="action-btn" onclick="originer('{$vo.id}',0);">查看原因</button>
                                <input id="ounger-{$vo.id}" type="hidden" value="{$vo.ment_caption|strip_tags|emoji_decode|filter_emoji}">
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.ment_time)}</td>
                            <td>
                                <a class="action-btn" href="{:url('journal/letterRecord')}&rid={$vo.proof_id}&uid={$vo.user_id}" target="_blank">查看私信</a>
                            </td>
                            <td>
                                {if $vo.status == 0}
                                <span class="am-text-warning">未查看</span>
                                {elseif $vo.status == 1}
                                <span class="am-text-success">已查看</span>
                                {/if}
                            </td>
                            <td>
                                {if $vo.status == 0}
                                <button type="button" class="action-btn" onclick="uknow('{$vo.id}','{$vo.mopt_id}','0');">标记为已查看</button>
                                {/if}
                                <button type="button" class="action-btn" style="color: #dd514c;" onclick="uknow('{$vo.id}','{$vo.mopt_id}','1');">删除</button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    !function () {
        $('#withole').click(function () {
            var wecked = $(this).prop('checked');
            if (wecked) {
                $('.elctive').prop('checked', true);
            } else {
                $('.elctive').prop('checked', false);
            }
        });
        $('.elctive').click(function () {
            var jack = 1;
            $('.elctive').each(function () {
                var weck = $(this).prop('checked');
                if (weck == false) {
                    jack = 0;
                    return false;
                }
            });
            if (jack) {
                $('#withole').prop('checked', true);
            } else {
                $('#withole').prop('checked', false);
            }
        });
    }();

    var originer = function (usid, type) {
        switch (type) {
            case 0:
                layer.alert($('#ounger-' + usid).val(), {'title': '投诉原因'});
                break;
            case 1:
                var dynamicUrl = $('<a></a>');
                dynamicUrl.attr('href', "&unid=" + usid);
                dynamicUrl.attr('target', '_blank');
                dynamicUrl.get(0).click();
                break;
        }
    }

    var rebatch = function (guid) {
        var tired = false;
        $('.elctive').each(function () {
            if ($(this).prop('checked')) {
                tired = true;
                return false;
            }
        });
        if (!tired) {
            layer.alert('很抱歉，当前没有选中要操作的数据', {'title': '系统提示'});
            return;
        }
        if (guid == 0) {
            layer.confirm('您确定要批量标记选中的数据状态为已查看吗？', {
                btn: ['确定', '取消']
            }, function(index){
                var i = 0;
                var j = 0;
                $('.elctive').each(function () {
                    if ($(this).prop('checked')) {
                        var suid = $(this).val();
                        var moid = $(this).attr('data-mvalue');
                        i++;
                        j += setrike(suid, moid, '0', '1');
                    }
                });
                if (i == j) {
                    layer.close(index);
                    layer.msg('批量标记成功', {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg('未知错误', {icon: 5, time: 1600}, function () {
                        location.reload();
                    });
                }
            }, function(index){
                layer.close(index);
            });
        } else {
            layer.confirm('您确定要批量删除选中的数据吗？', {
                btn: ['确定', '取消']
            }, function(index){
                var i = 0;
                var j = 0;
                $('.elctive').each(function () {
                    if ($(this).prop('checked')) {
                        var suid = $(this).val();
                        var moid = $(this).attr('data-mvalue');
                        i++;
                        j += setrike(suid, moid, '1', '1');
                    }
                });
                if (i == j) {
                    layer.close(index);
                    layer.msg('批量删除成功', {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg('未知错误', {icon: 5, time: 1600}, function () {
                        location.reload();
                    });
                }
            }, function(index){
                layer.close(index);
            });
        }
    }

    var uknow = function (suid, moid, uetype) {
        if (uetype == 0) {
            var title = '您确定要标记当前数据状态为已查看吗？';
        } else {
            var title = '您确定要删除这条数据吗？';
        }
        layer.confirm(title, {
            btn: ['确定', '取消']
        }, function (index) {
            layer.close(index);
            setrike(suid, moid, uetype, '0');
        }, function (index) {
            layer.close(index);
        });
    }


    var ulock = false;
    var setrike = function (suid, moid, uetype, batch) {
        if (!ulock) {
            ulock = true;
            if (uetype == 0) {
                var surl = "{:url('journal/jaureak')}";
            } else {
                var surl = "{:url('journal/sprelint')}";
            }
            var recode = 0;
            $.ajax({
                type: "post",
                url: surl,
                async: false,
                data: {'suid': suid, 'moid': moid},
                dataType: 'json',
                success: function (data) {
                    if (batch == 1) {
                        ulock = false;
                        if (data.code > 0) {
                            recode = 1;
                        } else {
                            recode = 0;
                        }
                    } else {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 1600}, function () {
                                ulock = false;
                            });
                        }
                    }
                }
            });
            return recode;
        }
    }



    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('journal/usmur')}&egon={$egon}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('journal/usmur')}&egon={$egon}&page={$page}";
        }
    }

</script>
{/block}