{extend name="/base"/}
{block name="main"}
<style>.customize-span{padding:3px 10px;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}.cust-btn{font-size:14px;padding:5px 10px;}.cust-btn-one{margin-left:-20px;}.cust-btn-activate{border-bottom:solid 2px cornflowerblue;}.layui-layer-page{border-radius:10px;overflow:hidden;}.template-container{float:left;width:200px;height:200px;margin:30px 20px;border-radius:5px;align-items:center;justify-content:center;position:relative;}.template-container-img{width:200px;height:100%;cursor:pointer;border-radius:5px;overflow:hidden;}.template-container-mask-layer{width:79%;height:10%;position:absolute;border-radius:5px 5px 0 0;top:0;}.template-container-text{display:flex;align-items:center;justify-content:center;color:white;text-align:center;flex-wrap:wrap;}.hiden{display:none;}.stimulate:hover div{display:flex;}.am-cf ul + span{top:25px !important;left:10px !important;}</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 插件列表
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="turtle();"></i>
                    <input type="text" class="form-control form-control-solid" id="searchName" value="{$searchName}" placeholder="搜索...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-6 text-left">
                <select id="pluginType" data-am-selected="{btnSize: 'sm'}" onchange="filterType();">
                    <option value="0" {if $egon==0}selected{/if}>全部</option>
                    <option value="1" {if $egon==1}selected{/if}>通用</option>
                    <option value="2" {if $egon==2}selected{/if}>同城</option>
                    <option value="3" {if $egon==3}selected{/if}>校园</option>
                    <option value="4" {if $egon==4}selected{/if}>其他</option>
                </select>
            </div>
            {php}
            if(count($list)>0){
                if($_W['role']=='founder'){
                    echo (base64_decode('PGRpdiBjbGFzcz0iYW0tdS1zbS02IHRleHQtcmlnaHQiPjxidXR0b24gY2xhc3M9ImFtLWJ0biBhbS1idG4tc20gYW0tYnRuLWRlZmF1bHQic3R5bGU9ImJhY2tncm91bmQ6ICM2NWIxNjU7Y29sb3I6IHdoaXRlOyJvbmNsaWNrPSJyZWZyZXNoUGx1Z2luKCk7Ij48aSBjbGFzcz0iYW0taWNvbi1yZWZyZXNoIj48L2k+IOWIt+aWsOaVsOaNrjwvYnV0dG9uPjwvZGl2Pg=='));
                }
            }
            {/php}
        </div>
        <div class="am-g" style="display: flex;justify-content: center;">
            <div class="am-u-sm-12" style="max-width: 1200px;display: flex;flex-wrap: wrap;justify-content: space-around;">
                {volist name="list" id="vo"}
                <div class="template-container">
                    <div class="stimulate" title="此插件仅支持小程序 v{$vo.narrowUnclasp} 及以上版本使用">
                        <div class="template-container-img">
                            <img src="{$vo.narrowImage}" width="200px" height="200px" onclick="lookImage('{$vo.narrowImage}','{$vo.narrowName}');">
                        </div>
                        <div class="template-container-mask-layer hiden" style="background: #000000;opacity: 0.7;width: 100%;height: 35px;"></div>
                        <div class="template-container-mask-layer template-container-text hiden" style="width: 100%;">
                            <span style="width:100%;position: absolute;top: 5px;">
                              v{$vo.narrowUnclasp}
                            </span>
                        </div>
                    </div>
                    {if $vo.narrowUnLock==1}
                    <span style="color: #fff;bottom: 0;text-align: center;position: absolute;width:200px;background-color: #33CC66;height: 35px;border-radius:0 0 5px 5px;line-height: 35px;opacity: 0.75;">已解锁</span>
                    {else}
                    <span style="color:#fff;bottom:0;text-align:center;position:absolute;width:200px;background-color:#CC3333;height:35px;border-radius:0 0 5px 5px;line-height:35px;opacity: 0.75;">未解锁</span>
                    {/if}
                    <div style="color:#37BD72;font-size: 14px;margin-top: 10px;width: 100%;display: flex;justify-content: center;align-items: center;">
                        <div style="margin-top: 1px;">
                            <span style="color: #000;">
                                {$vo.narrowName}{if $_W['role']=='founder' && $vo.narrowUnLock==0}（￥{$vo.narrowPrice|intval}）{/if}
                            </span>
                        </div>
                        {if $vo.narrowUnLock==1}
                        <div style="margin-left: 10px;" title="点击关闭或开启此插件">
                            <el-switch v-model="switchData.p{$vo.narrowId}"></el-switch>
                            <div id="p{$vo.narrowId}" class="hide">{$vo.secretMark}</div>
                        </div>
                        {/if}
                    </div>
                </div>
                {/volist}
                {php} if(count($list)>0){ $i = 8; if(count($list)<=4){ $i=4; } for($i;$i>count($list);$i--){ echo
                (base64_decode('PGRpdiBjbGFzcz0idGVtcGxhdGUtY29udGFpbmVyIj48L2Rpdj4=')); };} {/php}
            </div>
        </div>
        {if count($list)==0}
        <div class="tpl-alert"></div>
        {/if}
        <div class="am-g">
            <div class="tpl-alert"></div>
            <div class="am-u-sm-12 text-center">
                {$list->render()}
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    new Vue({
        el: '#app',
        data() {
            return {
                isReverting: false,
                switchData: {
                    //{php}echo(" -------- 注释开始 --------\n");foreach ($list as $key => $item) { echo "\t\t\t\t\tp".$item['narrowId'].' : '.'Boolean('.$item['selfSwitch']."),\n";}{/php}
                    //{php}echo(" -------- 注释结束 --------\n");{/php}
                }
            }
        }, computed: {
            newSwitchData() {
                return JSON.stringify(this.switchData)
            }
        }, mounted() {
            var findObjOperate = function (obj1, obj2) {
                const resultObj = {};
                Object.keys(obj1).forEach(key => {
                    if (obj1[key] !== obj2[key]) {
                        resultObj[key] = obj2[key];
                    }
                });
                return resultObj;
            }
            this.$watch('newSwitchData', {
                handler(newValue, oldValue) {
                    if (this.isReverting) {
                        this.isReverting = false;
                        return;
                    }
                    var self = this;
                    var newObj = JSON.parse(newValue);
                    var oldObj = JSON.parse(oldValue);
                    var result = findObjOperate(oldObj, newObj);
                    var objKey = Object.keys(result)[0];
                    var secMark = $.trim($('#' + objKey).text());
                    var objValue = Number(result[objKey]);
                    $.post("{:url('sketchpad/togglePluginSwitch')}", {secMark, objValue}, function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, { icon: 1, time: 1800 }, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, { icon: 5, time: 2200 }, function () {
                                if (objValue === 1) {
                                    self.isReverting = true;
                                    self.switchData[objKey] = false;
                                }
                            });
                        }
                    }, 'json');
                },
                deep: true
            });
        }
    });

    var lookImage = function (url, imgAlt) {
        var windowHeight = $(window).height();
        var width = windowHeight * 0.75;
        var height = windowHeight * 0.75;
        layer.open({
            type: 1,
            area: [width, height],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: '<img src="' + url + '" style="width: ' + width + 'px;height: ' + height + 'px;" title="' + imgAlt + '" alt="' + imgAlt + '">'+
                '<div style="position: absolute;top: 0;width: ' + width + 'px;height: ' + height + 'px;"></div>'
        });
    }

    var filterType = function (){
        var pluginType = $.trim($('#pluginType').val());
        var searchName = $.trim($('#searchName').val());
        location.href = "{:url('sketchpad/plugin')}&egon=" + pluginType + "&searchName=" + searchName + "&page={$page}";
    }

    var refreshPlugin = function () {
        $.post("{:url('sketchpad/refreshPlugin')}", {}, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2200});
            }
        }, 'json');
    }

    var turtle = function () {
        var searchName = $.trim($('#searchName').val());
        if (searchName) {
            location.href = "{:url('sketchpad/plugin')}&egon={$egon}&searchName=" + searchName + "&page={$page}";
        } else {
            location.href = "{:url('sketchpad/plugin')}&egon={$egon}&page={$page}";
        }
    }
</script>
{/block}