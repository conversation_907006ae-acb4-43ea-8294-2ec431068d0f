{extend name="/base"/}
{block name="main"}
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> {if $type==0}新增解析配置{else}新增适配解析{/if}
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-9 am-u-sm-push-1">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">解析名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="name" placeholder="请输入解析名称">
                        </div>
                    </div>
                    {if $type==1}
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">适配地址</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="domain" placeholder="请输入适配地址 例如：www.baidu.com">
                        </div>
                    </div>
                    {/if}
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">解析地址</label>
                        <div class="am-u-sm-9">
                            <input type="text" v-model="url" placeholder="请输入解析地址 例如：https://www.baidu.com/">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">请求方法</label>
                        <div class="am-u-sm-9">
                            <select v-model="reqMethod">
                                <option value="0">GET</option>
                                <option value="1">POST</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group" v-if="reqMethod == 1">
                        <label class="am-u-sm-3 am-form-label">请求类型</label>
                        <div class="am-u-sm-9">
                            <select v-model="reqType">
                                <option value="0">Array</option>
                                <option value="1">JSON</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">请求参数</label>
                        <div class="am-u-sm-9">
                            <div style="display:flex;">
                                <input type="text" disabled="disabled" placeholder="点击右侧添加参数">
                                <button type="button" class="btn btn-primary" style="word-break: keep-all;" @click="addReqParamsOtherParams">
                                    添加参数
                                </button>
                            </div>
                            <div class="row" style="margin: 10px 0; padding: 10px 0;border: 2px dashed rgb(204, 204, 204);">
                                <div style="display: flex;">
                                    <div style="width: 15%;display: flex;justify-content: center;align-items: center;font-size: small;">
                                        视频地址参数名
                                    </div>
                                    <div style="width: 25%;display: flex;justify-content: center;align-items: center;">
                                        <input type="text" value="url" disabled>
                                    </div>
                                    <div style="width: 15%;display: flex;justify-content: center;align-items: center;font-size: small;">
                                        自定义参数名
                                    </div>
                                    <div style="width: 25%;display: flex;justify-content: center;align-items: center;">
                                        <input type="text" v-model="reqParams.urlName" placeholder="视频地址参数值例如 url">
                                    </div>
                                    <div style="width: 20%;display: flex;justify-content: center;align-items: center;"></div>
                                </div>
                            </div>
                            <template v-for="(item,index) in reqParams.otherParams">
                                <div class="row" style="margin: 10px 0; padding: 10px 0;border: 2px dashed rgb(204, 204, 204);">
                                    <div style="display: flex;">
                                        <div style="width: 15%;display: flex;justify-content: center;align-items: center;font-size: small;">
                                            自定义参数名 - {{index+1}}
                                        </div>
                                        <div style="width: 25%;display: flex;justify-content: center;align-items: center;">
                                            <input type="text" v-model="item.key" placeholder="key">
                                        </div>
                                        <div style="width: 15%;display: flex;justify-content: center;align-items: center;font-size: small;">
                                            自定义参数值 - {{index+1}}
                                        </div>
                                        <div style="width: 25%;display: flex;justify-content: center;align-items: center;">
                                            <input type="text" v-model="item.value" placeholder="value">
                                        </div>
                                        <div style="width: 20%;display: flex;justify-content: center;align-items: center;">
                                            <button type="button" class="btn btn-danger" style="word-break: keep-all;height: 35px;" @click="removeReqParamsOtherParams(index)">
                                                移除参数
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">默认视频封面图</label>
                        <div class="am-u-sm-9">
                            <img :src="defaultVideoCover" onerror="this.src='static/wechat/image_vip_top.jpg'" @click="changePicture(0,0)" style="min-width:150px;height:150px;cursor:pointer;border: 1px dashed #ccc;">
                            <button type="button" style="margin-left:10px;font-size: 12px;" @click="changePicture(0,0)">
                                选择图片
                            </button>
                            <br>
                            <small style="color: red;">默认视频封面图 如第三方解析接口不提供封面图，则默认使用此图片代替</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">请求结果</label>
                        <div class="am-u-sm-9">
                            <div class="row" style="margin: 10px 0; padding: 10px 0;border: 2px dashed rgb(204, 204, 204);">
                                <div style="display: flex;">
                                    <div style="width: 30%;display: flex;justify-content: center;align-items: center;font-size: small;">
                                        视频标题返回结果名称
                                    </div>
                                    <div style="width: 60%;display: flex;justify-content: center;align-items: center;">
                                        <input type="text" v-model="resParams.title" placeholder="例如：data.title 如第三方接口未提供此项，可以不填写此项">
                                    </div>
                                    <div style="width: 10%;display: flex;justify-content: center;align-items: center;"></div>
                                </div>
                            </div>
                            <div class="row" style="margin: 10px 0; padding: 10px 0;border: 2px dashed rgb(204, 204, 204);">
                                <div style="display: flex;">
                                    <div style="width: 30%;display: flex;justify-content: center;align-items: center;font-size: small;">
                                        视频封面图返回结果名称
                                    </div>
                                    <div style="width: 60%;display: flex;justify-content: center;align-items: center;">
                                        <input type="text" v-model="resParams.cover" placeholder="例如：data.cover 如第三方接口未提供此项，可以不填写此项">
                                    </div>
                                    <div style="width: 10%;display: flex;justify-content: center;align-items: center;"></div>
                                </div>
                            </div>
                            <div class="row" style="margin: 10px 0; padding: 10px 0;border: 2px dashed rgb(204, 204, 204);">
                                <div style="display: flex;">
                                    <div style="width: 30%;display: flex;justify-content: center;align-items: center;font-size: small;">
                                        视频地址返回结果名称
                                    </div>
                                    <div style="width: 60%;display: flex;justify-content: center;align-items: center;">
                                        <input type="text" v-model="resParams.video" placeholder="例如：data.video">
                                    </div>
                                    <div style="width: 10%;display: flex;justify-content: center;align-items: center;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {if $type == 0}
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">默认解析地址</label>
                        <div class="am-u-sm-9">
                            <select v-model="isDefault">
                                <option value="0">否</option>
                                <option value="1">是</option>
                            </select>
                            <small>是否设置为默认解析地址 注：默认解析地址只能设置一个，如果当前的设置为默认解析地址，则其他的默认的地址被自动替换掉</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">解析接口备注</label>
                        <div class="am-u-sm-9">
                            <textarea v-model="appRemark" style="height: 150px;resize: none;" placeholder="视频解析时展示，例如：支持解析列表 抖音、快手、小红书、微博、微视、今日头条、西瓜视频、哔哩哔哩、秒拍、美拍、皮皮虾、皮皮搞笑、全民小视频、火山小视频、好看视频等"></textarea>
                        </div>
                    </div>
                    {/if}
                    <div class="am-form-group" style="display: flex; justify-content: center;margin: 60px 0 40px 0;">
                        <button type="button" class="am-btn am-btn-primary" @click="holdSave">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>
    var vm = new Vue({
        el: '#app',
        data() {
            return {
                name: '',
                type: '{$type}',
                domain: '',
                url: '',
                reqMethod: '0',
                reqType: '0',
                reqParams: {
                    urlName: 'url',
                    otherParams: []
                },
                resParams: {
                    title: '',
                    cover: '',
                    video: ''
                },
                defaultVideoCover: '',
                isDefault: '0',
                appRemark: ''
            };
        }, methods: {
            addReqParamsOtherParams() {
                var otherParamsLength = this.reqParams.otherParams.length + 1;
                this.reqParams.otherParams.push({
                    key: `key-${otherParamsLength}`,
                    value: `value-${otherParamsLength}`
                });
            },
            removeReqParamsOtherParams(index) {
                this.reqParams.otherParams.splice(index, 1);
            },
            changePicture: function (index, openPicture,picturePath) {
                switch (openPicture) {
                    case 0:
                        layer.open({
                            type: 2,
                            anim: 2,
                            scrollbar: true,
                            area: ['900px', '600px'],
                            title: false,
                            closeBtn: 0,
                            shadeClose: true,
                            content: ["{:url('images/dialogimages')}&gclasid=0&pictureIndex=" + index, 'no']
                        });
                        break;
                }
            },
            holdSave() {
                var setData = {};
                setData['name'] = $.trim(this.name);
                if (setData['name'] === '') {
                    layer.msg('请输入解析名称');
                    return;
                }
                setData['type'] = this.type;
                setData['domain'] = $.trim(this.domain);
                if (setData['domain'] === '' && '{$type}' === '1') {
                    layer.msg('请输入适配地址');
                    return;
                }
                setData['url'] = $.trim(this.url);
                if (setData['url'] === '') {
                    layer.msg('请输入解析地址');
                    return;
                }
                setData['reqMethod'] = this.reqMethod;
                setData['reqType'] = this.reqType;
                setData['reqParams'] = this.reqParams;
                setData['resParams'] = this.resParams;
                setData['defaultVideoCover'] = this.defaultVideoCover;
                if (setData['defaultVideoCover'] === '') {
                    layer.msg('请选择默认视频封面图');
                    return;
                }
                setData['isDefault'] = this.isDefault;
                setData['appRemark'] = this.appRemark;
                $.post("{:url('resolve/new_parse_list')}", setData, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                            location.href = "{if $type==0}{:url('resolve/parse_list')}{else}{:url('resolve/adaptation_parse_list')}{/if}";
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2200});
                    }
                }, 'json');
            }
        }
    });

    var sutake = function (eurl, pictureIndex, type) {
        switch (type) {
            case 0:
                vm.defaultVideoCover = eurl;
                break;
        }
        layer.closeAll();
    }
</script>
{/block}