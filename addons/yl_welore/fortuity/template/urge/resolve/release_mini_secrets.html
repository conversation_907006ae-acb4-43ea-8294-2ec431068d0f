{extend name="/base"/}
{block name="main"}
<style>.am-modal-hd .am-close {top: -5px;right: -3px;}.el-date-editor .el-input__inner{width:100% !important;margin-top:-1px !important;padding-left:30px !important;}.el-picker-panel.el-date-picker.el-popper{z-index:10001 !important;}</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-edit"></span> 发布小秘密
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div style="width: 78%;margin: 0 auto;">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">@用户编号</label>
                        <div class="am-u-sm-8 am-u-end">
                            <el-input v-model="atUserId" placeholder="请输入要@用户的编号" clearable></el-input>
                            <small>请输入要@用户的编号 [ 非必填项 ]</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">发布内容</label>
                        <div class="am-u-sm-8 am-u-end">
                            <div style="width: 100%;height: 100%;margin-top: 5px;position: relative;">
                                <div style="position: absolute;top: 0;padding-left: 3px;background: #dddddd;width: 100%;height: 30px;opacity: 0.75;">
                                    <ul style="display: flex;">
                                        <li style="width: 30px;display: flex;justify-content: center;color: #1055ab;">
                                            <span style="cursor: pointer;" title="点击添加表情" onclick="complicatedFunc(0);">
                                                <span class="am-icon-meh-o"></span>
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                                <textarea v-model="content" style="height: 250px;resize: none;padding-top: 35px;"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group replyPicture">
                        <label class="am-u-sm-3 am-form-label" style="margin-top:5px;">发布图片</label>
                        <div class="am-u-sm-8 am-u-end">
                            <div style="width: 100%;display: flex;align-items: center;flex-shrink: 0;flex-wrap: wrap;">
                                <draggable v-model="imgList" group="imgListGroup" @start="drag=true" @end="drag=false" style="display: flex;align-items: center;flex-wrap: wrap;">
                                    <div style="position: relative;width: 126px;height: 126px;margin: 2px;flex-shrink: 0;" v-for="(item,index) in imgList">
                                        <img :src="item" onerror="this.src='static/disappear/default.png'" style="width: 100%;height: 100%;border: 1px #ccc dashed;object-fit: contain;">
                                        <span style="position: absolute;top: -5px;right: 3px;cursor: pointer;" @click="removeImgList(index)">×</span>
                                    </div>
                                </draggable>
                                <button type="button" style="font-size: 12px;margin-top:10px;height: 25px;flex-shrink: 0;" onclick="cuonice();">选择图片</button>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">点赞人数</label>
                        <div class="am-u-sm-8 am-u-end">
                            <el-input v-model="praiseNumber" placeholder="请输入点赞人数" clearable></el-input>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">发布时间</label>
                        <div class="am-u-sm-8 am-u-end">
                            <el-date-picker style="width: 100%;" v-model="sendTime" type="datetime" value-format="timestamp" placeholder="选择日期时间"></el-date-picker>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin-top: 30px;display: flex;justify-content: center;">
                        <button type="button" class="am-btn am-btn-primary" @click="holdSave">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script src="assets/js/sortable.min.js"></script>
<script src="assets/js/vuedraggable.umd.min.js"></script>
<script>
    Vue.component('vuedraggable', window.vuedraggable.name);
    var vm = new Vue({
        components: {
            vuedraggable,
        },
        el: '#app',
        data() {
            return {
                userId: '{$Request.get.fid}',
                atUserId: '',
                content: '',
                imgList: [''],
                praiseNumber: '0',
                sendTime: '',
                isLock: false
            };
        }, methods: {
            removeImgList(index) {
                var isSplice = true;
                if (index === 0) {
                    if (this.imgList[index] !== '') {
                        this.imgList[index] = '';
                    }
                    isSplice = false;
                }
                if (isSplice) {
                    this.imgList.splice(index, 1);
                }
                this.$forceUpdate();
            },
            holdSave() {
                var setData = {};
                setData['userId'] = this.userId;
                setData['atUserId'] = parseInt(this.atUserId);
                setData['content'] = this.content.trim();
                if (setData['content'] === '') {
                    layer.msg('请输入发布内容');
                    return;
                }
                setData['imgList'] = this.imgList;
                setData['praiseNumber'] = parseInt(this.praiseNumber);
                setData['sendTime'] = $.trim(this.sendTime);
                if (setData['sendTime'] === '') {
                    layer.msg('请选择发布时间');
                    return;
                }
                this.isLock = true;
                $.post("{:url('resolve/release_mini_secrets')}", setData, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2200}, function () {
                            this.isLock = false;
                        });
                    }
                }, 'json');
            }
        }
    });

    function sutake(eurl) {
        var isInsert = true;
        if (vm.imgList.length === 1) {
            if (vm.imgList[0] === '') {
                vm.imgList[0] = eurl;
                isInsert = false;
            }
        }
        if (isInsert) {
            vm.imgList.push(eurl);
        }
        vm.$forceUpdate();
    }

    function cuonice() {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    function complicatedFunc(type) {
        switch (type) {
            case 0:
                layer.open({
                    type: 2,
                    anim: 2,
                    title: false,
                    area: ['450px', '400px'],
                    scrollbar: true,
                    closeBtn: false,
                    shadeClose: true,
                    content: ["{:url('tedious/emoji')}&type=" + type, 'no'],
                });
                break;
        }
    }

    function complicatedCallFunc(type, data) {
        switch (type) {
            case 0:
                vm.content = vm.content + data['content'];
                break;
        }
    }

</script>
{/block}