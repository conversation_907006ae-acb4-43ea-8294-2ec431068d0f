{extend name="/base"/}
{block name="main"}
<style>.am-modal-hd .am-close {top: -5px;right: -3px;}.el-date-editor .el-input__inner{width:100% !important;margin-top:-1px !important;padding-left:30px !important;}.el-picker-panel.el-date-picker.el-popper{z-index:10001 !important;}</style>
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-edit"></span> 回复小秘密
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div style="width: 78%;margin: 0 auto;">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">回复编号</label>
                        <div class="am-u-sm-8 am-u-end">
                            <el-input v-model="spId" placeholder="请输入要回复的小秘密ID" clearable></el-input>
                            <small>请输入要回复的小秘密ID</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">回复内容</label>
                        <div class="am-u-sm-8 am-u-end">
                            <div style="width: 100%;height: 100%;margin-top: 5px;position: relative;">
                                <div style="position: absolute;top: 0;padding-left: 3px;background: #dddddd;width: 100%;height: 30px;opacity: 0.75;">
                                    <ul style="display: flex;">
                                        <li style="width: 30px;display: flex;justify-content: center;color: #1055ab;">
                                            <span style="cursor: pointer;" title="点击添加表情" onclick="complicatedFunc(0);">
                                                <span class="am-icon-meh-o"></span>
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                                <textarea v-model="content" style="height: 250px;resize: none;padding-top: 35px;"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">回复时间</label>
                        <div class="am-u-sm-8 am-u-end">
                            <el-date-picker style="width: 100%;" v-model="replyTime" type="datetime" value-format="timestamp" placeholder="选择日期时间"></el-date-picker>
                        </div>
                    </div>
                    <div class="am-form-group" style="margin-top: 30px;display: flex;justify-content: center;">
                        <button type="button" class="am-btn am-btn-primary" @click="holdSave">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>
    var vm = new Vue({
        el: '#app',
        data() {
            return {
                spId: '',
                userId: '{$Request.get.fid}',
                content: '',
                replyTime: '',
                isLock: false
            };
        }, methods: {
            holdSave() {
                var setData = {};
                setData['spId'] = parseInt(this.spId);
                setData['userId'] = this.userId;
                setData['content'] = this.content.trim();
                if (setData['content'] === '') {
                    layer.msg('请输入回复内容');
                    return;
                }
                setData['replyTime'] = $.trim(this.replyTime);
                if (setData['replyTime'] === '') {
                    layer.msg('请选择回复时间');
                    return;
                }
                this.isLock = true;
                $.post("{:url('resolve/reply_mini_secrets')}", setData, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2200}, function () {
                            this.isLock = false;
                        });
                    }
                }, 'json');
            }
        }
    });


    function complicatedFunc(type) {
        switch (type) {
            case 0:
                layer.open({
                    type: 2,
                    anim: 2,
                    title: false,
                    area: ['450px', '400px'],
                    scrollbar: true,
                    closeBtn: false,
                    shadeClose: true,
                    content: ["{:url('tedious/emoji')}&type=" + type, 'no'],
                });
                break;
        }
    }

    function complicatedCallFunc(type, data) {
        switch (type) {
            case 0:
                vm.content = vm.content + data['content'];
                break;
        }
    }

</script>
{/block}