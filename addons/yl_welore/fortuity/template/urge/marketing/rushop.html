{extend name="/base"/}
{block name="main"}
<style>.am-modal-hd .am-close{top:-5px;right:-3px;}.w-e-menu{font-size:12px}.w-e-text,.w-e-text-container{height:500px !important;}@media screen and (max-width:1024px){.product-details{margin:10px 0 120px 0 !important;}}@media screen and (max-width:768px){.product-details{margin:10px 0 140px 0 !important;}}@media screen and (max-width:640px){.product-details{margin:10px 0 160px 0 !important;}}@media screen and (max-width:400px){.product-details{margin:10px 0 200px 0 !important;}}@media screen and (max-width:320px){.product-details{margin:10px 0 220px 0 !important;}}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 新增商品
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-10 am-u-end">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">商品名称</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="name">
                            <small>请输入商品名称</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">商品类型</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select id="type">
                                <option value="0">请选择类型</option>
                                {volist name="stypeList" id="vo"}
                                <option value="{$vo.id}">{$vo.name}</option>
                                {/volist}
                            </select>
                            <small>选择商品属于的分类</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">商品描述</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="text" id="synopsis">
                            <small>请输入商品描述</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">商品图片</label>
                        <div class="am-u-sm-9">
                            <div id="shion">
                                <div class="multiple-img" style="width:120px;height:120px;margin:10px 20px 10px 0;position: relative;float: left;">
                                    <img src="" name="sngimg" onerror="this.src='static/disappear/default.png'" style="width: 120px;height: 120px;margin: 7px 0 0 3px;"/>
                                    <div style="width: 126px;height: 126px;position: absolute;top: 4px;border: 1px solid #cccccc;border-radius: 3px;">
                                        <div class="am-modal-hd" style="text-align: left;">
                                            <a href="javascript: void(0);" class="am-close am-close-spin" style="color:#2D93CA;opacity:1;" onclick="multipleClose(this);">×</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="am-u-sm-9" style="margin-top: 30px;">
                            <button type="button" style="font-size: 12px;" onclick="cuonice(0,0);">
                                选择图片
                            </button>
                            <small>建议图片尺寸：260*260px</small>
                            <span style="color: red;font-size: 12px;">商品图片可以通过拖拽进行排序 </span>
                            <small><strong>( 第一张图片将作为商品主图 )</strong></small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">付款类型</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select id="payType">
                                <option value="-1">请选择</option>
                                <option value="0">贝壳支付</option>
                                <option value="1">积分支付</option>
                                <option value="2">微信支付</option>
                            </select>
                            <small>选择一个类型作为商品的付款方式</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">商品规格</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select id="multiple-specs">
                                <option value="0">默认规格</option>
                                <option value="1">多个规格</option>
                            </select>
                            <small>选择多个规格将分别设置数据</small>
                        </div>
                    </div>
                    <div id="multiple-variant-1">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">商品价格</label>
                            <div class="am-u-sm-8 am-u-end">
                                <input type="number" id="price" value="0.00" oninput="grender(this,2);">
                                <small>请输入商品价格</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">商品库存</label>
                            <div class="am-u-sm-8 am-u-end">
                                <input type="number" id="inventory" value="0" oninput="grender(this);">
                                <small>请输入商品库存</small>
                            </div>
                        </div>
                    </div>
                    <div id="multiple-variant-2" style="display: none;">
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">属性名称</label>
                            <div class="am-u-sm-8 am-u-end">
                                <input type="text" id="saName" placeholder="请输入属性名称">
                                <small>请输入属性名称</small>
                            </div>
                        </div>
                        <div class="am-form-group">
                            <label class="am-u-sm-3 am-form-label">规格种类</label>
                            <div class="am-u-sm-8 am-u-end">
                                <div style="display: flex;">
                                    <input type="text" disabled placeholder="设置规格种类">
                                    <button class="am-btn am-btn-primary am-active am-btn-xs" onclick="selectShopSpecs();">
                                        设置规格种类
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div id="app" class="am-form-group">
                            <div class="am-u-sm-offset-3 am-u-sm-8 am-u-end">
                                <div v-for="(item,index) in saList" style="border: 1px #ccc dashed;padding: 15px 0;margin: 5px 0;">
                                    <div style="display: flex;justify-content: space-between;">
                                        <div style="width: 10%;display: flex;justify-content: center;align-items: center;padding: 0 5px;">
                                            <label class="am-u-sm-6 am-form-label">规格名称</label>
                                            <div class="am-u-sm-6" style="font-size: 12px;font-weight: bold;">
                                                {{item.at_name}}
                                            </div>
                                        </div>
                                        <div style="width: 20%;display: flex;justify-content: center;align-items: center;padding: 0 5px;">
                                            <label class="am-u-sm-4 am-form-label">图片</label>
                                            <div class="am-u-sm-8 am-u-end" >
                                                <img :src="item.sa_img" @error="item.sa_img='./static/disappear/default.png'" style="width: 100px;height: 100px;cursor: pointer;border: 1px dashed #c2cad8;" @click="cuonice(1,index)">
                                            </div>
                                        </div>
                                        <div style="width: 20%;display: flex;justify-content: center;align-items: center;padding: 0 5px;">
                                            <label class="am-u-sm-4 am-form-label">价格</label>
                                            <div class="am-u-sm-8 am-u-end">
                                                <input type="text" v-model="item.price" placeholder="请输入价格" @input="numericalLimit(index,'price',2)">
                                            </div>
                                        </div>
                                        <div style="width: 20%;display: flex;justify-content: center;align-items: center;padding: 0 5px;">
                                            <label class="am-u-sm-4 am-form-label">库存</label>
                                            <div class="am-u-sm-8 am-u-end">
                                                <input type="text" v-model="item.inventory_count" placeholder="请输入库存" @input="numericalLimit(index,'inventory_count')">
                                            </div>
                                        </div>
                                        <div style="width: 20%;display: flex;justify-content: center;align-items: center;padding: 0 5px;">
                                            <label class="am-u-sm-4 am-form-label">排序</label>
                                            <div class="am-u-sm-8 am-u-end">
                                                <input type="text" v-model="item.sort" placeholder="请输入排序" @input="numericalLimit(index,'sort')">
                                            </div>
                                        </div>
                                        <div style="width: 10%;display: flex;justify-content: center;align-items: center;padding: 0 5px;">
                                            <span style="color: red; cursor: pointer; font-size: 26px;" title="移除规格" @click="removeImage(index)">
                                                ×
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">商品销量</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="number" id="volume" value="0" placeholder="请输入商品销量" oninput="grender(this);">
                            <small>请输入商品销量</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">限购数量</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="number" id="restrict" value="0" placeholder="请输入限购数量" oninput="grender(this);">
                            <small>请输入限购数量</small>
                            <small style="color: red;padding-left: 5px;">0 表示不限购</small>
                        </div>
                    </div>

                    <div class="am-form-group" {if !$offlinePickup}style="display: none"{/if}>
                        <label class="am-u-sm-3 am-form-label">商家商品</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select id="isOffline" onchange="isOfflineChange();">
                                <option value="0">否</option>
                                <option value="1">是</option>
                            </select>
                            <small>是否属于第三方商家线下自提商品</small>
                        </div>
                    </div>
                    <div class="am-form-group" {if !$autoDelivery}style="display: none"{/if}>
                        <label class="am-u-sm-3 am-form-label">发货方式</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select id="autoDelivery">
                                <option value="-1">请选择</option>
                                <option value="0" {if !$autoDelivery}selected{/if}>手动发货</option>
                                <option value="1">自动发货</option>
                            </select>
                            <small>选择自动发货需在 [ 卡密列表 ] 绑定或输入相应的卡密数据</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">开启会员专属</label>
                        <div class="am-u-sm-8 am-u-end">
                            <div class="tpl-switch">
                                <input id="exclusive" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn">
                                <div class="tpl-switch-btn-view">
                                    <div></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group noble-vent">
                        <label class="am-u-sm-3 am-form-label">开启会员折扣</label>
                        <div class="am-u-sm-8 am-u-end">
                            <div class="tpl-switch">
                                <input id="opdiscount" type="checkbox" style="display: none;" class="ios-switch bigswitch tpl-switch-btn">
                                <div class="tpl-switch-btn-view">
                                    <div></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group noble-vent">
                        <label class="am-u-sm-3 am-form-label">会员购物享受折扣比例</label>
                        <div class="am-u-sm-8">
                            <input type="number" id="nodiscount" value="100" placeholder="请输入会员购物折扣比例" oninput="grender(this);">
                            <small>
                                会员购物享受折扣率 例如折扣比例为 95% <span style="color: red;">计算方式 [ 实付价格 ( {$defaultNavigate.currency} )  = 商品价格 * 95% ]</span>
                            </small>
                        </div>
                        <label style="color: #999;font-weight: normal;font-size: 16px;line-height: 35px;width: auto;">%</label>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">会员购赠返{$defaultNavigate.confer}</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="number" id="rebate" value="0.00" placeholder="请输入返还{$defaultNavigate.confer}" oninput="grender(this);">
                        </div>
                    </div>
                    <div class="am-form-group product-details" style="margin:10px 0 60px 0;">
                        <label class="am-u-sm-3 am-form-label">商品详情</label>
                        <div class="am-u-sm-8 am-u-end">
                            <div style="height:500px;" id="detail" placeholder="请输入商品详情"></div>
                            <span id="customizeGallery" style="display:none;" onclick="cuonice(2,0);"></span>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">商品状态</label>
                        <div class="am-u-sm-8 am-u-end">
                            <select id="status">
                                <option value="0">已下架</option>
                                <option value="1">已上架</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">排序</label>
                        <div class="am-u-sm-8 am-u-end">
                            <input type="number" id="scores" value="0" placeholder="请输入排序数字" oninput="grender(this);">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <div class="am-u-sm-8 am-u-end am-u-sm-push-3">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script src="assets/js/jquery.dad.min.js?v=1.0"></script>
<script type="text/javascript" src="static/wangEditor/wangEditor.min.js?v=1.0"></script>
<link rel="stylesheet" href="static/wangEditor/wangEditor-fullscreen-plugin.css?v=1.0">
<script type="text/javascript" src="static/wangEditor/wangEditor-fullscreen-plugin.js?v=1.0"></script>
<script>

    var E = window.wangEditor;
    var editor = new E('#detail');
    editor.customConfig.uploadImgServer = true;
    editor.create();
    E.fullscreen.init('#detail');

    !function () {
        $('.tpl-switch').find('.tpl-switch-btn-view').on('click', function () {
            $(this).prev('.tpl-switch-btn').prop("checked", function () {
                if ($(this).is(':checked')) {
                    return false;
                } else {
                    return true;
                }
            });
            if ($('#exclusive').prop('checked')) {
                $('#opdiscount').prop('checked', false);
                $('.noble-vent').hide();
                $('#nodiscount').val('100');
            } else {
                $('.noble-vent').show();
            }
        });
        
        $('#multiple-specs').click(function () {
            if (Number($(this).val()) !== 0) {
                $('#multiple-variant-1').hide();
                $('#multiple-variant-2').show();
            }else {
                $('#multiple-variant-1').show();
                $('#multiple-variant-2').hide();
            }
        });
    }();

    var vm = new Vue({
        el: '#app',
        data() {
            return {
                saList: []
            }
        },
        methods: {
            saListDataSort() {
                for (var i = 0; i < Object.keys(this.saList).length; i++) {
                    for (var j = 0; j < Object.keys(this.saList).length - i - 1; j++) {
                        if (parseInt(this.saList[j].sort) > parseInt(this.saList[j + 1].sort)) {
                            var tempData = {};
                            tempData = this.saList[j];
                            this.saList[j] = this.saList[j + 1];
                            this.saList[j + 1] = tempData;
                        }
                    }
                }
            },
            numericalLimit(index, offset, limit) {
                if (limit === 2) {
                    this.saList[index][offset] = this.saList[index][offset].replace(/[^\d.]/g, "");
                    this.saList[index][offset] = this.saList[index][offset].replace(/^\./g, "");
                    this.saList[index][offset] = this.saList[index][offset].replace(/\.{2,}/g, ".");
                    this.saList[index][offset] = this.saList[index][offset].replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
                    this.saList[index][offset] = this.saList[index][offset].replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
                } else {
                    this.saList[index][offset] = Number((this.saList[index][offset].match(/^\d+(?:\.\d{0})?/)));
                }
            },
            removeImage(index) {
                this.saList.splice(index, 1);
            },
        }
    });

    var grender = function (obj, limit) {
        if (limit === 2) {
            obj.value = obj.value.replace(/[^\d.]/g, "");
            obj.value = obj.value.replace(/^\./g, "");
            obj.value = obj.value.replace(/\.{2,}/g, ".");
            obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
            obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
        } else {
            obj.value = Number((obj.value.match(/^\d+(?:\.\d{0})?/)));
        }
    }

    var selectShopSpecs = function () {
        layer.open({
            type: 2,
            anim: 2,
            title: false,
            area: ['900px', '640px'],
            scrollbar: false,
            closeBtn: true,
            shadeClose: true,
            content: ["{:url('tedious/select_shop_specs')}", 'no'],
        });
    }

    var AttributeRefrain = function (sid, at_name) {
        var isRepeat = false;
        for (var i = 0; i < vm.saList.length; i++) {
            if (vm.saList[i]['id'] === sid) {
                isRepeat = true;
                break;
            }
        }
        if (!isRepeat) {
            vm.saList.push({'sa_id': sid, 'sa_img': '', 'at_name': at_name, 'price': 0.00, 'inventory_count': 0, 'sort': 0});
            vm.$forceUpdate();
            layer.closeAll();
        } else {
            layer.msg('规格种类已存在，请重新选择！');
        }
    }

    var isOfflineChange = function () {
        var isOffline = Number($('#isOffline').val());
        if (isOffline === 0) {
            $('#autoDelivery').parent().parent().show();
        } else {
            $('#autoDelivery').parent().parent().hide();
        }
    }

    var cuonice = function (type, index) {
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogimages')}&gclasid=0&dynamicStyle=" + type + "&pictureIndex=" + index, 'no']
        });
    }

    var sutake = function (surl, index, type) {
        switch (type) {
            case 0:
                var multipleImg = $.trim($('.multiple-img').eq(0).attr('data-multiple-img'));
                if (multipleImg == '') {
                    $('#shion').html('');
                }
                var shtml = '<div class="multiple-img" style="width:120px;height:120px;margin:10px 20px 10px 0;position: relative;float: left;" data-multiple-img="' + surl + '">';
                shtml += '<img src="' + surl + '" name="sngimg" onerror="this.src=\'static/disappear/default.png\'" style="width: 120px;height: 120px;margin: 7px 0 0 3px;"/>';
                shtml += '<div style="width: 126px;height: 126px;position: absolute;top: 4px;border: 1px solid #cccccc;border-radius: 3px;">';
                shtml += '<div class="am-modal-hd" style="text-align: left;">';
                shtml += '<a href="javascript: void(0);" class="am-close am-close-spin" style="color:#2D93CA;opacity:1;" onclick="multipleClose(this);">×</a>';
                shtml += '</div>';
                shtml += '</div>';
                shtml += '</div>';
                $('#shion').append(shtml);
                $('#shion').dad();
                break;
            case 1:
                vm.saList[index]['sa_img'] = surl;
                break;
            case 2:
                editor.cmd.do('insertHTML', '<img src="' + surl + '" style="max-width:100%;"/>');
                break;
        }
        layer.closeAll();
    }

    var multipleClose = function (obj) {
        $(obj).parent().parent().parent().remove();
        setTimeout(function () {
            if ($('.multiple-img').length < 1) {
                var shtml = '<div class="multiple-img" style="width:120px;height:120px;margin:10px 20px 10px 0;position: relative;float: left;">';
                shtml += '<img src="" name="sngimg" onerror="this.src=\'static/disappear/default.png\'" style="width: 120px;height: 120px;margin: 7px 0 0 3px;"/>';
                shtml += '<div style="width: 126px;height: 126px;position: absolute;top: 4px;border: 1px solid #cccccc;border-radius: 3px;">';
                shtml += '<div class="am-modal-hd" style="text-align: left;">';
                shtml += '<a href="javascript: void(0);" class="am-close am-close-spin" style="color:#2D93CA;opacity:1;" onclick="multipleClose(this);">×</a>';
                shtml += '</div>';
                shtml += '</div>';
                shtml += '</div>';
                $('#shion').append(shtml);
            }
        }, 500);
    }
    var slock = false;
    function holdSave() {
        if (!slock) {

            var setData = {};
            setData['name'] = $.trim($('#name').val());
            setData['type'] = Number($('#type').val());
            setData['synopsis'] = $.trim($('#synopsis').val());
            setData['multipleImg'] = [];
            $('.multiple-img').each(function () {
                setData['multipleImg'].push($.trim($(this).attr('data-multiple-img')));
            });
            setData['detail'] = $.trim(editor.txt.html());
            setData['inventory'] = Number($('#inventory').val());
            setData['payType'] = Number($('#payType').val());
            setData['price'] = Number($('#price').val());
            setData['volume'] = Number($('#volume').val());
            setData['restrict'] = Number($('#restrict').val());
            setData['exclusive'] =  $('#exclusive').prop('checked') ? 1 : 0;
            setData['opdiscount'] = $('#opdiscount').prop('checked') ? 1 : 0;
            setData['nodiscount'] = Number($('#nodiscount').val());
            setData['rebate'] = Number($('#rebate').val());
            setData['isOffline'] = Number($('#isOffline').val());
            setData['autoDelivery'] = Number($('#autoDelivery').val());
            setData['status'] = $.trim($('#status').val());
            setData['scores'] = Number($('#scores').val());

            if (setData['name'] === '') {
                layer.msg('商品名称不能为空');
                return;
            }
            if (setData['type'] === 0) {
                layer.msg('请选择商品分类');
                return;
            }
            if (setData['synopsis'] === '') {
                layer.msg('商品描述不能为空');
                return;
            }
            if (setData['multipleImg'][0] === '' || setData['multipleImg'] === 'undefined' || setData['multipleImg'] === null) {
                layer.msg('请上传商品图片');
                return;
            }
            if (setData['detail'] === '' || setData['detail'] === 'undefined' || setData['detail'] == null) {
                layer.msg('商品详情不能为空');
                return;
            }
            if (setData['inventory'] <= 0) {
                $('#inventory').val('0');
            }
            if (setData['payType'] === -1) {
                layer.msg('请选择商品付款类型！');
                return;
            }

            setData['multipleSpecs'] = Number($('#multiple-specs').val());
            if (setData['multipleSpecs'] === 1) {
                setData['saName'] = $.trim($('#saName').val());
                if (setData['saName']===''){
                    layer.msg('属性名称不能为空');
                    return;
                }
                //  保存排序
                vm.saListDataSort();
                setData['saList'] = vm.saList;
                if (setData['saList'].length === 0) {
                    layer.msg('规格种类至少设置一项');
                    return;
                } else {
                    var dataIsPass = true;
                    for (var i = 0; i < setData['saList'].length; i++) {
                        if (setData['saList'][i]['sa_img'] === '' || setData['saList'][i]['sa_img'] === './static/disappear/default.png') {
                            dataIsPass = false;
                            layer.msg('请选择规格图片');
                            break;
                        }
                    }
                    if (!dataIsPass) {
                        return;
                    }
                }
            }

            if (setData['price'] <= 0) {
                $('#price').val('0.00');
            }
            if (setData['volume'] <= 0) {
                $('#volume').val('0');
            }
            if (setData['restrict'] <= 0) {
                $('#restrict').val('0');
            }
            if (setData['nodiscount'] > 100 || setData['nodiscount'] < 1) {
                layer.msg('会员购物享受折扣比例最小不能低于1%<br>最大不能超过100%');
                return;
            }
            if (setData['rebate'] <= 0) {
                $('#rebate').val('0.00');
            }
            if (setData['isOffline'] === 0 && setData['autoDelivery'] === -1) {
                layer.msg('请选择发货方式');
                return;
            }
            if (setData['scores'] <= 0) {
                $('#scores').val('0');
            }
            slock = true;
            $.ajax({
                type: "post",
                url: "{:url('marketing/rushop')}",
                data: setData,
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.href = "{:url('marketing/shop')}";
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                            slock = false;
                        });
                    }
                }
            });
        }
    }
</script>
{/block}