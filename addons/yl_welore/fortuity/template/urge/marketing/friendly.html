{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-gift {margin-right: 5px;color: #e91e63;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fff;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .add-btn {display: inline-flex;align-items: center;padding: 8px 16px;background: #23b7e5;color: #fff;border-radius: 4px;cursor: pointer;transition: all 0.3s;font-size: 14px;border: none;}
    .add-btn:hover {background: #1a9bc0;color: #fff;}
    .add-btn .am-icon-adn {margin-right: 5px;}
    .btn-section {margin-bottom: 20px;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;table-layout: fixed;width: 100%;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 12px 8px;line-height: 1.6;}
    .am-table > tbody > tr > td {padding: 12px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;overflow: hidden;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .am-table th, .am-table td {width: auto !important;}
    .am-table [class*="am-u-"] {width: auto !important;}
    .sort-input {width: 180px;max-width: 180px;min-width: 180px;height: 32px;padding: 4px 6px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fff;transition: all 0.3s;font-size: 13px;text-align: center;flex-shrink: 0;}
    .sort-input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .gift-image {width: 50px;height: 50px;border-radius: 4px;border: 1px solid #e8e8e8;object-fit: cover;}
    .status-badge {display: inline-block;padding: 4px 8px;border-radius: 12px;font-size: 11px;font-weight: 500;min-width: 50px;max-width: 50px;text-align: center;box-sizing: border-box;}
    .status-hidden {background-color: #f8d7da;color: #721c24;border: 1px solid #f5c6cb;}
    .status-normal {background-color: #d4edda;color: #155724;border: 1px solid #c3e6cb;}
    .action-btn {padding: 6px 12px;border-radius: 4px;font-size: 12px;cursor: pointer;transition: all 0.3s;border: none;margin: 0 2px;}
    .btn-edit {background: #fff;color: #17a2b8;border: 1px solid #17a2b8;}
    .btn-edit:hover {background: #17a2b8;color: #fff;}
    .btn-delete {background: #fff;color: #dc3545;border: 1px solid #dc3545;}
    .btn-delete:hover {background: #dc3545;color: #fff;}
    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-gift"></span> 礼物列表
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索礼物名称...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g btn-section">
            <div class="am-u-sm-12">
                <div class="am-btn-toolbar">
                    <button type="button" class="add-btn" onclick="saloof();">
                        <span class="am-icon-plus"></span> 新增礼物
                    </button>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="16.66%">排序</th>
                            <th width="16.66%">礼物图标</th>
                            <th width="16.66%">礼物名称</th>
                            <th width="16.66%">{$defaultNavigate.currency}数量</th>
                            <th width="16.66%">状态</th>
                            <th width="16.67%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                <div style="display: flex;justify-content: center;">
                                    <input type="text" class="sort-input" id="sfixed-{$vo.id}" value="{$vo.scores}" data-score="{$vo.scores}" onblur="supre('{$vo.id}','#sfixed-{$vo.id}');">
                                </div>
                            </td>
                            <td>
                                <img src="{$vo.tr_icon}" onerror="this.src='static/disappear/default.png'" class="gift-image"/>
                            </td>
                            <td>{$vo.tr_name}</td>
                            <td>{$vo.tr_conch}</td>
                            <td>
                                {if $vo.status == 0}
                                <span class="status-badge status-hidden">隐藏</span>
                                {else}
                                <span class="status-badge status-normal">正常</span>
                                {/if}
                            </td>
                            <td>
                                <button type="button" class="action-btn btn-edit" onclick="uploof('{$vo.id}');">
                                    <span class="am-icon-edit"></span> 编辑
                                </button>
                                <button type="button" class="action-btn btn-delete" onclick="navlintDel('{$vo.id}','{$vo.tr_name}');">
                                    <span class="am-icon-trash"></span> 删除
                                </button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    function supre(asyId, domId) {
        var dalue = $(domId).val();
        var daioe = $(domId).attr('data-score');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(domId).val(daioe);
            return false;
        }
        if (dalue != daioe) {
            var repair = exalter(asyId, dalue);
            if (repair.code > 0) {
                layer.msg(repair.msg, {icon: 1, time: 800});
                $(domId).attr('data-score', dalue);
            } else {
                layer.msg(repair.msg, {icon: 5, time: 1600});
            }
        }
    }

    function exalter(asyId, dalue) {
        var straw = [];
        $.ajax({
            type: "post",
            url: "{:url('sfrieng')}",
            data: {
                asyId: asyId,
                dalue: dalue
            },
            async: false,
            success: function (data) {
                straw = data;
            }
        });
        return straw;
    }


    function saloof() {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('marketing/rufriendly')}");
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }

    function uploof(uplid) {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('marketing/upfriendly')}&uplid=" + uplid);
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }


    var lock = false;

    function navlintDel(mid, vname) {
        if (!lock) {
            lock = true;
            var shint = '您确定要 <span style="color: red">删除</span> <span style="color: blue;">' + vname + '</span> 吗？';
            layer.confirm(shint, {
                btn: ['确定', '取消'], 'title': '删除提示 (<span style="color: #8dd1db;">数据将不可恢复</span>)'
            }, function () {
                $.post(
                    "{:url('defriendly')}",
                    {'ecid': mid},
                    function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                lock = false;
                            });
                        }
                    }, 'json');
            }, function () {
                lock = false;
            });
        }
    }

    function fuzzy() {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name)
            location.href = "{:url('marketing/friendly')}&hazy_name=" + fz_name + "&page={$page}";
        else
            location.href = "{:url('marketing/friendly')}&page={$page}";
    }

</script>
{/block}