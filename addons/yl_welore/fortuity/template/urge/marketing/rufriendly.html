{extend name="/base"/}
{block name="main"}
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 新增礼物
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">礼物名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="name" placeholder="请输入礼物名称">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">礼物图标</label>
                        <div class="am-u-sm-9">
                            <img src="" id="shion" onerror="this.src='static/disappear/default.png'" onclick="cuonice();" style="width: 100px;height: 100px;cursor: pointer;"/>
                            <button type="button" style="font-size: 12px;" onclick="cuonice();">选择图片</button>
                            <small>建议图片尺寸：158*150px</small>
                            <input type="hidden" name="sngimg">
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">{$defaultNavigate.currency}数量</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="conch" placeholder="请输入{$defaultNavigate.currency}数量">
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">状态</label>
                        <div class="am-u-sm-9">
                            <select id="status">
                                <option value="0">隐藏</option>
                                <option value="1">显示</option>
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">排序</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="scores" value="0" placeholder="请输入排序数字">
                        </div>
                    </div>

                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-push-3">
                            <button type="button" class="am-btn am-btn-primary" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    function cuonice() {
        layer.open({
            type: 2,
            anim:2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    function sutake(eurl) {
        $('#shion').attr('src', eurl);
        $("[name='sngimg']").val(eurl);
        layer.closeAll();
    }

    function excheck(name, sngimg, conch, scores) {
        if (name == '' || name == 'undefined' || name == null) {
            layer.msg('礼物名称不能为空');
            return false;
        }
        if (sngimg == '' || sngimg == 'undefined' || sngimg == null) {
            layer.msg('请上传礼物图标');
            return false;
        }
        if (conch == '' || conch == 'undefined' || conch == null) {
            layer.msg('{$defaultNavigate.currency}数量不能为空');
            return false;
        }
        if (scores == '' || scores == 'undefined' || scores == null) {
            layer.msg('排序不能为空');
            return false;
        }
        if (scores > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            return false;
        }
        return true;
    }

    var slock = false;

    function holdSave() {
        if (!slock) {
            var name = $.trim($('#name').val());
            var sngimg = $.trim($("[name='sngimg']").val());
            var conch = $.trim($('#conch').val());
            var status = $.trim($('#status').val());
            var scores = $.trim($('#scores').val());
            if (excheck(name, sngimg, conch, scores)) {
                slock = true;
                $.ajax({
                    type: "post",
                    url: "{:url('rufriendly')}",
                    data: {
                        'name': name,
                        'icon': sngimg,
                        'conch': conch,
                        'status': status,
                        'scores': scores
                    },
                    dataType: 'json',
                    success: function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.href = "{:url('friendly')}";
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                slock = false;
                            });
                        }
                    }
                });
            }
        }
    }
</script>
{/block}