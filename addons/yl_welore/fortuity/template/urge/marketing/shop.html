{extend name="/base"/}
{block name="main"}
<style>.am-table-striped > tbody > tr:nth-child(odd) > td,.am-table > tbody > tr > td{line-height:86px;}.am-btn-group > .am-btn:first-child:not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:last-child:not(:first-child),.am-btn-group > .am-btn:not(:first-child):not(:last-child):not(.am-dropdown-toggle),.am-btn-group > .am-btn:first-child{margin-top:8px;}.resth button{margin-right:5px;}.cust-btn{font-size:14px;padding:5px 10px;}.cust-btn-one{margin-left:-20px;}.cust-btn-activate{border-bottom:solid 2px cornflowerblue;}.cust-aver-img{width:82px;height:82px;position:absolute;top:calc(50% - 41px);left:calc(50% - 41px);border:1px solid #cccccc;border-radius:3px;}.cust-noble-img{background-image:url('static/disappear/icon_coupon.png');background-size:40px;background-repeat:no-repeat;width:82px;height:82px;position:absolute;top:calc(50% - 41px);left:calc(50% - 41px);border:1px solid #cccccc;border-radius:3px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-shopping-cart"></span> 商品列表
        </div>
        <div style="text-align: right;">
            <div class="am-btn-toolbar" style="padding-top: 5px;">
                <a href="{:url('marketing/rushop')}" class="customize-span">
                    <span class="am-icon-adn"></span> 新增商品
                </a>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div id="search-list" style="display: flex;flex-wrap: wrap;width: 100%;min-height: 38px;">
                <div class="search-wide-pitch search-inline">
                    <label class="search-label">ID</label>
                    <div class="search-input-inline">
                        <input type="text" name="sid" value="{$sid}" class="search-input">
                    </div>
                </div>
                <div class="search-wide-pitch search-inline">
                    <label class="search-label">商品名称</label>
                    <div class="search-input-inline">
                        <input type="text" name="name" value="{$name}" class="search-input">
                    </div>
                </div>
                <div class="search-wide-pitch search-inline">
                    <label class="search-label">商品类型</label>
                    <div class="search-input-inline">
                        <input type="text" name="type" value="{$type}" class="search-input">
                    </div>
                </div>
                <button class="search-wide-pitch search-btn" onclick="turtle();">
                    <i class="am-icon-search"></i> 搜 索
                </button>
            </div>
            <div class="am-g">
                <div class="am-u-sm-6" style="padding-left: 30px;">
                    <div class="am-btn-toolbar" style="margin: 10px 0 5px 20px;">
                        <div class="am-btn-group am-btn-group-xs resth no-wrap">
                            <a href="{:url('marketing/shop')}&egon=0&sid={$sid}&name={$name}&type={$type}" class="cust-btn cust-btn-one {if $egon==0}cust-btn-activate{/if}">全部</a>
                            <a href="{:url('marketing/shop')}&egon=1&sid={$sid}&name={$name}&type={$type}" class="cust-btn {if $egon==1}cust-btn-activate{/if}">已上架</a>
                            <a href="{:url('marketing/shop')}&egon=2&sid={$sid}&name={$name}&type={$type}" class="cust-btn {if $egon==2}cust-btn-activate{/if}">已下架</a>
                            <a href="{:url('marketing/shop')}&egon=3&sid={$sid}&name={$name}&type={$type}" class="cust-btn {if $egon==3}cust-btn-activate{/if}">已售罄</a>
                            <a href="{:url('marketing/shop')}&egon=4&sid={$sid}&name={$name}&type={$type}" class="cust-btn {if $egon==4}cust-btn-activate{/if}">会员专属</a>
                            <a href="{:url('marketing/shop')}&egon=5&sid={$sid}&name={$name}&type={$type}" class="cust-btn {if $egon==5}cust-btn-activate{/if}">回收站</a>
                        </div>
                    </div>
                </div>
                <div class="am-u-sm-6 no-wrap" style="text-align: right;">
                    {if $egon==5}
                    <button type="button" class="am-btn am-btn-default am-btn-xs am-btn-danger" onclick="rebatch('1');">
                        批量彻底删除
                    </button>
                    {else}
                    <button type="button" class="am-btn am-btn-default am-btn-xs am-btn-danger" onclick="rebatch('0');">
                        批量删除
                    </button>
                    {/if}
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form" style="overflow-x:auto;">
                    <table class="am-table am-table-striped am-table-hover table-main">
                        <thead>
                        <tr>
                            <th class="am-text-middle text-center am-text-nowrap" width="5%">
                                <input id="withole" type="checkbox" class="tpl-table-fz-check">全选
                            </th>
                            <th class="am-text-middle text-center am-text-nowrap" width="5%">ID</th>
                            <th class="am-text-middle text-center am-text-nowrap" width="7%">排序</th>
                            <th class="am-text-middle text-center am-text-nowrap" width="8%">商品缩略图</th>
                            <th class="am-text-middle text-center am-text-nowrap" width="10%">商品名称</th>
                            <th class="am-text-middle text-center am-text-nowrap" width="6%">商品类型</th>
                            <th class="am-text-middle text-center am-text-nowrap" width="6%">商品销量</th>
                            <th class="am-text-middle text-center am-text-nowrap" width="6%">商品库存</th>
                            <th class="am-text-middle text-center am-text-nowrap" width="10%">商品价格</th>
                            {if $autoDelivery}
                            <th class="am-text-middle text-center am-text-nowrap" width="8%">发货类型</th>
                            {/if}
                            <th class="am-text-middle text-center am-text-nowrap" width="8%">商品状态</th>
                            <th class="am-text-middle text-center am-text-nowrap" width="16%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle text-center am-text-nowrap">
                                <input type="checkbox" class="tpl-table-fz-check elctive" value="{$vo.id}">
                            </td>
                            <td class="am-text-middle text-center am-text-nowrap">
                                {$vo.id}
                            </td>
                            <td class="am-text-middle text-center am-text-nowrap">
                                <div style="display: flex;justify-content: center;align-items: center;">
                                    <input type="text" class="scfixed" id="sfixed-{$vo.id}" value="{$vo.scores}" data-score="{$vo.scores}" style="width: 50px;" onblur="supre('{$vo.id}','#sfixed-{$vo.id}');">
                                </div>
                            </td>
                            <td class="am-text-middle text-center am-text-nowrap" style="position: relative;">
                                <img src="{$vo.product_img|athumbnail}" style="width: 78px;height: 78px;border-radius: 3px;margin: 1.5px">
                                <div class="{if $vo.noble_exclusive==1}cust-noble-img{else}cust-aver-img{/if}"></div>
                            </td>
                            <td class="am-text-middle text-center am-text-nowrap">
                                <span title="{$vo.product_name}">
                                    {$vo.product_name|subtext=6}
                                </span>
                            </td>
                            <td class="am-text-middle text-center am-text-nowrap">{$vo.tpname}</td>
                            <td class="am-text-middle text-center am-text-nowrap">{$vo.sales_volume}</td>
                            <td class="am-text-middle text-center am-text-nowrap">{$vo.product_inventory}</td>
                            <td class="am-text-middle text-center am-text-nowrap">
                                {$vo.product_price} ( {switch $vo.pay_type}{case 0}贝壳{/case}{case 1}积分{/case}{case 2}微信支付{/case}{/switch} )
                            </td>
                            {if $autoDelivery}
                            <td class="am-text-middle text-center am-text-nowrap">
                                {if $vo.auto_delivery}
                                <a href="{:url('cammy/shopanomaly')}&sid={$vo.id}" title="点击跳转到绑定的商品卡密">
                                    自动发货
                                </a>
                                {else}
                                    手动发货
                                {/if}
                            </td>
                            {/if}
                            <td class="am-text-middle text-center am-text-nowrap">
                                {if $vo.status == 0}
                                <span style="background: red; color: white;padding: 5px;border-radius: 3px;">已下架</span>
                                {elseif $vo.status == 1}
                                <span style="background: lightseagreen; color: white;padding: 5px;border-radius: 3px;">已上架</span>
                                {/if}
                                {if $vo.product_inventory==0}
                                <span style="background: orange; color: white;padding: 5px;border-radius: 3px;">已售罄</span>
                                {/if}
                            </td>
                            <td class="am-text-middle text-center am-text-nowrap">
                                {if $vo.trash==0}
                                <button type="button" class="am-btn am-btn-default am-btn-xs am-text-secondary" onclick="unute('{$vo.id}');">
                                    <span class="am-icon-pencil-square-o"></span>编辑
                                </button>
                                {else}
                                <button type="button" class="am-btn am-btn-default am-btn-xs am-text-secondary" onclick="uecover('{$vo.id}');">
                                    <span class="am-icon-mail-reply"></span>恢复
                                </button>
                                {/if}
                                {if $vo.trash==0}
                                <button type="button" class="am-btn am-btn-default am-btn-xs am-text-danger" onclick="uknow('{$vo.id}','0','0');">
                                    <span class="am-icon-trash-o"></span>删除
                                </button>
                                {else}
                                <button type="button" class="am-btn am-btn-default am-btn-xs am-text-danger" onclick="uknow('{$vo.id}','0','1');">
                                    <span class="am-icon-trash-o"></span>彻底删除
                                </button>
                                {/if}
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                </div>
                <div class="am-u-sm-12 no-wrap" style="text-align:center;">
                    {$list->render()}
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>
    !function () {
        $('#withole').click(function () {
            var wecked = $(this).prop('checked');
            if (wecked) {
                $('.elctive').prop('checked', true);
            } else {
                $('.elctive').prop('checked', false);
            }
        });
        $('.elctive').click(function () {
            var jack = 1;
            $('.elctive').each(function () {
                var weck = $(this).prop('checked');
                if (weck == false) {
                    jack = 0;
                    return false;
                }
            });
            if (jack) {
                $('#withole').prop('checked', true);
            } else {
                $('#withole').prop('checked', false);
            }
        });
    }();

    function rebatch(thorough) {
        var tired = false;
        $('.elctive').each(function () {
            if ($(this).prop('checked')) {
                tired = true;
                return false;
            }
        });
        if (!tired) {
            layer.alert('很抱歉，当前没有选中要操作的数据', {'title': '系统提示'});
            return;
        }
        if (thorough == 0) {
            var rough = "您确定要批量删除选中的数据吗？";
        } else {
            var rough = "批量彻底删除后数据将不可恢复，您还要继续吗？";
        }
        layer.confirm(rough, {
            btn: ['确定', '取消']
        }, function (index) {
            var i = 0;
            var j = 0;
            $('.elctive').each(function () {
                if ($(this).prop('checked')) {
                    var suid = $(this).val();
                    i++;
                    j += setrike(suid, '1', thorough);
                }
            });
            if (i == j) {
                layer.close(index);
                layer.msg('批量删除成功', {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg('未知错误', {icon: 5, time: 1600}, function () {
                    location.reload();
                });
            }
        }, function (index) {
            layer.close(index);
        });
    }

    function supre(asyId, domId) {
        var dalue = $(domId).val();
        var daioe = $(domId).attr('data-score');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(domId).val(daioe);
            return false;
        }
        if (dalue != daioe) {
            var repair = exalter(asyId, dalue);
            if (repair.code > 0) {
                layer.msg(repair.msg, {icon: 1, time: 800});
                $(domId).attr('data-score', dalue);
            } else {
                layer.msg(repair.msg, {icon: 5, time: 1600});
            }
        }
    }

    function exalter(asyId, dalue) {
        var straw = {};
        $.ajax({
            type: "post",
            url: "{:url('dopslue')}",
            data: {
                asyId: asyId,
                dalue: dalue
            },
            async: false,
            success: function (data) {
                straw = data;
            }
        });
        return straw;
    }

    function unute(usid) {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('marketing/upshop')}&usid=" + usid);
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }

    function uknow(suid, batch, thorough) {
        if (thorough == 0) {
            var rough = "您确定要删除选中的数据吗？";
        } else {
            var rough = "彻底删除后数据将不可恢复，您还要继续吗？";
        }
        layer.confirm(rough, {
            btn: ['确定', '取消'], 'title': '系统提示'
        }, function (index) {
            layer.close(index);
            setrike(suid, batch, thorough);
        }, function (index) {
            layer.close(index);
        });
    }

    function uecover(suid) {
        layer.confirm("您确定要恢复这条数据吗？", {
            btn: ['确定', '取消'], 'title': '系统提示'
        }, function (index) {
            layer.close(index);
            $.post("{:url('resume')}",{'suid':suid},function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 1600});
                }
            })
        }, function (index) {
            layer.close(index);
        });
    }
    

    var ulock = false;
    function setrike(suid, batch, thorough) {
        if (!ulock) {
            ulock = true;
            var recode = 0;
            $.ajax({
                type: "post",
                url: "{:url('shoplint')}",
                async: false,
                data: {'suid': suid, 'thorough': thorough},
                dataType: 'json',
                success: function (data) {
                    if (batch == 1) {
                        ulock = false;
                        if (data.code > 0) {
                            recode = 1;
                        } else {
                            recode = 0;
                        }
                    } else {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 1600}, function () {
                                ulock = false;
                            });
                        }
                    }
                }
            });
            return recode;
        }
    }


    function turtle() {
        var searchInput = $('#search-list .search-input');
        var n = 0;
        for (var i = 0; i < searchInput.length; i++) {
            if (searchInput[i].value.trim() !== '') {
                n++;
            }
        }
        if (n > 0) {
            location.href = `{:url('marketing/shop')}&egon={$egon}&${searchInput.serialize()}&page={$page}`;
        } else {
            location.href = "{:url('marketing/shop')}&egon={$egon}&page={$page}";
        }
    }

</script>
{/block}