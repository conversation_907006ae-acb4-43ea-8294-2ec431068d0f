{extend name="/base"/}
{block name="main"}
<div id="app" class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-reorder"></span> 中奖详情
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g tpl-amazeui-form tpl-form-line-form">
            <div class="am-u-sm-12 am-u-md-12">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">抽奖编号</label>
                        <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                            <input type="text" disabled style="padding-left:10px;"  :value="list.record_number">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">奖品名称</label>
                        <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                            <input type="text" disabled style="padding-left:10px;" :value="list.prize_name">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">抽奖时间</label>
                        <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                            <input type="text" disabled style="padding-left:10px;" :value="list.join_time">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">用户姓名</label>
                        <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                            <input type="text" style="padding-left:10px;" v-model="list.address_details.name" placeholder="未填写">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">手机号</label>
                        <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                            <input type="text" style="padding-left:10px;" v-model="list.address_details.phone" placeholder="未填写">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">收货地址</label>
                        <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                            <input type="text" style="padding-left:10px;" v-model="list.address_details.address" placeholder="未填写">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">派奖状况</label>
                        <div class="am-u-sm-7 am-u-end" style="margin-top:3px;">
                            <template v-if="list.delivery_status==0">
                                <select v-model="list.delivery_status" style="padding-left:10px;" :style="{'color':list.delivery_status > 0 ? 'green':'red'}">
                                    <option value="0" style="color:red;">待派奖</option>
                                    <option value="1" style="color:green;">已派奖</option>
                                </select>
                            </template>
                            <template v-else>
                                <input type="text" disabled style="padding-left:10px;color:green;" value="已派奖">
                            </template>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">物流信息/兑换码</label>
                        <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                            <input type="text" style="padding-left:10px;" v-model="list.courier_convert" placeholder="未填写">
                        </div>
                    </div>
                    <div class="am-form-group" v-if="seeJudge">
                        <label class="am-u-sm-3 am-form-label">派发时间</label>
                        <div class="am-u-sm-7 am-u-end" style="margin-top: 3px;">
                            <input type="text" disabled style="padding-left:10px;" :value="list.see_time">
                        </div>
                    </div>
                    <div class="am-form-group" style="display: flex;justify-content: center;margin-top: 50px;">
                        <button type="button" class="am-btn am-btn-primary" @click="holdSave();">保存更改</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    var formatDate = function (outDate) {
        var date = new Date(outDate);
        let Y = date.getFullYear() + "-";
        let M = date.getMonth() + 1 + "-";
        let D = date.getDate()  + " ";
        let h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
        let m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes());
        return Y + M + D + h + m;
    }

    var vm = new Vue({
        el: '#app',
        data: {
            touch: [],
            list: [],
            seeJudge: false
        },
        created: function () {
            $.ajax({
                type: 'post',
                async: false,
                url: "{:url('unlawful/promulgate')}",
                data: {'getData': true, 'urrid': '{$urrid}'},
                dataType: 'json',
                success: data => {
                    this.touch = data[0];
                    if (data[1].address_details == null) {
                        data[1].address_details = '{"name":"","phone":"","address":""}';
                    }
                    data[1].address_details = eval('(' + data[1].address_details + ')');
                    data[1].join_time = formatDate(data[1].join_time * 1000);
                    if (data[1].see_time > 0) {
                        this.seeJudge = true;
                    }
                    data[1].see_time = formatDate(data[1].see_time * 1000);
                    this.list = data[1];
                }
            });
        },
        methods: {
            holdSave: function () {
                if ($.trim(this.list.courier_convert) == '') {
                    layer.msg('请填写 物流信息/兑换码');
                    return;
                }
                if (this.list.delivery_status == 0) {
                    layer.msg('请更改派奖状况');
                    return;
                }
                var arbData = {};
                arbData['id'] = this.list.id;
                arbData['uid'] = this.list.user_id;
                arbData['prizeName'] = this.list.prize_name;
                arbData['name'] = this.list.address_details.name;
                arbData['phone'] = this.list.address_details.phone
                arbData['address'] = this.list.address_details.address;
                arbData['status'] = this.list.delivery_status;
                arbData['postal'] = this.list.courier_convert;
                $.post("{:url('unlawful/promulgate')}", arbData, function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }, 'json');
            }
        }
    });


</script>
{/block}