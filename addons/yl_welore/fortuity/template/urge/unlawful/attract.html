{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-bullhorn {margin-right: 5px;color: #23b7e5;}
    .record-btn {background: linear-gradient(135deg, #28a745 0%, #20c997 100%);border: none;color: white;padding: 4px 8px;border-radius: 6px;font-size: 13px;text-decoration: none;cursor: pointer;transition: all 0.3s ease;box-shadow: 0 2px 4px rgba(40,167,69,0.2);display: inline-flex;align-items: center;gap: 6px;}
    .record-btn:hover {background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);box-shadow: 0 4px 8px rgba(40,167,69,0.3);transform: translateY(-1px);color: white;text-decoration: none;}
    .am-form-horizontal {background: #fff;border-radius: 6px;padding: 20px;box-shadow: 0 1px 3px rgba(0,0,0,0.05);}
    .am-form-group {margin-bottom: 20px;padding: 15px 0;border-bottom: 1px solid #f5f5f5;}
    .am-form-group:last-child {border-bottom: none;}
    .am-form-label {font-weight: 500;color: #333;line-height: 1.6;}
    .am-form-group input[type="text"] {height: 36px;padding: 8px 12px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fff;transition: all 0.3s;font-size: 14px;width: 100%;}
    .am-form-group input[type="text"]:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .am-form-group select {height: 36px;padding: 8px 12px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fff;transition: all 0.3s;font-size: 14px;width: 100%;}
    .am-form-group select:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .form-hint {color: #666;font-size: 12px;margin-top: 5px;display: block;line-height: 1.4;}
    .form-hint-warning {color: #dc3545;font-weight: 500;}
    .save-btn {background: linear-gradient(135deg, #23b7e5 0%, #1a9bc0 100%);border: none;color: white;padding: 12px 30px;border-radius: 6px;font-size: 14px;font-weight: 500;cursor: pointer;transition: all 0.3s ease;box-shadow: 0 2px 4px rgba(35,183,229,0.2);}
    .save-btn:hover {background: linear-gradient(135deg, #1a9bc0 0%, #1587a8 100%);box-shadow: 0 4px 8px rgba(35,183,229,0.3);transform: translateY(-1px);}
    .save-btn:active {transform: translateY(0);box-shadow: 0 2px 4px rgba(35,183,229,0.2);}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-bullhorn"></span> 新人营销
        </div>
        <div>
            <a href="{:url('unlawful/attractRecord')}" class="record-btn" target="_blank">
                任务完成记录
            </a>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g tpl-amazeui-form">
            <div class="am-u-sm-offset-1 am-u-sm-9">
                <div class="am-form am-form-horizontal">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">营销开关</label>
                        <div class="am-u-sm-7 am-u-end">
                            <select id="rewardStatus">
                                <option value="0" {if $list.reward_status==0}selected{/if}>关闭</option>
                                <option value="1" {if $list.reward_status==1}selected{/if}>开启</option>
                            </select>
                            <small class="form-hint">开启后新注册用户完成下列任务即可获得奖励</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">注册时间</label>
                        <div class="am-u-sm-7 am-u-end">
                            <input type="text" id="regLessDay" value="{$list.reg_less_day}" oninput="grender(this);">
                            <small class="form-hint">新用户注册需在多少天内完成任务才有奖励</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">奖励类型</label>
                        <div class="am-u-sm-7 am-u-end">
                            <select id="rewardType">
                                <option value="0" {if $list.reward_type==0}selected{/if}>积分</option>
                                <option value="1" {if $list.reward_type==1}selected{/if}>贝壳</option>
                            </select>
                            <small class="form-hint">新人完成任务奖励的内容</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">奖励分数</label>
                        <div class="am-u-sm-7 am-u-end">
                            <input type="text" id="rewardCode" value="{$list.reward_code}" oninput="grender(this,2);">
                            <small class="form-hint">完成新人任务奖励的分数 奖励内容为上方设置的奖励类型</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">最多奖励次数</label>
                        <div class="am-u-sm-7 am-u-end">
                            <input type="text" id="rewardCount" value="{$list.reward_count}" oninput="grender(this);">
                            <small class="form-hint">新人完成任务后最多可获得奖励的次数 0为不限制次数</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">圈子编号</label>
                        <div class="am-u-sm-7 am-u-end">
                            <input type="text" id="toryIds" value="{$list.tory_ids}">
                            <small class="form-hint form-hint-warning">为空则在任何圈子都可获得奖励</small>
                            <small class="form-hint">圈子编号 在指定圈子内发帖或回复可以获得奖励 多个圈子使用英文的逗号分隔 例如 1,2,3</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">帖子编号</label>
                        <div class="am-u-sm-7 am-u-end">
                            <input type="text" id="paperIds" value="{$list.paper_ids}">
                            <small class="form-hint form-hint-warning">为空则在任何帖子都可获得奖励</small>
                            <small class="form-hint">帖子编号 在指定帖子内回复或评论可以获得奖励 多个帖子使用英文的逗号分隔 例如 5,6,7</small>
                        </div>
                    </div>
                    <div class="am-form-group am-margin-top-lg" style="border-bottom: none;">
                        <div class="am-u-sm-9 am-u-sm-push-5" style="margin-left: 45px;">
                            <button type="button" class="save-btn" onclick="holdSave();">
                                <span class="am-icon-save"></span> 保存配置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    var grender = function (obj, limit) {
        if (limit === 2) {
            obj.value = obj.value.replace(/[^\d.]/g, "");
            obj.value = obj.value.replace(/^\./g, "");
            obj.value = obj.value.replace(/\.{2,}/g, ".");
            obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
            obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
        } else {
            obj.value = Number((obj.value.match(/^\d+(?:\.\d{0})?/)));
        }
    }

    var holdSave = function () {
        var setData = {};
        setData['uplid'] = '{$list.id}';
        setData['rewardStatus'] = $.trim($("#rewardStatus").val());
        setData['regLessDay'] = $.trim($("#regLessDay").val());
        setData['rewardType'] = $.trim($("#rewardType").val());
        setData['rewardCode'] = $.trim($("#rewardCode").val());
        setData['rewardCount'] = $.trim($("#rewardCount").val());
        setData['toryIds'] = $.trim($("#toryIds").val());
        setData['paperIds'] = $.trim($("#paperIds").val());
        $.post("{:url('unlawful/attract')}", setData, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg(data.msg, {icon: 5, time: 2000});
            }
        }, 'json');
    }
</script>
{/block}