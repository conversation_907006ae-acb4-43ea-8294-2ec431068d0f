{extend name="/base"/}
{block name="main"}
<style>
    .tpl-portlet-components {background: #fff;border-radius: 4px;box-shadow: 0 1px 3px rgba(0,0,0,0.1);padding: 20px;margin-bottom: 20px;position: relative;}
    .tpl-portlet-components:after {content: "";display: table;clear: both;}
    .portlet-title {display: flex;justify-content: space-between;align-items: center;padding-bottom: 15px;margin-bottom: 15px;border-bottom: 1px solid #f0f0f0;}
    .caption {font-size: 16px;color: #23b7e5;font-weight: 500;}
    .caption .am-icon-list-alt {margin-right: 5px;color: #23b7e5;}
    .tpl-portlet-input {position: relative;}
    .tpl-portlet-input input {height: 32px;width: 200px;padding: 0 30px 0 10px;border: 1px solid #e8e8e8;border-radius: 4px;background: #fff;transition: all 0.3s;}
    .tpl-portlet-input input:focus {border-color: #23b7e5;background: #fff;box-shadow: 0 0 0 2px rgba(35,183,229,0.1);}
    .tpl-portlet-input .am-icon-search {position: absolute;right: 10px;top: 50%;transform: translateY(-50%);color: #999;cursor: pointer;}
    .filter-tabs {display: inline-flex;gap: 0;margin: 10px 0 20px 0;border-radius: 6px;overflow: hidden;border: 1px solid #e8e8e8;width: auto;}
    .filter-tab {padding: 10px 20px;background: #fff;color: #666;text-decoration: none;border-right: 1px solid #e8e8e8;transition: all 0.3s;font-size: 14px;}
    .filter-tab:last-child {border-right: none;}
    .filter-tab:hover {background: #f8f9fa;color: #333;text-decoration: none;}
    .filter-tab.active {background: #23b7e5;color: #fff;}
    .filter-container {width: auto !important;}
    .filter-container .am-u-sm-12 {width: auto !important;}
    .am-table {border: 1px solid #f0f0f0;border-radius: 4px;font-size: 13px;margin-bottom: 15px;table-layout: fixed;width: 100%;}
    .am-table > thead:first-child > tr:first-child > th {background-color: #f9f9f9;border-bottom: 1px solid #eee;color: #333;font-weight: 500;font-size: 13px;text-align: center;padding: 12px 8px;line-height: 1.6;width: 12.5%;}
    .am-table > tbody > tr > td {padding: 12px 8px;border-top: 1px solid #f3f3f3;text-align: center;vertical-align: middle;color: #666;line-height: 1.6;width: 12.5%;overflow: hidden;}
    .am-table-striped > tbody > tr:nth-child(odd) > td {background-color: #fafafa;}
    .am-table > tbody > tr:hover > td {background-color: #f5fafd;}
    .am-table th, .am-table td {width: auto !important;}
    .am-table [class*="am-u-"] {width: auto !important;}
    .status-badge {display: inline-block;padding: 4px 8px;border-radius: 12px;font-size: 11px;font-weight: 500;white-space: nowrap;width: auto;max-width: 60px;text-align: center;}
    .status-pending {background-color: #fff3cd;color: #856404;border: 1px solid #ffeaa7;}
    .status-completed {background-color: #d4edda;color: #155724;border: 1px solid #c3e6cb;}
    .status-none {color: #6c757d;}
    .user-link {color: #23b7e5;text-decoration: none;transition: all 0.3s;}
    .user-link:hover {color: #1a9bc0;text-decoration: underline;}
    .openid-link {color: #6c757d;text-decoration: none;transition: all 0.3s;font-size: 12px;}
    .openid-link:hover {color: #495057;text-decoration: underline;}
    .action-btn {padding: 6px 12px;border-radius: 4px;font-size: 12px;cursor: pointer;transition: all 0.3s;border: 1px solid #17a2b8;background: #fff;color: #17a2b8;text-decoration: none;}
    .action-btn:hover {background: #17a2b8;color: #fff;text-decoration: none;}
    .am-pagination {margin: 10px 0;}
    .am-pagination > li > a {color: #666;background-color: #fff;border: 1px solid #e8e8e8;margin: 0 3px;border-radius: 3px;}
    .am-pagination > .am-active > a {background-color: #23b7e5;border-color: #23b7e5;}
    .am-pagination > li > a:hover {background-color: #f5fafd;border-color: #e8e8e8;color: #23b7e5;}
</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-list-alt"></span> 抽奖记录
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="turtle();"></i>
                <input type="text" id="hazyName" value="{$hazyName}" placeholder="搜索用户名称或编号...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g filter-container">
            <div class="am-u-sm-12">
                <div class="filter-tabs">
                    <a href="{:url('unlawful/cheer')}&erid={$erid}&egon=0" class="filter-tab {if $egon==0}active{/if}">全部</a>
                    <a href="{:url('unlawful/cheer')}&erid={$erid}&egon=1" class="filter-tab {if $egon==1}active{/if}">待派奖</a>
                    <a href="{:url('unlawful/cheer')}&erid={$erid}&egon=2" class="filter-tab {if $egon==2}active{/if}">已派奖</a>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="12.5%">抽奖编号</th>
                            <th width="12.5%">用户名称</th>
                            <th width="12.5%">用户openid</th>
                            <th width="12.5%">奖品名称</th>
                            <th width="12.5%">中奖类型</th>
                            <th width="12.5%">抽奖时间</th>
                            <th width="12.5%">派奖状况</th>
                            <th width="12.5%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>{$vo.record_number}</td>
                            <td>
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_nick_name|emoji_decode}" target="_blank" class="user-link">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user_nick_name}" class="user-link">
                                    {$vo.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td>
                                {if $vo.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1" title="{$vo.user_wechat_open_id}" target="_blank" class="openid-link">
                                    {$vo.user_wechat_open_id}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user_nick_name|filter_emoji}&page=1" title="{$vo.user_wechat_open_id}" target="_blank" class="openid-link">
                                    {$vo.user_wechat_open_id}
                                </a>
                                {/if}
                            </td>
                            <td>{$vo.prize_name}</td>
                            <td>
                                {switch $vo.win_type}
                                {case 0}未中奖{/case}
                                {case 1}实物奖励{/case}
                                {case 2}{$mize.confer}奖励{/case}
                                {case 3}{$mize.currency}奖励{/case}
                                {case 4}经验值奖励{/case}
                                {case 5}荣誉点奖励{/case}
                                {/switch}
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.join_time)}</td>
                            <td>
                                {if $vo.delivery_status==0}
                                <span class="status-badge status-pending">待派奖</span>
                                {elseif $vo.delivery_status==1}
                                {if $vo.win_type == 0}<span class="status-none">——</span>{else}<span class="status-badge status-completed">已派奖</span>{/if}
                                {/if}
                            </td>
                            <td>
                                {if $vo.win_type==1}
                                <a href="{:url('unlawful/promulgate')}&urrid={$vo.id}" target="_blank" class="action-btn">
                                    详情
                                </a>
                                {else}
                                <span class="status-none">——</span>
                                {/if}
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var turtle = function () {
        var hazyName = $.trim($('#hazyName').val());
        if (hazyName) {
            location.href = "{:url('unlawful/cheer')}&erid={$erid}&egon={$egon}&hazyName=" + hazyName + "&page={$page}";
        } else {
            location.href = "{:url('unlawful/cheer')}&erid={$erid}&egon={$egon}&page={$page}";
        }
    }

</script>
{/block}