{extend name="/base"/}
{block name="main"}
<style>.customize-span,.customize-span:hover{padding:3px 10px;color:black !important;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}.am-table > thead:first-child > tr:first-child > th{text-align:center;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 拦截手机
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="fz_name" value="{$hazy_name}" placeholder="搜索拦截规则...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g">
            <div class="am-u-sm-4">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-xs">
                        <a href="javascript:void(0);" class="customize-span" onclick="newAnomaly();">
                            <span class="am-icon-adn"></span> 新增拦截规则
                        </a>
                    </div>
                    <button class="am-btn am-btn-secondary am-btn-xs am-round" onclick="testIntercept();" style="margin:-2px 0 0 10px;">
                        测试拦截
                    </button>
                </div>
            </div>
            <div class="am-u-sm-8">
                <div class="am-form-group text-right">
                    <label>
                        <span style="font-size:16px;font-weight: 500;">匹配类型</span>
                        <select id="egon" data-am-selected="{btnSize: 'sm'}" onchange="filterAnomaly();">
                            <option value="0" {if $egon==0}selected{/if}>全部类型</option>
                            <option value="1" {if $egon==1}selected{/if}>完全匹配</option>
                            <option value="2" {if $egon==2}selected{/if}>正则匹配</option>
                        </select>
                    </label>
                    <label style="margin-left: 15px;">
                        <span style="font-size:16px;font-weight: 500;">拦截类型</span>
                        <select id="inType" data-am-selected="{btnSize: 'sm'}" onchange="filterAnomaly();">
                            <option value="0" {if $inType==0}selected{/if}>全部类型</option>
                            <option value="1" {if $inType==1}selected{/if}>黑名单</option>
                            <option value="2" {if $inType==2}selected{/if}>白名单</option>
                        </select>
                    </label>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover table-main" style="text-align:center;">
                        <thead>
                        <tr>
                            <th width="40%">匹配规则</th>
                            <th width="15%">匹配类型</th>
                            <th width="15%">拦截类型</th>
                            <th width="15%">创建时间</th>
                            <th width="15%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle">
                                <span>{$vo.block_rule}</span>
                            </td>
                            <td class="am-text-middle">
                                {switch $vo.block_type}
                                {case 0}完全匹配{/case}
                                {case 1}正则匹配{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle">
                                {if !$vo.intercept_type}黑名单{else}白名单{/if}
                            </td>
                            <td class="am-text-middle">
                                {:date('Y-m-d H:i:s',$vo.create_time)}
                            </td>
                            <td class="am-text-middle">
                                <span onclick="eraseAnomaly('{$vo.id}',0);">
                                    <span style="color:#fa2222;padding:4px 12px;background:#fdfdfd;cursor: pointer;border: 1px solid #ccc;word-break: keep-all;">
                                        删除规则
                                    </span>
                                </span>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
<div id="mutual" class="hide">
    <div class="am-g tpl-amazeui-form" style="background: #fcfcfc;width: 100%;height: 100%;">
        <div class="am-form am-form-horizontal">
            <div class="am-form-group" style="margin-top: 35px;">
                <label class="am-u-sm-4 am-form-label">匹配类型</label>
                <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                    <select class="blockType" style="width: 236px;" onchange="selectType(this);">
                        <option value="-1">请选择</option>
                        <option value="0">完全匹配</option>
                        <option value="1">正则匹配</option>
                    </select>
                    <small class="blockTypeZero blockTypeOne">ㅤ</small>
                    <small class="blockTypeTwo" style="display: none;">
                        <strong>
                            正则匹配为 <span style="color: red;">正则表达式匹配</span>
                        </strong>
                    </small>
                </div>
            </div>
            <div class="am-form-group" style="margin-top: 30px;">
                <label class="am-u-sm-4 am-form-label">匹配规则</label>
                <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                    <input class="blockRule" type="text" style="width: 236px;" >
                    <small class="blockTypeZero">ㅤ<br>ㅤ</small>
                    <small class="blockTypeOne" style="display: none;">
                        <strong>完全匹配示例为：</strong>
                        <strong style="color: red;">18800000000</strong>
                        <br/>
                        <strong>( 示例仅供参考 )</strong>
                    </small>
                    <small class="blockTypeTwo" style="display: none;">
                        <strong>正则匹配示例为：</strong>
                        <strong style="color: red;">/^1[3456789]\d{9}$/</strong>
                        <br/>
                        <strong>
                            ( 如不了解 建议百度关键词：<a href="https://www.baidu.com/s?wd=%E6%AD%A3%E5%88%99%E8%A1%A8%E8%BE%BE%E5%BC%8F" target="_blank">正则表达式</a> )
                        </strong>
                    </small>
                </div>
            </div>
            <div class="am-form-group" style="margin-top: 20px;">
                <label class="am-u-sm-4 am-form-label">拦截类型</label>
                <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                    <select class="interceptType" style="width: 236px;">
                        <option value="-1">请选择</option>
                        <option value="0">黑名单</option>
                        <option value="1">白名单</option>
                    </select>
                </div>
            </div>
            <div class="am-form-group" style="margin-top: 22px;font-size: 12px;text-align: center;">
                <strong>
                    白名单规则优先于黑名单 <span style="color: #00cdac;">( 白名单为不拦截 黑名单无法绑定手机号 )</span>
                </strong>
                <br/><br/>
                <strong style="white-space: nowrap;">完全匹配白名单 > 完全匹配黑名单 > 正则匹配白名单 > 正则匹配黑名单</strong>
            </div>
            <div class="am-form-group" style="margin-top: 30px;display: flex;justify-content: center;">
                <div style="cursor:pointer;color:#ffffff;background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;">
                    <div style="width: 100%;height: 100%;" onclick="holdSave()">
                        保存规则
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="testMutual" class="hide">
    <div class="am-g tpl-amazeui-form" style="background: #fcfcfc;width: 100%;height: 100%;">
        <div class="am-form am-form-horizontal">
            <div class="am-form-group" style="margin-top: 30px;">
                <label class="am-u-sm-4 am-form-label">手机号码</label>
                <div class="am-u-sm-6 am-u-end" style="margin-top: 3px;">
                    <input class="testPhone" type="text" style="width: 236px;" >
                </div>
            </div>
            <div class="am-form-group" style="margin-top: 30px;display: flex;justify-content: center;">
                <div style="cursor:pointer;color:#ffffff;background-image: linear-gradient(to top, #00c6fb 0%, #005bea 100%);height: 38px;width: 120px;font-size: 15px;text-align: center;line-height: 38px;border-radius: 20px;">
                    <div style="width: 100%;height: 100%;" onclick="testMatch();">
                        测试拦截
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    var testIntercept = function () {
        layer.open({
            type: 1,
            title: false,
            scrollbar: false,
            closeBtn: true,
            area: ['600px', '180px'],
            shadeClose: true,
            content: $('#testMutual').html()
        });
    }

    var testMatch = function () {
        var testPhone = $.trim($('.layui-layer .testPhone').val());
        $.post("{:url('unlawful/testMatchPhone')}", {'phone': testPhone}, function (data) {
            if (data.code > 0) {
                layer.msg(data.msg, {icon: 1, time: 1200});
            } else {
                layer.msg(data.msg, {icon: 5, time: 1800});
            }
        }, 'json');
    }

    var newAnomaly = function () {
        layer.open({
            type: 1,
            title: false,
            scrollbar: false,
            closeBtn: true,
            area: ['600px', '470px'],
            shadeClose: true,
            content: $('#mutual').html(),
            end: function () {
                $('.blockTypeOne').hide();
                $('.blockTypeTwo').hide();
                $('.blockTypeZero').show();
            }
        });
    }

    var selectType = function (obj) {
        var blockTypeZero = $('.blockTypeZero');
        var blockTypeOne = $('.blockTypeOne');
        var blockTypeTwo = $('.blockTypeTwo');
        switch (Number($(obj).val())) {
            case 0:
                blockTypeZero.hide();
                blockTypeTwo.hide();
                blockTypeOne.show();
                break;
            case 1:
                blockTypeZero.hide();
                blockTypeOne.hide();
                blockTypeTwo.show();
                break;
            default:
                blockTypeOne.hide();
                blockTypeTwo.hide();
                blockTypeZero.show();
                break;
        }
    }

    var isLock = false;
    var holdSave = function () {
        if (!isLock) {
            var setData = {};
            setData['blockType'] = Number($('.layui-layer .blockType').val());
            if (setData['blockType'] === -1) {
                layer.msg('请选择匹配类型！');
                return;
            }
            setData['blockRule'] = $.trim($('.layui-layer .blockRule').val());
            if (setData['blockRule'] === '') {
                layer.msg('请输入匹配规则！');
                return;
            }
            setData['interceptType'] = Number($('.layui-layer .interceptType').val());
            if (setData['interceptType'] === -1) {
                layer.msg('请选择拦截类型！');
                return;
            }
            isLock = true;
            $.post("{:url('unlawful/newBlockingPhone')}", setData, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200}, function () {
                        isLock = false;
                    });
                }
            }, 'json');
        }
    }

    var eraseAnomaly = function (fids) {
        layer.confirm('您确定要删除这条规则吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('unlawful/delBlockingPhone')}", {fid: fids}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200});
                }
            });
        }, function (index) {
            layer.close(index);
        });
    }

    var filterAnomaly = function (){
        var egon = $.trim($('#egon').val());
        var inType = $.trim($('#inType').val());
        var fz_name = $.trim($('#fz_name').val());
        location.href = "{:url('unlawful/blockingPhone')}&egon=" + egon + "&inType=" + inType + "&hazy_name=" + fz_name + "&page={$page}";
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('unlawful/blockingPhone')}&egon={$egon}&inType={$inType}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('unlawful/blockingPhone')}&page={$page}";
        }
    }

</script>
{/block}