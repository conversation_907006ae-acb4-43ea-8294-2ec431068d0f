{extend name="/base"/}
{block name="main"}
<style>.customize-span,.customize-span:hover{padding:3px 10px;color:black !important;background:#a3b6c1;border-radius:3px;cursor:pointer;font-size:14px;margin-left:12px;}.am-table > thead:first-child > tr:first-child > th{text-align:center;}.table-main{border:1px solid #f0f0f0;border-radius:4px;}.table-main thead th{background:#f8f9fa;color:#333;font-weight:500;padding:12px 8px;border-bottom:2px solid #e8e8e8;}.table-main tbody td{padding:12px 8px;border-bottom:1px solid #f0f0f0;}.table-main tbody tr:hover{background-color:#f9f9f9;}.status-tag{padding:4px 8px;border-radius:3px;font-size:12px;}.status-pending{background:#fff3e0;color:#ff9800;}.status-approved{background:#e8f5e9;color:#4caf50;}.status-rejected{background:#ffebee;color:#f44336;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption font-green bold">
            <span class="am-icon-code"></span> 求职招聘发布信息列表
        </div>
        <div class="tpl-portlet-input tpl-fz-ml">
            <div class="portlet-input input-small input-inline">
                <div class="input-icon right">
                    <i class="am-icon-search" onclick="fuzzy();"></i>
                    <input type="text" class="form-control form-control-solid" id="hazy_name" value="{$hazy_name}" placeholder="搜索发布信息...">
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g am-margin-bottom-sm">
            <div class="am-u-sm-12">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-xs">
                        <a href="javascript:void(0);" onclick="commonAdd();" class="customize-span">
                            <span class="am-icon-adn"></span> 新增信息
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table table-main">
                        <thead>
                        <tr>
                            <th class="text-center" width="11.11%">用户昵称</th>
                            <th class="text-center" width="11.11%">岗位名称</th>
                            <th class="text-center" width="11.11%">岗位类型</th>
                            <th class="text-center" width="11.11%">发布类型</th>
                            <th class="text-center" width="11.11%">联系方式</th>
                            <th class="text-center" width="11.11%">工作地点</th>
                            <th class="text-center" width="11.11%">审核状态</th>
                            <th class="text-center" width="11.11%">发布时间</th>
                            <th class="text-center" width="11.11%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="am-text-middle text-center">
                                {if $vo.user.uvirtual == 0}
                                <a href="{:url('user/index')}&openid={$vo.user.user_wechat_open_id}&page=1" title="{$vo.user.user_nick_name|emoji_decode}" target="_blank">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {else}
                                <a href="{:url('user/theoretic')}&hazy_name={$vo.user.user_nick_name|filter_emoji}&page=1" target="_blank" title="{$vo.user.user_nick_name|emoji_decode}">
                                    {$vo.user.user_nick_name|emoji_decode}
                                </a>
                                {/if}
                            </td>
                            <td class="am-text-middle text-center">
                                {$vo.job_name|emoji_decode}
                            </td>
                            <td class="am-text-middle text-center">
                                {$vo.type_name}
                            </td>
                            <td class="am-text-middle text-center">
                                {switch $vo.release_type}
                                {case 0}招聘{/case}
                                {case 1}求职{/case}
                                {case 2}兼职{/case}
                                {case 3}合伙人{/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle text-center">
                                {$vo.contact_details}
                            </td>
                            <td class="am-text-middle text-center">
                                {$vo.work_address}
                            </td>
                            <td class="am-text-middle text-center">
                                {switch $vo.audit_status}
                                {case 0}
                                <span class="status-tag status-pending">待审核</span>
                                {/case}
                                {case 1}
                                <span class="status-tag status-approved">已通过</span>
                                {/case}
                                {case 2}
                                <span class="status-tag status-rejected">已拒绝</span>
                                {/case}
                                {/switch}
                            </td>
                            <td class="am-text-middle text-center">
                                {:date('Y-m-d H:i:s',$vo.create_time)}
                            </td>
                            <td class="am-text-middle text-center">
                                <div class="am-btn-group am-btn-group-sm" style="display: flex;justify-content: center;margin-top: -5px;">
                                    <button class="am-btn" style="padding: 0.5em 0.5em; color:#000;height: 30px;border-left:1px solid #9c9c9c;border-top:1px solid #9c9c9c;border-bottom:1px solid #9c9c9c;">
                                        功能列表
                                    </button>
                                    <div class="am-dropdown" data-am-dropdown style="height: 30px;line-height: 0;border-right:1px solid #9c9c9c;border-top:1px solid #9c9c9c;border-bottom:1px solid #9c9c9c;">
                                        <button class="am-btn am-dropdown-toggle" data-am-dropdown-toggle style="padding: 0.2em 0.5em;">
                                            <span class="am-icon-caret-down"></span>
                                        </button>
                                        <ul class="am-dropdown-content" style="width: 120px;min-width: initial;">
                                            <li style="display: flex;justify-content: center;">
                                                <a href="javascript:void(0);" onclick="reviewDetail('{$vo.id}')" style="padding: 5px;color: #000;">
                                                    查看详情
                                                </a>
                                            </li>
                                            <li style="display: flex;justify-content: center;">
                                                <a href="javascript:void(0);" onclick="commonEdit('{$vo.id}')" style="padding: 5px;color: #000;">
                                                    编辑信息
                                                </a>
                                            </li>
                                            {if $vo.audit_status==0}
                                            <li style="display: flex;justify-content: center;">
                                                <a href="javascript:void(0);" onclick="auditCorrect('{$vo.id}',1)" style="padding: 5px;color: #000;">
                                                    审核通过
                                                </a>
                                            </li>
                                            <li style="display: flex;justify-content: center;">
                                                <a href="javascript:void(0);" onclick="auditCorrect('{$vo.id}',2)" style="padding: 5px;color: #000;">
                                                    审核拒绝
                                                </a>
                                            </li>
                                            {/if}
                                            <li style="display: flex;justify-content: center;">
                                                <a href="javascript:void(0);" onclick="delCorrect('{$vo.id}')" style="padding: 5px;color: #000;">
                                                    删除信息
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var reviewDetail = function (fid) {
        layer.open({
            type: 2,
            anim: 2,
            title: false,
            area: ['520px', '550px'],
            scrollbar: true,
            closeBtn: true,
            shadeClose: true,
            content: ["{:url('career/employment_found_detail')}&fid=" + fid],
        });
    }

    var auditCorrect = function (fid, process) {
        var twoCheck = function (fid, process, reaValue) {
            $.ajax({
                type: "post",
                url: "{:url('career/trial_employment_found')}",
                data: {'fid': fid, 'process': process, 'inject': reaValue},
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.reload();
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000});
                    }
                }
            });
        }
        switch (process) {
            case 1:
                layer.confirm('您确定要审核通过这条数据吗？', {
                    btn: ['确定', '取消'], title: '提示'
                }, function () {
                    twoCheck(fid, process);
                }, function (index) {
                    layer.close(index);
                });
                break;
            case 2:
                layer.prompt({
                    title: '请输入这条数据未通过审核的原因：',
                    formType: 2,
                    area: ['300px', '100px'],
                    btn: ['确定', '取消'],
                }, function (reaValue, index) {
                    if (reaValue.trim() === '') {
                        return false;
                    }
                    twoCheck(fid, process, reaValue);
                    layer.close(index);
                });
                break;
        }
    }

    var delCorrect = function (fid) {
        layer.confirm('您确定要删除这条数据吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.post("{:url('career/del_employment_found')}", {'fid': fid}, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, {icon: 1, time: 1200}, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, {icon: 5, time: 2200});
                }
            }, 'json');
        }, function (index) {
            layer.close(index);
        });
    }

    var fuzzy = function () {
        var hazy_name = $.trim($('#hazy_name').val());
        if (hazy_name) {
            location.href = "{:url('career/employment_found')}&hazy_name=" + hazy_name + "&page={$page}";
        } else {
            location.href = "{:url('career/employment_found')}&page={$page}";
        }
    }

    function commonAdd() {
        location.href = "{:url('career/new_employment_found')}";
    }

    function commonEdit(fid) {
        location.href = "{:url('career/edit_employment_found')}&fid=" + fid;
    }
</script>
{/block}