{extend name="/base"/}
{block name="main"}
<style>.tpl-portlet-components{background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.1);padding:20px;margin-bottom:20px;}.portlet-title{display:flex;justify-content:space-between;align-items:center;padding-bottom:15px;margin-bottom:15px;border-bottom:1px solid #f0f0f0;}.caption{font-size:16px;color:#333;font-weight:500;}.caption .am-icon-code{margin-right:5px;color:#23b7e5;}.am-form-label{text-align:right;color:#333;font-weight:normal;}.am-form-group small{display:block;margin-top:.5rem;color:#6c757d;font-size:12px;}.tpl-form-input{display:block;width:100%;height:36px;padding:8px 12px;font-size:14px;line-height:1.5;color:#495057;background-color:#fff;background-clip:padding-box;border:1px solid #ced4da;border-radius:4px;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;}.tpl-form-input:focus{color:#495057;background-color:#fff;border-color:#80bdff;outline:0;box-shadow:0 0 0 0.2rem rgba(35,183,229,.25);}textarea.tpl-form-input{height:auto;min-height:90px;resize:vertical;}.confirm-btn{background:#23b7e5;color:white;border:none;border-radius:3px;padding:8px 18px;font-size:14px;cursor:pointer;transition:all 0.3s;}.confirm-btn:hover{background:#1a9fd4;}.image-picker{display:flex;align-items:center;gap:15px;}.image-picker .image-preview{flex-shrink:0;border:1px dashed #ced4da;border-radius:4px;cursor:pointer;background-color:#f8f9fa;display:flex;align-items:center;justify-content:center;overflow:hidden;}.image-picker .image-preview img{max-width:100%;max-height:100%;object-fit:cover;}.image-picker .image-controls{display:flex;flex-direction:column;align-items:flex-start;gap:8px;}.action-btn{display:inline-block;padding:5px 10px;font-size:12px;font-weight:normal;line-height:1.4;text-align:center;cursor:pointer;border:1px solid #ced4da;border-radius:4px;transition:all .3s;background-color:#fff;}.action-btn:hover{background-color:#f8f9fa;}.action-btn.btn-danger{color:#fff;background-color:#dc3545;border-color:#dc3545;}.action-btn.btn-danger:hover{background-color:#c82333;border-color:#bd2130;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-code"></span> 新增圈子
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal tpl-form-line-form">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">圈子名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="name" class="tpl-form-input" placeholder="请输入圈子名称">
                            <small>输入圈子名称</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">圈子图标</label>
                        <div class="am-u-sm-9">
                            <div class="image-picker">
                                <div class="image-preview" onclick="cuonice(this);" style="width: 100px; height: 100px;">
                                    <img src="" onerror="this.src='static/disappear/default.png'"/>
                                    <input type="hidden" name="circle-img">
                                </div>
                                <div class="image-controls">
                                    <button type="button" class="action-btn" onclick="$(this).closest('.image-picker').find('.image-preview').trigger('click')">选择图片</button>
                                    <small>建议尺寸：200*200px</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">圈子背景图</label>
                        <div class="am-u-sm-9">
                            <div class="image-picker">
                                <div class="image-preview" onclick="cuonice(this);" style="width: 210px; height: 118px;">
                                    <img src="" onerror="this.src='static/wechat/image_vip_top.jpg'"/>
                                    <input type="hidden" name="circle-back-img">
                                </div>
                                <div class="image-controls">
                                    <button type="button" class="action-btn" onclick="$(this).closest('.image-picker').find('.image-preview').trigger('click')">选择图片</button>
                                    <button type="button" class="action-btn btn-danger" onclick="deleteImage(this);">删除图片</button>
                                    <small>建议尺寸：16:9</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">群聊二维码</label>
                        <div class="am-u-sm-9">
                             <div class="image-picker">
                                <div class="image-preview" onclick="cuonice(this);" style="width: 120px; height: 120px;">
                                    <img src="" onerror="this.src='static/disappear/default.png'"/>
                                    <input type="hidden" name="group-qrcode">
                                </div>
                                 <div class="image-controls">
                                    <button type="button" class="action-btn" onclick="$(this).closest('.image-picker').find('.image-preview').trigger('click')">选择图片</button>
                                    <button type="button" class="action-btn btn-danger" onclick="deleteImage(this);">删除图片</button>
                                    <small>建议尺寸：1:1</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">圈子类型</label>
                        <div class="am-u-sm-9">
                            <select id="needle_id" class="tpl-form-input">
                                <option value="0">请选择</option>
                                {volist name="needleList" id="vo"}
                                <option value="{$vo.id}">{$vo.name}</option>
                                {/volist}
                            </select>
                            <small>选择圈子属于的广场</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">圈子简介</label>
                        <div class="am-u-sm-9">
                            <textarea rows="5" id="intro" style="resize: none;" class="tpl-form-input" placeholder="请输入圈子简介"></textarea>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">浏览权限</label>
                        <div class="am-u-sm-9">
                            <select id="attention" class="tpl-form-input">
                                <option value="0">所有用户可见</option>
                                <option value="1">审核加入可见</option>
                                <option value="2">会员用户可见</option>
                                <option value="3">用户关注可见</option>
                            </select>
                            <small>
                                审核加入可见说明：
                                <span style="color:red;">
                                    只有加入圈子的人才可以看到圈子里发布的内容，加入圈子，需要通过圈子管理员审核
                                </span>
                            </small>
                        </div>
                    </div>

                    <div class="am-form-group" style="display: none;">
                        <label class="am-u-sm-3 am-form-label">暗号状态</label>
                        <div class="am-u-sm-9">
                            <select id="atence" class="tpl-form-input">
                                <option value="0">关闭暗号</option>
                                <option value="1">开启暗号</option>
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group" style="display: none;">
                        <label class="am-u-sm-3 am-form-label">暗号</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="atcipher" class="tpl-form-input" placeholder="请输入暗号">
                            <small style="color:red;">用户输入暗号后无需等待管理审核即可加入访问受限的圈子</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">发帖等级</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="release_level" value="0" class="tpl-form-input" placeholder="请输入发帖等级">
                            <small>请输入圈子发帖等级限制 ( 圈主、管理员不受限制 )</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">访问等级</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="visit_level" value="0" class="tpl-form-input" placeholder="请输入访问等级">
                            <small>请输入圈子访问等级限制 ( 圈主、管理员不受限制 )</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">发帖次数</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="release_count" value="0" class="tpl-form-input" placeholder="请输入圈子发帖次数限制">
                            <small>0为不限制 ( 用户在此圈子里的发布帖子的次数 圈主、管理员不受限制 )</small>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">圈子状态</label>
                        <div class="am-u-sm-9">
                            <select id="status" class="tpl-form-input">
                                <option value="0">暂停访问</option>
                                <option value="1">正常访问</option>
                            </select>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">关注人数</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="concern" value="0" class="tpl-form-input" placeholder="请输入关注人数">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">排序</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="scores" value="0" class="tpl-form-input" placeholder="请输入排序数字">
                        </div>
                    </div>

                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-push-3">
                            <button type="button" class="confirm-btn" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>

    !function () {
        $('#attention').change(function () {
            if ($(this).val() == 1) {
                $('#atence').parent().parent().show();
            } else {
                $('#atence').parent().parent().hide();
                $('#atence').val('0');
                $('#atcipher').parent().parent().hide();
            }
        });

        $('#atence').change(function () {
            if ($(this).val() == 1) {
                $('#atcipher').parent().parent().show();
            } else {
                $('#atcipher').parent().parent().hide();
            }
        });
    }();


    var cuonice = function (obj) {
        $(obj).children('img').addClass('img-select');
        layer.open({
            type: 2,
            anim: 2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no'],
            end: function (index, layer) {
                $('.img-select').removeClass('img-select');
            }
        });
    }

    var sutake = function (eurl) {
        $('.img-select').attr('src', eurl).parent().children('input').val(eurl);
        layer.closeAll();
    }

    var deleteImage = function (obj) {
        var picker = $(obj).closest('.image-picker');
        picker.find('img').attr('src', '').addClass('img-select'); // Add class for sutake compatibility if needed
        picker.find('input[type=hidden]').val('');
        // This is a workaround for onerror not re-triggering if src is set to "" then back to ""
        var originalOnError = picker.find('img').get(0).onerror;
        picker.find('img').get(0).onerror = null;
        picker.find('img').attr('src','');
        picker.find('img').get(0).onerror = originalOnError;
        $('.img-select').removeClass('img-select');
    }

    var slock = false;
    var holdSave = function () {
        if (!slock) {
            var setData = {};
            setData['name'] = $.trim($('#name').val());
            setData['circle_img'] = $.trim($("[name='circle-img']").val());
            setData['circle_back_img'] = $.trim($("[name='circle-back-img']").val());
            setData['group_qrcode'] = $.trim($("[name='group-qrcode']").val());
            setData['needle_id'] = Number($('#needle_id').val());
            setData['intro']= $.trim($('#intro').val());
            setData['attention']= Number($('#attention').val());
            setData['atence']= Number($('#atence').val());
            setData['atcipher']= $.trim($('#atcipher').val());
            setData['status']= Number($('#status').val());
            setData['release_level']= Number($('#release_level').val());
            setData['visit_level']= Number($('#visit_level').val());
            setData['release_count']= Number($('#release_count').val());
            setData['concern']= Number($('#concern').val());
            setData['scores']=  Number($('#scores').val());
            if (setData['name'] === '') {
                layer.msg('广场名称不能为空');
                return;
            }
            if (setData['circle_img'] === '') {
                layer.msg('请上传广场图标');
                return;
            }
            if (setData['needle_id'] === 0) {
                layer.msg('请选择圈子类型');
                return;
            }
            if (setData['intro'] === '') {
                layer.msg('圈子简介不能为空');
                return;
            }
            slock = true;
            $.ajax({
                type: "post",
                url: "{:url('compass/rulfence')}",
                data: setData,
                dataType: 'json',
                success: function (data) {
                    if (data.code > 0) {
                        layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                            location.href = "{:url('compass/fence')}";
                        });
                    } else {
                        layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                            slock = false;
                        });
                    }
                }
            });
        }
    }
</script>
{/block}