{extend name="/base"/}
{block name="main"}
<style>.tpl-portlet-components{background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.1);padding:20px;margin-bottom:20px;position:relative;}.tpl-portlet-components:after{content:"";display:table;clear:both;}.am-form{position:relative;overflow:visible;}.portlet-title{display:flex;justify-content:space-between;align-items:center;padding-bottom:15px;margin-bottom:15px;border-bottom:1px solid #f0f0f0;}.caption{font-size:16px;color:#23b7e5;font-weight:500;}.caption .am-icon-map-signs{margin-right:5px;color:#23b7e5;}.tpl-portlet-input{position:relative;}.tpl-portlet-input input{height:32px;width:200px;padding:0 30px 0 10px;border:1px solid #e8e8e8;border-radius:4px;background:#fafafa;transition:all 0.3s;}.tpl-portlet-input input:focus{border-color:#23b7e5;background:#fff;box-shadow:0 0 0 2px rgba(35,183,229,0.1);}.tpl-portlet-input .am-icon-search{position:absolute;right:10px;top:50%;transform:translateY(-50%);color:#999;cursor:pointer;}.action-btn{display:inline-block;padding:4px 8px;background:#fff;border:1px solid #ddd;color:#23b7e5;border-radius:3px;font-size:12px;cursor:pointer;transition:all 0.3s;margin:2px;}.action-btn:hover{border-color:#23b7e5;color:#23b7e5;background-color:#f5fafd;}.am-table{border:1px solid #f0f0f0;border-radius:4px;font-size:13px;margin-bottom:15px;}.am-table > thead:first-child > tr:first-child > th{background-color:#f9f9f9;border-bottom:1px solid #eee;color:#333;font-weight:500;font-size:13px;text-align:center;padding:10px 8px;}.am-table > tbody > tr > td{padding:10px 8px;border-top:1px solid #f3f3f3;text-align:center;vertical-align:middle;color:#666;line-height:1.6;position:relative;}.am-table-striped > tbody > tr:nth-child(odd) > td{background-color:#fafafa;}.am-table > tbody > tr:hover > td{background-color:#f5fafd;}.am-pagination{margin:10px 0;}.am-pagination > li > a{color:#666;background-color:#fff;border:1px solid #e8e8e8;margin:0 3px;border-radius:3px;}.am-pagination > .am-active > a{background-color:#23b7e5;border-color:#23b7e5;}.am-pagination > li > a:hover{background-color:#f5fafd;border-color:#e8e8e8;color:#23b7e5;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-map-signs"></span> 广场列表
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索广场...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <a href="javascript:void(0);" class="am-btn am-btn-success am-btn-sm" onclick="saloof();">
                        <span class="am-icon-plus"></span> 新增广场
                    </a>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="16.66%" class="text-center">排序</th>
                            <th width="16.66%" class="text-center">图标</th>
                            <th width="16.66%" class="text-center">名称</th>
                            <th width="16.66%" class="text-center">关键词</th>
                            <th width="16.66%" class="text-center">状态</th>
                            <th width="16.67%" class="text-center">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr class="text-center">
                            <td>
                                <input type="text" class="scfixed" id="sfixed-{$vo.id}" value="{$vo.scores}" data-score="{$vo.scores}" style="width: 50px; text-align: center;" onblur="supre('{$vo.id}','#sfixed-{$vo.id}');">
                            </td>
                            <td>
                                <a href="{$vo.icon}" target="_blank">
                                    <img src="{$vo.icon}" onerror="this.src='static/wechat/image_vip_top.jpg'" style="width: 100px;height:52px; border-radius: 4px;"/>
                                </a>
                            </td>
                            <td>
                                <a href="{:url('compass/fence')}&egon=0&nid={$vo.id}" target="_blank">
                                {$vo.name}
                                </a>
                            </td>
                            <td>{$vo.keyword}</td>
                            <td>
                                {if $vo.status == 0}
                                <span class="am-badge am-badge-warning">隐藏</span>
                                {else}
                                <span class="am-badge am-badge-success">正常</span>
                                {/if}
                            </td>
                            <td>
                                <button type="button" class="action-btn" onclick="uploof('{$vo.id}');">
                                    <span class="am-icon-pencil-square-o"></span> 编辑
                                </button>
                                <button type="button" class="action-btn am-btn-danger" onclick="navlintDel('{$vo.id}','{$vo.name}');">
                                    <span class="am-icon-trash-o"></span> 删除
                                </button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    function supre(asyId, domId) {
        var dalue = $(domId).val();
        var daioe = $(domId).attr('data-score');
        if (dalue > 2147483646) {
            layer.msg('排序数字最大不能超过 2147483646');
            $(domId).val(daioe);
            return false;
        }
        if (dalue != daioe) {
            var repair = exalter(asyId, dalue);
            if (repair.code > 0) {
                layer.msg(repair.msg, {icon: 1, time: 800});
                $(domId).attr('data-score', dalue);
            } else {
                layer.msg(repair.msg, {icon: 5, time: 1600});
            }
        }
    }

    function exalter(asyId, dalue) {
        var straw = {};
        $.ajax({
            type: "post",
            url: "{:url('slue')}",
            data: {
                asyId: asyId,
                dalue: dalue
            },
            async: false,
            success: function (data) {
                straw = data;
            }
        });
        return straw;
    }


    function saloof() {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('rulnav')}");
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }

    function uploof(uplid) {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('uplnav')}&uplid=" + uplid);
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }


    var lock = false;

    function navlintDel(mid, vname) {
        if (!lock) {
            lock = true;
            layer.confirm('您确定要删除 <span style="color: blue;">' + vname + '</span> 广场吗？<br>请确保该广场下没有所关联的圈子，<br>用户创建圈子申请也会被关联删除。', {
                btn: ['确定', '取消'], "title": "提示"
            }, function () {
                $.post(
                    "{:url('navlint')}",
                    {'ecid': mid},
                    function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                lock = false;
                            });
                        }
                    }, 'json');
            }, function () {
                lock = false;
            });
        }
    }

    function fuzzy() {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name)
            location.href = "{:url('nav')}&hazy_name=" + fz_name + "&page={$page}";
        else
            location.href = "{:url('nav')}&page={$page}";
    }

</script>
{/block}