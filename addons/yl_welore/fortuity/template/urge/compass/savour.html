{extend name="/base"/}
{block name="main"}
<style>.tpl-portlet-components{background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.1);padding:20px;margin-bottom:20px;position:relative;}.tpl-portlet-components:after{content:"";display:table;clear:both;}.am-form{position:relative;overflow:visible;}.portlet-title{display:flex;justify-content:space-between;align-items:center;padding-bottom:15px;margin-bottom:15px;border-bottom:1px solid #f0f0f0;}.caption{font-size:16px;color:#23b7e5;font-weight:500;}.caption .am-icon-ioxhost{margin-right:5px;color:#23b7e5;}.tpl-portlet-input{position:relative;}.tpl-portlet-input input{height:32px;width:200px;padding:0 30px 0 10px;border:1px solid #e8e8e8;border-radius:4px;background:#fafafa;transition:all 0.3s;}.tpl-portlet-input input:focus{border-color:#23b7e5;background:#fff;box-shadow:0 0 0 2px rgba(35,183,229,0.1);}.tpl-portlet-input .am-icon-search{position:absolute;right:10px;top:50%;transform:translateY(-50%);color:#999;cursor:pointer;}.action-btn{display:inline-block;padding:4px 8px;background:#fff;border:1px solid #ddd;color:#23b7e5;border-radius:3px;font-size:12px;cursor:pointer;transition:all 0.3s;margin:2px;}.action-btn:hover{border-color:#23b7e5;color:#23b7e5;background-color:#f5fafd;}.am-table{border:1px solid #f0f0f0;border-radius:4px;font-size:13px;margin-bottom:15px;}.am-table > thead:first-child > tr:first-child > th{background-color:#f9f9f9;border-bottom:1px solid #eee;color:#333;font-weight:500;font-size:13px;text-align:center;padding:10px 8px;}.am-table > tbody > tr > td{padding:10px 8px;border-top:1px solid #f3f3f3;text-align:center;vertical-align:middle;color:#666;line-height:1.6;position:relative;}.am-table-striped > tbody > tr:nth-child(odd) > td{background-color:#fafafa;}.am-table > tbody > tr:hover > td{background-color:#f5fafd;}.am-pagination{margin:10px 0;}.am-pagination > li > a{color:#666;background-color:#fff;border:1px solid #e8e8e8;margin:0 3px;border-radius:3px;}.am-pagination > .am-active > a{background-color:#23b7e5;border-color:#23b7e5;}.am-pagination > li > a:hover{background-color:#f5fafd;border-color:#e8e8e8;color:#23b7e5;}.filter-btn-group .am-btn{margin-right:10px;border-radius:3px;}.filter-btn-group .am-btn.active{background-color:#23b7e5;color:white;border-color:#23b7e5;}.batch-actions-group > .am-btn + .am-btn{margin-left:8px;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-ioxhost"></span>
            <a href="{:url('compass/caveat')}&tyid={$toryInfo.id}" style="color:#23b7e5 !important;" target="_blank" title="点击跳转到已关注用户列表">
                {$toryInfo.realm_name|emoji_decode} 关注审核列表
            </a>
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="turtle();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索用户名称...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-7">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-sm filter-btn-group">
                        <a href="{:url('compass/savour')}&hazy_bering={$hazy_bering}&egon=0" class="am-btn am-btn-default {if $egon==0}active{/if}">全部</a>
                        <a href="{:url('compass/savour')}&hazy_bering={$hazy_bering}&egon=1" class="am-btn am-btn-default {if $egon==1}active{/if}">待审核</a>
                        <a href="{:url('compass/savour')}&hazy_bering={$hazy_bering}&egon=2" class="am-btn am-btn-default {if $egon==2}active{/if}">已通过</a>
                        <a href="{:url('compass/savour')}&hazy_bering={$hazy_bering}&egon=3" class="am-btn am-btn-default {if $egon==3}active{/if}">已拒绝</a>
                    </div>
                </div>
            </div>
            <div class="am-u-sm-12 am-u-md-5" style="text-align: right;">
                <div class="am-btn-group am-btn-group-sm batch-actions-group">
                    {if $egon==0||$egon==1}
                    <button type="button" class="am-btn am-btn-success" onclick="rebatch('0');">批量同意</button>
                    <button type="button" class="am-btn am-btn-danger" onclick="rebatch('1');">批量拒绝</button>
                    {else}
                    <button type="button" class="am-btn am-btn-danger" onclick="rebatch('2');">批量删除</button>
                    {/if}
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="7%">
                                <input id="withole" type="checkbox" class="tpl-table-fz-check"> 全选
                            </th>
                            <th width="10%">用户</th>
                            <th width="10%">名称</th>
                            <th width="10%">关注圈子</th>
                            <th width="10%">加入理由</th>
                            <th width="10%">申请时间</th>
                            <th width="10%">审核时间</th>
                            <th width="12%">状态</th>
                            <th width="14%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                <input type="checkbox" class="tpl-table-fz-check elctive" value="{$vo.id}">
                            </td>
                            <td>
                                <img src="{$vo.user_head_sculpture}" style="width: 40px;height: 40px;border-radius: 50%;">
                            </td>
                            <td>
                                <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}" target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                {$vo.user_nick_name|emoji_decode|subtext=10}
                                </a>
                            </td>
                            <td>{$vo.realm_name}</td>
                            <td>
                                <button type="button" class="action-btn" onclick="tance('{$vo.id}');">查看</button>
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.sult_time)}</td>
                            <td>{if $vo.rest_time}{:date('Y-m-d H:i:s',$vo.rest_time)}{/if}</td>
                            <td>
                                {if $vo.status == 0}
                                <span class="am-badge am-badge-warning">待审核</span>
                                {elseif $vo.status == 1}
                                <span class="am-badge am-badge-success">已通过</span>
                                {elseif $vo.status == 2}
                                <span class="am-badge am-badge-danger">已拒绝</span>
                                {/if}
                            </td>
                            <td>
                                {if $vo.status == 0}
                                <button type="button" class="action-btn am-btn-success" onclick="uknow('{$vo.id}','1');">同意</button>
                                <button type="button" class="action-btn am-btn-danger" onclick="uknow('{$vo.id}','2');">拒绝</button>
                                {else}
                                <button type="button" class="action-btn am-btn-danger" onclick="uknow('{$vo.id}','3');">
                                    <span class="am-icon-trash-o"></span> 删除
                                </button>
                                {/if}
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>
        </div>
    </div>
    <div class="tpl-alert"></div>
    <textarea id="editor" style="width: 600px;display: none;"></textarea>
</div>
{/block}
{block name="script"}
<script>
    !function () {
        $('#withole').click(function () {
            var wecked = $(this).prop('checked');
            if (wecked) {
                $('.elctive').prop('checked', true);
            } else {
                $('.elctive').prop('checked', false);
            }
        });
        $('.elctive').click(function () {
            var jack = 1;
            $('.elctive').each(function () {
                var weck = $(this).prop('checked');
                if (weck == false) {
                    jack = 0;
                    return false;
                }
            });
            if (jack) {
                $('#withole').prop('checked', true);
            } else {
                $('#withole').prop('checked', false);
            }
        });
    }();

    function rebatch(thorough) {
        var tired = false;
        $('.elctive').each(function () {
            if ($(this).prop('checked')) {
                tired = true;
                return false;
            }
        });
        if (!tired) {
            layer.alert('很抱歉，当前没有选中要操作的数据', {'title': '系统提示'});
            return;
        }

        switch (thorough)
        {
            case "0":
                var rough = "您确定要批量同意选中的用户关注此圈子吗？";
                break;
            case "1":
                var rough = "您确定要批量拒绝选中的用户关注此圈子吗？";
                break;
            case "2":
                var rough = "您确定要批量删除选中的数据吗？";
                break;
        }
        layer.confirm(rough, {
            btn: ['确定', '取消'], 'title': '提示'
        }, function (index) {
            var i = 0;
            var j = 0;
            $('.elctive').each(function () {
                if ($(this).prop('checked')) {
                    var suid = $(this).val();
                    i++;
                    switch (thorough)
                    {
                        case "0":
                            j += setrike(suid, '1', '1');
                            break;
                        case "1":
                            j += setrike(suid, '2', '1');
                            break;
                        case "2":
                            j += savourintDel(suid, '1');
                            break;
                    }
                }
            });
            if (i == j) {
                layer.close(index);
                layer.msg('批量操作成功', {icon: 1, time: 1000}, function () {
                    location.reload();
                });
            } else {
                layer.msg('未知错误', {icon: 5, time: 1600}, function () {
                    location.reload();
                });
            }
        }, function (index) {
            layer.close(index);
        });
    }

    function uknow(suid, status) {

        switch (status){
            case "1":
                var rough = "您确定要同意用户关注此圈子吗？";
                break;
            case "2":
                var rough = "您确定要拒绝用户关注此圈子吗？";
                break;
            case "3":
                var rough = "您确定要删除这条的数据吗吗？";
                break;
        }
        if (status == 1 || status == 2) {
            layer.confirm(rough, {
                btn: ['确定', '取消'], 'title': '系统提示'
            }, function (index) {
                layer.close(index);
                setrike(suid, status);
            }, function (index) {
                layer.close(index);
            });
        } else {
            layer.confirm(rough, {
                btn: ['确定', '取消'], 'title': '系统提示'
            }, function (index) {
                layer.close(index);
                savourintDel(suid);
            }, function (index) {
                layer.close(index);
            });
        }
    }

    var ulock = false;
    function setrike(suid, status, batch) {
        if (!ulock) {
            ulock = true;
            var recode = 0;
            $.ajax({
                type: "post",
                url: "{:url('compass/arcanum')}",
                async: false,
                data: {'suid': suid, 'status': status},
                dataType: 'json',
                success: function (data) {
                    if (batch == 1) {
                        ulock = false;
                        if (data.code > 0) {
                            recode = 1;
                        } else {
                            recode = 0;
                        }
                    } else {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 1600}, function () {
                                ulock = false;
                            });
                        }
                    }
                }
            });
            return recode;
        }
    }

    var elock = false;
    var savourintDel = function (suid, batch) {
        if (!elock) {
            elock = true;
            var recode = 0;
            $.ajax({
                type: "post",
                url: "{:url('compass/savour_link')}",
                async: false,
                data: {'suid': suid},
                dataType: 'json',
                success: function (data) {
                    if (batch == 1) {
                        elock = false;
                        if (data.code > 0) {
                            recode = 1;
                        } else {
                            recode = 0;
                        }
                    } else {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 1600}, function () {
                                elock = false;
                            });
                        }
                    }
                }
            });
            return recode;
        }
    }


    function tance(ecid) {
        $.getJSON("{:url('compass/savour_tance')}", {'ecid': ecid}, function (data) {
            layer.msg(data.info);
        });
    }


    var turtle = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('compass/savour')}&hazy_bering={$hazy_bering}&egon={$egon}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('compass/savour')}&hazy_bering={$hazy_bering}&egon={$egon}&page={$page}";
        }
    }

</script>
{/block}