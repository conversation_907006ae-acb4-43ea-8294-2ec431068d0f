{extend name="/base"/}
{block name="main"}
<style>.tpl-portlet-components{background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.1);padding:20px;margin-bottom:20px;position:relative;}.tpl-portlet-components:after{content:"";display:table;clear:both;}.am-form{position:relative;overflow:visible;}.portlet-title{display:flex;justify-content:space-between;align-items:center;padding-bottom:15px;margin-bottom:15px;border-bottom:1px solid #f0f0f0;}.caption{font-size:16px;color:#23b7e5;font-weight:500;}.caption .am-icon-circle-o{margin-right:5px;color:#23b7e5;}.tpl-portlet-input{position:relative;}.tpl-portlet-input input{height:32px;width:200px;padding:0 30px 0 10px;border:1px solid #e8e8e8;border-radius:4px;background:#fafafa;transition:all 0.3s;}.tpl-portlet-input input:focus{border-color:#23b7e5;background:#fff;box-shadow:0 0 0 2px rgba(35,183,229,0.1);}.tpl-portlet-input .am-icon-search{position:absolute;right:10px;top:50%;transform:translateY(-50%);color:#999;cursor:pointer;}.action-btn{display:inline-block;padding:4px 8px;background:#fff;border:1px solid #ddd;color:#23b7e5;border-radius:3px;font-size:12px;cursor:pointer;transition:all 0.3s;margin:2px;}.action-btn:hover{border-color:#23b7e5;color:#23b7e5;background-color:#f5fafd;}.am-table{border:1px solid #f0f0f0;border-radius:4px;font-size:13px;margin-bottom:15px;}.am-table > thead:first-child > tr:first-child > th{background-color:#f9f9f9;border-bottom:1px solid #eee;color:#333;font-weight:500;font-size:13px;text-align:center;padding:10px 8px;}.am-table > tbody > tr > td{padding:10px 8px;border-top:1px solid #f3f3f3;text-align:center;vertical-align:middle;color:#666;line-height:1.6;position:relative;}.am-table-striped > tbody > tr:nth-child(odd) > td{background-color:#fafafa;}.am-table > tbody > tr:hover > td{background-color:#f5fafd;}.am-pagination{margin:10px 0;}.am-pagination > li > a{color:#666;background-color:#fff;border:1px solid #e8e8e8;margin:0 3px;border-radius:3px;}.am-pagination > .am-active > a{background-color:#23b7e5;border-color:#23b7e5;}.am-pagination > li > a:hover{background-color:#f5fafd;border-color:#e8e8e8;color:#23b7e5;}.filter-btn-group .am-btn{margin-right:10px;border-radius:3px;}.filter-btn-group .am-btn.active{background-color:#23b7e5;color:white;border-color:#23b7e5;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-circle-o"></span> 圈子审核
        </div>
        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}"
                       placeholder="搜索圈子或用户...">
            </div>
        </div>
    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12">
                <div class="am-btn-toolbar">
                    <div class="am-btn-group am-btn-group-sm filter-btn-group">
                        <a href="{:url('compass/solicit')}&egon=0" class="am-btn am-btn-default {if $egon==0}active{/if}">全部</a>
                        <a href="{:url('compass/solicit')}&egon=1" class="am-btn am-btn-default {if $egon==1}active{/if}">待审核</a>
                        <a href="{:url('compass/solicit')}&egon=2" class="am-btn am-btn-default {if $egon==2}active{/if}">已通过</a>
                        <a href="{:url('compass/solicit')}&egon=3" class="am-btn am-btn-default {if $egon==3}active{/if}">已拒绝</a>
                        <a href="{:url('compass/solicit')}&egon=4" class="am-btn am-btn-default {if $egon==4}active{/if}">已打回</a>
                        <a href="{:url('compass/solicit')}&egon=5" class="am-btn am-btn-default {if $egon==5}active{/if}">数据重复</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                        <tr>
                            <th width="8%">圈子名称</th>
                            <th width="8%">圈子类型</th>
                            <th width="10%">申请用户</th>
                            <th width="20%">用户openid</th>
                            <th width="10.8%">申请次数</th>
                            <th width="10.8%">审核状态</th>
                            <th width="10.8%">申请时间</th>
                            <th width="10.8%">审核时间</th>
                            <th width="10.8%">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        {volist name="list" id="vo"}
                        <tr>
                            <td>
                                <a href="{:url('compass/fence')}&hazy_name={$vo.realm_name}&page=1"
                                   title="{$vo.realm_name}" target="_blank">
                                    {$vo.realm_name|subtext=6}
                                </a>
                            </td>
                            <td>{$vo.name}</td>
                            <td>
                                <a href="{:url('user/index')}&userName={$vo.user_nick_name|emoji_decode|filter_emoji}&page=1"
                                   title="{$vo.user_nick_name|emoji_decode}"
                                   target="_blank">
                                {$vo.user_nick_name|emoji_decode|subtext=6}
                                </a>
                            </td>
                            <td>{$vo.user_wechat_open_id}</td>
                            <td>{$vo.solicit_rate}</td>
                            <td>
                                {if $vo.realm_status == 0}
                                <span class="am-badge am-badge-warning">待审核</span>
                                {elseif $vo.realm_status == 1}
                                <span class="am-badge am-badge-success">已通过</span>
                                {elseif $vo.realm_status == 2}
                                <span class="am-badge am-badge-secondary">已打回</span>
                                {elseif $vo.realm_status == 3}
                                <span class="am-badge am-badge-danger">已拒绝</span>
                                {elseif $vo.realm_status == 4}
                                <span class="am-badge am-badge-danger">数据重复</span>
                                {/if}
                            </td>
                            <td>{:date('Y-m-d H:i:s',$vo.found_lasting)}</td>
                            <td>
                                {notempty name="vo.review_lasting"}{:date('Y-m-d H:i:s',$vo.review_lasting)}{/notempty}
                            </td>
                            <td>
                                <button type="button" class="action-btn" onclick="uploof('{$vo.id}');">
                                    <span class="am-icon-search"></span> 查看
                                </button>
                                <button type="button" class="action-btn am-btn-danger" onclick="citlintDel('{$vo.id}');">
                                    <span class="am-icon-trash-o"></span> 删除
                                </button>
                            </td>
                        </tr>
                        {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
</div>
{/block}
{block name="script"}
<script>

    var lock = false;

    function citlintDel(mid) {
        if (!lock) {
            lock = true;
            layer.confirm('您确定要删除这条数据吗 ( <span style="color: red;">数据不可恢复</span> ) ？', {
                btn: ['确定', '取消'], "title": "提示"
            }, function () {
                $.post(
                    "{:url('citlint')}",
                    {'ecid': mid},
                    function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.reload();
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                lock = false;
                            });
                        }
                    }, 'json');
            }, function () {
                lock = false;
            });
        }
    }


    function uploof(uplid) {
        var dynamicUrl = $('<a></a>');
        dynamicUrl.attr('href', "{:url('aspsolicit')}&uplid=" + uplid);
        dynamicUrl.attr('target', '_self');
        dynamicUrl.get(0).click();
    }

    function fuzzy() {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('compass/solicit')}&egon={$egon}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('compass/solicit')}&egon={$egon}&page={$page}";
        }
    }

</script>
{/block}