{extend name="/base"/}
{block name="main"}
<style>.tpl-portlet-components{background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.1);padding:20px;margin-bottom:20px;position:relative;}.tpl-portlet-components:after{content:"";display:table;clear:both;}.am-form{position:relative;overflow:visible;}.portlet-title{display:flex;justify-content:space-between;align-items:center;padding-bottom:15px;margin-bottom:15px;border-bottom:1px solid #f0f0f0;}.caption{font-size:16px;color:#23b7e5;font-weight:500;}.caption .am-icon-location-arrow{margin-right:5px;color:#23b7e5;}.tpl-portlet-input{position:relative;}.tpl-portlet-input input{height:32px;width:200px;padding:0 30px 0 10px;border:1px solid #e8e8e8;border-radius:4px;background:#fafafa;transition:all 0.3s;}.tpl-portlet-input input:focus{border-color:#23b7e5;background:#fff;box-shadow:0 0 0 2px rgba(35,183,229,0.1);}.tpl-portlet-input .am-icon-search{position:absolute;right:10px;top:50%;transform:translateY(-50%);color:#999;cursor:pointer;}.action-btn{display:inline-block;padding:4px 8px;background:#fff;border:1px solid #ddd;color:#23b7e5;border-radius:3px;font-size:12px;cursor:pointer;transition:all 0.3s;margin:2px;}.action-btn:hover{border-color:#23b7e5;color:#23b7e5;background-color:#f5fafd;}.am-table{border:1px solid #f0f0f0;border-radius:4px;font-size:13px;margin-bottom:15px;}.am-table>thead:first-child>tr:first-child>th{background-color:#f9f9f9;border-bottom:1px solid #eee;color:#333;font-weight:500;font-size:13px;text-align:center;padding:10px 8px;}.am-table>tbody>tr>td{padding:10px 8px;border-top:1px solid #f3f3f3;text-align:center;vertical-align:middle;color:#666;line-height:1.6;position:relative;}.am-table-striped>tbody>tr:nth-child(odd)>td{background-color:#fafafa;}.am-table>tbody>tr:hover>td{background-color:#f5fafd;}.am-pagination{margin:10px 0;}.am-pagination>li>a{color:#666;background-color:#fff;border:1px solid #e8e8e8;margin:0 3px;border-radius:3px;}.am-pagination>.am-active>a{background-color:#23b7e5;border-color:#23b7e5;}.am-pagination>li>a:hover{background-color:#f5fafd;border-color:#e8e8e8;color:#23b7e5;}.am-modal-dialog{border-radius:4px;overflow:hidden;box-shadow:0 5px 15px rgba(0,0,0,0.2);}.am-modal-hd{background:#f9f9f9;padding:10px 15px;border-bottom:1px solid #eee;}.am-modal-bd{padding:15px;}.am-form-group{margin-top:20px;}.am-form-label{font-size:13px;color:#666;font-weight:normal;line-height:32px;text-align:right;padding:0;}.tpl-form-input{height:32px;padding:6px 10px;font-size:13px;border:1px solid #e8e8e8;border-radius:3px;transition:all 0.3s;width:100%;}input.tpl-form-input{padding-top:4px;padding-bottom:4px;}.confirm-btn{background:#23b7e5;color:white;border:none;border-radius:3px;padding:6px 15px;font-size:13px;cursor:pointer;transition:all 0.3s;}.confirm-btn:hover{background:#1a9fd4;box-shadow:0 2px 5px rgba(26,159,212,0.2);}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-location-arrow"></span> {$realmName|emoji_decode} 置顶列表
        </div>

        <div class="tpl-portlet-input">
            <div class="input-icon">
                <i class="am-icon-search" onclick="fuzzy();"></i>
                <input type="text" id="fz_name" value="{$hazy_name}" placeholder="搜索帖子...">
            </div>
        </div>

    </div>
    <div class="tpl-block">
        <div class="am-g" style="margin-bottom: 15px;">
            <div class="am-u-sm-12 am-u-md-6">
                <div class="am-btn-toolbar">
                    <a href="javascript:void(0);" class="am-btn am-btn-success am-btn-sm"
                        data-am-modal="{target: '#shandsel', closeViaDimmer: 0, width: 400, height: 220}">
                        <span class="am-icon-plus"></span> 新增置顶
                    </a>
                </div>
            </div>
        </div>
        <div class="am-g">
            <div class="am-u-sm-12">
                <div class="am-form">
                    <table class="am-table am-table-striped am-table-hover">
                        <thead>
                            <tr>
                                <th width="5%">帖子ID</th>
                                <th width="18%">发帖用户</th>
                                <th width="18%">发帖 标题 / 内容</th>
                                <th width="10%">帖子类型</th>
                                <th width="18%">发帖时间</th>
                                <th width="18%">置顶时间</th>
                                <th width="13%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {volist name="list" id="vo"}
                            <tr>
                                <td>{$vo.id}</td>
                                <td>
                                    <a href="{:url('user/index')}&openid={$vo.user_wechat_open_id}&page=1"
                                        target="_blank" title="{$vo.user_nick_name|emoji_decode}">
                                        {$vo.user_nick_name|emoji_decode|subtext=15}
                                    </a>
                                </td>
                                <td>
                                    <a href="{:url('essay/setails')}&uplid={$vo.id}" target="_blank">
                                        {if $vo.study_title}
                                        {$vo.study_title|emoji_decode|subtext=15}
                                        {elseif $vo.study_content}
                                        {$vo.study_content|emoji_decode|subtext=15}
                                        {else}
                                        无
                                        {/if}
                                    </a>
                                </td>
                                <td>
                                    {if $vo.study_type==0} 图文 {elseif $vo.study_type==1} 语音 {elseif $vo.study_type==2}
                                    视频 {/if}
                                </td>
                                <td>{:date('Y-m-d H:i:s',$vo.adapter_time)}</td>
                                <td>{:date('Y-m-d H:i:s',$vo.topping_time)}</td>
                                <td>
                                    <button type="button" class="action-btn am-btn-danger"
                                        onclick="cancelStrike('{$vo.id}');">取消置顶</button>
                                </td>
                            </tr>
                            {/volist}
                        </tbody>
                    </table>
                    <div class="am-cf">
                        <div class="am-fr">
                            {$list->render()}
                        </div>
                    </div>
                    <hr>
                </div>
            </div>

        </div>
    </div>
    <div class="tpl-alert"></div>
    <div class="am-modal am-modal-no-btn" tabindex="-1" id="shandsel">
        <div class="am-modal-dialog">
            <div class="am-modal-hd">
                <span>置顶帖子</span>
                <a id="euModalClose" href="javascript: void(0);" class="am-close am-close-spin"
                    data-am-modal-close>&times;</a>
            </div>
            <div class="am-modal-bd am-form">
                <div class="am-form-group am-g">
                    <label class="am-u-sm-3 am-form-label">帖子编号</label>
                    <div class="am-u-sm-9">
                        <input type="number" id="prid" oninput="digitalCheck(this);" class="tpl-form-input"
                            placeholder="请输入要置顶帖子的编号">
                    </div>
                </div>
                <div class="am-u-sm-12" style="margin-top:15px;text-align: center;">
                    <button type="button" class="confirm-btn" onclick="sendGifts();">确定置顶</button>
                </div>
            </div>
        </div>
    </div>
</div>
{/block}
{block name="script"}
<script>

    var digitalCheck = function (obj) {
        obj.value = Number($.trim($(obj).val()).match(/^\d+(?:\.\d{0,0})?/));
    }

    var sendGifts = function () {
        var prid = $.trim($('#prid').val());
        if (prid == 0 || prid > 2147483646) {
            layer.msg('\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u5e16\u5b50\u7f16\u53f7');
            return;
        }
        layer.confirm('您确定要置顶这条帖子吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("{:url('compass/inctoping')}", { 'trid': '{$tory_id}', 'prid': prid }, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, { icon: 5, time: 2000 });
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }

    var cancelStrike = function (prid) {
        layer.confirm('您确定要取消置顶当前帖子吗？', {
            btn: ['确定', '取消'], title: '提示'
        }, function () {
            $.ajaxSettings.async = false;
            $.post("{:url('compass/topping')}", { 'prid': prid }, function (data) {
                if (data.code > 0) {
                    layer.msg(data.msg, { icon: 1, time: 1000 }, function () {
                        location.reload();
                    });
                } else {
                    layer.msg(data.msg, { icon: 5, time: 2000 }, function () {
                        location.reload();
                    });
                }
            }, 'json');
            $.ajaxSettings.async = true;
        }, function (index) {
            layer.close(index);
        });
    }

    var fuzzy = function () {
        var fz_name = $.trim($('#fz_name').val());
        if (fz_name) {
            location.href = "{:url('compass/topping')}&tory_id={$tory_id}&hazy_name=" + fz_name + "&page={$page}";
        } else {
            location.href = "{:url('compass/topping')}&tory_id={$tory_id}&page={$page}";
        }
    }

</script>
{/block}