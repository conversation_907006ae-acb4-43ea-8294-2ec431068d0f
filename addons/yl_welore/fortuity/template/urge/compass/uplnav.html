{extend name="/base"/}
{block name="main"}
<style>.tpl-portlet-components{background:#fff;border-radius:4px;box-shadow:0 1px 3px rgba(0,0,0,0.1);padding:20px;margin-bottom:20px;}.portlet-title{display:flex;justify-content:space-between;align-items:center;padding-bottom:15px;margin-bottom:15px;border-bottom:1px solid #f0f0f0;}.caption{font-size:16px;color:#333;font-weight:500;}.caption .am-icon-edit{margin-right:5px;color:#23b7e5;}.am-form-label{text-align:right;color:#333;font-weight:normal;}.am-form-group small{display:block;margin-top:.5rem;color:#6c757d;font-size:12px;}.tpl-form-input{display:block;width:100%;height:36px;padding:8px 12px;font-size:14px;line-height:1.5;color:#495057;background-color:#fff;background-clip:padding-box;border:1px solid #ced4da;border-radius:4px;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;}.tpl-form-input:focus{color:#495057;background-color:#fff;border-color:#80bdff;outline:0;box-shadow:0 0 0 0.2rem rgba(35,183,229,.25);}textarea.tpl-form-input{height:auto;min-height:90px;resize:vertical;}.confirm-btn{background:#23b7e5;color:white;border:none;border-radius:3px;padding:8px 18px;font-size:14px;cursor:pointer;transition:all 0.3s;}.confirm-btn:hover{background:#1a9fd4;}.image-picker{display:flex;align-items:center;gap:15px;}.image-picker .image-preview{flex-shrink:0;border:1px dashed #ced4da;border-radius:4px;cursor:pointer;background-color:#f8f9fa;display:flex;align-items:center;justify-content:center;overflow:hidden;}.image-picker .image-preview img{max-width:100%;max-height:100%;object-fit:cover;}.image-picker .image-controls{display:flex;flex-direction:column;align-items:flex-start;gap:8px;}.action-btn{display:inline-block;padding:5px 10px;font-size:12px;font-weight:normal;line-height:1.4;text-align:center;cursor:pointer;border:1px solid #ced4da;border-radius:4px;transition:all .3s;background-color:#fff;}.action-btn:hover{background-color:#f8f9fa;}</style>
<div class="tpl-portlet-components">
    <div class="portlet-title">
        <div class="caption">
            <span class="am-icon-edit"></span> 编辑广场
        </div>
    </div>
    <div class="tpl-block ">
        <div class="am-g">
            <div class="am-u-sm-12 am-u-md-9">
                <div class="am-form am-form-horizontal tpl-form-line-form">
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">广场名称</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="name" value="{$list.name}" class="tpl-form-input" placeholder="请输入广场名称">
                            <small>输入广场名字 例如：生活 社区 电视剧 电影 等</small>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">广场图标</label>
                        <div class="am-u-sm-9">
                            <div class="image-picker">
                                <div class="image-preview" onclick="cuonice();" style="width: 158px; height: 82px;">
                                    <img src="{$list.icon}" id="shion" onerror="this.src='static/wechat/image_vip_top.jpg'"/>
                                </div>
                                <div class="image-controls">
                                    <button type="button" class="action-btn" onclick="cuonice();">选择图片</button>
                                    <small>建议图片尺寸：158*82px</small>
                                </div>
                                <input type="hidden" name="sngimg" value="{$list.icon}">
                            </div>
                        </div>
                    </div>

                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">关键词</label>
                        <div class="am-u-sm-9">
                            <input type="text" id="keyword" value="{$list.keyword}" class="tpl-form-input" placeholder="请输入关键词">
                            <small>建议填写多个关键词并用空格隔开 例如：休闲活动 兴趣收藏 艺术自然</small>
                        </div>
                    </div>


                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">状态</label>
                        <div class="am-u-sm-9">
                            <select id="status" class="tpl-form-input">
                                <option value="0" {if $list.status==0}selected{/if}>隐藏</option>
                                <option value="1" {if $list.status==1}selected{/if}>显示</option>
                            </select>
                        </div>
                    </div>
                    <div class="am-form-group">
                        <label class="am-u-sm-3 am-form-label">排序</label>
                        <div class="am-u-sm-9">
                            <input type="number" id="scores" value="{$list.scores}" class="tpl-form-input" placeholder="请输入排序数字">
                        </div>
                    </div>
                    <div class="am-form-group">
                        <div class="am-u-sm-9 am-u-sm-push-3">
                            <button type="button" class="confirm-btn" onclick="holdSave();">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
{/block}
{block name="script"}
<script>


    function cuonice() {
        layer.open({
            type: 2,
            anim:2,
            scrollbar: true,
            area: ['900px', '600px'],
            title: false,
            closeBtn: 0,
            shadeClose: true,
            content: ["{:url('images/dialogImages')}&gclasid=0", 'no']
        });
    }

    function sutake(eurl) {
        $('#shion').attr('src', eurl);
        $("[name='sngimg']").val(eurl);
        layer.closeAll();
    }

    function excheck(name, sngimg, skeyword, scores) {
        if (name == '' || name == 'undefined' || name == null) {
            layer.msg('广场名称不能为空');
            return false;
        }
        if (sngimg == '' || sngimg == 'undefined' || sngimg == null) {
            layer.msg('请上传广场图标');
            return false;
        }
        if (skeyword == '' || skeyword == 'undefined' || skeyword == null) {
            layer.msg('关键词不能为空');
            return false;
        }
        if (scores == '') {
            $('#scores').val('0');
            return false;
        }
        return true;
    }

    var slock = false;

    function holdSave() {
        if (!slock) {
            var name = $.trim($('#name').val());
            var sngimg = $.trim($("[name='sngimg']").val());
            var skeyword = $.trim($('#keyword').val());
            var status = $.trim($('#status').val());
            var scores = $.trim($('#scores').val());
            if (excheck(name, sngimg, skeyword, scores)) {
                slock = true;
                $.ajax({
                    type: "post",
                    url: "{:url('uplnav')}",
                    data: {
                        'uplid': '{$list.id}',
                        'name': name,
                        'icon': sngimg,
                        'keyword': skeyword,
                        'status': status,
                        'scores': scores
                    },
                    dataType: 'json',
                    success: function (data) {
                        if (data.code > 0) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                location.href = "{:url('nav')}";
                            });
                        } else {
                            layer.msg(data.msg, {icon: 5, time: 2000}, function () {
                                slock = false;
                            });
                        }
                    }
                });
            }
        }
    }
</script>
{/block}