<?php

namespace app\api\service;

use app\common\Gyration;
use think\Db;

class UserService
{

    //默认配置
    protected $config = array(
        'url' => "https://api.weixin.qq.com/sns/jscode2session", //微信获取session_key接口url
        'appid' => '', // APPId
        'secret' => '', // 秘钥
        'grant_type' => 'authorization_code', // grant_type，一般情况下固定的
    );

    /**
     * 获取openid 参数准备 (微信)
     * @param $code
     * @return mixed
     */
    public function checkLogin($code, $much_id)
    {
        /**
         * 这是一个 HTTP 接口，开发者服务器使用登录凭证 code 获取 session_key 和 openid。其中 session_key 是对用户数据进行加密签名的密钥。
         * 为了自身应用安全，session_key 不应该在网络上传输。
         * 接口地址："https://api.weixin.qq.com/sns/jscode2session?appid=APPID&secret=SECRET&js_code=JSCODE&grant_type=authorization_code"
         */
        $getConfig = cache('fatal_' . $much_id);
        if (!$getConfig) {
            $getConfig = Db::name('config')->where('much_id', $much_id)->find();
            if ($getConfig) {
                foreach ($getConfig as $key => $value) {
                    if ($key != 'id' && $key != 'pay_react' && $key != 'much_id') {
                        $getConfig[$key] = authcode($getConfig[$key], 'DECODE', 'YuluoNetwork', 0);
                    }
                }
                cache('fatal_' . $much_id, $getConfig);
            }
        }
        $params = array(
            'appid' => $getConfig['app_id'],
            'secret' => $getConfig['app_secret'],
            'js_code' => $code,
            'grant_type' => $this->config['grant_type']
        );
        $res = $this->makeRequest($this->config['url'], $params);
        return $res['result'];
    }

    /**
     * 获取openid 参数准备(头条)
     * @param $code
     * @return mixed
     */
    public function checkLoginTt($code, $much_id)
    {
        $util = new Util();
        $url = "https://developer.toutiao.com/api/apps/v2/jscode2session";
        $params = [
            'appid' => '',
            'secret' => '',
            'anonymous_code' => '',
            'code' => $code
        ];
        return $util->TouTiaoApiPost($url, $params, $much_id);
    }

    /**
     * 发起http请求
     * @param string $url 访问路径
     * @param array $params 参数，该数组多于1个，表示为POST
     * @param int $expire 请求超时时间
     * @param array $extend 请求伪造包头参数
     * @param string $hostIp HOST的地址
     * @return array    返回的为一个请求状态，一个内容
     */
    protected function makeRequest($url, $params = array(), $expire = 0, $extend = array(), $hostIp = '')
    {
        if (empty($url)) {
            return array('code' => '100');
        }

        $_curl = curl_init();
        $_header = array(
            'Accept-Language: zh-CN',
            'Connection: Keep-Alive',
            'Cache-Control: no-cache'
        );
        // 方便直接访问要设置host的地址
        if (!empty($hostIp)) {
            $urlInfo = parse_url($url);
            if (empty($urlInfo['host'])) {
                $urlInfo['host'] = substr(DOMAIN, 7, -1);
                $url = "http://{$hostIp}{$url}";
            } else {
                $url = str_replace($urlInfo['host'], $hostIp, $url);
            }
            $_header[] = "Host: {$urlInfo['host']}";
        }

        // 只要第二个参数传了值之后，就是POST的
        if (!empty($params)) {
            curl_setopt($_curl, CURLOPT_POSTFIELDS, http_build_query($params));
            curl_setopt($_curl, CURLOPT_POST, true);
        }

        if (substr($url, 0, 8) == 'https://') {
            curl_setopt($_curl, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($_curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        }
        curl_setopt($_curl, CURLOPT_URL, $url);
        curl_setopt($_curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($_curl, CURLOPT_USERAGENT, 'API PHP CURL');
        curl_setopt($_curl, CURLOPT_HTTPHEADER, $_header);

        if ($expire > 0) {
            curl_setopt($_curl, CURLOPT_TIMEOUT, $expire); // 处理超时时间
            curl_setopt($_curl, CURLOPT_CONNECTTIMEOUT, $expire); // 建立连接超时时间
        }

        // 额外的配置
        if (!empty($extend)) {
            curl_setopt_array($_curl, $extend);
        }
        $result['result'] = curl_exec($_curl);
        $result['code'] = curl_getinfo($_curl, CURLINFO_HTTP_CODE);
        $result['info'] = curl_getinfo($_curl);
        if ($result['result'] === false) {
            $result['result'] = curl_error($_curl);
            $result['code'] = -curl_errno($_curl);
        }
        curl_close($_curl);
        return $result;
    }


    public static $OK = 0;
    public static $IllegalAesKey = -41001;
    public static $IllegalIv = -41002;
    public static $IllegalBuffer = -41003;
    public static $DecodeBase64Error = -41004;

    /**
     * 检验数据的真实性，并且获取解密后的明文.
     * @param $encryptedData string 加密的用户数据
     * @param $sessionKey string
     * @param $iv string 与用户数据一同返回的初始向量
     * @param $app_id string
     * @param $data string 解密后的原文
     *
     * @return int 成功0，失败返回对应的错误码
     */
    public function decryptData($encryptedData, $sessionKey, $app_id, $iv, &$data)
    {
        if (strlen($sessionKey) != 24) {
            return self::$IllegalAesKey;
        }
        $aesKey = base64_decode($sessionKey);


        if (strlen($iv) != 24) {
            return self::$IllegalIv;
        }
        $aesIV = base64_decode($iv);

        $aesCipher = base64_decode($encryptedData);

        $result = openssl_decrypt($aesCipher, "AES-128-CBC", $aesKey, 1, $aesIV);

        $dataObj = json_decode($result);
        if ($dataObj == NULL) {
            return self::$IllegalBuffer;
        }
        if ($dataObj->watermark->appid != $app_id) {
            return self::$IllegalBuffer;
        }
        $data = $result;
        return self::$OK;
    }

    /**
     * 审核增加积分
     */
    public function examine($id, $much_id)
    {
        // 启动事务
        Db::startTrans();
        try {
            //审核通过
            $paper_src = Db::name('paper')->where('id', $id)->where('much_id', $much_id)->update(['prove_time' => time(), 'study_status' => 1]);
            if (!$paper_src) {
                Db::rollback();
                return json_encode(['status' => 'error', 'id' => 0, 'msg' => '审核失败，请稍候重试！3']);
            }

            // 提交事务
            Db::commit();
            return json_encode(['status' => 'success', 'msg' => '审核成功！']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return json_encode(['status' => 'error', 'id' => 0, 'msg' => '审核失败，请稍候重试！' . $e->getMessage()]);
        }
    }

    /**
     * 给用户送礼
     * user_id=获赠者
     * uid=赠送者
     */
    public function user_reward($data)
    {

        //查询收益详情
        $tribute_taxation = Db::name('tribute_taxation')->where('much_id', $data['much_id'])->find();
        //查询礼物详情
        $li_wu = Db::name('tribute')->where('id', $data['li_id'])->where('much_id', $data['much_id'])->find();
        //查询用户详情
        $user_info = Db::name('user')->where('id', $data['uid'])->where('much_id', $data['much_id'])->find();
        $user_info_h = Db::name('user')->where('id', $data['user_id'])->where('much_id', $data['much_id'])->find();
        //查询用户贝壳是否满足赠送条件
        $conch = intval(abs($data['num'])) * $li_wu['tr_conch'];
        //查询当前兑换比例
        $fraction_scale = Db::name('user_punch_range')->where('much_id', $data['much_id'])->find();
        $scale = 10;
        if (!empty($fraction_scale['fraction_scale'])) {
            $scale = $fraction_scale['fraction_scale'];
        }
        $is['much_id'] = $data['much_id'];
        $is['exchange_rate'] = $scale;
        $is['con_user_id'] = $data['uid'];
        $is['sel_user_id'] = $data['user_id'];
        $is['bute_name'] = intval(abs($data['num'])) . "个" . $li_wu['tr_name'];
        $is['bute_price'] = $li_wu['tr_conch'];
        $is['allow_scale'] = 1 - $tribute_taxation['taxing'];
        $is['bute_time'] = time();
        //$fraction = $conch * $is['allow_scale'];
        $user_b = bcsub($conch, bcmul($conch, $tribute_taxation['taxing'], 2), 2);

        Db::startTrans();
        try {
            //获赠者明细
            $user_amount = $this->add_user_amount($data['user_id'], 4, $user_b, '获赠礼物收益并扣除手续费', $data['much_id'], $is['allow_scale']);
            if (!$user_amount) {
                $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！2'];
                Db::rollback();
                return $rs;
            }
            //赠送礼物表增加数据
            $ins = Db::name('user_subsidy')->insert($is);
            if (!$ins) {
                $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！3'];
                Db::rollback();
                return $rs;
            }
            //增加获赠者的贝壳
            $up_user = Db::name('user')->where('id', $data['user_id'])->update(['conch' => bcadd($user_info_h['conch'], $user_b, 2)]);
            if (!$up_user) {
                $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！5'];
                Db::rollback();
                return $rs;
            }
            //明细表增加数据

            $rs = ['status' => 'success', 'msg' => '赠送成功！'];

            $util = new Util();
            //发送模版
            $util->add_template(['much_id' => $data['much_id'],
                'at_id' => 'YL0007',
                'user_id' => $data['user_id'],
                'page' => 'yl_welore/pages/index/index',
                'keyword1' => emoji_decode($user_info['user_nick_name']) . '，赠送给您' . intval(abs($data['num'])) . "个" . $li_wu['tr_name'],
                'keyword2' => date('Y-m-d H:i', time()),
            ]);
            Db::commit();
            return $rs;
        } catch (\Exception $e) {
            Db::rollback();
            $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！'];
            return $rs;
        }
    }

    /**
     * 明细表增加数据
     */
    public function add_user_amount($user_id, $category, $finance, $solution, $much_id, $tribute_taxation)
    {
        //查询用户详情
        $user_info = Db::name('user')->where('id', $user_id)->where('much_id', $much_id)->find();
        $fraction_scale = Db::name('user_punch_range')->where('much_id', $much_id)->find();
        $scale = 10;
        if (!empty($fraction_scale['fraction_scale'])) {
            $scale = $fraction_scale['fraction_scale'];
        }
        $data['user_id'] = $user_id;
        $data['category'] = $category;
        $data['ruins_time'] = time();
        $data['solution'] = $solution;
        $data['much_id'] = $much_id;
        //收益
        if ($category == 3) {
            $data['evaluate'] = 1;
            $data['finance'] = ($finance * $tribute_taxation) * $scale;
            $data['poem_fraction'] = $user_info['fraction'];//初始积分
            $data['surplus_fraction'] = $user_info['fraction'] + ($finance * $tribute_taxation) * $scale;//增加后积分
            $data['poem_conch'] = $user_info['conch'];//初始贝壳
            $data['surplus_conch'] = $user_info['conch'];//后贝壳
        }
        //消费
        if ($category == 2) {
            $data['evaluate'] = 0;
            $data['finance'] = -$finance;
            $data['poem_conch'] = $user_info['conch'];//初始贝壳
            $data['surplus_conch'] = $user_info['conch'] - $finance;//减少后贝壳

            $data['poem_fraction'] = $user_info['fraction'];//初始积分
            $data['surplus_fraction'] = $user_info['fraction'];//后积分
        }
        //增加贝壳
        if ($category == 4) {
            $data['evaluate'] = 0;
            $data['finance'] = $finance;
            $data['poem_conch'] = $user_info['conch'];//初始贝壳
            $data['surplus_conch'] = $user_info['conch'] + $finance;//减少后贝壳

            $data['poem_fraction'] = $user_info['fraction'];//初始积分
            $data['surplus_fraction'] = $user_info['fraction'];//后积分
        }
        $ins = Db::name('user_amount')->insert($data);
        if ($ins) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 手机黑名单
     */
    public function BlockingPhone($phone, $much_id)
    {
        $phone = $phone . '';
        // 0：通行  1：禁止
        //完全匹配白名单
        $blacklist = Db::name('phone_blacklist')->where('much_id', $much_id)->where('intercept_type', 1)->where('block_type', 0)->select();
        if (!empty($blacklist)) {
            foreach ($blacklist as $k => $v) {
                if (strcasecmp($v['block_rule'], $phone) == 0) {
                    return 0;
                }
            }
        }

        //完全匹配黑名单
        $blacklist = Db::name('phone_blacklist')->where('much_id', $much_id)->where('intercept_type', 0)->where('block_type', 0)->select();
        if (!empty($blacklist)) {
            foreach ($blacklist as $k => $v) {
                if (strcasecmp($v['block_rule'], $phone) == 0) {
                    return 1;
                }
            }
        }
        //正则匹配白名单
        $blacklist = Db::name('phone_blacklist')->where('much_id', $much_id)->where('intercept_type', 1)->where('block_type', 1)->select();
        if (!empty($blacklist)) {
            foreach ($blacklist as $k => $v) {
                if (preg_match($v['block_rule'], $phone)) {
                    return 0;
                }
            }
        }
        // 正则匹配黑名单
        $blacklist = Db::name('phone_blacklist')->where('much_id', $much_id)->where('intercept_type', 0)->where('block_type', 1)->select();
        if (!empty($blacklist)) {
            foreach ($blacklist as $k => $v) {
                if (preg_match($v['block_rule'], $phone)) {
                    return 1;
                }
            }
        }
        return 0;
    }
}