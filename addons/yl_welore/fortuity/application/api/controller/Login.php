<?php

namespace app\api\controller;

use app\api\service\UserService;
use app\api\service\Util;
use app\common\MagicTrick;
use app\common\Pisces;
use app\common\Suspense;
use think\Cache;
use think\Controller;
use think\Db;
use think\Request;

class Login extends Controller
{
    public function HdmOypShGCsKHhyYqSaZuIvQmWDvCoTh()
    {
        if (request()->isPost()) {
            $data = input('post.');
            $dataToday = input('post.today');
            $today = (strtotime(date("Y-m-d H:i"), time())) * 2;
            $acknowledge = false;
            if ($today == $dataToday && !empty($data['uid']) && !empty($data['phone']) && !empty($data['much_id'])) {
                $acknowledge = true;
            }
            if ($acknowledge) {
                Db::startTrans();
                try {
                    Db::name('user')->where('id', $data['uid'])->where('much_id', $data['much_id'])->update(['user_phone' => $data['phone']]);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
                return 1;
            } else {
                abort(404);
            }
        } else {
            abort(404);
        }
    }

    /**
     *  获取openID
     */
    public function index()
    {
        $rs = array('code' => 0, 'info' => array());
        $code = input('param.code');
        $much_id = input('param.much_id');
        $user_open_type = input('param.user_open_type');
        $user = new UserService();
        if (empty($user_open_type) || $user_open_type == 0) {
            $info = $user->checkLogin($code, $much_id);
            $rs['info'] = json_decode($info, true);
            $rs['code'] = $rs['info']['errcode'] ? $rs['info']['errcode'] : 0;
            if ($rs['code'] == 0) {
                //查询是否有当前用户
                $check_user = Db::name('user')->where('user_wechat_open_id', $rs['info']['openid'])->where('much_id', $much_id)->find();
                if (!empty($check_user)) { //查询到用户
                    if (isset($rs['info']['unionid'])) {
                        Db::name('user')->where('id', $check_user['id'])->update(['user_wechat_union_id' => $rs['info']['unionid']]);
                        //查询绑定表
                        $wx_popular_bind_user = Db::name('wx_popular_bind_user')->where('user_id',0)->where('union_id', $rs['info']['unionid'])->where('much_id', $much_id)->find();
                        if (!empty($wx_popular_bind_user)) {
                            Db::name('wx_popular_bind_user')->where('id', $wx_popular_bind_user['id'])->update(['user_id' => $check_user['id']]);
                            $u = new Wechat();
                            $key = [
                                'open_id' => $wx_popular_bind_user['open_id'],
                                'first' => '已成功绑定公众号模板消息',
                                'keyword1' => '绑定',
                                'keyword2' => '小程序互动信息不再错过',
                                'remark' => '前往小程序',
                            ];
                            $u->send_template($key, $much_id);
                        }
                    }
                }
            }
        }
        if ($user_open_type == 1) {
            $info = $user->checkLoginTt($code, $much_id);
            $info = json_decode($info, true);
            $key['openid'] = $info['data']['openid'];
            $key['session_key'] = $info['data']['session_key'];
            $key['err_no'] = $info['err_no'];
            $key['err_tips'] = $info['err_tips'];
            $rs['code'] = $key['err_no'];
            $rs['info'] = $key;
        }
        return json_encode($rs);
    }

    /**
     * 截取字符串
     */
    public function subtext($text, $length)
    {
        if (mb_strlen($text, 'utf8') > $length) {
            return mb_substr($text, 0, $length, 'utf8');
        }
        return $text;
    }

    public function new_do_login()
    {
        $data = input('param.');
        $data['nike_name'] = $this->subtext($data['nike_name'], 12);
        $util = new Util();
        $check_title = $util->get_check_msg($data['nike_name'], $data['much_id'], $data['openid']);
        if ($check_title['status'] == 'error') {
            return json_encode($check_title);
        }
        $user_info = Db::name('user')->where('user_wechat_open_id', $data['openid'])->where('much_id', $data['much_id'])->find();
        if ($user_info['tourist'] == 0) {
            return json_encode(['code' => 1, 'msg' => '已经设置成功！']);
        }
        if (empty($data['nike_name'])) {
            return json_encode(['code' => 1, 'msg' => '填个昵称吧~']);
        }
        $check = Db::name('user')->where('user_nick_name', emoji_encode($data['nike_name']))->where('much_id', $data['much_id'])->find();
        if ($check) {
            return json_encode(['code' => 1, 'msg' => '昵称重复了呢~']);
        }
        $wx['gender'] = 1;
        $wx['tourist'] = 0;
        $wx['user_reg_time'] = time();
        $wx['user_head_sculpture'] = $data['avatarUrl'];
        $wx['user_nick_name'] = emoji_encode($data['nike_name']);
        $wx['autograph'] = '';
        $wx['user_open_type'] = 0;
        $res = Db::name('user')->where('id', $user_info['id'])->update($wx);
        if (!$res) {
            return json_encode(['code' => 1, 'msg' => '设置失败！']);
        }
        $user = Db::name('user')->where('id', $user_info['id'])->find();
        $user['user_nick_name'] = emoji_decode($user['user_nick_name']);
        if (!empty($user['user_phone'])) {
            $user['user_phone'] = $this->cipher_text($user['user_phone']);
        }
        $user['openid'] = $user['user_wechat_open_id'];
        $user['uid'] = $user['id'];
        return json_encode(['code' => 0, 'msg' => '设置成功！', 'info' => $user]);
    }

    /*
    * 储存wx openID
    */
    public function do_login()
    {
        $rs = array('code' => 0);
        $data = input('param.');
        $user_open_type = input('param.user_open_type');
        /**
         * 第一个版本低于第二个版本的时候 return -1
         * 第一个版本等于第二个版本的时候 return 0
         * 第一个版本高于第二个版本的时候 return 1
         */
        //return json_encode($data);
        if (isset($data['version'])) {
            $data['userInfo'] = json_decode($data['userInfo'], true);
        }
        if (strlen($data['wx_openid']) != 28 || empty($data['wx_openid'])) {
            return ['code' => 1];
        }
        $prevent_duplication = Db::name('authority')->where('much_id', $data['uniacid'])->find();
        $wx_d['user_head_sculpture'] = $data['userInfo']['avatarUrl'];
        if ($prevent_duplication['prevent_duplication'] == 1) {
            $wx_d['user_nick_name'] = $this->user_nick_name(5);
        } else {
            $wx_d['user_nick_name'] = emoji_encode($data['userInfo']['nickName']);
        }
        $wx_d['user_nick_name'] = $this->subtext($wx_d['user_nick_name'], 12);
        $wx_d['gender'] = $data['userInfo']['gender'];
        $wx_d['user_wechat_open_id'] = $data['wx_openid'];
        $wx_d['user_reg_time'] = time();
        $wx_d['much_id'] = $data['uniacid'];
        $wx_d['token'] = $this->create_uuid();
        $wx_d['token_impede'] = 259200 + time();
        $wx_d['autograph'] = '';
        if ($user_open_type == 1) {
            $wx_d['user_open_type'] = 1;
        } else {
            $wx_d['user_open_type'] = 0;
        }
        $check = Db::name('user')->where('user_open_type', $wx_d['user_open_type'])->where('user_wechat_open_id', $data['wx_openid'])->where('much_id', $data['uniacid'])->find();
        if ($check) {
            $rs['id'] = $check['id'];
            if ($check['token_impede'] == 0 || $check['token_impede'] <= time()) {
                $token_impede = 259200 + time();
                $token = $this->create_uuid();//更新token
                Db::name('user')->where('id', $check['id'])->update(['token' => $token, 'token_impede' => $token_impede]);
                $check_token['token_impede'] = $token_impede;
            }
            $check_token = Db::name('user')->where('id', $rs['id'])->find();
            if (!empty($check_token['user_phone'])) {
                $check_token['user_phone'] = $this->cipher_text($check_token['user_phone']);
            }
        } else {
            $res = Db::name('user')->insertGetId($wx_d);
            $rs['id'] = $res;
            $check_token = Db::name('user')->where('id', $res)->find();
            if (!empty($check_token['user_phone'])) {
                $check_token['user_phone'] = $this->cipher_text($check_token['user_phone']);
            }
        }

        if (version_compare($data['version'], '1.0.55') == 1) {

            if ($check && $check['tourist'] == 1) {
                $d['user_head_sculpture'] = $data['userInfo']['avatarUrl'];
                $d['user_nick_name'] = emoji_encode($data['userInfo']['nickName']);
                $d['gender'] = $data['userInfo']['gender'];
                $d['autograph'] = '';
                $d['user_phone'] = NULL;
                $d['tourist'] = 0;
                $d['token_impede'] = 259200 + time();//更新时间
                $d['token'] = $this->create_uuid();//更新token
                Db::name('user')->where('id', $check['id'])->update($d);
                $check_token = Db::name('user')->where('id', $check['id'])->find();
                $check_token['user_nick_name'] = emoji_decode($check_token['user_nick_name']);
                if (!empty($check_token['user_phone'])) {
                    $check_token['user_phone'] = $this->cipher_text($check_token['user_phone']);
                }
            }

        }
        $rs = $check_token;
        return json_encode($rs);
    }

    /**
     * 游客登陆
     */
    public function add_tourist()
    {
        $data = input('param.');
        $user_open_type = input('param.user_open_type');
        if (empty($user_open_type) || $user_open_type == 0) {
            $user_open_type = 0;
        } else {
            $user_open_type = 1;
        }
        if (strlen($data['openid']) != 28 || empty($data['openid'])) {
            return json_encode(['status' => 'error', 'info' => '-1']);
        }
        $domain = explode(':', $_SERVER['HTTP_HOST']);
        $absAddress = explode("index.php", $_SERVER['SCRIPT_NAME']);
        $def = "https://{$domain[0]}{$absAddress[0]}static/applet_icon/default.png";
        //查询是否有这个用户
        $user_info = Db::name('user')->where('user_open_type', $user_open_type)->where('user_wechat_open_id', $data['openid'])->find();
        if (empty($user_info)) {
            $ip = \request()->ip();
            // 启动事务
            Db::startTrans();
            try {
                $user_nick_name = $this->user_nick_yk();
                $user_nick_name = $this->subtext($user_nick_name, 12);
                //token过期时间
                $token_impede = 259200;//三天
                $ins = Db::name('user')->insertGetId(['user_open_type' => $user_open_type, 'user_access_ip' => $ip, 'user_head_sculpture' => $def, 'user_nick_name' => $user_nick_name, 'user_phone' => NULL, 'user_wechat_open_id' => $data['openid'], 'tourist' => 1, 'much_id' => $data['much_id'], 'token' => $this->create_uuid(), 'token_impede' => time() + $token_impede, 'user_reg_time' => time()]);
                if (!$ins) {
                    Db::rollback();
                    return json_encode(['status' => 'error', 'info' => '-2']);
                }
                Db::commit();
//                if (!empty($data['session_key'])) {
//                    $user_info['session_key'] = $data['session_key'];
//                }
                $user_info = Db::name('user')->where('id', $ins)->where('much_id', $data['much_id'])->find();
                if (!empty($data['session_key'])) {
                    $user_info['session_key'] = $data['session_key'];
                }
                $user_info['openid'] = $data['openid'];
                $user_info['uid'] = $ins;
                $user_info['user_nick_name'] = $user_nick_name;
                return json_encode(['status' => 'success', 'info' => $user_info, 'data' => $data]);
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return json_encode(['status' => 'error', 'info' => $e->getMessage()]);
            }
            //}
        } else {
            //如果much_id 不相等
            if ($user_info['much_id'] != $data['much_id']) {
                $up = Db::name('user')->where('id', $user_info['id'])->update(['much_id' => $data['much_id']]);
                if (!$up) {
                    return json_encode(['status' => 'error', 'info' => '-3']);
                }
                $user_info = Db::name('user')->where('user_open_type', $user_open_type)->where('much_id', $data['much_id'])->where('user_wechat_open_id', $data['openid'])->find();
            }
        }
        if (!empty($data['session_key'])) {
            $user_info['session_key'] = $data['session_key'];
        }
        $user_info['openid'] = $user_info['user_wechat_open_id'];
        $user_info['uid'] = $user_info['id'];
        $user_info['user_nick_name'] = emoji_decode($user_info['user_nick_name']);
        //判断

        if (!empty($user_info['user_phone'])) {
            $user_info['user_phone'] = $this->cipher_text($user_info['user_phone']);
        }
        $u = $this->login_chenk_token($user_info, $data);
        return json_encode(['status' => 'success', 'info' => $u, 'data' => $data]);
    }

    /*
     * 登陆时候判断token
     */
    public function login_chenk_token($user_info, $data)
    {
        //判断token是否过期或者是否为0
        if ($user_info['token_impede'] == 0 || $user_info['token_impede'] <= time()) {
            $token_impede = 259200 + time();//更新时间
            $token = $this->create_uuid();//更新token
            Db::name('user')->where('id', $user_info['id'])->update(['token' => $token, 'token_impede' => $token_impede]);
            $user_info['token_impede'] = $token_impede;
            $user_info['token'] = $token;
        }
        //判断版本号，和token不为空
        if (!empty($data['version']) && !empty($data['token'])) {
            if (version_compare($data['version'], '1.1.39', '>=')) {
                //能查到open_id这个用户但是和前端不匹配，更新token和时间
                if (strcmp($user_info['token'], $data['token']) != 0) {
                    $token_impede = 259200 + time();//更新时间
                    $token = $this->create_uuid();//更新token
                    Db::name('user')->where('id', $user_info['id'])->update(['token' => $token, 'token_impede' => $token_impede]);
                    $user_info['token_impede'] = $token_impede;
                    $user_info['token'] = $token;
                }
            }
        }
        return $user_info;
    }

    /**
     * 获取站点信息
     */
    public function get_authority()
    {
        $data = input('param.');
        $info = Db::name('authority')->where('much_id', $data['much_id'])->find();
        return json_encode($info);
    }

    public function cipher_text($string)
    {
        $wteh = substr($string, 0, 3);
        $yeth = substr($string, -4);
        return "{$wteh}********{$yeth}";
    }

    /**
     * @param string $prefix
     * @return string
     * 生成文件名
     */
    public function create_uuid($prefix = "Q")
    {
        $str = md5(uniqid(mt_rand(), true));
        $uuid = substr($str, 0, 8) . '';
        $uuid .= substr($str, 8, 4) . '';
        $uuid .= substr($str, 12, 4) . '';
        $uuid .= substr($str, 16, 4) . '';
        $uuid .= substr($str, 20, 12);
        return $prefix . $uuid;
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        $data = input('param.');
        $unfamiliar = Suspense::silence();
        $rhododendron = MagicTrick::pinafore();
        $foreign = MagicTrick::headpiece();
        $roseate = false;
        if ($rhododendron == $foreign) {
            Cache::clear();
            $roseate = true;
        }
        if ($roseate) {
            $abnormal = Suspense::silence();
        } else {
            $abnormal = $unfamiliar;
        }
        if ($unfamiliar !== $abnormal) {
            $rhododendron = MagicTrick::pinafore();
            Pisces::slothful($data['uniacid']);
        }
        $external = MagicTrick::chapeau();
        if ($rhododendron != $external) {
            echo 'error';
            exit();
        }
    }

    public function user_nick_yk()
    {
        $d = mt_rand(10000, 999999);
        return '游客_' . $d;
    }

    public function user_nick_name($len)
    {
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
        $string = time();
        for (; $len >= 1; $len--) {
            $position = rand() % strlen($chars);
            $position2 = rand() % strlen($string);
            $string = substr_replace($string, substr($chars, $position, 1), $position2, 0);
        }
        return 'wx_' . $string;
    }
}