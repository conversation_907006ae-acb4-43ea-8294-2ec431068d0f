<?php

namespace app\api\controller;


use app\api\service\Alternative;
use app\api\service\Util;
use think\Db;

class Conversation extends Base
{
    public function ins_article()
    {
        $data = input('param.');
        //是否开启网络验证
        //$offend = Db::name('user_violation')->where('much_id', $data['much_id'])->find();

        if ($this->user_info['tourist'] == 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '请登陆后发帖！']);
        }
        $util = new Util();
        //查询拉黑
        $check_hei = Db::name('user_blacklist')->where('much_id', $data['much_id'])->where('da_user_id', $this->user_info['id'])->where('pu_user_id', $data['re_user_id'])->find();
        $check_hei_to = Db::name('user_blacklist')->where('much_id', $data['much_id'])->where('da_user_id', $data['re_user_id'])->where('pu_user_id', $this->user_info['id'])->find();
        if ($check_hei) {
            $rs = ['status' => 'error', 'msg' => '发送失败，您已把对方加入黑名单，请移除后再发送！'];
            return $this->json_rewrite($rs);
        }
        if ($check_hei_to) {
            $rs = ['status' => 'error', 'msg' => '发送失败，对方已把你加入黑名单！'];
            return $this->json_rewrite($rs);
        }
        if (empty($data['paper_id'])) {
            $rs = ['status' => 'error', 'msg' => '发送失败，请稍后重试！'];
            return $this->json_rewrite($rs);
        }
        $dd['re_user_id'] = $data['re_user_id'];
        $dd['le_type'] = 1;
        $dd['se_user_id'] = $this->user_info['id'];
        $study_title = emoji_encode($data['study_title']);
        $dd['le_content'] = json_encode(['title' => $study_title, 'paid' => $data['paper_id'], 'type' => $data['paper_type']], 320);
        $dd['le_time'] = time();
        $dd['much_id'] = $data['much_id'];
        $dd['le_read_status'] = 0;
        // 启动事务
        Db::startTrans();
        try {
            $rs = Db::name('user_leave_word')->insertGetId($dd);
            if (!$rs) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '失败！']);
            }
            $check = Db::name('user_recent_contacts')->where('chat_user_id', $this->user_info['id'])->where('recent_user_id', $data['re_user_id'])->where('much_id', $data['much_id'])->find();
            if (empty($check)) {
                $rsd = Db::name('user_recent_contacts')->insert(['chat_user_id' => $this->user_info['id'], 'recent_user_id' => $data['re_user_id'], 'blatter_time' => time(), 'last_msg' => $study_title, 'much_id' => $data['much_id']]);
                if (!$rsd) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '失败！']);
                }
            } else {
                $rsd = Db::name('user_recent_contacts')->where('id', $check['id'])->update(['blatter_del' => 0, 'blatter_time' => time(), 'last_msg' => $study_title]);
                if (!$rsd) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '失败！']);
                }
            }


            $check_shou = Db::name('user_recent_contacts')->where('recent_user_id', $this->user_info['id'])->where('chat_user_id', $data['re_user_id'])->where('much_id', $data['much_id'])->find();
            if (empty($check_shou)) {
                $rss = Db::name('user_recent_contacts')->insert(['recent_user_id' => $this->user_info['id'], 'chat_user_id' => $data['re_user_id'], 'blatter_time' => time(), 'last_msg' => $study_title, 'much_id' => $data['much_id']]);
                if (!$rss) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '失败！']);
                }
            } else {
                $rss = Db::name('user_recent_contacts')->where('id', $check_shou['id'])->update(['blatter_del' => 0, 'blatter_time' => time(), 'last_msg' => $study_title]);
                if (!$rss) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '失败！']);
                }
            }
            //发送模版
            $util->add_template(['much_id' => $data['much_id'],
                'at_id' => 'YL0006',
                'user_id' => $data['re_user_id'],
                'page' => 'yl_welore/pages/packageB/private_letter/index?id=' . $this->user_info['id'],
                'keyword1' => '发送了一个帖子：' . emoji_decode($dd['le_content']),
                'keyword2' => date('Y年m月d日 H:i', time()),
                'keyword3' => emoji_decode($this->user_info['user_nick_name']),
            ]);
            $user = Db::name('user')->where('id', $data['re_user_id'])->where('much_id', $data['much_id'])->find();
            $info['le_content'] = ['title' => $study_title, 'paid' => $data['paper_id'], 'type' => $data['paper_type']];
            $info['se_user_head'] = $this->user_info['user_head_sculpture'];
            $info['re_user_head'] = $user['user_head_sculpture'];
            $info['se_user_id'] = $this->user_info['id'];
            $info['le_type'] = 1;
            $info['re_user_id'] = $user['id'];
            $info['id'] = $rs;
            $info['le_time'] = date('m-d H:i:s', $dd['le_time']);

            // 提交事务
            Db::commit();
            return $this->json_rewrite(['status' => 'success', 'msg' => '成功！', 'info' => $info]);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'msg' => '失败！']);
        }
    }

    /**
     * 获取话题
     */
    public function get_gambit()
    {
        $data = input('param.');
        $where['gambit_name'] = ['like', '%' . $data['search_name'] . '%'];
        $list = Db::name('gambit')->where($where)
            ->where('is_del', 0)
            ->where('much_id', $data['much_id'])
            ->where($where)
            ->page($data['page'], 15)
            ->order('scores,add_time desc')
            ->select();
        foreach ($list as $k => $v) {
            $list[$k]['gambit_name'] = emoji_decode($v['gambit_name']);
            $list[$k]['gambit_name'] = str_replace("#", '', $list[$k]['gambit_name']);
            $list[$k]['top_name'] = mb_substr($list[$k]['gambit_name'], 0, 1, 'utf-8');
        }
        return $this->json_rewrite($list);
    }

    /**
     * 增加话题
     */
    public function add_gambit()
    {
        $data = input('param.');
        //验证内容安全
        $util = new Util();
        if (empty($data['gambit_name'])) {
            return $this->json_rewrite(['msg' => '话题名称不能为空！', 'status' => 'error']);
        }
        $check_title = $util->get_check_msg($data['gambit_name'], $data['much_id'], $data['openid']);
        if ($check_title['status'] == 'error') {
            return $this->json_rewrite($check_title);
        }
        //查询是否能创建
        $check_config = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($check_config['allow_user_topic'] == 0) {
            return $this->json_rewrite(['msg' => '禁止用户创建话题！', 'status' => 'error']);
        }
        $gambit_name = str_replace("#", "", emoji_encode($data['gambit_name']));
        $gambit_name = str_replace("＃", "", $gambit_name);
        $d['gambit_name'] = '#' . $gambit_name . '#';
        $check = Db::name('gambit')->where('gambit_name', $d['gambit_name'])->where('much_id', $data['much_id'])->find();
        if ($check) {
            return $this->json_rewrite(['msg' => '已经有该话题了！', 'status' => 'error']);
        }
        $d['add_time'] = time();
        $d['scores'] = 0;
        $d['is_del'] = 0;
        $d['much_id'] = $data['much_id'];
        $res = Db::name('gambit')->insertGetId($d);
        if ($res) {
            return $this->json_rewrite(['msg' => '成功！', 'status' => 'success', 'id' => $res]);
        } else {
            return $this->json_rewrite(['msg' => '创建失败！', 'status' => 'error']);
        }
    }

    /**
     * 列表
     */
    public function get_gambit_list()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $where = [];
        $where_not_in = [];
        $page = $data['index_page'];
        if (isset($data['tory_id'])) {
            $where['p.tory_id'] = ['eq', $data['tory_id']];
            $where['p.topping_time'] = ['eq', 0];
        }
        if ($this->version == 1) {
            $where['p.study_type'] = ['in', ['0', '1', '3']];
            $where['p.is_buy'] = ['eq', 0];
        }
        $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($authority['hair_video_arbor'] == 0) {
            $where['p.study_type'] = ['in', ['0', '1', '3', '4', '5']];
        }
        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();

        /**
         * 第一个版本低于第二个版本的时候 return -1
         * 第一个版本等于第二个版本的时候 return 0
         * 第一个版本高于第二个版本的时候 return 1
         */
        //查询我关注的圈子
        $user_trailing = Db::name('user_trailing')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
        $user_trailing_id = '';
        foreach ($user_trailing as $k => $v) {
            $user_trailing_id .= $v['tory_id'] . ",";
        }
        $user_trailing_id = substr($user_trailing_id, 0, -1);

        //查询有权限的圈子
        $q_tory = Db::name('territory')->whereNotIn('id', $user_trailing_id)->where('status', 1)->where('attention', 1)->where('much_id', $data['much_id'])->select();
        $q_tory_id = '';
        foreach ($q_tory as $k => $v) {
            $q_tory_id .= $v['id'] . ",";
        }
        $q_tory_id = substr($q_tory_id, 0, -1);

        if ($cg == 0) {
            $where['t.id'] = array('not in', $q_tory_id);
        }
        //return $this->json_rewrite($where_not_in);
        //查询置顶的帖子
//        $home = Db::name('home_topping')->where('much_id', $data['much_id'])->select();
//        $home_id = '';
//        foreach ($home as $k => $v) {
//            $home_id .= $v['paper_id'] . ",";
//        }

        $order = 'p.adapter_time';
        if ($data['order_time'] == 'huifu') {
            $order = 'huifu_time';
        } else {
            $order = 'p.adapter_time';
        }

        //$home_id = substr($home_id, 0, -1);
        $list = Db::name('paper')
            ->alias('p')
            ->join('user u', 'p.user_id=u.id')
            ->join('territory t', 'p.tory_id=t.id')
            ->join('paper_reply r', 'r.paper_id=p.id', 'LEFT')
            ->where('p.whether_delete', '0')
            ->where('p.study_status', '1')
            ->where('p.much_id', $data['much_id'])
            ->where('t.status', 1)
            ->where('p.tg_id', $data['id'])
            ->where($where)
            //->whereNotIn('p.id', $home_id)
            ->field('p.*,u.level,u.gender,u.wear_merit,u.user_nick_name,u.user_head_sculpture,t.realm_name,t.realm_icon,u.user_wechat_open_id,max(r.apter_time) as huifu_time')
            ->order($order, 'desc')
            ->group('p.id')
            ->page($page, '12')
            ->select();
        //return $this->json_rewrite($list);
        if ($list) {
            foreach ($list as $k => $v) {
                $util = new Util();
                if (isset($data['tory_id'])) {//判断是否是 圈主或者管理员

                    $list[$k]['check_qq'] = $util->check_qq($v['user_wechat_open_id'], $v['tory_id']);
                }
                $list[$k]['user_wechat_open_id'] = null;
                if (isset($v['huifu_time'])) {//判断是否是 圈主或者管理员
                    $list[$k]['huifu_time'] = formatTime($v['huifu_time']);
                }

                //计算全部回复人数
                $huifu_all_count = Db::name('paper_reply')->where('reply_status', 1)->where('paper_id', $v['id'])->where('whether_delete=0')->where('much_id', $data['much_id'])->count();

                $huifu_hui_count = Db::name('paper_reply')->alias('p')
                    ->join('paper_reply_duplex r', 'r.reply_id=p.id')
                    ->where('p.whether_delete=0')
                    ->where('p.much_id', $data['much_id'])
                    ->where('p.paper_id', $v['id'])
                    ->count();

                $list[$k]['is_voice'] = false;
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['study_title'] = emoji_decode($v['study_title']);


                $modd = '/<div class="stealth_module".*>([\s\S]*)<\/div>/i';

                preg_match_all($modd, emoji_decode($v['study_content']), $moddUrl);

                if (count($moddUrl[1]) > 0) {
                    for ($i = 0; $i < count($moddUrl[1]); $i++) {
                        $moddHtml = "<div></div>";
                        $v['study_content'] = preg_replace($modd, $moddHtml, emoji_decode($v['study_content']), 1);
                    }
                } else {
                    if ($v['is_buy'] == 1) {
                        $v['study_content'] = "";
                    } else {
                        $v['study_content'] = emoji_decode($v['study_content']);
                    }
                }


                $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
                preg_match_all($preg, emoji_decode($v['study_content']), $match);

                //$list[$k]['mmmmmmatch'] = $v['study_content'];
                if (!empty(json_decode($v['image_part'], true))) {
                    //$list[$k]['image_part_1'] = '1';
                    $list[$k]['image_part'] = json_decode($v['image_part']);
                } else {
                    //$list[$k]['image_part_1'] = '0';
                    //$list[$k]['image_part_2'] = $match;
                    if (!empty($match[0])) {
                        $list[$k]['image_part'] = $match[1];
                    } else {
                        $list[$k]['image_part'] = [];
                    }
                }
                if ($data['pattern'] == 'concise') {
                    $list[$k]['image_part'] = [];
                }

                //$ling = count(json_decode($list[$k]['image_part']));
                $list[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));
                $list[$k]['study_heat'] = formatNumber($v['study_heat']);
                $list[$k]['study_laud'] = formatNumber($v['study_laud']);
                $list[$k]['study_repount'] = formatNumber($huifu_hui_count + $huifu_all_count);
                $list[$k]['user_vip'] = $util->get_user_vip($v['user_id']);
                $list[$k]['adapter_time'] = formatTime($v['adapter_time']);

                $list[$k]['study_voice'] = link_urldecode($v['study_voice']);
                $list[$k]['study_voice_time'] = s_to_hs($v['study_voice_time']);
                $list[$k]['starttime'] = '00:00';
                $list[$k]['offset'] = 0;
                $list[$k]['max'] = $v['study_voice_time'];

                $sc = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
                $list[$k]['is_info_zan'] = $sc == 0 ? false : true;
                //$count = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('paper_id', $v['id'])-
                $count = $v['study_laud'];
                $list[$k]['info_zan_count'] = formatNumber($count);
                $list[$k]['info_zan_count_this'] = $count;
                //查询当前帖子是否是红包贴
                $red = Db::name('paper_red_packet')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                //查询是否购买了当前帖子
                $purchase = Db::name('paper_buy_user')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                $list[$k]['purchase'] = $purchase;
                $list[$k]['red'] = $red;
                //获取当前话题
                if ($v['tg_id'] != 0) {
                    $gambit = Db::name('gambit')->where('much_id', $data['much_id'])->where('id', $data['id'])->find();
                    //$list[$k]['gambit_name'] = emoji_decode($gambit['gambit_name']);
                    $list[$k]['gambit_name'] = emoji_decode(str_replace('#', '', $gambit['gambit_name']));
                    $rs['gambit_name'] = emoji_decode($gambit['gambit_name']);
                    $list[$k]['gambit_id'] = $gambit['id'];
                }
                $list[$k]['level'] = $util->get_user_level($v['level'], $data['much_id'])['level_icon'];
                //当前用户的勋章
                $list[$k]['wear_merit'] = $util->get_medal($v['wear_merit'], $data['much_id']);
                //当前用户昵称
                $list[$k]['special'] = $util->get_user_special_nickname($v['user_id'], $data['much_id']);
                $list[$k]['avatar_frame'] = $util->get_user_avatar_frame($v['user_id'], $data['much_id']);
                //是否是投票贴
                $list[$k]['vo_id'] = [];
                //查询我是否投票了
                $list[$k]['is_vo_check'] = Db::name('user_vote')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                if ($list[$k]['is_vo_check'] > 0) {
                    $my_vo = Db::name('user_vote')->where('paper_id', $v['id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
                    foreach ($my_vo as $c => $d) {
                        array_push($list[$k]['vo_id'], intval($d['pv_id']));
                    }
                }
                $pvInfo = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->select();
                $pvInfoSum = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->sum('cheat_ballot');
                $pvPeopleCount = Db::name('user_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                $pvPeopleCountTotal = ($pvInfoSum + $pvPeopleCount);
                $list[$k]['vo_count'] = $pvPeopleCountTotal;
                for ($i = 0; $i < count($pvInfo); $i++) {
                    $voters = Db::name('user_vote')->where('paper_id', $v['id'])->where('pv_id', $pvInfo[$i]['id'])->where('much_id', $data['much_id'])->count();
                    $pvInfo[$i]['voters'] = (intval($pvInfo[$i]['cheat_ballot']) + $voters);
                    $ratio = (($pvInfo[$i]['voters'] / $pvPeopleCountTotal) * 100);
                    if (is_nan($ratio)) {
                        $pvInfo[$i]['ratio'] = 0;
                    } else {
                        $pvInfo[$i]['ratio'] = $ratio;
                    }
                }
                $list[$k]['vo'] = $pvInfo;
                //判断截止时间
                if ($v['vote_deadline'] != 0) {
                    if ($v['vote_deadline'] > time()) {
                        $list[$k]['vote_deadline'] = date('Y-m-d H:i', $v['vote_deadline']);
                    } else {
                        $list[$k]['vote_deadline'] = -1;//已截止
                        $list[$k]['is_vo_check'] = 1;
                    }
                }
                //判断是否有身份铭牌
                if (intval($v['uccid']) != 0) {
                    //查询佩戴的身份
                    $card_info = Db::name('camouflage_card')->where('id', $v['uccid'])->where('much_id', $data['much_id'])->find();
                    $user_card = Db::name('user_camouflage_card')->where('ccid', $v['uccid'])->where('user_id', $v['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                    $list[$k]['user_head_sculpture'] = $card_info['forgery_head'];
                    $list[$k]['user_id'] = 0;
                    //$list[$k]['user_nick_name']=$card_info['forgery_name'].'-'.$user_card['id'];
                    $list[$k]['user_nick_name'] = $card_info['forgery_name'];
                    $list[$k]['attr'] = '';
                } else {
                    $attr = $util->get_att_info($v['user_id'], $data['much_id']);
                    $list[$k]['attr'] = $attr;
                }
            }

            $rs['info'] = $list;
        } else {
            $rs['info'] = [];
        }
        //获取当前话题名称
        $g = Db::name('gambit')->where('much_id', $data['much_id'])->where('id', $data['id'])->where('is_del', 0)->find();
        $rs['gambit_name'] = emoji_decode(str_replace('#', '', $g['gambit_name']));
        $rs['version'] = $this->version;
        return $this->json_rewrite($rs);
    }

    /**
     * 增加留言
     */
    public function add_private()
    {
        $data = input('param.');
        //是否开启网络验证
        $offend = Db::name('user_violation')->where('much_id', $data['much_id'])->find();

        if ($this->user_info['tourist'] == 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '请登陆后发送消息！']);
        }
        if (trim($data['get_text']) == '') {
            return $this->json_rewrite(['status' => 'error', 'msg' => '发送内容为空！']);
        }
        $util = new Util();
        if ($offend['open_network_content_offend'] == 1) {
            $check_title = $util->get_check_msg($data['get_text'], $data['much_id'], $data['openid']);
            if ($check_title['status'] == 'error') {
                return $this->json_rewrite($check_title);
            }
        }
        //查询拉黑
        $check_hei = Db::name('user_blacklist')->where('much_id', $data['much_id'])->where('da_user_id', $this->user_info['id'])->where('pu_user_id', $data['re_user_id'])->find();
        $check_hei_to = Db::name('user_blacklist')->where('much_id', $data['much_id'])->where('da_user_id', $data['re_user_id'])->where('pu_user_id', $this->user_info['id'])->find();
        if ($check_hei) {
            $rs = ['status' => 'error', 'msg' => '发送失败，您已把对方加入黑名单，请移除后再发送！'];
            return $this->json_rewrite($rs);
        }
        if ($check_hei_to) {
            $rs = ['status' => 'error', 'msg' => '发送失败，对方已把你加入黑名单！'];
            return $this->json_rewrite($rs);
        }

        $dd['re_user_id'] = $data['re_user_id'];
        $dd['se_user_id'] = $this->user_info['id'];
        $dd['le_content'] = emoji_encode($data['get_text']);
        $dd['le_time'] = time();
        $dd['le_type'] = 0;
        $dd['much_id'] = $data['much_id'];
        $dd['le_read_status'] = 0;
        // 启动事务
        Db::startTrans();
        try {
            $rs = Db::name('user_leave_word')->insertGetId($dd);
            if (!$rs) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '发送失败！']);
            }
            $check = Db::name('user_recent_contacts')->where('chat_user_id', $this->user_info['id'])->where('recent_user_id', $data['re_user_id'])->where('much_id', $data['much_id'])->find();
            if (empty($check)) {
                $rsd = Db::name('user_recent_contacts')->insert(['chat_user_id' => $this->user_info['id'], 'recent_user_id' => $data['re_user_id'], 'blatter_time' => time(), 'last_msg' => $dd['le_content'], 'much_id' => $data['much_id']]);
                if (!$rsd) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '发送失败！']);
                }
            } else {
                $rsd = Db::name('user_recent_contacts')->where('id', $check['id'])->update(['blatter_del' => 0, 'blatter_time' => time(), 'last_msg' => $dd['le_content']]);
                if (!$rsd) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '发送失败！']);
                }
            }


            $check_shou = Db::name('user_recent_contacts')->where('recent_user_id', $this->user_info['id'])->where('chat_user_id', $data['re_user_id'])->where('much_id', $data['much_id'])->find();
            if (empty($check_shou)) {
                $rss = Db::name('user_recent_contacts')->insert(['recent_user_id' => $this->user_info['id'], 'chat_user_id' => $data['re_user_id'], 'blatter_time' => time(), 'last_msg' => $dd['le_content'], 'much_id' => $data['much_id']]);
                if (!$rss) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '发送失败！']);
                }
            } else {
                $rss = Db::name('user_recent_contacts')->where('id', $check_shou['id'])->update(['blatter_del' => 0, 'blatter_time' => time(), 'last_msg' => $dd['le_content']]);
                if (!$rss) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '发送失败！']);
                }
            }
            //发送模版
            $util->add_template(['much_id' => $data['much_id'],
                'at_id' => 'YL0006',
                'user_id' => $dd['re_user_id'],
                'page' => 'yl_welore/pages/packageB/private_letter/index?id=' . $this->user_info['id'],
                'keyword1' => emoji_decode($dd['le_content']),
                'keyword2' => date('Y年m月d日 H:i', time()),
                'keyword3' => emoji_decode($this->user_info['user_nick_name']),
            ]);
            $user = Db::name('user')->where('id', $data['re_user_id'])->where('much_id', $data['much_id'])->find();
            $info['le_content'] = $dd['le_content'];
            $info['se_user_head'] = $this->user_info['user_head_sculpture'];
            $info['re_user_head'] = $user['user_head_sculpture'];
            $info['se_user_id'] = $this->user_info['id'];
            $info['re_user_id'] = $user['id'];
            $info['id'] = $rs;
            $info['le_time'] = date('m-d H:i:s', $dd['le_time']);
            $info['le_type'] = 0;
            // 提交事务
            Db::commit();
            return $this->json_rewrite(['status' => 'success', 'msg' => '成功！', 'info' => $info]);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'msg' => '失败！']);
        }
    }

    /**
     * 列表
     */
    public function get_private_list()
    {
        $data = input('param.');
        $where = [];
        if ($data['top_id'] != 0) {
            $where['id'] = ['<', $data['top_id']];
        }

        //发送用户
        $seUserId = $this->user_info['id'];
        //接收用户
        $reUserId = $data['re_user_id'];

        //查询数据
        $list = Db::name('user_leave_word')
            ->where(function ($query) use ($seUserId, $reUserId) {
                $query->where('se_user_id', $seUserId)->whereOr('se_user_id', $reUserId);
            })
            ->where(function ($query) use ($seUserId, $reUserId) {
                $query->where('re_user_id', $seUserId)->whereOr('re_user_id', $reUserId);
            })
            ->where(function ($query) use ($seUserId) {
                $query->where(function ($querys) use ($seUserId) {
                    $querys->where('se_user_id', $seUserId)->where('se_user_del', 0);
                })->whereOr(function ($querys) use ($seUserId) {
                    $querys->where('re_user_id', $seUserId)->where('re_user_del', 0);
                });
            })
            ->where($where)
            ->where('much_id', $data['much_id'])
            ->order('id desc')
            ->select();


        if ($data['top_id'] != 0) {
            $sortKey = array_column($list, 'id');
            array_multisort($sortKey, SORT_DESC, $list);
        }


        $user = Db::name('user')->where('id', $data['re_user_id'])->where('much_id', $data['much_id'])->field('id,user_head_sculpture,user_nick_name')->find();
        $id = 0;
        if (!empty($list)) {
            foreach ($list as $k => $v) {
                if ($v['le_type'] == 1) {
                    $list[$k]['le_content'] = json_decode($v['le_content'], true);
                } else {
                    $list[$k]['le_content'] = emoji_decode($v['le_content']);
                }
                $list[$k]['le_content'] = Alternative::ExpressionHtml($list[$k]['le_content']);
                $list[$k]['se_user_head'] = $this->user_info['user_head_sculpture'];
                $list[$k]['re_user_head'] = $user['user_head_sculpture'];
                $list[$k]['le_time'] = date('m-d H:i:s', $v['le_time']);
                if ($v['le_read_status'] == 0 && $v['re_user_id'] == $seUserId) {
                    Db::name('user_leave_word')->where('id', $v['id'])->update(['le_read_status' => 1]);
                }
            }
            $id = $list[count($list) - 1]['id'];
        }
        $user['user_nick_name'] = emoji_decode($user['user_nick_name']);
        return $this->json_rewrite(['status' => 'success', 'info' => $list, 'id' => $id, 'user' => empty($user) ? [] : $user]);
    }

    /**
     * 删除
     */
    public function del_private()
    {
        $data = input('param.');
        $info = Db::name('user_leave_word')
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->find();
        if ($this->user_info['id'] == $info['se_user_id']) {
            $d['se_user_del'] = 1;
        } else {
            $d['re_user_del'] = 1;
        }
        $up = Db::name('user_leave_word')
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->update($d);
        if ($up) {
            return $this->json_rewrite(['status' => 'success', 'msg' => '成功！']);
        } else {
            return $this->json_rewrite(['status' => 'error', 'msg' => '失败！']);
        }
    }

    /**
     * 站内信消息
     */
    public function get_my_private()
    {
        $data = input('param.');
        //我发送的
        $rs = Db::name('user_recent_contacts')
            ->where('much_id', $data['much_id'])
            ->where('chat_user_id', $this->user_info['id'])
            ->order('blatter_time desc')
            ->page($data['page'], '25')
            ->where('blatter_del', 0)
            ->select();
        if (!empty($rs)) {
            $sum = 0;
            foreach ($rs as $k => $v) {
                $user = Db::name('user')->where('id', $v['recent_user_id'])->where('much_id', $data['much_id'])->find();
                $rs[$k]['last_msg'] = emoji_decode($v['last_msg']);
                $rs[$k]['re_user_head'] = $user['user_head_sculpture'];
                $rs[$k]['blatter_time'] = date('m-d H:i', $v['blatter_time']);
                $rs[$k]['user_nick_name'] = emoji_decode($user['user_nick_name']);
                //查询当前用户多少未读
                $rs[$k]['msg_count'] = Db::name('user_leave_word')->where('re_user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->where('se_user_id', $v['recent_user_id'])->where('le_read_status', 0)->count();
                $sum += $rs[$k]['msg_count'];
            }

        }
        return $this->json_rewrite(['status' => 'success', 'info' => $rs, 'sum' => $sum]);
    }

    /**
     *  获取未读消息
     */
    public function get_overall_situation()
    {
        $data = input('param.');
        $msg_count = Db::name('user_leave_word')->where('re_user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->where('le_read_status', 0)->count();
        $user_male = Db::name('user_smail')->where('user_id', $this->user_info['id'])->where('status', 0)->count();
        $msg_count+=$user_male;
        return $this->json_rewrite(['count' => ($msg_count>99?'99+':$msg_count)]);
    }
    public function overall_set(){
        $data = input('param.');
        $user_male = Db::name('user_smail')->where('user_id', $this->user_info['id'])->find();
        if($user_male['user_id']!=$this->user_info['id']){
            return $this->json_rewrite(['status' => 'error', 'msg' => '您没有权限！',]);
        }
        Db::name('user_smail')->where('id', $data['id'])->update(['status'=>1]);
        return $this->json_rewrite(['status' => 'success', 'msg' => '成功']);
    }

    /**
     * 删除最近联系人
     */
    public function del_my_contacts()
    {
        $data = input('param.');
        $res = Db::name('user_recent_contacts')->where('id', $data['id'])->update(['blatter_del' => 1]);
        if ($res) {
            return $this->json_rewrite(['status' => 'success', 'msg' => '成功！']);
        } else {
            return $this->json_rewrite(['status' => 'error', 'msg' => '失败！']);
        }
    }

    /**
     * 拉黑
     */
    public function lahei_do()
    {
        $data = input('param.');
        $d['da_user_id'] = $this->user_info['id'];
        $d['pu_user_id'] = $data['id'];
        $d['bl_time'] = time();
        $d['much_id'] = $data['much_id'];

        $check = Db::name('user_blacklist')->where('da_user_id', $this->user_info['id'])->where('pu_user_id', $data['id'])->where('much_id', $data['much_id'])->find();
        if (empty($check)) {
            $res = Db::name('user_blacklist')->insert($d);
            if ($res) {
                return $this->json_rewrite(['status' => 'success', 'msg' => '拉黑成功！']);
            } else {
                return $this->json_rewrite(['status' => 'error', 'msg' => '拉黑失败！']);
            }
        } else {
            return $this->json_rewrite(['status' => 'success', 'msg' => '该用户已经在黑名单！']);
        }

    }

    /**
     * 举报
     */
    public function add_jubao()
    {
        $data = input('param.');
        if (empty($data['ment_caption'])) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '内容不能为空！']);
        }
        if ($data['id'] == $this->user_info['id']) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '自己举报自己也没谁了！']);
        }
        $d['proof_id'] = $this->user_info['id'];
        $d['mopt_id'] = 10;
        $d['ment_type'] = 2;
        $d['tory_id'] = NULL;
        $d['labor'] = NULL;
        $d['user_id'] = $data['id'];
        $d['ment_caption'] = emoji_encode($data['ment_caption']);
        $d['status'] = 0;
        $d['ment_time'] = time();
        $d['much_id'] = $data['much_id'];
        $check = Db::name('lament')->where('ment_type', 2)->where('proof_id', $this->user_info['id'])->where('user_id', $data['id'])->where('much_id', $data['much_id'])->find();
        if ($check) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '您已举报过该用户！']);
        } else {
            $res = Db::name('lament')->insert($d);
            if ($res) {
                return $this->json_rewrite(['status' => 'success', 'msg' => '举报成功！']);
            } else {
                return $this->json_rewrite(['status' => 'error', 'msg' => '举报失败！']);
            }
        }
    }

    /**
     * 黑名单
     */
    public function get_black_list()
    {
        $data = input('param.');
        $list = Db::name('user_blacklist')->alias('b')
            ->join('user u', 'u.id=b.pu_user_id')
            ->where('b.da_user_id', $this->user_info['id'])
            ->where('b.much_id', $data['much_id'])
            ->field('b.*,u.user_nick_name,u.user_head_sculpture')
            ->select();
        foreach ($list as $k => $v) {
            $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
            $list[$k]['bl_time'] = date('Y-m-d H:i', $v['bl_time']);
        }
        return $this->json_rewrite($list);
    }

    /**
     * 移除黑名单
     */
    public function del_black()
    {
        $data = input('param.');
        $check = Db::name('user_blacklist')->where('da_user_id', $this->user_info['id'])->where('pu_user_id', $data['pu_user_id'])->where('much_id', $data['much_id'])->find();
        if (empty($check)) {
            return $this->json_rewrite(['status' => 'success', 'msg' => '移除成功！']);
        } else {
            $del = Db::name('user_blacklist')->where('da_user_id', $this->user_info['id'])->where('pu_user_id', $data['pu_user_id'])->where('much_id', $data['much_id'])->delete();
            if ($del) {
                return $this->json_rewrite(['status' => 'success', 'msg' => '移除成功！']);
            } else {
                return $this->json_rewrite(['status' => 'error', 'msg' => '移除失败！']);
            }
        }
    }

    /**
     * 获取最新一条私信数据
     */
    public function get_new_message()
    {
        $data = input('param.');
        //查询数据
        //发送用户
        $seUserId = $this->user_info['id'];
        //接收用户
        $reUserId = $data['id'];

        $list = Db::name('user_leave_word')
            ->where(function ($query) use ($seUserId, $reUserId) {
                $query->where('se_user_id', $seUserId)->whereOr('se_user_id', $reUserId);
            })
            ->where(function ($query) use ($seUserId, $reUserId) {
                $query->where('re_user_id', $seUserId)->whereOr('re_user_id', $reUserId);
            })
            ->where(function ($query) use ($seUserId) {
                $query->where(function ($querys) use ($seUserId) {
                    $querys->where('se_user_id', $seUserId)->where('se_user_del', 0);
                })->whereOr(function ($querys) use ($seUserId) {
                    $querys->where('re_user_id', $seUserId)->where('re_user_del', 0);
                });
            })
            ->where('much_id', $data['much_id'])
            ->where('id', '>', $data['last_id'])
            //->fetchSql()
            ->select();
        //dump($list);exit();

        foreach ($list as $k => $v) {
            $se_user_head = Db::name('user')->where('id', $v['se_user_id'])->where('much_id', $data['much_id'])->find();
            if ($v['le_type'] == 1) {
                $list[$k]['le_content'] = json_decode($v['le_content'], true);
            } else {
                $list[$k]['le_content'] = emoji_decode($v['le_content']);
            }
            $list[$k]['le_content'] = Alternative::ExpressionHtml($list[$k]['le_content']);
            $list[$k]['re_user_head'] = $se_user_head['user_head_sculpture'];
            $list[$k]['se_user_head'] = $this->user_info['user_head_sculpture'];
            $list[$k]['le_time'] = date('m-d H:i:s', $v['le_time']);
            //更新已读
            if ($v['le_read_status'] == 0 && $v['re_user_id'] == $seUserId) {
                Db::name('user_leave_word')->where('id', $v['id'])->update(['le_read_status' => 1]);
            }

        }
        return $this->json_rewrite($list);
    }

}