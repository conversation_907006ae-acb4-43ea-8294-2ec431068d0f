<?php

namespace app\api\controller;


use app\api\service\Alternative;
use app\api\service\Util;
use think\Controller;
use think\Db;
use think\Log;

class Notify extends Controller
{
    public function get_notify()
    {
        $notify_data = input('param.');
        //file_put_contents("test.txt", $notify_data['attach'], FILE_APPEND);
        $user_serial = Db::name('user_serial')->where('single_mark', $notify_data['out_trade_no'])->find();
        $user_info = Db::name('user')->where('id', $user_serial['user_id'])->find();
        if ($user_serial['status'] == 1) {
            exit("<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>");
        }
        if ($notify_data['attach'] == 0) {
            $data['serial_id'] = $user_serial['id'];
            $data['user_id'] = $user_serial['user_id'];
            $data['category'] = 0;
            $data['finance'] = $user_serial['money'];
            $data['ruins_time'] = time();
            $data['much_id'] = $user_serial['much_id'];
            $design = Db::name('design')->where('much_id', $user_serial['much_id'])->find();
            $data['solution'] = '充值' . $design['currency'];
            $data['poem_fraction'] = $user_info['fraction'];//原始积分
            $data['poem_conch'] = $user_info['conch'];//原始贝壳
            $data['surplus_fraction'] = $user_info['fraction'];//原始积分
            $data['evaluate'] = 0;//贝壳
            $data['surplus_conch'] = bcadd($user_serial['money'], $user_info['conch'], 2);//落实贝壳
            Db::startTrans();
            try {
                $up = Db::name('user_serial')->where('single_mark', $notify_data['out_trade_no'])->update(['status' => 1, 'pay_money' => $notify_data['cash_fee'] / 100]);
                if (!$up) {
                    Db::rollback();
                }
                $inc = Db::name('user_amount')->insert($data);
                if (!$inc) {
                    Db::rollback();
                }
                $bei = Db::name('user')->where('id', $user_serial['user_id'])->setInc('conch', $user_serial['money']);
                if (!$bei) {
                    Db::rollback();
                }
                Db::commit();
            } catch (\Exception $e) {
                Log::write($e->getMessage(), 'pay_exc');
                Db::rollback();
            }

        }
        if ($notify_data['attach'] == 1) {
            Db::name('user_serial')->where('single_mark', $notify_data['out_trade_no'])->update(['status' => 1, 'pay_money' => $notify_data['cash_fee'] / 100]);
        }
        if ($notify_data['attach'] == 2) {
            Db::name('user_serial')->where('single_mark', $notify_data['out_trade_no'])->update(['status' => 1, 'pay_money' => $notify_data['cash_fee'] / 100]);
            $order_info = Db::name('shop_order')
                ->where('order_number', $notify_data['out_trade_no'])
                ->find();
            $good_info = Db::name('shop')
                ->where('id', $order_info['product_id'])
                ->find();
            //查询自动发货
            if ($good_info['auto_delivery'] == 1) {
                //查询卡密
                $card = Db::name('mucilage')
                    ->where('is_sell', 0)
                    ->where('is_use', 0)
                    ->where('status', 1)
                    ->where('is_del', 0)
                    ->where('card_type', 1)
                    ->where('shop_id', $good_info['id'])
                    ->find();
                //更新卡密状态
                Db::name('mucilage')->where('id', $card['id'])->update(['is_sell' => 1]);
                //更新订单状态
                $order['shipment'] = $card['card_code'];
                //发货状态
                $order['status'] = 1;
                //发货时间
                $order['ship_time'] = time();
            }
            $order['pay_status'] = 1;
            Db::name('shop_order')->where('order_number', $notify_data['out_trade_no'])->update($order);
            $data['serial_id'] = $notify_data['out_trade_no'];
            $data['user_id'] = $user_serial['user_id'];
            $data['category'] = 2;
            $data['finance'] = -$order_info['actual_price'];
            $data['ruins_time'] = time();
            $data['much_id'] = $user_serial['much_id'];
            $data['solution'] = '购买商品：' . $order_info['product_name'];
            $data['poem_fraction'] = $user_info['fraction'];//原始积分
            $data['surplus_fraction'] = $user_info['fraction'];//原始积分
            $data['poem_conch'] = $user_info['conch'];//原始贝壳
            $data['surplus_conch'] = $user_info['conch'];//落实贝壳
            $data['evaluate'] = 2;//微信
            Db::name('user_amount')->insert($data);
            //检测sku
            $sku = Alternative::GetShopSku($good_info['id'], $good_info['much_id']);
            //物品库存减少
            if (!$sku) {
                Db::name('shop')
                    ->where('id', $order_info['product_id'])
                    ->setDec('product_inventory');
            } else {
                $sku_info = json_decode($order_info['vested_attribute'], true);
                foreach ($sku['list'] as $k => $v) {
                    if ($v['at_name'] == $sku_info[1]) {
                        $sku['list'][$k]['inventory_count'] = bcsub($v['inventory_count'], 1, 0);
                    }
                }
                Db::name('shop_vested')->where('id', $sku['id'])->update(['sa_list' => json_encode($sku['list'], JSON_UNESCAPED_UNICODE)]);
            }
            //如果是商家订单
            if ($order_info['is_offline'] == 1) {
                $util = new Util();
                $products = Db::name('easy_info_shop_products')->where('product_id', $order_info['product_id'])->find();
                if ($products) {
                    $easy_info = Db::name('easy_info_list')->where('id', $products['eil_id'])->find();
                    $easy_data['user_id'] = $user_info['id'];
                    $easy_data['product_id'] = $products['product_id'];
                    $easy_data['eil_id'] = $easy_info['id'];
                    $easy_data['so_id'] = $order_info['id'];
                    $easy_data['redemption_code'] = $util->create_uuid();
                    $easy_data['use_status'] = 0;
                    $easy_data['order_status'] = 1;
                    $easy_data['create_time'] = time();
                    $easy_data['much_id'] = $products['much_id'];
                    Db::name('easy_info_shop_order')->insertGetId($easy_data);
                }
            }
        }
        if ($notify_data['attach'] == 3) {
            Db::name('user_serial')->where('single_mark', $notify_data['out_trade_no'])->update(['status' => 1, 'pay_money' => $notify_data['cash_fee'] / 100]);
            //更新
            $info = Db::name('lost_item_top')->where('usl_id', $user_serial['id'])->find();
            //更新置顶
            $lost_info = Db::name('lost_item')->where('id', $info['li_id'])->find();
            //更新置顶
            if (bccomp(time(), $lost_info['top_time']) == 1) {
                $time = bcadd(bcmul($info['top_day'], 86400), time());
            } else {
                $time = bcadd(bcmul($info['top_day'], 86400), $lost_info['top_time']);
            }
            Db::name('lost_item')->where('id', $info['li_id'])->update(['top_time' => $time]);
            Db::name('lost_item_top')->where('usl_id', $user_serial['id'])->update(['is_pay' => 1]);
        }
        if ($notify_data['attach'] == 4) {
            Db::name('user_serial')->where('single_mark', $notify_data['out_trade_no'])->update(['status' => 1, 'pay_money' => $notify_data['cash_fee'] / 100]);
            //更新
            $info = Db::name('used_goods_item_top')->where('usl_id', $user_serial['id'])->find();
            //更新置顶
            $lost_info = Db::name('used_goods_item')->where('id', $info['ugi_id'])->find();
            //更新置顶
            if (bccomp(time(), $lost_info['top_time']) == 1) {
                $time = bcadd(bcmul($info['top_day'], 86400), time());
            } else {
                $time = bcadd(bcmul($info['top_day'], 86400), $lost_info['top_time']);
            }
            Db::name('used_goods_item')->where('id', $info['ugi_id'])->update(['top_time' => $time]);
            Db::name('used_goods_item_top')->where('usl_id', $user_serial['id'])->update(['is_pay' => 1]);
        }
        if ($notify_data['attach'] == 5) {
            Db::name('user_serial')->where('single_mark', $notify_data['out_trade_no'])->update(['status' => 1, 'pay_money' => $notify_data['cash_fee'] / 100]);
            //更新
            $info = Db::name('employment_item_top')->where('ei_id', $user_serial['id'])->find();
            //更新置顶
            $lost_info = Db::name('employment_item')->where('id', $info['ugi_id'])->find();
            //更新置顶
            if (bccomp(time(), $lost_info['top_time']) == 1) {
                $time = bcadd(bcmul($info['top_day'], 86400), time());
            } else {
                $time = bcadd(bcmul($info['top_day'], 86400), $lost_info['top_time']);
            }
            Db::name('employment_item')->where('id', $info['ugi_id'])->update(['top_time' => $time]);
            Db::name('employment_item_top')->where('ei_id', $user_serial['id'])->update(['is_pay' => 1]);
        }
        //file_put_contents("test1.txt", $notify_data);
        Log::write($notify_data['out_trade_no'] . ',支付状态更改成功', 'shop_pay_state_change_success');
        exit("<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>");
    }
}