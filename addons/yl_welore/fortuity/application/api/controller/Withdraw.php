<?php

namespace app\api\controller;


use app\api\service\Util;
use app\api\service\WxChatPayUtil;
use app\api\service\WxCompany;
use think\Cache;
use think\Db;

class Withdraw extends Base
{
    /**
     * 提现
     */
    public function withdraw()
    {
        $data = input('param.');

        //获取用户信息
        $user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        //获取提现信息
        $setting = Db::name('raws_setting')->where('much_id', $data['much_id'])->find();
        if ($setting['open_offline_payment'] == 1) {
            if (empty($setting['payment_tariff'])) {
                $rs = ['status' => 'error', 'msg' => '请输入正确的支付宝帐号！'];
                return $this->json_rewrite($rs);
            }
        }

        if (!is_numeric($data['withdraw_money'])) {
            $rs = ['status' => 'error', 'msg' => '请输入正确的金额！'];
            return $this->json_rewrite($rs);
        }

        //计算提现金额
        $moeny = sprintf("%.2f", $data['withdraw_money'] - ($data['withdraw_money'] * $setting['payment_tariff']));
        if ($setting['lowest_money'] > $data['withdraw_money']) {
            $rs = ['status' => 'error', 'msg' => '金额不满足最低提现金额！'];
            return $this->json_rewrite($rs);
        }
        if ($data['withdraw_money'] > $user_info['conch']) {
            $rs = ['status' => 'error', 'msg' => '用户余额不足！'];
            return $this->json_rewrite($rs);
        }
        $apiclientCertWe = EXTEND_PATH . 'Wxpay' . DS . 'Cert' . DS . 'wxp_cert' . $data['much_id'] . '.pem';
        $apiclientCert = EXTEND_PATH . 'Wxpay' . DS . 'Cert' . DS . 'apiclient_cert_' . $data['much_id'] . '.pem';
        $apiclientKey = EXTEND_PATH . 'Wxpay' . DS . 'Cert' . DS . 'apiclient_key_' . $data['much_id'] . '.pem';
        Db::startTrans();
        try {
            $data_withdraw = array();
            //插入提现表
            $data_withdraw['user_id'] = $this->user_info['id'];
            $data_withdraw['user_account'] = $data['withdraw_number'];
            $data_withdraw['display_money'] = $data['withdraw_money'];
            $data_withdraw['tariff'] = $setting['payment_tariff'];
            $data_withdraw['actual_amount'] = $moeny;
            $data_withdraw['withdraw_type'] = $setting['open_offline_payment'];
            $data_withdraw['much_id'] = $data['much_id'];
            if ($setting['auto_review_payment'] == 1) {
                $data_withdraw['seek_time'] = time();
                $data_withdraw['verify_time'] = time();
                $data_withdraw['status'] = 1;
            } else {
                $data_withdraw['seek_time'] = time();
                $data_withdraw['status'] = 0;
            }

            $withdraw_ins = Db::name('user_withdraw_money')->insertGetId($data_withdraw);
            if (!$withdraw_ins) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '提现失败，请稍候重试！'];
                return $this->json_rewrite($rs);
            }
            $amount = array();
            //货币明细表增加数据
            $amount['user_id'] = $this->user_info['id'];
            $amount['category'] = 1;

            $amount['finance'] = -$data['withdraw_money'];

            $amount['poem_conch'] = $user_info['conch'] - $data['withdraw_money'];
            $amount['surplus_conch'] = 0;

            $amount['poem_fraction'] = $user_info['fraction'];
            $amount['surplus_fraction'] = $user_info['fraction'];

            $amount['ruins_time'] = time();
            $amount['solution'] = '提现';
            $amount['evaluate'] = 0;
            $amount['much_id'] = $data['much_id'];
            $amount_ins = Db::name('user_amount')->insert($amount);
            if (!$amount_ins) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '提现失败，请稍候重试！'];
                return $this->json_rewrite($rs);
            }
            //用户减少贝壳
            $user_update = Db::name('user')
                ->where('id', $this->user_info['id'])
                ->where('much_id', $data['much_id'])->update(['conch' => $amount['poem_conch']]);
            if (!$user_update) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '提现失败，请稍候重试！'];
                return $this->json_rewrite($rs);
            }

            if ($setting['open_offline_payment'] == 0 && $setting['auto_review_payment'] == 1) {
                $wxcom = new WxCompany();
                $result = $wxcom->companyToPocket($user_info['user_wechat_open_id'], $moeny, '', '提现', '', $data['much_id']);
                @unlink($apiclientCert);        //删除证书
                @unlink($apiclientKey);
                @unlink($apiclientCertWe);
                if ($result['status'] == 0) {
                    $rs = ['status' => 'success', 'msg' => '提现成功！'];

                } else {
                    Db::rollback();
                    $rs = ['status' => 'error', 'msg' => $result['msg']];
                    return $this->json_rewrite($rs);
                }
            } else {
                Db::name('prompt_msg')->insert(['capriole' => 12, 'tyid' => 0, 'msg_time' => time(), 'type' => 0, 'retter' => '用户：' . $this->user_info['user_nick_name'] . '申请提现！', 'status' => 0, 'much_id' => $data['much_id']]);
                $notices = Db::name('prompt_msg')
                    ->where('status', 0)
                    ->where('type', 0)
                    ->where('much_id', $data['much_id'])
                    ->count('*');
                cache('notices_' . $data['much_id'], $notices);
                $rs = ['status' => 'success', 'msg' => '审核通过后，即可提现成功！'];
            }
            @unlink($apiclientCert);        //删除证书
            @unlink($apiclientKey);
            @unlink($apiclientCertWe);
            Db::commit();
            return $this->json_rewrite($rs);
        } catch (\Exception $e) {
            Db::rollback();
            @unlink($apiclientCert);        //删除证书
            @unlink($apiclientKey);
            @unlink($apiclientCertWe);
            $rs = ['status' => 'error', 'msg' => '提现失败，请稍候重试！' . $e->getMessage()];
            return $this->json_rewrite($rs);
        }
    }

    /**
     * 获取提现配置
     */
    public function get_raws_setting()
    {
        $data = input('param.');
        //获取提现信息
        $setting = Db::name('raws_setting')->where('much_id', $data['much_id'])->find();
        //获取用户信息
        $user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        $setting['user_info'] = $user_info;
        return $this->json_rewrite($setting);
    }

    /**
     * 获取提现列表
     */
    public function get_withdraw_list()
    {
        $data = input('param.');
        $list = Db::name('user_withdraw_money')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->page($data['page'], '20')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {
                if (!empty($v['seek_time'])) {
                    $list[$k]['seek_time'] = date('Y-m-d H:i:s', $v['seek_time']);
                }
                if (!empty($v['verify_time'])) {
                    $list[$k]['verify_time'] = date('Y-m-d H:i:s', $v['verify_time']);
                }
            }
        }
        return $this->json_rewrite($list);
    }

    /**
     * 获取礼物
     */
    public function get_liwu()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $list = Db::name('tribute')->where('status', 1)->where('much_id', $data['much_id'])->order('scores')->select();
        if (!$list) {
            $rs = ['status' => 'error', 'msg' => '获取礼物失败！'];
        } else {
            $rs['user_info'] = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
            $rs['info'] = $list;
        }
        return $this->json_rewrite($rs);
    }

    /**
     * 获取会员充值
     */
    public function get_user_honorary()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $list = Db::name('user_honorary')->where('much_id', $data['much_id'])->find();

        $user_info = Db::name('user')->where('id', $this->user_info['id'])->find();

        if (!$list) {
            $rs = ['status' => 'error', 'msg' => '获取会员价格失败！'];
        } else {
            if ($list['chop_type'] == 0) {
                if ($user_info['vip_end_time'] == 0) {
                    $dd[0] = array('hono_price' => sprintf("%.1f", $list['hono_price']), 'hono_name' => '1个月', 'time' => 1, 'first_discount' => $list['first_discount'], 'discount_scale' => $list['discount_scale'] * 10, 'avg' => sprintf("%.1f", $list['discount_scale'] * $list['hono_price']));
                } else {
                    $dd[0] = array('hono_price' => sprintf("%.1f", $list['hono_price']), 'hono_name' => '1个月', 'time' => 1, 'first_discount' => 0);
                }

                $dd[1] = array('hono_price' => sprintf("%.1f", $list['hono_price'] * 3), 'hono_name' => '3个月', 'time' => 3, 'first_discount' => 0);
                $dd[2] = array('hono_price' => sprintf("%.1f", $list['hono_price'] * 6), 'hono_name' => '6个月', 'time' => 6, 'first_discount' => 0);
                $dd[3] = array('hono_price' => sprintf("%.1f", $list['hono_price'] * 12), 'hono_name' => '12个月', 'time' => 12, 'first_discount' => 0);

            } else {
                $dd = json_decode($list['define_price'], true);
            }
            $rs['info'] = $dd;
            $rs['user_honorary'] = $list;
        }
        return $this->json_rewrite($rs);
    }


    /**
     * 给用户送礼
     */
    public function user_reward()
    {
        $data = input('param.');
        //查询收益详情
        $tribute_taxation = Db::name('tribute_taxation')->where('much_id', $data['much_id'])->find();
        //查询礼物详情
        $li_wu = Db::name('tribute')->where('id', $data['li_id'])->where('much_id', $data['much_id'])->find();
        //查询用户详情
        $user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        //查询用户贝壳是否满足赠送条件
        $conch = $data['num'] * $li_wu['tr_conch'];
        if ($user_info['conch'] < $conch) {
            $rs = ['status' => 'error', 'msg' => $this->design['currency'] . '不足，请兑换！'];
            return $this->json_rewrite($rs);
        }
        //查询当前兑换比例
        $fraction_scale = Db::name('user_punch_range')->where('much_id', $data['much_id'])->find();
        $scale = 10;
        if (!empty($fraction_scale['fraction_scale'])) {
            $scale = $fraction_scale['fraction_scale'];
        }
        $is['much_id'] = $data['much_id'];
        $is['exchange_rate'] = $scale;
        $is['con_user_id'] = $this->user_info['id'];
        $is['sel_user_id'] = $data['user_id'];
        $is['bute_name'] = $data['num'] . "个" . $li_wu['tr_name'];
        $is['bute_price'] = $li_wu['tr_conch'];
        $is['allow_scale'] = 1 - $tribute_taxation['taxing'];
        $is['bute_time'] = time();
        $fraction = $conch * $is['allow_scale'] * $scale;
        Db::startTrans();
        try {
            //赠送者明细
            $uid_amount = $this->add_user_amount($this->user_info['id'], 2, $conch, '赠送礼物', $data['much_id'], $is['allow_scale']);
            if (!$uid_amount) {
                $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！'];
                Db::rollback();
                return $this->json_rewrite($rs);
            }
            //获赠者明细
            $user_amount = $this->add_user_amount($data['user_id'], 3, $conch, '获赠礼物收益并扣除手续费', $data['much_id'], $is['allow_scale']);
            if (!$user_amount) {
                $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！'];
                Db::rollback();
                return $this->json_rewrite($rs);
            }
            //赠送礼物表增加数据
            $ins = Db::name('user_subsidy')->insert($is);
            if (!$ins) {
                $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！'];
                Db::rollback();
                return $this->json_rewrite($rs);
            }
            //减去赠送用户的贝壳
            $up = Db::name('user')->where('id', $this->user_info['id'])->setDec('conch', $conch);
            if (!$up) {
                $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！'];
                Db::rollback();
                return $this->json_rewrite($rs);
            }
            //增加获赠者的积分
            $up_user = Db::name('user')->where('id', $data['user_id'])->setInc('fraction', $fraction);
            if (!$up_user) {
                $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！'];
                Db::rollback();
                return $this->json_rewrite($rs);
            }
            //明细表增加数据
            Db::commit();
            $rs = ['status' => 'success', 'msg' => '赠送成功！'];
            return $this->json_rewrite($rs);
        } catch (\Exception $e) {
            Db::rollback();
            $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！'];
            return $this->json_rewrite($rs);
        }
    }

    /**
     * 给用户送礼
     */
    public function user_reward_new()
    {
        $data = input('param.');
        if ($data['user_id'] == $this->user_info['id']) {
            $rs = ['status' => 'error', 'msg' => '不能给自己送礼物！'];
            return $this->json_rewrite($rs);
        }
        //查询收益详情
        $tribute_taxation = Db::name('tribute_taxation')->where('much_id', $data['much_id'])->find();
        //查询礼物详情
        $li_wu = Db::name('tribute')->where('id', $data['li_id'])->where('much_id', $data['much_id'])->find();
        //查询用户详情
        $user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        $user_info_h = Db::name('user')->where('id', $data['user_id'])->where('much_id', $data['much_id'])->find();
        //查询用户贝壳是否满足赠送条件
        $conch = intval(abs($data['num'])) * $li_wu['tr_conch'];
        if ($user_info['conch'] < $conch) {
            $rs = ['status' => 'error', 'msg' => $this->design['currency'] . '不足，请兑换！'];
            return $this->json_rewrite($rs);
        }
        //查询当前兑换比例
        $fraction_scale = Db::name('user_punch_range')->where('much_id', $data['much_id'])->find();
        $scale = 10;
        if (!empty($fraction_scale['fraction_scale'])) {
            $scale = $fraction_scale['fraction_scale'];
        }
        $is['much_id'] = $data['much_id'];
        $is['exchange_rate'] = $scale;
        $is['con_user_id'] = $this->user_info['id'];
        $is['sel_user_id'] = $data['user_id'];
        $is['bute_name'] = intval(abs($data['num'])) . "个" . $li_wu['tr_name'];
        $is['bute_price'] = $li_wu['tr_conch'];
        $is['allow_scale'] = 1 - $tribute_taxation['taxing'];
        $is['bute_time'] = time();
        //$fraction = $conch * $is['allow_scale'];
        $user_b = bcsub($conch, bcmul($conch, $tribute_taxation['taxing'], 2), 2);

        Db::startTrans();
        try {
            //赠送者明细
            $uid_amount = $this->add_user_amount($this->user_info['id'], 2, $conch, '赠送礼物', $data['much_id'], $is['allow_scale']);
            if (!$uid_amount) {
                $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！1'];
                Db::rollback();
                return $this->json_rewrite($rs);
            }
            //获赠者明细
            $user_amount = $this->add_user_amount($data['user_id'], 4, $user_b, '获赠礼物收益并扣除手续费', $data['much_id'], $is['allow_scale']);
            if (!$user_amount) {
                $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！2'];
                Db::rollback();
                return $this->json_rewrite($rs);
            }
            //赠送礼物表增加数据
            $ins = Db::name('user_subsidy')->insert($is);
            if (!$ins) {
                $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！3'];
                Db::rollback();
                return $this->json_rewrite($rs);
            }
            //减去赠送用户的贝壳
            $up = Db::name('user')->where('id', $this->user_info['id'])->update(['conch' => bcsub($user_info['conch'], $conch, 2)]);
            if (!$up) {
                $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！4'];
                Db::rollback();
                return $this->json_rewrite($rs);
            }
            //增加获赠者的贝壳
            $up_user = Db::name('user')->where('id', $data['user_id'])->update(['conch' => bcadd($user_info_h['conch'], $user_b, 2)]);
            if (!$up_user) {
                $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！5'];
                Db::rollback();
                return $this->json_rewrite($rs);
            }
            $icon=$li_wu['tr_icon'];
            //赠送成功进行留言
            $msg1=emoji_decode($this->user_info['user_nick_name']) . '，赠送了' . intval(abs($data['num'])) . "个" . $li_wu['tr_name'];
            $msg="<span style='vertical-align: center'>{$msg1}</span><img src='{$icon}' style='margin-left: 3px;margin-bottom: -5px;vertical-align: center;width: 20px;height: 20px;'></img>";
            $p['paper_id']=$data['paper_id'];
            $p['user_id']=$this->user_info['id'];
            $p['is_gift']=1;
            $p['reply_content']=$msg;
            $p['reply_type']=0;
            $p['apter_time']=time();
            $p['prove_time']=time();
            $p['reply_status']=1;
            $p['much_id']=$data['much_id'];
            $phase = Db::name('paper_reply')->where('much_id', $data['much_id'])->where('paper_id', $data['paper_id'])->max('phase');
            if ($phase == 0) {
                $p['phase'] = 2;
            } else {
                $p['phase'] = $phase + 1;
            }
            $reply=Db::name('paper_reply')->insert($p);
            if (!$reply) {
                $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！6'];
                Db::rollback();
                return $this->json_rewrite($rs);
            }
            //明细表增加数据
            $rs = ['status' => 'success', 'msg' => '赠送成功！'];

            $util = new Util();
            //发送模版
            $util->add_template(['much_id' => $data['much_id'],
                'at_id' => 'YL0007',
                'user_id' => $data['user_id'],
                'page' => 'yl_welore/pages/index/index',
                'keyword1' => emoji_decode($this->user_info['user_nick_name']) . '，赠送给您' . intval(abs($data['num'])) . "个" . $li_wu['tr_name'],
                'keyword2' => date('Y-m-d H:i', time()),
            ]);
            Db::commit();
            return $this->json_rewrite($rs);
        } catch (\Exception $e) {
            Db::rollback();
            $rs = ['status' => 'error', 'msg' => '赠送失败，请稍候重试！'];
            return $this->json_rewrite($rs);
        }
    }

    /**
     * 积分兑换贝壳
     */
    public function get_ji_bei()
    {
        $data = input('param.');
        $fraction_scale = Db::name('user_punch_range')->where('much_id', $data['much_id'])->find();
        $scale = 10;
        if (!empty($fraction_scale['fraction_scale'])) {
            $scale = $fraction_scale['fraction_scale'];
        }
        //积分兑换贝壳开关
        if ($fraction_scale['fraction_redemption_channel'] == 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '兑换通道已关闭！']);
        }
        $aaaTime = md5(uniqid(mt_rand(), true));
        $check_d = Cache::remember('check_ji_' . $data['openid'], $aaaTime, 60);
        if ($aaaTime == $check_d) {
            //Cache::set('check_ji_' . $data['openid'], 1, 60);
            // 启动事务
            Db::startTrans();
            try {
                $rs = ['status' => 'success', 'msg' => '兑换成功！'];

                $money = abs($data['bei_money_b']);
                $user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
                if ($money < 0.01) {
                    Db::rollback();
                    $rs = ['status' => 'error', 'msg' => '至少大于0.1' . $this->design['confer']];
                    return $this->json_rewrite($rs);
                }
                if ($money > $user_info['fraction']) {
                    Db::rollback();
                    $rs = ['status' => 'error', 'msg' => '余额不足'];
                    return $this->json_rewrite($rs);
                }
                if (abs($money) > $user_info['fraction']) {
                    Db::rollback();
                    $rs = ['status' => 'error', 'msg' => '余额不足'];
                    return $this->json_rewrite($rs);
                }

                //应得贝壳
                $finance1 = $money;
                //return $finance1;
                $evaluate1['category'] = 1;
                $evaluate1['evaluate'] = 1;
                $evaluate1['finance'] = -(bcmul($money, $scale, 2));

                $evaluate1['poem_fraction'] = $user_info['fraction'];//初始积分
                $evaluate1['surplus_fraction'] = bcsub($user_info['fraction'], (bcmul($money, $scale, 2)), 2);//减少后积分

                $evaluate1['poem_conch'] = $user_info['conch'];//初始贝壳
                $evaluate1['surplus_conch'] = $user_info['conch'];//后贝壳

                $evaluate1['solution'] = $this->design['confer'] . '兑换' . $this->design['currency'] . '，' . $this->design['confer'] . '减少';
                $evaluate1['user_id'] = $this->user_info['id'];
                $evaluate1['ruins_time'] = time();
                $evaluate1['much_id'] = $data['much_id'];


                $evaluate0['category'] = 1;
                $evaluate0['evaluate'] = 0;
                $evaluate0['finance'] = $finance1;
                $evaluate0['poem_conch'] = $user_info['conch'];//初始贝壳
                $evaluate0['surplus_conch'] = bcadd($user_info['conch'], $finance1, 2);//增加后贝壳


                $evaluate0['poem_fraction'] = $user_info['fraction'];//初始积分
                $evaluate0['surplus_fraction'] = $user_info['fraction'];//后积分

                $evaluate0['solution'] = $this->design['confer'] . '兑换' . $this->design['currency'] . '，' . $this->design['currency'] . '增加';
                $evaluate0['user_id'] = $this->user_info['id'];
                $evaluate0['ruins_time'] = time();
                $evaluate0['much_id'] = $data['much_id'];

                if ($evaluate1['surplus_conch'] < 0) {
                    Db::rollback();
                    Cache::rm('check_' . $data['openid']);
                    $rs = ['status' => 'error', 'msg' => '余额不足'];
                    return $this->json_rewrite($rs);
                }

                //积分日志
                $evaluate1_ins = Db::name('user_amount')->insert($evaluate1);
                if (!$evaluate1_ins) {
                    $rs = ['status' => 'error', 'msg' => '兑换失败，请稍候重试！1'];
                    Db::rollback();
                }
                //贝壳日志
                $evaluate0_ins = Db::name('user_amount')->insert($evaluate0);
                if (!$evaluate0_ins) {
                    $rs = ['status' => 'error', 'msg' => '兑换失败，请稍候重试！2'];
                    Db::rollback();
                }
                //给用户增加贝壳
                $user_up = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['conch' => $evaluate0['surplus_conch'], 'fraction' => $evaluate1['surplus_fraction']]);
                if (!$user_up) {
                    $rs = ['status' => 'error', 'msg' => '兑换失败，请稍候重试！3'];
                    Db::rollback();
                }
                Db::commit();
            } catch (\Exception $e) {
                $rs = ['status' => 'error', 'msg' => '兑换失败，请稍候重试！' . $e->getMessage()];
                Db::rollback();
            }
            Cache::rm('check_ji_' . $data['openid']);
            return $this->json_rewrite($rs);
        } else {
            Cache::rm('check_ji_' . $data['openid']);
            $rs = ['status' => 'error', 'msg' => '点的太快啦！'];
            return $this->json_rewrite($rs);
        }

    }

    /**
     * 贝壳兑换积分
     */
    public function add_bei_ji()
    {
        $data = input('param.');
//        $bei_jie = $this->user_currency_conversion($this->user_info['id'], 0, $data['much_id']);
//        if ($bei_jie == 0) {
//            $rs = ['status' => 'error', 'msg' => '今日兑换次数已经超限！'];
//            return $this->json_rewrite($rs);
//        }
        $aaaTime = md5(uniqid(mt_rand(), true));
        $check_d = Cache::remember('check_bei_' . $data['openid'], $aaaTime, 60);
        if ($aaaTime == $check_d) {
            //Cache::set('check_bei_' . $data['openid'], 1, 60);
            // 启动事务
            Db::startTrans();
            try {
                $rs = ['status' => 'success', 'msg' => '兑换成功！'];
                $money = abs($data['bei_money']);
                $user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();

                if ($money > $user_info['conch']) {
                    $rs = ['status' => 'error', 'msg' => '兑换失败，余额不足！'];
                    return $this->json_rewrite($rs);
                }
                if (abs($money) > $user_info['conch']) {
                    $rs = ['status' => 'error', 'msg' => '兑换失败，余额不足！'];
                    return $this->json_rewrite($rs);
                }
                $fraction_scale = Db::name('user_punch_range')->where('much_id', $data['much_id'])->find();
                $scale = 10;
                if (!empty($fraction_scale['fraction_scale'])) {
                    $scale = $fraction_scale['fraction_scale'];
                }
                //兑换积分
                $finance1 = bcmul($money, $scale, 2);

                //增加积分
                $evaluate1['category'] = 1;
                $evaluate1['evaluate'] = 1;
                $evaluate1['finance'] = $finance1;

                $evaluate1['poem_fraction'] = $user_info['fraction'];//初始积分
                $evaluate1['surplus_fraction'] = bcadd($user_info['fraction'], $finance1, 2);//增加后积分

                $evaluate1['poem_conch'] = $user_info['conch'];//贝壳
                $evaluate1['surplus_conch'] = $user_info['conch'];//贝壳

                $evaluate1['solution'] = $this->design['currency'] . '兑换' . $this->design['confer'] . '，' . $this->design['confer'] . '增加';
                $evaluate1['user_id'] = $this->user_info['id'];
                $evaluate1['ruins_time'] = time();
                $evaluate1['much_id'] = $data['much_id'];

                //减少贝壳
                $evaluate0['category'] = 1;
                $evaluate0['evaluate'] = 0;
                $evaluate0['finance'] = -$money;

                $evaluate0['poem_conch'] = $user_info['conch'];//初始贝壳
                $evaluate0['surplus_conch'] = bcsub($user_info['conch'], $money, 2);//减少后贝壳

                $evaluate0['poem_fraction'] = $user_info['fraction'];//积分
                $evaluate0['surplus_fraction'] = $user_info['fraction'];//后积分

                $evaluate0['solution'] = $this->design['currency'] . '兑换' . $this->design['confer'] . '，' . $this->design['currency'] . '减少';
                $evaluate0['user_id'] = $this->user_info['id'];
                $evaluate0['ruins_time'] = time();
                $evaluate0['much_id'] = $data['much_id'];

                if ($evaluate0['surplus_conch'] < 0) {
                    Db::rollback();
                    Cache::rm('check_' . $data['openid']);
                    $rs = ['status' => 'error', 'msg' => '余额不足'];
                    return $this->json_rewrite($rs);
                }
                //积分日志
                $evaluate1_ins = Db::name('user_amount')->insert($evaluate1);
                if (!$evaluate1_ins) {
                    $rs = ['status' => 'error', 'msg' => '兑换失败，请稍候重试！1'];
                    Db::rollback();
                }
                //贝壳日志
                $evaluate0_ins = Db::name('user_amount')->insert($evaluate0);
                if (!$evaluate0_ins) {
                    $rs = ['status' => 'error', 'msg' => '兑换失败，请稍候重试！2'];
                    Db::rollback();
                }
                //给用户增加积分&&减少贝壳
                $user_up = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['conch' => $evaluate0['surplus_conch'], 'fraction' => $evaluate1['surplus_fraction']]);
                if (!$user_up) {
                    $rs = ['status' => 'error', 'msg' => '兑换失败，请稍候重试！3'];
                    Db::rollback();
                }
                Db::commit();
            } catch (\Exception $e) {
                $rs = ['status' => 'error', 'msg' => '兑换失败，请稍候重试！' . $e->getMessage()];
                Db::rollback();
            }
            Cache::rm('check_' . $data['openid']);
            return $this->json_rewrite($rs);
        } else {
            $rs = ['status' => 'error', 'msg' => '点的太快啦！'];
            return $this->json_rewrite($rs);
        }

    }

    /**
     * 明细表增加数据
     */
    public function add_user_amount($user_id, $category, $finance, $solution, $much_id, $tribute_taxation)
    {
        //查询用户详情
        $user_info = Db::name('user')->where('id', $user_id)->where('much_id', $much_id)->find();
        $fraction_scale = Db::name('user_punch_range')->where('much_id', $much_id)->find();
        $scale = 10;
        if (!empty($fraction_scale['fraction_scale'])) {
            $scale = $fraction_scale['fraction_scale'];
        }
        $data['user_id'] = $user_id;
        $data['category'] = $category;
        $data['ruins_time'] = time();
        $data['solution'] = $solution;
        $data['much_id'] = $much_id;
        //收益
        if ($category == 3) {
            $data['evaluate'] = 1;
            $data['finance'] = ($finance * $tribute_taxation) * $scale;
            $data['poem_fraction'] = $user_info['fraction'];//初始积分
            $data['surplus_fraction'] = $user_info['fraction'] + ($finance * $tribute_taxation) * $scale;//增加后积分
            $data['poem_conch'] = $user_info['conch'];//初始贝壳
            $data['surplus_conch'] = $user_info['conch'];//后贝壳
        }
        //消费
        if ($category == 2) {
            $data['evaluate'] = 0;
            $data['finance'] = -$finance;
            $data['poem_conch'] = $user_info['conch'];//初始贝壳
            $data['surplus_conch'] = $user_info['conch'] - $finance;//减少后贝壳

            $data['poem_fraction'] = $user_info['fraction'];//初始积分
            $data['surplus_fraction'] = $user_info['fraction'];//后积分
        }
        //增加贝壳
        if ($category == 4) {
            $data['evaluate'] = 0;
            $data['finance'] = $finance;
            $data['poem_conch'] = $user_info['conch'];//初始贝壳
            $data['surplus_conch'] = $user_info['conch'] + $finance;//减少后贝壳

            $data['poem_fraction'] = $user_info['fraction'];//初始积分
            $data['surplus_fraction'] = $user_info['fraction'];//后积分
        }
        $ins = Db::name('user_amount')->insert($data);
        if ($ins) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 支付付费贴
     */
    public function do_paper_money_new()
    {
        $data = input('param.');
        //帖子详情
        $paper_info = Db::name('paper')->where('id', $data['money_id'])->where('whether_delete', 0)->where('much_id', $data['much_id'])->find();
        if ($paper_info['buy_price_type'] == 0) { //0贝壳
            $msg = $this->design['currency'];
        } else {
            $msg = $this->design['confer'];
        }
        if (empty($paper_info)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '帖子已被删除！']);
        }
        //购买者用户
        if ($this->user_info['id'] == $paper_info['user_id']) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '这是您自己的帖子！']);
        }
        //判断是否已经买过
        $buy = Db::name('paper_buy_user')->where('user_id', $this->user_info['id'])->where('paper_id', $data['money_id'])->where('much_id', $data['much_id'])->find();
        if ($buy) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '已经购买无需重复购买！']);
        }
        $paper_user_info = Db::name('user')->where('id', $paper_info['user_id'])->find();
        //付费贴税率
        $paper_smingle = Db::name('paper_smingle')->where('much_id', $data['much_id'])->find();
        /*dump($paper_info);
        exit();*/
        //判断用户贝壳是否满足需要
        if ($paper_info['buy_price_type'] == 0) { //0贝壳
            if (bccomp($this->user_info['conch'], $paper_info['buy_price'], 2) == -1) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '兑换失败，' . $msg . '不足！','error_code' => -1]);
            }
        } else {
            if (bccomp($this->user_info['fraction'], $paper_info['buy_price'], 2) == -1) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '兑换失败，' . $msg . '不足！','error_code' => -1]);
            }
        }
        //计算用户所得
        $money = $paper_info['buy_price'];
        if ($paper_info['buy_price'] >= 1) {
            $money = bcsub($money, (bcmul($money, $paper_smingle['buy_paper_taxing'], 2)), 2);
        }
        $aaaTime = md5(uniqid(mt_rand(), true));
        $check_d = Cache::remember('check_money_' . $data['openid'], $aaaTime, 60);
        if ($aaaTime == $check_d) {
            //Cache::set('check_money_' . $data['openid'], 1, 60);
            Db::startTrans();
            try {
                //保存用户购买表
                $buy_user['paper_id'] = $data['money_id'];
                $buy_user['user_id'] = $this->user_info['id'];
                $buy_user['buy_price'] = $paper_info['buy_price'];
                $buy_user['buy_taxing'] = $paper_smingle['buy_paper_taxing'];
                $buy_user['buy_time'] = time();
                $buy_user['much_id'] = $data['much_id'];
                $buy_user['buy_type'] = 0;
                $paper_buy_user = Db::name('paper_buy_user')->insert($buy_user);
                if (!$paper_buy_user) {
                    Db::rollback();
                    Cache::rm('check_money_' . $data['openid']);
                    $rs = ['status' => 'error', 'msg' => '兑换失败,请稍候重试！', 'error_code' => 1];
                    return $this->json_rewrite($rs);
                }
                //判断是否有文件
                $check_file = Db::name('netdisc_sell')->where('pa_id', $data['money_id'])->where('much_id', $data['much_id'])->find();
                if ($paper_info['is_buy'] != 0 && !empty($check_file)) {
                    //查询用户文件
                    $belong = Db::name('netdisc_belong')->where('id', $check_file['nb_id'])->where('much_id', $data['much_id'])->find();
                    if ($belong['is_del'] == 1) {
                        Db::rollback();
                        Cache::rm('check_money_' . $data['openid']);
                        $rs = ['status' => 'error', 'msg' => '兑换失败,文件已被删除！', 'error_code' => 1];
                        return $this->json_rewrite($rs);
                    }
                    if ($check_file['nc_id'] != 0) {
                        $belong_nc = Db::name('netdisc')->where('id', $belong['nc_id'])->where('much_id', $data['much_id'])->find();
                        if ($belong_nc['file_status'] == 0) {
                            Db::rollback();
                            Cache::rm('check_money_' . $data['openid']);
                            $rs = ['status' => 'error', 'msg' => '兑换失败,文件已被屏蔽，禁止保存！', 'error_code' => 1];
                            return $this->json_rewrite($rs);
                        }
                    }

                    //if (!empty($belong)) {
                    //保存
                    $ins_my_n = Db::name('netdisc_belong')->insertGetId([
                        'nc_id' => $belong['nc_id'],
                        'user_id' => $this->user_info['id'],
                        'file_name' => $belong['file_name'],
                        'parent_path_id' => 0,
                        'is_dir' => $belong['is_dir'],
                        'add_time' => time(),
                        'is_sell' => $check_file['is_sell'],
                        'is_del' => 0,
                        'much_id' => $belong['much_id'],
                    ]);
                    if (!$ins_my_n) {
                        Db::rollback();
                        Cache::rm('check_money_' . $data['openid']);
                        $rs = ['status' => 'error', 'msg' => '兑换失败,请稍候重试！', 'error_code' => 8];
                        return $this->json_rewrite($rs);
                    }
                    if ($belong['is_dir'] == 1) {
                        $belong_in = Db::name('netdisc_belong')->where('parent_path_id', $belong['id'])->where('much_id', $data['much_id'])->select();
                        foreach ($belong_in as $k => $v) {
                            $ins_my_in = Db::name('netdisc_belong')->insert([
                                'nc_id' => $v['nc_id'],
                                'user_id' => $this->user_info['id'],
                                'file_name' => $v['file_name'],
                                'parent_path_id' => $ins_my_n,
                                'is_dir' => $v['is_dir'],
                                'add_time' => time(),
                                'is_sell' => $check_file['is_sell'],
                                'is_del' => 0,
                                'much_id' => $v['much_id'],
                            ]);
                            if (!$ins_my_in) {
                                Db::rollback();
                                Cache::rm('check_money_' . $data['openid']);
                                $rs = ['status' => 'error', 'msg' => '兑换失败,请稍候重试！', 'error_code' => 9];
                                return $this->json_rewrite($rs);
                            }
                        }
                    }
                    //}
                }
                //增加积分表数据(积分减少)
                $amount['user_id'] = $this->user_info['id'];
                $amount['category'] = 2;
                $amount['finance'] = -$paper_info['buy_price'];
                if ($paper_info['buy_price_type'] == 0) { //0贝壳
                    $amount['poem_fraction'] = $this->user_info['fraction'];//积分
                    $amount['surplus_fraction'] = $this->user_info['fraction'];//积分
                    $amount['poem_conch'] = $this->user_info['conch'];//初始贝壳
                    $amount['surplus_conch'] = bcsub($this->user_info['conch'], $paper_info['buy_price'], 2);
                    $update = ['conch' => $amount['surplus_conch']];
                } else {
                    $amount['poem_fraction'] = $this->user_info['fraction'];//积分
                    $amount['surplus_fraction'] = bcsub($this->user_info['fraction'], $paper_info['buy_price'], 2);
                    $amount['poem_conch'] = $this->user_info['conch'];//初始贝壳
                    $amount['surplus_conch'] = $this->user_info['conch'];//初始贝壳
                    $update = ['fraction' => $amount['surplus_fraction']];
                }

                $amount['ruins_time'] = time();
                $amount['solution'] = '兑换订阅贴';
                $amount['evaluate'] = $paper_info['buy_price_type'];
                $amount['much_id'] = $data['much_id'];
                $amount_res = Db::name('user_amount')->insert($amount);
                if (!$amount_res) {
                    Db::rollback();
                    Cache::rm('check_money_' . $data['openid']);
                    $rs = ['status' => 'error', 'msg' => '兑换失败,请稍候重试！', 'error_code' => 2];
                    return $this->json_rewrite($rs);
                }
                //用户积分减少
                $this_user_update = Db::name('user')->where('id', $this->user_info['id'])->update($update);
                if (!$this_user_update) {
                    Db::rollback();
                    Cache::rm('check_money_' . $data['openid']);
                    $rs = ['status' => 'error', 'msg' => '兑换失败,请稍候重试！', 'error_code' => 3];
                    return $this->json_rewrite($rs);
                }
                //增加积分表数据(积分增加)
                $amount_jia['user_id'] = $paper_info['user_id'];
                $amount_jia['category'] = 3;
                $amount_jia['finance'] = $money;
                if ($paper_info['buy_price_type'] == 0) { //0贝壳
                    $amount_jia['poem_conch'] = $paper_user_info['conch'];//初始贝壳
                    $amount_jia['surplus_conch'] = bcadd($paper_user_info['conch'], $money, 2);

                    $amount_jia['poem_fraction'] = $paper_user_info['fraction'];//积分
                    $amount_jia['surplus_fraction'] = $paper_user_info['fraction']; //积分
                    $jia = ['conch' => $amount_jia['surplus_conch']];
                } else {
                    $amount_jia['poem_conch'] = $paper_user_info['conch'];//初始贝壳
                    $amount_jia['surplus_conch'] = $paper_user_info['conch'];//初始贝壳

                    $amount_jia['poem_fraction'] = $paper_user_info['fraction'];//积分
                    $amount_jia['surplus_fraction'] = bcadd($paper_user_info['fraction'], $money, 2);
                    $jia = ['fraction' => $amount_jia['surplus_fraction']];
                }


                $amount_jia['ruins_time'] = time();
                if ($paper_info['buy_price'] >= 1) {
                    $amount_jia['solution'] = '订阅贴收益（税率' . (100 * $paper_smingle['buy_paper_taxing']) . '%)';
                } else {
                    $amount_jia['solution'] = '订阅贴收益:' . $money;
                }
                $amount_jia['evaluate'] = $paper_info['buy_price_type'];
                $amount_jia['much_id'] = $data['much_id'];
                $amount_jia_res = Db::name('user_amount')->insert($amount_jia);
                if (!$amount_jia_res) {
                    Db::rollback();
                    Cache::rm('check_money_' . $data['openid']);
                    $rs = ['status' => 'error', 'msg' => '兑换失败,请稍候重试！', 'error_code' => 4];
                    return $this->json_rewrite($rs);
                }
                //用户积分增加
                $paper_user_update = Db::name('user')->where('id', $paper_info['user_id'])->update($jia);
                if (!$paper_user_update) {
                    Db::rollback();
                    Cache::rm('check_money_' . $data['openid']);
                    $rs = ['status' => 'error', 'msg' => '兑换失败,请稍候重试！', 'error_code' => 5];
                    return $this->json_rewrite($rs);
                }
                Db::commit();
                if ($paper_info['is_buy'] > 1) {
                    $return_msg = '文件已保存到网盘！';
                    $rs = ['status' => 'success', 'msg' => $return_msg, 'code' => 1];
                } else {
                    $return_msg = '兑换成功！';
                    $rs = ['status' => 'success', 'msg' => $return_msg, 'code' => 0];
                }
                Cache::rm('check_money_' . $data['openid']);
                return $this->json_rewrite($rs);
            } catch
            (\Exception $e) {
                Db::rollback();
                Cache::rm('check_money_' . $data['openid']);
                $rs = ['status' => 'error', 'msg' => '兑换失败,请稍候重试！' . $e->getMessage()];
                return $this->json_rewrite($rs);
            }
        } else {
            $rs = ['status' => 'error', 'msg' => '点的太快啦！'];
            return $this->json_rewrite($rs);
        }
    }
}