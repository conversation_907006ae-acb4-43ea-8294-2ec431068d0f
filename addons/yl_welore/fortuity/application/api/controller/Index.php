<?php

namespace app\api\controller;


use app\api\service\Alternative;
use app\api\service\RankingUtil;
use app\api\service\RedPaper;
use app\api\service\TmplService;
use app\api\service\UserService;
use app\api\service\Util;
use app\common\Gyration;
use think\Cache;
use think\Db;

class Index extends Base
{

    /**
     * 获取首页数据
     */
    public function get_index_list()
    {

        /*
        $a = Db::name('user_withdraw_money')->where('seek_time', 'NULL')->fetchSql(true)->update([
            'seek_time' => Db::raw('verify_time'),
        ]);
        dump($a);
        exit();
        */
        $data = input('param.');
        $cacheName = md5(json_encode($data, true));
        $getIndexListCache = cache($cacheName);
        if ($getIndexListCache && Cache::get("globalIndexCache_" . $data['much_id'])) {
            for ($i = 0; $i < count($getIndexListCache['info']); $i++) {
                $getIndexListCache['info'][$i]['adapter_time'] = formatTime($getIndexListCache['info'][$i]['adapter_time']);
                if (isset($getIndexListCache['info'][$i]['huifu_time'])) {
                    $getIndexListCache['info'][$i]['huifu_time'] = formatTime($getIndexListCache['info'][$i]['huifu_time']);
                }
            }
            return $this->json_rewrite($getIndexListCache);
        } else {
            $rs = ['status' => 'success', 'msg' => '获取成功'];
            $where = [];
            $util = new Util();
            $where_not_in = [];
            $page = $data['index_page'];
            $check_qq = 'no';
            if (isset($data['tory_id'])) {
                $where['p.tory_id'] = ['eq', $data['tory_id']];
                $where['p.topping_time'] = ['eq', 0];
                $check_qq = $util->check_qq($this->user_info['user_wechat_open_id'], $data['tory_id']);
            }
            if ($this->version == 1) {
                $where['p.study_type'] = ['in', ['0', '1', '3']];
                $where['p.is_buy'] = ['eq', 0];
            }
            $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
            if ($authority['hair_video_arbor'] == 0) {
                $where['p.study_type'] = ['in', ['0', '1', '3', '4', '5']];
            }

            //获取当前用户是否是超管
            $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
            //查询当前用户是否是VIP
            $user_vip = $util->get_user_vip($this->user_info['id']);

            //查询有权限的圈子
            $q_tory = Db::name('territory')->where('status', 1)->where('much_id', $data['much_id'])->select();
            $q_tory_id = '';
            $vip_tory_id = '';
            $level_tory_id = '';
            foreach ($q_tory as $k => $v) {
                if ($v['attention'] == 1) {
                    $user_trailing = Db::name('user_trailing')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->where('tory_id', $v['id'])->find();
                    if (empty($user_trailing)) {
                        $q_tory_id .= $v['id'] . ",";
                    }
                }
                if ($v['attention'] == 2) {
                    $vip_tory_id .= $v['id'] . ",";
                }
                if ($v['visit_level'] > $this->user_info['level']) {
                    $level_tory_id .= $v['id'] . ",";
                }

            }
            //return $level_tory_id;
            //权限
            $q_tory_id = substr($q_tory_id, 0, -1);
            //等级
            $level_tory_id = substr($level_tory_id, 0, -1);
            //vip
            $vip_tory_id = substr($vip_tory_id, 0, -1);


            if ($check_qq == 'no') {
                if ($user_vip == 0 && $vip_tory_id && $cg == 0) {
                    $q_tory_id = $q_tory_id . ',' . $vip_tory_id;
                }
                if ($level_tory_id && $cg == 0) {
                    $q_tory_id = $q_tory_id . ',' . $level_tory_id;
                }
                if ($cg == 0) {
                    $where['t.id'] = array('not in', $q_tory_id);
                }
            }


            //查询置顶的帖子
            $home = Db::name('home_topping')->where('much_id', $data['much_id'])->select();
            $home_id = '';
            foreach ($home as $k => $v) {
                $home_id .= $v['paper_id'] . ",";
            }
            $home_id = substr($home_id, 0, -1);
            //查询被封禁的用户
            $user_status = Db::name('user')->where('much_id', $data['much_id'])->where('status', 0)->select();
            $user_status_id = '';
            foreach ($user_status as $k => $v) {
                $user_status_id .= $v['id'] . ",";
            }
            $user_status_id = substr($user_status_id, 0, -1);

            $order_check = false;
            if ($data['order_time'] == 'huifu') {
                $order = 'huifu_time desc';
                $order_check = true;
            }
            if ($data['order_time'] == 'fatie') {
                $order = 'p.adapter_time desc';
            }
            if ($data['order_time'] == 'dianzan') {
                $order = 'p.study_laud desc';
            }

            if (!$order_check) {
                if (!isset($data['tory_id']) && $authority['home_random_arbor'] == 1) {
                    $order = Db::raw('rand()');
                }
            }

            //$where['p.essence_time'] = ['eq', 0];
            $list = Db::name('paper')
                ->alias('p')
                ->join('user u', 'p.user_id=u.id')
                ->join('territory t', 'p.tory_id=t.id')
                ->join('paper_reply r', 'r.paper_id=p.id', 'LEFT')
                ->where('p.whether_delete', '0')
                ->where('p.study_status', '1')
                ->where('p.much_id', $data['much_id'])
                ->where('t.status', 1)
                ->where('t.is_del', 0)
                ->where($where)
                ->whereNotIn('p.id', $home_id)
                ->whereNotIn('p.user_id', $user_status_id)
                ->field('p.uccid,p.video_type,p.third_part_vid,p.study_video,u.user_wechat_open_id,p.id,p.user_id,p.tory_id,p.tg_id,p.study_title,p.study_title_color,p.study_content,p.study_type,p.is_buy,p.topping_time,p.image_part,p.study_voice,p.study_voice_time,p.study_heat,p.study_laud,p.study_repount,p.adapter_time,p.vote_deadline,p.is_open,u.level,u.gender,u.wear_merit,u.autograph,u.user_nick_name,u.user_head_sculpture,t.realm_name,max(r.apter_time) as huifu_time')
                ->order($order)
                ->group('p.id')
                ->page($page, '8')
                ->select();
            $home_list_a = [];
            if (!empty($home)) {
                $home_list = Db::name('paper')->alias('p')
                    ->join('user u', 'p.user_id=u.id')
                    ->join('territory t', 'p.tory_id=t.id')
                    ->join('home_topping h', 'h.paper_id=p.id')
                    ->where('p.whether_delete', '0')
                    ->where('p.study_status', '1')
                    ->where('p.much_id', $data['much_id'])
                    ->where('t.status', 1)
                    ->where('t.is_del', 0)
                    ->where($where)
                    ->whereNotIn('t.id', $q_tory_id)
                    ->field('p.uccid,p.video_type,p.study_video,p.third_part_vid,u.user_wechat_open_id,h.top_time,h.style_type,p.id,p.user_id,p.tory_id,p.tg_id,p.study_title,p.study_title_color,p.study_content,p.study_type,p.is_buy,p.topping_time,p.image_part,p.study_voice,p.study_voice_time,p.study_heat,p.study_laud,p.study_repount,p.adapter_time,p.vote_deadline,p.is_open,u.level,u.gender,u.wear_merit,u.user_nick_name,u.user_head_sculpture,t.realm_name')
                    //->field(',p.id,p.user_id,p.tory_id,p.tg_id,p.study_title,p.study_title_color,p.study_content,p.topping_time,u.level,u.gender,u.wear_merit,u.user_nick_name,u.user_head_sculpture')
                    ->order('h.top_time desc')
                    ->select();

                if ($page == 1) {
                    foreach ($home_list as $k => $v) {
                        if ($v['style_type'] == 0) {
                            array_unshift($list, $v);
                        } else {
                            $v['study_content'] = strip_tags(emoji_decode($v['study_content']));
                            $v['study_title'] = emoji_decode($v['study_title']);
                            array_push($home_list_a, $v);
                        }

                    }
                }
            }
            if ($list) {
                foreach ($list as $k => $v) {

                    if (isset($data['tory_id'])) {//判断是否是 圈主或者管理员

                        $list[$k]['check_qq'] = $util->check_qq($v['user_wechat_open_id'], $v['tory_id']);
                    }
                    $list[$k]['user_wechat_open_id'] = null;
                    if (isset($v['huifu_time'])) {
                        //$list[$k]['huifu_time'] = formatTime($v['huifu_time']);
                        $list[$k]['huifu_time'] = $v['huifu_time'];
                    }
                    $list[$k]['is_voice'] = false;
                    $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                    $list[$k]['study_title'] = emoji_decode($v['study_title']);


                    //查询是否购买了当前帖子
                    $purchase = Db::name('paper_buy_user')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();


                    //判断是否是 圈主，管理，超管，自己
                    $check_look = 0;
                    $da_xiao = $util->check_qq($data['openid'], $v['tory_id']);
                    if ($da_xiao == 'da' || $da_xiao == 'xiao') { //不是圈主或者管理
                        $check_look = 1;
                    }

                    if ($cg == 1) {//超管
                        $check_look = 1;

                    }
                    if ($v['user_id'] == $this->user_info['id']) {  //不是自己发的
                        $check_look = 1;
                    }
                    //return $this->user_info['id'];
                    if ($purchase == 1) {//已经购买
                        $check_look = 1;
                    }

                    $list[$k]['check_look'] = $check_look;

                    $modd = '/<div class="stealth_module".*>([\s\S]*)<\/div>/i';

                    preg_match_all($modd, emoji_decode($v['study_content']), $moddUrl);

                    if (count($moddUrl[1]) > 0) {
                        for ($i = 0; $i < count($moddUrl[1]); $i++) {
                            $moddHtml = "<div></div>";
                            $v['study_content'] = preg_replace($modd, $moddHtml, emoji_decode($v['study_content']), 1);
                        }
                        if (empty(strip_tags(emoji_decode($v['study_content'])))) {
                            $v['study_content'] = "<div>这里是隐藏文本，需要回复才能看到</div>";
                        }
                    } else {
                        if ($v['is_buy'] == 1) {
                            $v['study_content'] = "<div>这里是隐藏文本，需要订阅才能看到</div>";
                        } else {
                            $v['study_content'] = emoji_decode($v['study_content']);
                        }

                    }


                    $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';

                    preg_match_all($preg, emoji_decode($v['study_content']), $match);

                    if (!empty(json_decode($v['image_part'], true))) {
                        $list[$k]['image_part'] = json_decode($v['image_part']);
                    } else {
                        if (!empty($match[0])) {
                            $img = array();
                            foreach ($match[1] as $a => $b) {
                                $img[$a] = htmlspecialchars_decode($b);
                            }
                            $list[$k]['image_part'] = $img;
                        } else {
                            $list[$k]['image_part'] = [];
                        }
                    }

                    $list[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));
                    $list[$k]['study_content'] = Alternative::ExpressionHtml($list[$k]['study_content']);
                    $list[$k]['study_heat'] = formatNumber($v['study_heat']);
                    $list[$k]['study_laud'] = formatNumber($v['study_laud']);
                    $list[$k]['study_repount'] = $util->get_paper_reply($v['id'], $data['much_id']);
                    $list[$k]['user_vip'] = $util->get_user_vip($v['user_id']);

                    $list[$k]['study_voice'] = link_urldecode($v['study_voice']);

                    $list[$k]['study_voice_time'] = s_to_hs($v['study_voice_time']);
                    $list[$k]['starttime'] = '00:00';
                    $list[$k]['index6_time'] = $this->getMyDate($v['adapter_time']);
                    //$list[$k]['adapter_time'] = formatTime($v['adapter_time']);
                    $list[$k]['adapter_time'] = $v['adapter_time'];
                    //如果是腾讯视频
                    if ($v['video_type'] == 1) {
                        $list[$k]['study_video'] = $this->getVideoInfo($v['third_part_vid']);

                    }
                    $list[$k]['offset'] = 0;
                    $list[$k]['max'] = $v['study_voice_time'];

                    $sc = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
                    $list[$k]['is_info_zan'] = $sc == 0 ? false : true;
                    //$count = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('paper_id', $v['id'])-
                    $count = $v['study_laud'];
                    $list[$k]['info_zan_count'] = formatNumber($count);
                    $list[$k]['info_zan_count_this'] = $count;
                    //查询当前帖子是否是红包贴
                    $red = Db::name('paper_red_packet')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                    //查询是否购买了当前帖子
                    $list[$k]['red'] = $red;
                    //获取当前话题
                    if ($v['tg_id'] != 0) {
                        $gambit = Db::name('gambit')->where('much_id', $data['much_id'])->where('id', $v['tg_id'])->find();
                        $list[$k]['gambit_name'] = emoji_decode(str_replace('#', '', $gambit['gambit_name']));
                        $list[$k]['gambit_id'] = $gambit['id'];
                    }
                    $list[$k]['level'] = $util->get_user_level($v['level'], $data['much_id'])['level_icon'];
                    //当前用户的勋章
                    $list[$k]['wear_merit'] = $util->get_medal($v['wear_merit'], $data['much_id']);
                    //当前用户昵称
                    $list[$k]['special'] = $util->get_user_special_nickname($v['user_id'], $data['much_id']);
                    //当前用户头像框
                    $list[$k]['avatar_frame'] = $util->get_user_avatar_frame($v['user_id'], $data['much_id']);
                    //是否是投票贴
                    $list[$k]['vo_id'] = [];
                    //查询我是否投票了
                    $list[$k]['is_vo_check'] = Db::name('user_vote')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                    if ($list[$k]['is_vo_check'] > 0) {
                        $my_vo = Db::name('user_vote')->where('paper_id', $v['id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
                        foreach ($my_vo as $c => $d) {
                            array_push($list[$k]['vo_id'], intval($d['pv_id']));
                        }
                    }
                    $pvInfo = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->select();
                    $pvInfoSum = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->sum('cheat_ballot');
                    $pvPeopleCount = Db::name('user_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                    $pvPeopleCountTotal = ($pvInfoSum + $pvPeopleCount);
                    $list[$k]['vo_count'] = $pvPeopleCountTotal;
                    for ($i = 0; $i < count($pvInfo); $i++) {
                        $voters = Db::name('user_vote')->where('paper_id', $v['id'])->where('pv_id', $pvInfo[$i]['id'])->where('much_id', $data['much_id'])->count();
                        $pvInfo[$i]['voters'] = (intval($pvInfo[$i]['cheat_ballot']) + $voters);
                        $ratio = (($pvInfo[$i]['voters'] / $pvPeopleCountTotal) * 100);
                        if (is_nan($ratio)) {
                            $pvInfo[$i]['ratio'] = 0;
                        } else {
                            $pvInfo[$i]['ratio'] = $ratio;
                        }
                    }
                    $list[$k]['vo'] = $pvInfo;
                    //判断截止时间
                    if ($v['vote_deadline'] != 0) {
                        if ($v['vote_deadline'] > time()) {
                            $list[$k]['vote_deadline'] = date('Y-m-d H:i', $v['vote_deadline']);
                        } else {
                            $list[$k]['vote_deadline'] = -1;//已截止
                            $list[$k]['is_vo_check'] = 1;
                        }
                    }
                    //判断是否有身份铭牌
                    if (intval($v['uccid']) != 0) {
                        //查询佩戴的身份
                        $card_info = Db::name('camouflage_card')->where('id', $v['uccid'])->where('much_id', $data['much_id'])->find();
                        $user_card = Db::name('user_camouflage_card')->where('ccid', $v['uccid'])->where('user_id', $v['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                        $list[$k]['user_head_sculpture'] = $card_info['forgery_head'];
                        $list[$k]['user_id'] = 0;
                        //$list[$k]['user_nick_name']=$card_info['forgery_name'].'-'.$user_card['id'];
                        $list[$k]['user_nick_name'] = $card_info['forgery_name'];
                        $list[$k]['attr'] = '';
                    } else {
                        $attr = $util->get_att_info($v['user_id'], $data['much_id']);
                        $list[$k]['attr'] = $attr;
                    }
                    //获取当前帖子的评论
                    $list[$k]['reply_list'] = $util->get_paper_reply_list($v['id'], $data['much_id']);

                }

                $rs['info'] = $list;
                $rs['dddd'] = $home_list;
                $rs['home_list'] = $home_list_a;
            } else {
                $rs['info'] = [];
                $rs['home_list'] = $home_list_a;
            }
            $rs['version'] = $this->version;
            //$resultJsonRewrite = json($rs);

            Cache::tag("globalIndexCache_{$data['much_id']}")->set($cacheName, $rs, 3600);

            for ($i = 0; $i < count($rs['info']); $i++) {
                $rs['info'][$i]['adapter_time'] = formatTime($rs['info'][$i]['adapter_time']);
                if (isset($rs['info'][$i]['huifu_time'])) {
                    $rs['info'][$i]['huifu_time'] = formatTime($rs['info'][$i]['huifu_time']);
                }
            }

            return $this->json_rewrite($rs);
        }
    }

    /**
     * 获取首页我关注的数据
     */
    public function get_my_index_list()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');

        $user_track = Db::name('user_track')->where('at_user_id', $this->user_info['id'])->select();
        $user_arr = [];
        foreach ($user_track as $k => $v) {
            $user_arr[$k] = $v['qu_user_id'];
        }
        $user['user_track'] = formatNumber($user_track);

        $where = [];
        if ($this->version == 1) {
            $where['p.study_type'] = ['in', ['0', '1', '3']];
            $where['p.is_buy'] = ['eq', 0];
        }
        $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($authority['hair_video_arbor'] == 0) {
            $where['p.study_type'] = ['in', ['0', '1', '3']];
        }
        /**
         * 第一个版本低于第二个版本的时候 return -1
         * 第一个版本等于第二个版本的时候 return 0
         * 第一个版本高于第二个版本的时候 return 1
         */
        if (version_compare($data['version'], '1.0.36') == -1) {

            $where['p.is_buy'] = ['eq', 0];

        }
        //$where['p.study_type'] = ['in', ['0', '1']];
        $where['p.user_id'] = ['in', $user_arr];
        $page = $data['index_page'];
        $list = Db::name('paper')->alias('p')
            ->join('user u', 'p.user_id=u.id')
            ->join('territory t', 'p.tory_id=t.id')
            ->where('p.whether_delete', '0')
            ->where('p.study_status', '1')
            ->where('p.much_id', $data['much_id'])
            ->where($where)
            ->where('t.status', 1)
            ->where('t.is_del', 0)
            ->field('p.uccid,p.video_type,p.study_video,p.third_part_vid,u.user_wechat_open_id,p.id,p.user_id,p.tory_id,p.tg_id,p.study_title,p.study_title_color,p.study_content,p.study_type,p.is_buy,p.topping_time,p.image_part,p.study_voice,p.study_voice_time,p.study_heat,p.study_laud,p.study_repount,p.adapter_time,p.vote_deadline,p.is_open,u.level,u.gender,u.wear_merit,u.user_nick_name,u.user_head_sculpture,t.realm_name')
            ->order('p.adapter_time desc')
            ->page($page, '15')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {
                $list[$k]['is_voice'] = false;
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['study_title'] = emoji_decode($v['study_title']);

                $modd = '/<div class="stealth_module".*>([\s\S]*)<\/div>/i';

                preg_match_all($modd, emoji_decode($v['study_content']), $moddUrl);

                if (count($moddUrl[1]) > 0) {
                    for ($i = 0; $i < count($moddUrl[1]); $i++) {
                        $moddHtml = "<div></div>";
                        $v['study_content'] = preg_replace($modd, $moddHtml, emoji_decode($v['study_content']), 1);
                    }
                    if (empty(strip_tags(emoji_decode($v['study_content'])))) {
                        $v['study_content'] = "<div>这里是隐藏文本，需要回复才能看到</div>";
                    }
                } else {
                    if ($v['is_buy'] == 1) {
                        $v['study_content'] = "<div>这里是隐藏文本，需要订阅才能看到</div>";
                    } else {
                        $v['study_content'] = emoji_decode($v['study_content']);
                    }
                }

                $list[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));
                $list[$k]['study_content'] = Alternative::ExpressionHtml($list[$k]['study_content']);
                $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
                preg_match_all($preg, emoji_decode($v['study_content']), $match);

                if (count($match[0]) != 0) {
                    $list[$k]['image_part'] = $match[1];
                } else {
                    $list[$k]['image_part'] = json_decode($v['image_part']);
                }
                $util = new Util();
                $ling = count($list[$k]['image_part']);
                $list[$k]['study_heat'] = formatNumber($v['study_heat']);
                $list[$k]['study_laud'] = formatNumber($v['study_laud']);
                $list[$k]['study_repount'] = formatNumber($v['study_repount']);
                $list[$k]['user_vip'] = $util->get_user_vip($v['user_id']);
                $list[$k]['adapter_time'] = formatTime($v['adapter_time']);
                $list[$k]['study_voice'] = link_urldecode($v['study_voice']);
                $list[$k]['study_voice_time'] = s_to_hs($v['study_voice_time']);
                $list[$k]['starttime'] = '00:00';
                $list[$k]['adapter_time'] = formatTime($v['adapter_time']);
                $list[$k]['offset'] = 0;
                $list[$k]['max'] = $v['study_voice_time'];

                $sc = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
                $list[$k]['is_info_zan'] = $sc == 0 ? false : true;
                //$count = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('paper_id', $v['id'])->count();
                $count = $v['study_laud'];
                $list[$k]['info_zan_count'] = formatNumber($count);
                $list[$k]['info_zan_count_this'] = $count;
                //查询当前帖子是否是红包贴
                $red = Db::name('paper_red_packet')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                //查询是否购买了当前帖子
                $purchase = Db::name('paper_buy_user')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                $list[$k]['purchase'] = $purchase;
                $list[$k]['red'] = $red;
                //获取当前话题
                if ($v['tg_id'] != 0) {
                    $gambit = Db::name('gambit')->where('much_id', $data['much_id'])->where('id', $v['tg_id'])->find();
                    $list[$k]['gambit_name'] = emoji_decode(str_replace('#', '', $gambit['gambit_name']));
                    $list[$k]['gambit_id'] = $gambit['id'];
                }
                $list[$k]['level'] = $util->get_user_level($v['level'], $data['much_id'])['level_icon'];
                //当前用户的勋章
                $list[$k]['wear_merit'] = $util->get_medal($v['wear_merit'], $data['much_id']);
                //当前用户昵称
                $list[$k]['special'] = $util->get_user_special_nickname($v['user_id'], $data['much_id']);
                //当前用户头像框
                $list[$k]['avatar_frame'] = $util->get_user_avatar_frame($v['user_id'], $data['much_id']);
                //是否是投票贴
                $list[$k]['vo_id'] = [];
                //查询我是否投票了
                $list[$k]['is_vo_check'] = Db::name('user_vote')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                if ($list[$k]['is_vo_check'] > 0) {
                    $my_vo = Db::name('user_vote')->where('paper_id', $v['id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
                    foreach ($my_vo as $c => $d) {
                        array_push($list[$k]['vo_id'], intval($d['pv_id']));
                    }
                }
                $pvInfo = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->select();
                $pvInfoSum = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->sum('cheat_ballot');
                $pvPeopleCount = Db::name('user_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                $pvPeopleCountTotal = ($pvInfoSum + $pvPeopleCount);
                $list[$k]['vo_count'] = $pvPeopleCountTotal;
                for ($i = 0; $i < count($pvInfo); $i++) {
                    $voters = Db::name('user_vote')->where('paper_id', $v['id'])->where('pv_id', $pvInfo[$i]['id'])->where('much_id', $data['much_id'])->count();
                    $pvInfo[$i]['voters'] = (intval($pvInfo[$i]['cheat_ballot']) + $voters);
                    $ratio = (($pvInfo[$i]['voters'] / $pvPeopleCountTotal) * 100);
                    if (is_nan($ratio)) {
                        $pvInfo[$i]['ratio'] = 0;
                    } else {
                        $pvInfo[$i]['ratio'] = $ratio;
                    }
                }
                $list[$k]['vo'] = $pvInfo;
                //判断截止时间
                if ($v['vote_deadline'] != 0) {
                    if ($v['vote_deadline'] > time()) {
                        $list[$k]['vote_deadline'] = date('Y-m-d H:i', $v['vote_deadline']);
                    } else {
                        $list[$k]['vote_deadline'] = -1;//已截止
                        $list[$k]['is_vo_check'] = 1;
                    }
                }
                //判断是否有身份铭牌
                if (intval($v['uccid']) != 0) {
                    //查询佩戴的身份
                    $card_info = Db::name('camouflage_card')->where('id', $v['uccid'])->where('much_id', $data['much_id'])->find();
                    $user_card = Db::name('user_camouflage_card')->where('ccid', $v['uccid'])->where('user_id', $v['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                    $list[$k]['user_head_sculpture'] = $card_info['forgery_head'];
                    $list[$k]['user_id'] = 0;
                    //$list[$k]['user_nick_name']=$card_info['forgery_name'].'-'.$user_card['id'];
                    $list[$k]['user_nick_name'] = $card_info['forgery_name'];
                    $list[$k]['attr'] = '';
                } else {
                    $attr = $util->get_att_info($v['user_id'], $data['much_id']);
                    $list[$k]['attr'] = $attr;
                }
            }

            $rs['info'] = $list;
        } else {
            $rs['info'] = [];
        }
        $rs['version'] = $this->version;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取首页推荐的数据
     */
    public function get_index_tj_list()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $util = new Util();
        $user_track = Db::name('user_track')->where('at_user_id', $this->user_info['id'])->select();
        $user_arr = [];
        foreach ($user_track as $k => $v) {
            $user_arr[$k] = $v['qu_user_id'];
        }
        $user['user_track'] = formatNumber($user_track);

        $where = [];
        if ($this->version == 1) {
            $where['p.study_type'] = ['in', ['0', '1', '3']];
            $where['p.is_buy'] = ['eq', 0];
        }
        $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($authority['hair_video_arbor'] == 0) {
            $where['p.study_type'] = ['in', ['0', '1', '3']];
        }
        /**
         * 第一个版本低于第二个版本的时候 return -1
         * 第一个版本等于第二个版本的时候 return 0
         * 第一个版本高于第二个版本的时候 return 1
         */
        if (version_compare($data['version'], '1.0.36') == -1) {

            $where['p.is_buy'] = ['eq', 0];

        }

        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        $user_vip = $util->get_user_vip($this->user_info['id']);
        /**
         * 第一个版本低于第二个版本的时候 return -1
         * 第一个版本等于第二个版本的时候 return 0
         * 第一个版本高于第二个版本的时候 return 1
         */
        //查询我关注的圈子
        $user_trailing = Db::name('user_trailing')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
        $user_trailing_id = '';
        foreach ($user_trailing as $k => $v) {
            $user_trailing_id .= $v['tory_id'] . ",";
        }
        $user_trailing_id = substr($user_trailing_id, 0, -1);

        //查询有权限的圈子
        $q_tory = Db::name('territory')->where('status', 1)->where('much_id', $data['much_id'])->select();
        $q_tory_id = '';
        $vip_tory_id = '';
        $level_tory_id = '';
        foreach ($q_tory as $k => $v) {
            if ($v['attention'] == 1) {
                $q_tory_id .= $v['id'] . ",";
            }
            if ($v['attention'] == 2) {
                $vip_tory_id .= $v['id'] . ",";
            }
            if ($v['release_level'] > $this->user_info['level']) {
                $level_tory_id .= $v['id'] . ",";
            }

        }
        $q_tory_id = substr($q_tory_id, 0, -1);
        $vip_tory_id = substr($vip_tory_id, 0, -1);
        //等级
        $level_tory_id = substr($level_tory_id, 0, -1);
        $check_qq = 'no';
        if (isset($data['tory_id'])) {
            $where['p.tory_id'] = ['eq', $data['tory_id']];
            $check_qq = $util->check_qq($this->user_info['user_wechat_open_id'], $data['tory_id']);
        }

        if ($check_qq == 'no') {
            if ($user_vip == 0 && $vip_tory_id) {
                $q_tory_id = $q_tory_id . ',' . $vip_tory_id;
            }
            if ($level_tory_id && $cg == 0) {
                $q_tory_id = $q_tory_id . ',' . $level_tory_id;
            }
            if ($cg == 0) {
                $where['t.id'] = array('not in', $q_tory_id);
            }
        }


        //$where['p.study_type'] = ['in', ['0', '1']];


        if ($authority['home_random_arbor'] == 1) {
            $order = Db::raw('rand()');
        } else {
            $order = ['p.adapter_time' => 'desc'];
        }


        $where['p.essence_time'] = ['neq', 0];
        $page = $data['index_tj_page'];
        $list = Db::name('paper')->alias('p')
            ->join('user u', 'p.user_id=u.id')
            ->join('territory t', 'p.tory_id=t.id')
            ->where('p.whether_delete', '0')
            ->where('p.study_status', '1')
            ->where('p.much_id', $data['much_id'])
            ->where($where)
            ->where('t.status', 1)
            ->where('t.is_del', 0)
            ->field('p.uccid,p.video_type,p.study_video,p.third_part_vid,u.user_wechat_open_id,p.id,p.user_id,p.tory_id,p.tg_id,p.study_title,p.study_title_color,p.study_content,p.study_type,p.is_buy,p.topping_time,p.image_part,p.study_voice,p.study_voice_time,p.study_heat,p.study_laud,p.study_repount,p.adapter_time,p.vote_deadline,p.is_open,u.level,u.gender,u.wear_merit,u.user_nick_name,u.user_head_sculpture,t.realm_name')
            ->order($order)
            ->page($page, '15')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {
                $list[$k]['is_voice'] = false;
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['study_title'] = emoji_decode($v['study_title']);
                $modd = '/<div class="stealth_module".*>([\s\S]*)<\/div>/i';

                preg_match_all($modd, emoji_decode($v['study_content']), $moddUrl);

                if (count($moddUrl[1]) > 0) {
                    for ($i = 0; $i < count($moddUrl[1]); $i++) {
                        $moddHtml = "<div></div>";
                        $v['study_content'] = preg_replace($modd, $moddHtml, emoji_decode($v['study_content']), 1);
                    }
                    if (empty(strip_tags(emoji_decode($v['study_content'])))) {
                        $v['study_content'] = "<div>这里是隐藏文本，需要回复才能看到</div>";
                    }
                } else {
                    if ($v['is_buy'] == 1) {
                        $v['study_content'] = "<div>这里是隐藏文本，需要订阅才能看到</div>";
                    } else {
                        $v['study_content'] = emoji_decode($v['study_content']);
                    }
                }

                $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';

                preg_match_all($preg, emoji_decode($v['study_content']), $match);

                //$list[$k]['mmmmmmatch'] = $v['study_content'];
                if (!empty(json_decode($v['image_part'], true))) {
                    //$list[$k]['image_part_1'] = '1';
                    $list[$k]['image_part'] = json_decode($v['image_part']);
                } else {
                    //$list[$k]['image_part_1'] = '0';
                    //$list[$k]['image_part_2'] = $match;
                    if (!empty($match[0])) {
                        $img = array();
                        foreach ($match[1] as $a => $b) {
                            $img[$a] = htmlspecialchars_decode($b);
                        }
                        $list[$k]['image_part'] = $img;
                    } else {
                        $list[$k]['image_part'] = [];
                    }
                }
                $list[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));
                $list[$k]['study_heat'] = formatNumber($v['study_heat']);
                $list[$k]['study_laud'] = formatNumber($v['study_laud']);
                $list[$k]['study_repount'] = formatNumber($v['study_repount']);
                $list[$k]['user_vip'] = $util->get_user_vip($v['user_id']);
                $list[$k]['adapter_time'] = formatTime($v['adapter_time']);
                $list[$k]['study_voice'] = link_urldecode($v['study_voice']);
                $list[$k]['study_voice_time'] = s_to_hs($v['study_voice_time']);
                $list[$k]['starttime'] = '00:00';
                $list[$k]['index6_time'] = $this->getMyDate($v['adapter_time']);
                $list[$k]['adapter_time'] = formatTime($v['adapter_time']);
                $list[$k]['offset'] = 0;
                $list[$k]['max'] = $v['study_voice_time'];

                $sc = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
                $list[$k]['is_info_zan'] = $sc == 0 ? false : true;
                //$count = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('paper_id', $v['id'])->count();
                $count = $v['study_laud'];
                $list[$k]['info_zan_count'] = formatNumber($count);
                $list[$k]['info_zan_count_this'] = $count;
                //查询当前帖子是否是红包贴
                $red = Db::name('paper_red_packet')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                //查询是否购买了当前帖子
                $purchase = Db::name('paper_buy_user')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                $list[$k]['purchase'] = $purchase;
                $list[$k]['red'] = $red;
                //获取当前话题
                if ($v['tg_id'] != 0) {
                    $gambit = Db::name('gambit')->where('much_id', $data['much_id'])->where('id', $v['tg_id'])->find();
                    $list[$k]['gambit_name'] = emoji_decode(str_replace('#', '', $gambit['gambit_name']));
                    $list[$k]['gambit_id'] = $gambit['id'];
                }
                $list[$k]['level'] = $util->get_user_level($v['level'], $data['much_id'])['level_icon'];
                //当前用户的勋章
                $list[$k]['wear_merit'] = $util->get_medal($v['wear_merit'], $data['much_id']);
                //当前用户昵称
                $list[$k]['special'] = $util->get_user_special_nickname($v['user_id'], $data['much_id']);
                //当前用户头像框
                $list[$k]['avatar_frame'] = $util->get_user_avatar_frame($v['user_id'], $data['much_id']);
                //是否是投票贴
                $list[$k]['vo_id'] = [];
                //查询我是否投票了
                $list[$k]['is_vo_check'] = Db::name('user_vote')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                if ($list[$k]['is_vo_check'] > 0) {
                    $my_vo = Db::name('user_vote')->where('paper_id', $v['id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
                    foreach ($my_vo as $c => $d) {
                        array_push($list[$k]['vo_id'], intval($d['pv_id']));
                    }
                }
                $pvInfo = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->select();
                $pvInfoSum = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->sum('cheat_ballot');
                $pvPeopleCount = Db::name('user_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                $pvPeopleCountTotal = ($pvInfoSum + $pvPeopleCount);
                $list[$k]['vo_count'] = $pvPeopleCountTotal;
                for ($i = 0; $i < count($pvInfo); $i++) {
                    $voters = Db::name('user_vote')->where('paper_id', $v['id'])->where('pv_id', $pvInfo[$i]['id'])->where('much_id', $data['much_id'])->count();
                    $pvInfo[$i]['voters'] = (intval($pvInfo[$i]['cheat_ballot']) + $voters);
                    $ratio = (($pvInfo[$i]['voters'] / $pvPeopleCountTotal) * 100);
                    if (is_nan($ratio)) {
                        $pvInfo[$i]['ratio'] = 0;
                    } else {
                        $pvInfo[$i]['ratio'] = $ratio;
                    }
                }
                $list[$k]['vo'] = $pvInfo;
                //判断截止时间
                if ($v['vote_deadline'] != 0) {
                    if ($v['vote_deadline'] > time()) {
                        $list[$k]['vote_deadline'] = date('Y-m-d H:i', $v['vote_deadline']);
                    } else {
                        $list[$k]['vote_deadline'] = -1;//已截止
                        $list[$k]['is_vo_check'] = 1;
                    }
                }
                //判断是否有身份铭牌
                if (intval($v['uccid']) != 0) {
                    //查询佩戴的身份
                    $card_info = Db::name('camouflage_card')->where('id', $v['uccid'])->where('much_id', $data['much_id'])->find();
                    $user_card = Db::name('user_camouflage_card')->where('ccid', $v['uccid'])->where('user_id', $v['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                    $list[$k]['user_head_sculpture'] = $card_info['forgery_head'];
                    $list[$k]['user_id'] = 0;
                    //$list[$k]['user_nick_name']=$card_info['forgery_name'].'-'.$user_card['id'];
                    $list[$k]['user_nick_name'] = $card_info['forgery_name'];
                    $list[$k]['attr'] = '';
                } else {
                    $attr = $util->get_att_info($v['user_id'], $data['much_id']);
                    $list[$k]['attr'] = $attr;
                }
            }

            $rs['info'] = $list;
        } else {
            $rs['info'] = [];
        }
        $rs['version'] = $this->version;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取自己首页数据
     */
    public function get_my_list()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $util = new Util();
        $where = [];
        if ($this->version == 1) {
            $where['p.study_type'] = ['in', ['0', '1']];
            $where['p.is_buy'] = ['eq', 0];
        }
        $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($authority['hair_video_arbor'] == 0) {
            $where['p.study_type'] = ['in', ['0', '1']];
        }
        /**
         * 第一个版本低于第二个版本的时候 return -1
         * 第一个版本等于第二个版本的时候 return 0
         * 第一个版本高于第二个版本的时候 return 1
         */
        if (version_compare($data['version'], '1.0.36') == -1) {

            $where['p.is_buy'] = ['eq', 0];

        }
//        //查询我关注的圈子
//        $user_trailing = Db::name('user_trailing')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
//        $user_trailing_id = '';
//        foreach ($user_trailing as $k => $v) {
//            $user_trailing_id .= $v['tory_id'] . ",";
//        }
//        $user_trailing_id = substr($user_trailing_id, 0, -1);

        //查询当前用户是否是VIP
        $user_vip = $util->get_user_vip($this->user_info['id']);
        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        //查询有权限的圈子
        $q_tory = Db::name('territory')->where('status', 1)->where('much_id', $data['much_id'])->select();
        $q_tory_id = '';
        $vip_tory_id = '';
        $level_tory_id = '';
        foreach ($q_tory as $k => $v) {
            if ($v['attention'] == 1) {
                $user_trailing = Db::name('user_trailing')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->where('tory_id', $v['id'])->find();
                if (empty($user_trailing)) {
                    $q_tory_id .= $v['id'] . ",";
                }
            }
            if ($v['attention'] == 2) {
                $vip_tory_id .= $v['id'] . ",";
            }
            if ($v['visit_level'] > $this->user_info['level']) {
                $level_tory_id .= $v['id'] . ",";
            }

        }
        //return $level_tory_id;
        //权限
        $q_tory_id = substr($q_tory_id, 0, -1);
        //等级
        $level_tory_id = substr($level_tory_id, 0, -1);
        //vip
        $vip_tory_id = substr($vip_tory_id, 0, -1);


        if ($user_vip == 0 && $vip_tory_id && $cg == 0) {
            $q_tory_id = $q_tory_id . ',' . $vip_tory_id;
        }
        if ($level_tory_id && $cg == 0) {
            $q_tory_id = $q_tory_id . ',' . $level_tory_id;
        }
        if ($cg == 0) {
            $where['t.id'] = array('not in', $q_tory_id);
        }
        if ($this->user_info['id'] != $data['id']) {
            $where['p.uccid'] = ['eq', 0];
        }


        $page = $data['index_page'];
        $list = Db::name('paper')->alias('p')
            ->join('user u', 'p.user_id=u.id')
            ->join('territory t', 'p.tory_id=t.id')
            ->where('p.whether_delete', '0')
            ->where('p.study_status', '1')
            ->where('p.much_id', $data['much_id'])
            ->where('p.user_id', $data['id'])
            ->where('t.status', 1)
            ->where('t.is_del', 0)
            ->where($where)
            ->whereNotIn('t.id', $q_tory_id)
            ->field('p.uccid,p.id,p.user_id,p.tory_id,p.tg_id,p.study_title,p.study_title_color,p.study_content,p.study_type,p.is_buy,p.topping_time,p.image_part,p.study_voice,p.study_voice_time,p.study_heat,p.study_laud,p.study_repount,p.adapter_time,p.is_open,u.level,u.gender,u.wear_merit,u.user_nick_name,u.user_head_sculpture,t.realm_name')
            ->order('p.adapter_time desc')
            ->page($page, '15')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {
                $list[$k]['is_voice'] = false;
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['study_title'] = emoji_decode($v['study_title']);
                $modd = '/<div class="stealth_module".*>([\s\S]*)<\/div>/i';

                preg_match_all($modd, emoji_decode($v['study_content']), $moddUrl);

                if (count($moddUrl[1]) > 0) {
                    for ($i = 0; $i < count($moddUrl[1]); $i++) {
                        $moddHtml = "<div></div>";
                        $v['study_content'] = preg_replace($modd, $moddHtml, emoji_decode($v['study_content']), 1);
                    }
                    if (empty(strip_tags(emoji_decode($v['study_content'])))) {
                        $v['study_content'] = "<div>这里是隐藏文本，需要回复才能看到</div>";
                    }
                } else {
                    if ($v['is_buy'] == 1) {
                        $v['study_content'] = "<div>这里是隐藏文本，需要订阅才能看到</div>";
                    } else {
                        $v['study_content'] = emoji_decode($v['study_content']);
                    }
                }


                $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';

                preg_match_all($preg, emoji_decode($v['study_content']), $match);

                //$list[$k]['mmmmmmatch'] = $v['study_content'];
                if (!empty(json_decode($v['image_part'], true))) {
                    //$list[$k]['image_part_1'] = '1';
                    $list[$k]['image_part'] = json_decode($v['image_part']);
                } else {
                    //$list[$k]['image_part_1'] = '0';
                    //$list[$k]['image_part_2'] = $match;
                    if (!empty($match[0])) {
                        $img = array();
                        foreach ($match[1] as $a => $b) {
                            $img[$a] = htmlspecialchars_decode($b);
                        }
                        $list[$k]['image_part'] = $img;
                    } else {
                        $list[$k]['image_part'] = [];
                    }
                }
                $ling = count($list[$k]['image_part']);
                $util = new Util();
                $list[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));
                $list[$k]['study_heat'] = formatNumber($v['study_heat']);
                $list[$k]['study_laud'] = formatNumber($v['study_laud']);
                $list[$k]['study_repount'] = formatNumber($v['study_repount']);
                $list[$k]['user_vip'] = $util->get_user_vip($v['user_id']);
                $list[$k]['adapter_time'] = date('Y-m-d', $v['adapter_time']);

                $list[$k]['study_voice'] = link_urldecode($v['study_voice']);
                $list[$k]['study_voice_time'] = s_to_hs($v['study_voice_time']);
                $list[$k]['starttime'] = '00:00';
                $list[$k]['offset'] = 0;
                $list[$k]['max'] = $v['study_voice_time'];

                $list[$k]['study_content'] = Alternative::ExpressionHtml($list[$k]['study_content']);

                $sc = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
                $list[$k]['is_info_zan'] = $sc == 0 ? false : true;
//                $count = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('paper_id', $v['id'])->count();
                $count = $v['study_laud'];
                $list[$k]['info_zan_count'] = formatNumber($count);
                //查询当前帖子是否是红包贴
                $red = Db::name('paper_red_packet')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                //查询是否购买了当前帖子
                $purchase = Db::name('paper_buy_user')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                $list[$k]['purchase'] = $purchase;
                $list[$k]['red'] = $red;
                if ($v['tg_id'] != 0) {
                    $gambit = Db::name('gambit')->where('much_id', $data['much_id'])->where('id', $v['tg_id'])->find();
                    $list[$k]['gambit_name'] = emoji_decode(str_replace('#', '', $gambit['gambit_name']));
                    $list[$k]['gambit_id'] = $gambit['id'];
                }
                //是否是投票贴
                $list[$k]['vo_id'] = [];
                //查询我是否投票了
                $list[$k]['is_vo_check'] = Db::name('user_vote')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                if ($list[$k]['is_vo_check'] > 0) {
                    $my_vo = Db::name('user_vote')->where('paper_id', $v['id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
                    foreach ($my_vo as $c => $d) {
                        array_push($list[$k]['vo_id'], intval($d['pv_id']));
                    }
                }
                $pvInfo = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->select();
                $pvInfoSum = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->sum('cheat_ballot');
                $pvPeopleCount = Db::name('user_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                $pvPeopleCountTotal = ($pvInfoSum + $pvPeopleCount);
                $list[$k]['vo_count'] = $pvPeopleCountTotal;
                for ($i = 0; $i < count($pvInfo); $i++) {
                    $voters = Db::name('user_vote')->where('paper_id', $v['id'])->where('pv_id', $pvInfo[$i]['id'])->where('much_id', $data['much_id'])->count();
                    $pvInfo[$i]['voters'] = (intval($pvInfo[$i]['cheat_ballot']) + $voters);
                    $ratio = (($pvInfo[$i]['voters'] / $pvPeopleCountTotal) * 100);
                    if (is_nan($ratio)) {
                        $pvInfo[$i]['ratio'] = 0;
                    } else {
                        $pvInfo[$i]['ratio'] = $ratio;
                    }
                }
                $list[$k]['vo'] = $pvInfo;

            }


            $res = array();
            foreach ($list as $key => $val) {
                $res[$val['adapter_time']][] = $val;
            }
            $re = [];
            foreach ($res as $ke => $va) {
                $re[]['time'] = $ke;
                foreach ($re as $a => $v) {
                    foreach ($va as $key => $value) {
                        if ($v['time'] == $value['adapter_time']) {
                            $re[$a]['list'] = $va;
                        }
                    }
                }
            }
            foreach ($re as $k => $v) {
                $time = explode("-", $v["time"]);
                $re[$k]['month'] = numToWord($time[1]) . '月';
                $re[$k]['day'] = $time[2];
                $re[$k]['year'] = $time[0];
            }

            $rs['info'] = $re;
        } else {
            $rs['info'] = [];
        }
        return $this->json_rewrite($rs);
    }

    /**
     * 获取自己首页数据
     */
    public function get_my_list_sh()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');

        $where = [];
        if ($this->version == 1) {
            $where['p.study_type'] = ['in', ['0', '1']];
            $where['p.is_buy'] = ['eq', 0];
        }
        //查询我关注的圈子
        $user_trailing = Db::name('user_trailing')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
        $user_trailing_id = '';
        foreach ($user_trailing as $k => $v) {
            $user_trailing_id .= $v['tory_id'] . ",";
        }
        $user_trailing_id = substr($user_trailing_id, 0, -1);

        //查询有权限的圈子
        $q_tory = Db::name('territory')->whereNotIn('id', $user_trailing_id)->where('status', 1)->where('attention', 1)->where('much_id', $data['much_id'])->select();
        $q_tory_id = '';
        foreach ($q_tory as $k => $v) {
            $q_tory_id .= $v['id'] . ",";
        }
        $q_tory_id = substr($q_tory_id, 0, -1);

        $page = $data['index_page'];
        $list = Db::name('paper')->alias('p')
            ->join('user u', 'p.user_id=u.id')
            ->join('territory t', 'p.tory_id=t.id')
            ->where('p.whether_delete', '0')
            ->where('p.study_status', '0')
            ->where('p.much_id', $data['much_id'])
            ->where('p.user_id', $data['id'])
            ->where('t.status', 1)
            ->where('t.is_del', 0)
            ->where($where)
            ->whereNotIn('t.id', $q_tory_id)
            ->field('p.tg_id,p.id,p.user_id,p.tory_id,p.tg_id,p.study_title,p.study_title_color,p.study_content,p.study_type,p.is_buy,p.topping_time,p.image_part,p.study_voice,p.study_voice_time,p.study_heat,p.study_laud,p.study_repount,p.adapter_time,p.is_open,u.level,u.gender,u.wear_merit,u.user_nick_name,u.user_head_sculpture,t.realm_name')
            ->order('p.adapter_time desc')
            ->page($page, '15')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {
                $list[$k]['is_voice'] = false;
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['study_title'] = emoji_decode($v['study_title']);
                $list[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));
                $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
                preg_match_all($preg, emoji_decode($v['study_content']), $match);

                if (count($match[0]) != 0) {
                    $list[$k]['image_part'] = $match[1];
                } else {
                    $list[$k]['image_part'] = json_decode($v['image_part']);
                }

                $ling = count($list[$k]['image_part']);
                $util = new Util();
                $list[$k]['study_heat'] = formatNumber($v['study_heat']);
                $list[$k]['study_laud'] = formatNumber($v['study_laud']);
                $list[$k]['study_repount'] = formatNumber($v['study_repount']);
                $list[$k]['user_vip'] = $util->get_user_vip($v['user_id']);
                $list[$k]['adapter_time'] = date('Y-m-d', $v['adapter_time']);
                $list[$k]['study_voice'] = link_urldecode($v['study_voice']);
                $list[$k]['study_voice_time'] = s_to_hs($v['study_voice_time']);
                $list[$k]['starttime'] = '00:00';
                //$list[$k]['adapter_time'] = formatTime($v['adapter_time']);
                $list[$k]['offset'] = 0;
                $list[$k]['max'] = $v['study_voice_time'];

                $sc = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
                $list[$k]['is_info_zan'] = $sc == 0 ? false : true;
                //$count = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('paper_id', $v['id'])->count();
                $count = $v['study_laud'];
                $list[$k]['info_zan_count'] = formatNumber($count);
                //获取当前话题
//                if ($v['tg_id'] != 0) {
//                    $gambit = Db::name('gambit')->where('much_id', $data['much_id'])->where('id', $v['tg_id'])->find();
//                    $list[$k]['gambit_name'] = emoji_decode(str_replace('#','',$gambit['gambit_name']));
//                    $list[$k]['gambit_id'] = $gambit['id'];
//                }
            }


            $res = array();
            foreach ($list as $key => $val) {
                $res[$val['adapter_time']][] = $val;
            }
            $re = [];
            foreach ($res as $ke => $va) {
                $re[]['time'] = $ke;
                foreach ($re as $a => $v) {
                    foreach ($va as $key => $value) {
                        if ($v['time'] == $value['adapter_time']) {
                            $re[$a]['list'] = $va;
                        }
                    }
                }
            }
            foreach ($re as $k => $v) {
                $time = explode("-", $v["time"]);
                $re[$k]['month'] = numToWord($time[1]) . '月';
                $re[$k]['day'] = $time[2];
            }
            $rs['info'] = $re;
        } else {
            $rs['info'] = [];
        }
        return $this->json_rewrite($rs);
    }

    /**
     * 获取帖子详情
     */
    public function get_article_info()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $id = $data['id'];
        $cop = Db::name('authority')->where('much_id', $data['much_id'])->find();

        Db::name('paper')->where('id', $id)->where('much_id', $data['much_id'])->setInc('study_heat');
        $ad = Db::name('advertise')->where('much_id', $data['much_id'])->find();
        $pre_post_id = '';
        if ($ad['pre_post_twig'] == 1) {
            $pre_post_id = $ad['pre_post_id'];
        }
        $info = Db::name('paper')->alias('p')
            ->join('user u', 'p.user_id=u.id')
            ->join('territory t', 'p.tory_id=t.id')
            ->where('p.whether_delete', '0')
            ->where('p.id', $id)
            ->field('p.call_phone,p.buy_price_type,p.img_show_type,p.uccid,p.study_video_bulk,p.video_type,p.user_id,p.study_video,p.address_details,p.address_name,p.address_latitude,p.address_longitude,p.third_part_vid,p.id,p.buy_price,p.user_id,p.tory_id,p.tg_id,p.essence_time,p.topping_time,p.study_title,p.study_title_color,p.study_content,p.study_type,p.is_buy,p.topping_time,p.image_part,p.study_voice,p.study_voice_time,p.study_heat,p.study_laud,p.study_repount,p.adapter_time,p.is_open,p.vote_deadline,u.level,u.gender,u.wear_merit,u.user_nick_name,u.user_head_sculpture,t.realm_name,t.realm_icon,t.concern,t.id as realm_id')
            ->find();

        $util = new Util();
        $plug = new Plugunit();
        if ($info) {

            $info['user_vip'] = $util->get_user_vip($info['user_id']);
            //$info['adapter_time'] = formatTime($info['adapter_time']);
            $info['is_voice'] = false;
            $info['user_nick_name'] = emoji_decode($info['user_nick_name']);
            $info['study_title'] = emoji_decode($info['study_title']);

            if ($this->version == 1 || $cop['hair_video_arbor'] == 0) {
                $info['study_content'] = preg_replace('/<video.*?src=[\"|\']?(.*?)[\"|\']?\s.*?><\/video>/i', '', $info['study_content']);
              
            } else {

                $preg = '/<video.*?src=[\"|\']?(.*?)[\"|\']?\s.*?><\/video>/i';
                preg_match_all($preg, emoji_decode($info['study_content']), $videoUrl);
                if (count($videoUrl[1]) > 0) {
                    $info['study_content'] = preg_replace_callback($preg, function ($matches) use ($pre_post_id) {
                        $videoSrc = $matches[1];
                        $videoHtml = "<video unit='{$pre_post_id}' controls='controls' style='''><source unit='{$pre_post_id}' src='{$videoSrc}' type='video/mp4'></video>";
                        return $videoHtml;
                    }, $info['study_content']);
                } else {
                    $info['study_content'] = emoji_decode($info['study_content']);
                }
            }
  $info['study_content'] = emoji_decode($info['study_content']);
            //获取文本
            $info['chun_text'] = Alternative::ExpressionHtml(strip_tags(emoji_decode($info['study_content'])));;


            //获取当前用户是否是超管
            $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
            //查询是否购买了当前帖子
            $purchase = Db::name('paper_buy_user')->where('user_id', $this->user_info['id'])->where('paper_id', $id)->where('much_id', $data['much_id'])->count();


            //判断是否是 圈主，管理，超管，自己
            $check_look = 0;
            $da_xiao = $util->check_qq($data['openid'], $info['tory_id']);
            if ($da_xiao == 'da' || $da_xiao == 'xiao') { //是圈主或者管理
                $check_look = 1;
            }

            if ($cg == 1) {//超管
                $check_look = 1;

            }
            if ($info['user_id'] == $this->user_info['id']) {  //是自己发的
                $check_look = 1;
            }
            //return $this->user_info['id'];
            if ($purchase == 1) {//已经购买
                $check_look = 1;
            }
            if ($check_look == 0) {//没有购买
                $modd = '/<div class="stealth_module".*>([\s\S]*)<\/div>/i';
                preg_match_all($modd, emoji_decode($info['study_content']), $moddUrl);
                if (count($moddUrl[1]) > 0) {
                    //查询是否回复了该贴
                    if ($info['is_buy'] == 0) {
                        $check_repl = $util->reply_article($info['id'], $this->user_info['id'], $data['much_id']);
                        if ($check_repl > 0) {
                            $check_look = 1;
                        } else {
                            $info['chun_text'] = '请回复后查看！';
                        }
                    } else {
                        if ($info['study_type'] == 3) {
                            $check_look = 1;
                        }
                    }
                } else {
                    if ($info['is_buy'] == 0) {
                        $check_look = 1;
                    }
                }
            }
            if ($check_look == 0) {
                $info['chun_text'] = '暂无法查看！';
            }

            $rank = new RankingUtil();
            //查询当前内容是否有文件
            $file_info = '';
            $check_file = Db::name('netdisc_sell')->where('pa_id', $id)->where('much_id', $data['much_id'])->find();
            if (!empty($check_file)) {
                $file_info = Db::name('netdisc_belong')->where('id', $check_file['nb_id'])->where('much_id', $data['much_id'])->field('id,nc_id,user_id,file_name,is_dir,add_time,is_del')->find();
                if (!empty($file_info)) {
                    if ($file_info['is_dir'] == 1) {//如果是文件夹
                        $file_info['file_count'] = Db::name('netdisc_belong')
                            ->where('much_id', $data['much_id'])
                            ->where('parent_path_id', $check_file['nb_id'])
                            ->count();
                        $file_info['file_icon'] = $rank->fileTypeIcon('dir');
                    } else {
                        //查询文件是否已经被屏蔽
                        $netdisc = Db::name('netdisc')
                            ->where('much_id', $data['much_id'])
                            ->where('id', $file_info['nc_id'])
                            ->find();
                        $file_info['file_size'] = $this->setupSize($netdisc['file_size']);
                        $file_info['file_icon'] = $rank->fileTypeIcon($netdisc['file_suffix']);
                        $file_info['file_suffix'] = $netdisc['file_suffix'];
                        $file_info['suf'] = $rank->CheckfileTypeIcon($netdisc['file_suffix']);
                    }
                    $file_info['add_time'] = date('Y-m-d H:i:s', $file_info['add_time']);
                }

            }
            if ($check_look == 0) {
                $vg = '/<div class=\"stealth_module\">([\s\S]*?)<\/div>/i';
                $str = preg_replace($vg, '<div yl_id="0" class="stealth_module"></div>', emoji_decode($info['study_content']));
                $info['study_content'] = $str;
            }
//            //判断一键拨号  未开启  隐藏
            if (!$plug->check_plug('c2b0e5ea-90e6-16af-7086-5e095954cf05', $data['much_id'])) {
                $info['call_phone'] = '';
            }
            $info['forward_img'] = get_images_from_html($info['study_content']);
            $info['study_content'] = Alternative::ExpressionHtml($info['study_content']);
            $info['study_video_bulk'] = explode(",", $info['study_video_bulk']);
            $info['image_part'] = json_decode($info['image_part'], true);
            $info['is_voice'] = false;
            $info['study_heat'] = formatNumber($info['study_heat']);
            $info['study_repount'] = $util->get_reply_count($info['id'], $data['much_id']);
            $info['concern'] = formatNumber($info['concern']);
            $info['paper_number'] = formatNumber(Db::name('paper')->where('tory_id', $info['tory_id'])->where('study_status', 1)->where('whether_delete', 0)->where('much_id', $data['much_id'])->count());
            $sc = Db::name('user_collect')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $id)->count();
            $info['is_info_sc'] = $sc == 0 ? false : true;

            $zan = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('applaud_type', 0)->where('paper_id', $id)->count();
            $info['is_info_zan'] = $zan == 0 ? false : true;
            //如果是腾讯视频
            if ($info['video_type'] == 1) {
                $info['study_video'] = $this->getVideoInfo($info['third_part_vid']);
            }
            //判断解析
            if ($plug->check_plug('203421c1-ebc5-d393-96a4-424251758adb', $data['much_id'])) {
                if ($info['video_type'] == 2) {
                    $res = Alternative::GetAnalysis($info['third_part_vid'], $data['much_id'], 0);
                    $info['study_video'] = $res['video'];
                }

            }

            $count = Db::name('user_collect')->where('paper_id', $id)->count();
            $info['info_sc_count'] = formatNumber($count);
            $info['info_sc_count_this'] = $count;
            $count_zan = $info['study_laud'];
            $info['info_zan_count'] = formatNumber($count_zan);
            $info['info_zan_count_this'] = $info['study_laud'];

            $info['max'] = $info['study_voice_time'];
            $info['study_voice'] = link_urldecode($info['study_voice']);
            $info['study_voice_time'] = s_to_hs($info['study_voice_time']);
            $info['starttime'] = '00:00';
            $info['adapter_time'] = formatTime($info['adapter_time']);
            $info['offset'] = 0;

            //判断是否开启插件
            if ($plug->check_plug('6a51605b-70e8-642a-a55f-cb9c064cdee9', $data['much_id'])) {
                //查询设置
                $paper_review_config = Db::name('paper_review_config')->where('much_id', $data['much_id'])->find();
                //获取当前帖子的点评
                //如果当前用户不是超管
                $review_where = [];
                if ($cg == 0) {
                    $review_where['r.is_show'] = ['eq', '1'];
                }
                if ($info['user_id'] == $this->user_info['id']) {
                    $review_where['r.is_show'] = ['in', '0,1'];
                }
                $review_score = Db::name('paper_review_score')->alias('r')
                    ->join('user u', 'u.id=r.user_id')
                    ->where('r.pa_id', $info['id'])
                    ->where('r.much_id', $data['much_id'])
                    ->where($review_where)
                    ->where('r.audit_status', 1)
                    ->field('r.id,r.user_id,r.is_show,r.assess_score,r.assess_content,u.user_head_sculpture,u.user_nick_name')
                    ->limit('3')
                    ->select();
                //自动审核 0关闭 1开启
                // $is_auto_audit = empty($paper_review_config)?0:$paper_review_config['is_auto_audit'];
                //0圈主 1所有人
                $is_all_review = empty($paper_review_config) ? 0 : $paper_review_config['is_all_review'];
                $check_review = Db::name('paper_review_score')
                    ->where('pa_id', $info['id'])
                    ->where('user_id', $this->user_info['id'])
                    ->where('much_id', $data['much_id'])
                    ->find();
                if ($is_all_review == 0) {

                    if ($da_xiao == 'da') {
                        //查询我是否点评过
                        if (empty($check_review)) {
                            $info['is_review'] = 0;
                        } else {
                            $info['is_review'] = 1;
                        }
                    } else {
                        $review_score = '';
                        $info['is_review'] = 1;
                    }
                } else {
                    //查询我是否点评过
                    if (empty($check_review)) {
                        $info['is_review'] = 0;
                    } else {
                        $info['is_review'] = 1;
                    }
                }

            } else {
                $review_score = '';
                $info['is_review'] = 0;
            }
            if (!empty($review_score)) {
                foreach ($review_score as $k => $v) {
                    $review_score[$k]['assess_content'] = emoji_decode($v['assess_content']);
                    $review_score[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                }
            }
            $info['review_score'] = empty($review_score) ? '' : $review_score;
            //当前用户是不是圈主或者管理员
            $info['is_qq'] = $util->check_qq($data['openid'], $info['tory_id']);
            //检测当前帖子是否有红包
            $red = Db::name('paper_red_packet')
                ->where('paper_id', $id)
                ->where('much_id', $data['much_id'])
                ->field('initial_type,initial_conch,surplus_conch,initial_quantity,surplus_quantity,initial_fraction,surplus_fraction')
                ->find();
            if (!empty($red)) {
                //领取多少
                $red['surplus_quantity'] = $red['initial_quantity'] - $red['surplus_quantity'];
//                if($red['initial_type']==0){
//                    $red['surplus_conch'] = bcsub($red['initial_conch'], $red['surplus_conch'], 2);
//                }else{
//                    $red['surplus_fraction'] = bcsub($red['initial_fraction'], $red['surplus_fraction'], 2);
//                }

                $info['red'] = $red;
            }

            //$cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
            $info['admin'] = $cg;
            $info['version'] = $this->version;
            if ($info['tg_id'] != 0) {
                $gambit = Db::name('gambit')->where('much_id', $data['much_id'])->where('id', $info['tg_id'])->find();
                $info['gambit_name'] = emoji_decode(str_replace('#', '', $gambit['gambit_name']));
                //$info['gambit_name'] = emoji_decode($gambit['gambit_name']);
                $info['gambit_id'] = $gambit['id'];
                //$gambit_id = $gambit['id'];
                //$info['study_content'] = "<yuluo id='{$gambit_id}'>" . $info['gambit_name'] . "</yuluo>" . $info['study_content'];
            }
            //获取活动详情
            if ($info['study_type'] == 3) {
                $brisk_team = Db::name('brisk_team')->where('paper_id', $info['id'])->where('much_id', $data['much_id'])->find();

                if ($brisk_team['end_time'] < time()) {
                    $brisk_team['overdue'] = 2;  //已过期
                } else {
                    $brisk_team['overdue'] = 1;  //未过期
                }
                $brisk_team['start_time_y'] = $brisk_team['start_time'];
                $brisk_team['end_time_y'] = $brisk_team['end_time'];
                $brisk_team['start_time'] = date('Y年m月d日 H:i', $brisk_team['start_time']);
                $brisk_team['end_time'] = date('Y年m月d日 H:i', $brisk_team['end_time']);


                $user_head = Db::name('user_brisk_team')->alias('b')
                    ->join('user u', 'u.id=b.user_id')
                    ->where('b.brisk_id', $brisk_team['id'])
                    ->where('b.much_id', $data['much_id'])
                    ->field('u.user_head_sculpture')
                    ->limit(10)
                    ->select();

                $brisk_team['user_head'] = $user_head;
                //查询是否已参加活动
                $check = Db::name('user_brisk_team')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->where('paper_id', $info['id'])->where('brisk_id', $brisk_team['id'])->count();
                if ($check > 0) {
                    $brisk_team['overdue'] = 3;
                }
                //查询多少人参加了活动
                $brisk_count = Db::name('user_brisk_team')->where('much_id', $data['much_id'])->where('paper_id', $info['id'])->where('brisk_id', $brisk_team['id'])->count();
                $info['brisk_team'] = $brisk_team;
                $info['brisk_count'] = $brisk_count;
            } else {
                $info['brisk_team'] = [];
            }
            $info['level'] = $util->get_user_level($info['level'], $data['much_id'])['level_icon'];
            //当前用户的勋章
            $info['wear_merit'] = $util->get_medal($info['wear_merit'], $data['much_id']);


            //$info['study_content']= preg_replace( '/(<img.*?)(style=.+?[\'|"])|((width)=[\'"]+[0-9]+[\'"]+)|((height)=[\'"]+[0-9]+[\'"]+)/i', '$1' , $info['study_content']);
            //$info['study_content'] = str_replace("<img ", "<img style='width:100%!important;height:auto;display:block;' ", $info['study_content']);
            $info['check_look'] = $check_look;
            $info['tractate_font_size'] = Db::name('paper_smingle')->where('much_id', $data['much_id'])->value('tractate_font_size');
            $info['forum_declaration'] = Db::name('paper_smingle')->where('much_id', $data['much_id'])->value('forum_declaration');
            $info['is_show_forum_declaration'] = Db::name('paper_smingle')->where('much_id', $data['much_id'])->value('is_show_forum_declaration');
            //当前用户昵称
            $info['special'] = $util->get_user_special_nickname($info['user_id'], $data['much_id']);
            //当前用户头像框
            $info['avatar_frame'] = $util->get_user_avatar_frame($info['user_id'], $data['much_id']);

            $info['vo_id'] = [];
            //查询我是否投票了
            $info['is_vo_check'] = Db::name('user_vote')->where('user_id', $this->user_info['id'])->where('paper_id', $id)->where('much_id', $data['much_id'])->count();
            if ($info['is_vo_check'] > 0) {
                $my_vo = Db::name('user_vote')->where('paper_id', $id)->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
                foreach ($my_vo as $c => $d) {
                    array_push($info['vo_id'], intval($d['pv_id']));
                }
            }
            $pvInfo = Db::name('paper_vote')->where('paper_id', $id)->where('much_id', $data['much_id'])->select();
            $pvInfoSum = Db::name('paper_vote')->where('paper_id', $id)->where('much_id', $data['much_id'])->sum('cheat_ballot');
            $pvPeopleCount = Db::name('user_vote')->where('paper_id', $id)->where('much_id', $data['much_id'])->count();
            $pvPeopleCountTotal = ($pvInfoSum + $pvPeopleCount);
            $info['vo_count'] = $pvPeopleCountTotal;
            for ($i = 0; $i < count($pvInfo); $i++) {
                $voters = Db::name('user_vote')->where('paper_id', $id)->where('pv_id', $pvInfo[$i]['id'])->where('much_id', $data['much_id'])->count();
                $pvInfo[$i]['voters'] = (intval($pvInfo[$i]['cheat_ballot']) + $voters);
                $ratio = (($pvInfo[$i]['voters'] / $pvPeopleCountTotal) * 100);
                if (is_nan($ratio)) {
                    $pvInfo[$i]['ratio'] = 0;
                } else {
                    $pvInfo[$i]['ratio'] = $ratio;
                }
            }
            //判断截止时间
            if ($info['vote_deadline'] != 0) {
                if ($info['vote_deadline'] > time()) {
                    $info['vote_deadline'] = date('Y-m-d H:i', $info['vote_deadline']);
                } else {
                    $info['vote_deadline'] = -1;//已截止
                    $info['is_vo_check'] = 1;
                }
            }
            //判断是否有身份铭牌
            if (intval($info['uccid']) != 0) {
                if ($info['user_id'] == $this->user_info['id']) {
                    $info['user_id_time'] = 1;
                } else {
                    $info['user_id_time'] = 0;
                }
                //查询佩戴的身份
                $card_info = Db::name('camouflage_card')->where('id', $info['uccid'])->where('much_id', $data['much_id'])->find();
                $user_card = Db::name('user_camouflage_card')->where('ccid', $info['uccid'])->where('user_id', $info['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                $info['user_head_sculpture'] = $card_info['forgery_head'];
                $info['user_id'] = 0;
                //$info['user_nick_name']=$card_info['forgery_name'].'-'.$user_card['id'];
                $info['user_nick_name'] = $card_info['forgery_name'];
                $info['attest'] = '';
            }

            $info['vo'] = $pvInfo;
            $info['file_info'] = $file_info;
            //判断内容是否已不在
            if ($info['is_buy'] == 1 && $cop['pre_content_arbor'] == 1) {
                $httpCode = 0; //0.未知 1.正常 2.失效
                //  如果存在文字
                if (trim(strip_tags($info['study_content'])) != '') {
                    $httpCode = 1;
                }
                //  判断图片
                if (!empty($info['forward_img']) && $httpCode != 1) {
                    foreach ($info['forward_img'] as $k => $v) {
                        $headerCode = Gyration::_requestCode($v);
                        if (($headerCode >= 400 && $headerCode < 500) || $headerCode == 0) {
                            $httpCode = 2;
                        } else {
                            $httpCode = 1; // 1.正常
                            break;
                        }
                    }
                }
                //判断语音
                if (!empty($info['study_voice']) && $httpCode != 1) {
                    $headerCode = Gyration::_requestCode($info['study_voice']);
                    if (($headerCode >= 400 && $headerCode < 500) || $headerCode == 0) {
                        $httpCode = 2;
                    } else {
                        $httpCode = 1; //1.正常
                    }
                }
                //判断视频
                if (!empty($info['study_video']) && $httpCode != 1) {
                    $headerCode = Gyration::_requestCode($info['study_video']);
                    if (($headerCode >= 400 && $headerCode < 500) || $headerCode == 0) {
                        $httpCode = 2;
                    } else {
                        $httpCode = 1; //1.正常
                    }
                }
                $info['is_buy'] = $httpCode == 2 ? 0 : $info['is_buy'];
            }
            $info['expression'] = Alternative::GetEmoji();
            //判断是否是视频号
            if ($info['study_type'] == 6) {
                $videoHao = Db::name('paper_wechat_channel_video')->where('paper_id', $id)->where('much_id', $data['much_id'])->find();
                $info['feed_token'] = $videoHao['feed_token'];
            }
            $rs['info'] = $info;
        } else {
            $rs['status'] = 'error';
            $rs['msg'] = '该信息已被删除';
        }
        return $this->json_rewrite($rs);
    }

    /**
     * 回复列表
     */
    public function get_article_huifu()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $id = $data['id'];
        $page = $data['page'];
        $show_type = $data['show_type'];
        $where = [];
        $where['r.reply_status'] = ['eq', 1];
        //获取帖子uid
        $t_uid = Db::name('paper')->where('id', $id)->where('much_id', $data['much_id'])->find();
        if ($show_type == 'main') {
            $where['r.user_id'] = ['eq', $t_uid['user_id']];
        }
        if ($show_type == 'my') {
            $where['r.user_id'] = ['eq', $this->user_info['id']];
        }

        $pl = Db::name('paper_reply')->alias('r')
            ->join('user u', 'u.id=r.user_id')
            ->where('r.paper_id', $id)
            ->where('r.whether_delete', '0')
            ->where($where)
            ->where('r.whether_type', 0)
            ->field('r.uccid,r.id,r.paper_id,r.user_id,r.reply_type,r.is_gift,r.phase,r.reply_content,r.image_part,r.reply_voice,r.reply_voice_time,r.apter_time,r.prove_time,r.praise,u.gender,u.wear_merit,u.level,u.user_nick_name,u.user_head_sculpture,u.user_wechat_open_id')
            ->order('r.praise desc,r.phase')
            ->page($page, '5')
            ->select();
        foreach ($pl as $k => $v) {
            $pl[$k]['yinchang'] = 1;
            $pl[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
            $util = new Util();
            $pl[$k]['is_qq'] = $util->check_qq($v['user_wechat_open_id'], $util->get_user_applaud($v['id'])['tory_id']);
            $pl[$k]['user_wechat_open_id'] = null;
            $pl[$k]['user_vip'] = $util->get_user_vip($v['user_id']);
            $pl[$k]['reply_content'] = Alternative::ExpressionHtml(emoji_decode($pl[$k]['reply_content']));
            $pl[$k]['apter_time'] = formatTime($v['apter_time']);
            $pl[$k]['image_part'] = json_decode($v['image_part']);
            $pl[$k]['is_voice'] = false;
            $pl[$k]['is_paper_user'] = $util->get_page_user($id)['user_id'];
            $check_hf_zan = Db::name('user_applaud')->where('applaud_type', 1)->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->find();
            $pl[$k]['is_huifu_zan_count'] = formatNumber($v['praise']);
            if ($check_hf_zan > 0) {
                $pl[$k]['is_huifu_zan'] = true;
            } else {
                $pl[$k]['is_huifu_zan'] = false;
            }
            $pl[$k]['is_huifu_zan_count'] = formatNumber($pl[$k]['is_huifu_zan_count']);
            $hui_and_key = Db::name('paper_reply_duplex')->where('duplex_status', 1)
                ->where('whether_delete', 0)->where('reply_id', $v['id'])->where('duplex_status', 1)->count();
            $pl[$k]['huifu_count'] = $hui_and_key == 0 ? '' : formatNumber($hui_and_key);

            //评论回复
            $info_list = Db::name('paper_reply_duplex')->alias('r')
                ->join('user u', 'u.id=r.user_id')
                ->where('r.reply_id', $v['id'])
                ->where('r.duplex_status', 1)
                ->where('r.whether_delete', 0)
                ->limit(2)
                ->select();
            if (!empty($info_list)) {
                foreach ($info_list as $a => $b) {
                    $info_list[$a]['duplex_content'] = Alternative::ExpressionHtml(emoji_decode($b['duplex_content']));
                    $info_list[$a]['duplex_time'] = formatTime($b['duplex_time']);
                    $info_list[$a]['user_nick_name'] = emoji_decode($b['user_nick_name']);
                    if ($info_list[$a]['reply_user_id'] != 0) {
                        $user = Db::name('user')->where('id', $v['reply_user_id'])->where('much_id', $data['much_id'])->find();
                        $info_list[$a]['hui_nick_name'] = emoji_decode($user['user_nick_name']);
                    }
                    if (intval($b['uccid']) != 0) {
                        //查询佩戴的身份
                        $card_info_a = Db::name('camouflage_card')->where('id', $b['uccid'])->where('much_id', $data['much_id'])->find();
                        $info_list[$a]['user_head_sculpture'] = $card_info_a['forgery_head'];
                        $info_list[$a]['user_id'] = 0;
                        $info_list[$a]['user_nick_name'] = $card_info_a['forgery_name'];
                    }
                }
            }
            //回复是否有红包
            $res = Db::name('user_red_packet')->where('reply_id', $v['id'])->where('much_id', $data['much_id'])->find();
            if ($res) {
                if ($res['obtain_fraction'] == 0) {
                    $pl[$k]['is_red_hui'] = $res['obtain_conch'];
                } else {
                    $pl[$k]['is_red_hui'] = $res['obtain_fraction'];
                }
            } else {
                $pl[$k]['is_red_hui'] = 0;
            }
            $pl[$k]['huifu_huifu'] = $info_list;
            $pl[$k]['level_info'] = $util->get_user_level($v['level'], $data['much_id'])['level_icon'];
            //当前用户的勋章
            $pl[$k]['wear_merit'] = $util->get_medal($v['wear_merit'], $data['much_id']);
            //当前用户昵称
            $pl[$k]['special'] = $util->get_user_special_nickname($v['user_id'], $data['much_id']);
            $pl[$k]['avatar_frame'] = $util->get_user_avatar_frame($v['user_id'], $data['much_id']);

            //判断是否有身份铭牌
            if (intval($v['uccid']) != 0) {
                //查询佩戴的身份
                $card_info = Db::name('camouflage_card')->where('id', $v['uccid'])->where('much_id', $data['much_id'])->find();
                $user_card = Db::name('user_camouflage_card')->where('ccid', $v['uccid'])->where('user_id', $v['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                $pl[$k]['user_head_sculpture'] = $card_info['forgery_head'];
                $pl[$k]['user_id'] = 0;
                //$pl[$k]['user_nick_name']=$card_info['forgery_name'].'-'.$user_card['id'];
                $pl[$k]['user_nick_name'] = $card_info['forgery_name'];
            } else {
                $pl[$k]['attest'] = $util->get_att_info($v['user_id'], $data['much_id']);
            }
            if ($t_uid['user_id'] == $v['user_id']) {
                if ($t_uid['uccid'] != 0) {
                    $pl[$k]['yinchang'] = 0;
                }
            }

        }

        $hui_count = Db::name('paper_reply')->where('reply_status', 1)->where('whether_delete', 0)->where('paper_id', $id)->count();
        $rs['huifu_count'] = formatNumber($hui_count);
        $rs['huifu'] = $pl;
        return $this->json_rewrite($rs);
    }

    /**
     * 回复帖子
     */
    public function add_paper_reply_new()
    {
        $data = input('param.');
        // $paper = Db::name('paper_red_packet')->where('paper_id', $data['id'])->where('much_id', $data['much_id'])->find();
//        if ($paper['initial_type'] == 1) {
//            return $this->add_paper_reply();
//        }
        $rs = ['status' => 'success', 'msg' => '回复成功！'];

        if ($this->user_info['tourist'] == 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '请登陆后回复！']);
        }
        //获取是否打开强制手机号
        $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($this->version == 0) {
            if ($authority['re_force_phone_arbor'] == 1 && empty($this->user_info['user_phone'])) {
                return $this->json_rewrite(['status' => 'error', 'code' => 1, 'id' => $this->version, 'msg' => '请绑定手机号！']);
            }
        }
        $util = new Util();
        if (!empty($data['text'])) {
            $check_title = $util->get_check_msg($data['text'], $data['much_id'], $data['openid']);
            if ($check_title['status'] == 'error') {
                return $this->json_rewrite($check_title);
            }
        }

        //帖子详情
        $fa_info = $util->get_page_user($data['id']);
        //查询是否超出回复数
        if ($this->paper_smingle['reply_number_limit'] != 0 && $this->user_info['id'] != $fa_info['user_id']) {
            //查询今日这个帖子该用户回复数量
            $paper_hui_count = Db::name('paper_reply')->where('paper_id', $data['id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->whereTime('apter_time', 'today')->count();
            if ($paper_hui_count >= $this->paper_smingle['reply_number_limit']) {
                $rs = ['status' => 'error', 'msg' => '您已超出每日单个帖子用户最多回复次数'];
                return $this->json_rewrite($rs);
            }
        }


        //是否开启自动审核//1开启0关闭
        if ($this->paper_smingle['reply_auto_review'] == 0) {
            $ins['reply_status'] = 0;
        } else {
            $ins['reply_status'] = 1;
            $ins['prove_time'] = time();
        }


        //获取帖子在圈子
        $chech_tory_id = Db::name('paper')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();

        $check_banned = Db::name('user_banned')->where('tory_id', $chech_tory_id['tory_id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        if ($check_banned['refer_time'] > time()) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '您已被禁言，解除时间:' . date('Y-m-d H:i:s', $check_banned['refer_time'])]);
        }

        //判断身份铭牌
        if (isset($data['name_card']) && intval($data['name_card']) != 0) {
            //判断当前是否是否过期
            $check_name_card = Db::name('user_camouflage_card')
                ->where('ccid', $data['name_card'])
                ->where('user_id', $this->user_info['id'])
                ->where('much_id', $data['much_id'])
                ->where('expired_time', '> time', date('Y-m-d H:i:s', time()))
                ->find();
            if (empty($check_name_card)) {
                return $this->json_rewrite(['status' => 'name_error', 'msg' => '身份铭牌已过期！']);
            } else {
                $ins['uccid'] = $data['name_card'];
            }
        } else {
            $ins['uccid'] = 0;
        }

        if ($authority['engrave_arbor'] == 0) {
            $paper['uccid'] = 0;
        }

        $ins['paper_id'] = $data['id'];
        $ins['user_id'] = $this->user_info['id'];
        $ins['reply_type'] = $data['reply_type'];
        if ($data['reply_type'] == 1) {
            $ins['reply_voice'] = $data['file'];
            if (intval($data['file_ss']) < 3) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '录音时间太短了！']);
            }
            $ins['reply_voice_time'] = $data['file_ss'];
        } else {
            $a[] = $data['img_arr'];
            $ins['image_part'] = json_encode($a);;
        }
        $phase = Db::name('paper_reply')->where('much_id', $data['much_id'])->where('paper_id', $data['id'])->max('phase');
        if ($phase == 0) {
            $ins['phase'] = 2;
        } else {
            $ins['phase'] = $phase + 1;
        }

        $ins['reply_content'] = $this->safe_html(emoji_encode($data['text']));

        $ins['apter_time'] = time();
        $ins['much_id'] = $data['much_id'];
        $add = Db::name('paper_reply')->insertGetId($ins);
        if ($add) {
            Db::name('paper')->where('much_id', $data['much_id'])->where('id', $data['id'])->setInc('study_repount');
            $rs['id'] = $add;
        } else {
            $rs['status'] = 'error';
            $rs['msg'] = '回复失败';
            return $this->json_rewrite($rs);
        }
        //是否开启自动审核//1开启0关闭
        if ($this->paper_smingle['reply_auto_review'] == 0) {
            Db::name('prompt_msg')->insert(['capriole' => 5, 'tyid' => 0, 'msg_time' => time(), 'type' => 0, 'retter' => '用户：' . $this->user_info['user_nick_name'] . '发布了一条回复待审核！', 'status' => 0, 'much_id' => $data['much_id']]);
            $notices = Db::name('prompt_msg')
                ->where('status', 0)
                ->where('type', 0)
                ->where('much_id', $data['much_id'])
                ->count('*');
            cache('notices_' . $data['much_id'], $notices);
            return $this->json_rewrite(['status' => 'success', 'msg' => '回复成功,请等待审核！']);
        }
        //if ($this->user_info['id'] != $chech_tory_id['user_id']) {
        $con = emoji_decode($chech_tory_id['study_title'] == '' ? $chech_tory_id['study_content'] : $chech_tory_id['study_title']);
        if ($ins['uccid'] != 0) {
            $card_info_a = Db::name('camouflage_card')->where('id', $ins['uccid'])->where('much_id', $data['much_id'])->find();
            $user_card_info = $card_info_a['forgery_name'];
        } else {
            $user_card_info = $this->user_info['user_nick_name'];
        }
        $msg = '用户【' . $user_card_info . '】，评论了您的帖子[' . strip_tags($con) . ']';
        $util->add_user_smail($chech_tory_id['user_id'], emoji_encode($msg), $data['much_id'], '3', $data['id'] . '&type=' . $chech_tory_id['study_type']);
        //}
        //用户详情
        $user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();

        //回复详情
        $hui_info = $util->paper_reply($add);
        //查询今日帖子回复数量
        //$paper_hui_count = Db::name('paper_reply')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->whereTime('apter_time', 'today')->count();
        // 启动事务
        Db::startTrans();
        try {
            $zong_money = 0;
            //查询回复积分
            //查询当前帖子是否是红包帖子
            $paper = Db::name('paper_red_packet')->where('paper_id', $data['id'])->where('much_id', $data['much_id'])->find();
            if (!empty($paper)) {

                //红包数量不等于0
                if ($paper['surplus_quantity'] != 0) {
                    //拼手气红包
                    if ($paper['red_type'] == 1) {
                        $red = new RedPaper();
                        if ($paper['initial_type'] == 0) {
                            $red->amount = $paper['surplus_conch'];
                        } else {
                            $red->amount = $paper['surplus_fraction'];
                        }

                        $red->num = $paper['surplus_quantity'];
                        $red->paper_min = 0.01;
                        $get_money = $red->handle()['items'][0];
                    }
                    //普通红包
                    if ($paper['red_type'] == 0) {
                        if ($paper['initial_type'] == 0) {
                            $get_money = $this->rob_red_avg($paper['surplus_conch'], $paper['surplus_quantity']);
                        } else {
                            $get_money = $this->rob_red_avg($paper['surplus_fraction'], $paper['surplus_quantity']);
                        }

                    }
                    //Db::rollback();
                    //return $this->json_rewrite($get_money);
                    //查询当前用户今天回复次数
                    //$user_red_packet = Db::name('user_red_packet')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->whereTime('obtain_time', 'today')->count();
                    //回复获得系统积分，今日获得积分次数小于，系统
                    //查询是否已经领过这个红包了
                    $check_paer_hui = Db::name('user_red_packet')->where('red_packet_id', $paper['id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
                    //没领过
                    if (empty($check_paer_hui)) {
                        $packet['user_id'] = $this->user_info['id'];
                        $packet['red_packet_id'] = $paper['id'];
                        $packet['obtain_conch'] = $get_money;
                        $packet['obtain_time'] = time();
                        $packet['much_id'] = $data['much_id'];
                        $packet['reply_id'] = $add;
                        $packet_res = Db::name('user_red_packet')->insert($packet);
                        if (!$packet_res) {
                            Db::rollback();
                            $rs = ['status' => 'error', 'msg' => '回复失败！4'];
                            return $this->json_rewrite($rs);
                        }
                        //增加贝壳表数据
                        $amount['user_id'] = $this->user_info['id'];
                        $amount['category'] = 3;
                        $amount['finance'] = $get_money;
                        $amount['ruins_time'] = time();
                        $amount['solution'] = '回帖红包奖励';
                        $amount['evaluate'] = $paper['initial_type'];
                        $amount['much_id'] = $data['much_id'];

                        if ($paper['initial_type'] == 0) {
                            $amount['poem_fraction'] = $user_info['fraction'];
                            $amount['surplus_fraction'] = $user_info['fraction'];
                            $amount['poem_conch'] = $user_info['conch'];
                            $amount['surplus_conch'] = bcadd($user_info['conch'], $get_money, 2);
                        } else {

                            $amount['poem_fraction'] = $user_info['fraction'];
                            $amount['surplus_fraction'] = bcadd($user_info['fraction'], $get_money, 2);;

                            $amount['poem_conch'] = $user_info['conch'];
                            $amount['surplus_conch'] = $user_info['conch'];
                        }
                        $amount_res = Db::name('user_amount')->insert($amount);
                        if (!$amount_res) {
                            Db::rollback();
                            $rs = ['status' => 'error', 'msg' => '回复失败！3'];
                            return $this->json_rewrite($rs);
                        } else {
                            $rs = ['info' => $hui_info, 'status' => 'success', 'msg' => '回复成功'];
                        }
                        //$zong_money += $get_money;
                        //更新红包数据
                        $packet_c['surplus_quantity'] = $paper['surplus_quantity'] - 1;
                        if ($paper['initial_type'] == 0) {
                            $packet_c['surplus_conch'] = bcsub($paper['surplus_conch'], $get_money, 2);
                        } else {
                            $packet_c['surplus_fraction'] = bcsub($paper['surplus_fraction'], $get_money, 2);

                        }
                        $packet_c_res = Db::name('paper_red_packet')->where('id', $paper['id'])->where('much_id', $data['much_id'])->update($packet_c);
                        if (!$packet_c_res) {
                            Db::rollback();
                            $rs = ['status' => 'error', 'msg' => '回复失败！1'];
                            return $this->json_rewrite($rs);
                        }
                        //用户增加贝壳
                        if ($paper['initial_type'] == 0) {
                            $uop['conch'] = $amount['surplus_conch'];
                        } else {
                            $uop['fraction'] = $amount['surplus_fraction'];
                        }
                        $user_j_res = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update($uop);
                        if (!$user_j_res) {
                            Db::rollback();
                            $rs = ['status' => 'error', 'msg' => '回复失败！6'];
                            return $this->json_rewrite($rs);
                        }


                    }

                }
            }

            //帖子标题
            $page_title = $fa_info['study_title'] == '' ? '' : subtext($fa_info['study_title'], 10);
            //回复详情
            $hui_title = subtext($hui_info['reply_content'], 10);

            if (empty($hui_info['reply_content'])) {
                if ($hui_info['reply_type'] == 0) {
                    $hui_title = '[一张图片]';
                }
                if ($hui_info['reply_type'] == 1) {
                    $hui_title = '[一段语音]';
                }
            }

            if (empty($page_title)) {
                if ($fa_info['study_type'] == 0) {
                    $page_title = '[图片帖子]';
                }
                if ($fa_info['study_type'] == 1) {
                    $page_title = '[语音帖子]';
                }
                if ($fa_info['study_type'] == 2) {
                    $page_title = '[视频帖子]';
                }
            }
//            if($ins['uccid']!=0){
//                $card_info_a=Db::name('camouflage_card')->where('id',$ins['uccid'])->where('much_id',$data['much_id'])->find();
//                //$user_card_b=Db::name('user_camouflage_card')->where('ccid',$ins['uccid'])->where('user_id',$user_info['id'])->order('expired_time desc')->where('much_id',$data['much_id'])->find();
//                //$user_info['user_nick_name']=$card_info_a['forgery_name'].'-'.$user_card_b['id'];
//                $user_info['user_nick_name']=$card_info_a['forgery_name'];
//            }
            //发送模版
            if (empty($user_card_info)) {
                $user_card_info = $this->user_info['user_nick_name'];
            }
            $c = $util->add_template(['much_id' => $data['much_id'],
                'at_id' => 'YL0001',
                'user_id' => $fa_info['user_id'],
                'page' => 'yl_welore/pages/packageA/article/index?id=' . $fa_info['id'] . '&type=' . $fa_info['study_type'],
                'keyword1' => empty(strip_tags(emoji_decode($page_title))) ? '暂无标题' : strip_tags(emoji_decode($page_title)),
                'keyword2' => emoji_decode($user_card_info),
                'keyword3' => $hui_title,
                'keyword4' => date('Y年m月d日 H:i:s', time()),
            ]);
            // 提交事务
            Db::commit();
            // 新人营销
            if ($this->paper_smingle['reply_auto_review'] == 1) {
                $task = $util->new_user_task(['uid' => $this->user_info['id'], 'key' => 2, 'tory_id' => 0, 'paper_id' => $ins['paper_id'], 'much_id' => $data['much_id']]);
                if ($task['code'] == 0) {
                    $rs = ['status' => 'success', 'msg' => $task['msg']];
                } else if ($task['code'] == 1) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '回复失败，请稍候重试！7']);
                }
            }
            Cache::clear("globalIndexCache_" . $data['much_id']);
            return $this->json_rewrite($rs);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $rs = ['status' => 'error', 'msg' => '回复失败！' . $e->getMessage()];
            return $this->json_rewrite($rs);
        }

    }

    /**
     * 普通红包
     */
    public function rob_red_avg($sum, $num)
    {
        $res = $sum / $num;
        for ($i = 0; $i < $num; $i++) {
            $arr[$i] = $res;
        }
        //check($arr);
        return $arr[0];
    }

    /**
     * 获取需要审核的帖子
     */
    public function get_index_list_admin()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');

        $page = $data['index_page'];

        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg == 0) {
            $rs = ['status' => 'error', 'msg' => '您不是超管哦！'];
            return $this->json_rewrite($rs);
        }
        $list = Db::name('paper')
            ->alias('p')
            ->join('user u', 'p.user_id=u.id')
            ->join('territory t', 'p.tory_id=t.id')
            ->join('paper_reply r', 'r.paper_id=p.id', 'LEFT')
            ->where('p.whether_delete', '0')
            ->where('p.study_status', '0')
            ->where('p.much_id', $data['much_id'])
            ->where('t.status', 1)
            ->field('p.*,u.gender,u.level,u.user_nick_name,u.user_head_sculpture,t.realm_name,t.realm_icon,u.user_wechat_open_id,max(r.apter_time) as huifu_time')
            ->group('p.id')
            ->order('p.adapter_time', 'asc')
            ->page($page, '10')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {
                $util = new Util();
                if (isset($data['tory_id'])) {//判断是否是 圈主或者管理员

                    $list[$k]['check_qq'] = $util->check_qq($v['user_wechat_open_id'], $v['tory_id']);
                }
                $list[$k]['user_wechat_open_id'] = null;
                if (isset($v['huifu_time'])) {//判断是否是 圈主或者管理员
                    $list[$k]['huifu_time'] = formatTime($v['huifu_time']);
                }

                //计算全部回复人数
                $huifu_all_count = Db::name('paper_reply')->where('paper_id', $v['id'])->where('whether_delete=0')->where('much_id', $data['much_id'])->count();

                $huifu_hui_count = Db::name('paper_reply')->alias('p')
                    ->join('paper_reply_duplex r', 'r.reply_id=p.id')
                    ->where('p.whether_delete=0')
                    ->where('p.much_id', $data['much_id'])
                    ->where('p.paper_id', $v['id'])
                    ->count();

                $list[$k]['is_voice'] = false;
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['study_title'] = emoji_decode($v['study_title']);
                $list[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));

                $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
                preg_match_all($preg, emoji_decode($v['study_content']), $match);


                if (!empty(json_decode($v['image_part'], true))) {
                    //$list[$k]['image_part_1'] = '1';
                    $list[$k]['image_part'] = json_decode($v['image_part']);
                } else {
                    //$list[$k]['image_part_1'] = '0';
                    //$list[$k]['image_part_2'] = $match;
                    if (!empty($match[0])) {
                        $list[$k]['image_part'] = $match[1];
                    } else {
                        $list[$k]['image_part'] = [];
                    }
                }

                $list[$k]['study_content'] = Alternative::ExpressionHtml($list[$k]['study_content']);
                //$ling = count(json_decode($list[$k]['image_part']));

                $list[$k]['study_heat'] = formatNumber($v['study_heat']);
                $list[$k]['study_laud'] = formatNumber($v['study_laud']);
                $list[$k]['study_repount'] = formatNumber($huifu_hui_count + $huifu_all_count);
                $list[$k]['user_vip'] = $util->get_user_vip($v['user_id']);
                $list[$k]['adapter_time'] = formatTime($v['adapter_time']);

                $sc = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
                $list[$k]['is_info_zan'] = $sc == 0 ? false : true;
                //$count = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('paper_id', $v['id'])-
                $count = $v['study_laud'];
                $list[$k]['info_zan_count'] = formatNumber($count);
                $list[$k]['info_zan_count_this'] = $count;
                //查询当前帖子是否是红包贴
                $red = Db::name('paper_red_packet')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                //查询是否购买了当前帖子
//                $purchase = Db::name('paper_buy_user')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
//                $list[$k]['purchase'] = $purchase;
                $list[$k]['red'] = $red;
                $list[$k]['level'] = $util->get_user_level($v['level'], $data['much_id'])['level_icon'];
                //是否是投票贴
                $list[$k]['vo_id'] = [];
                //查询我是否投票了
                $list[$k]['is_vo_check'] = Db::name('user_vote')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                if ($list[$k]['is_vo_check'] > 0) {
                    $my_vo = Db::name('user_vote')->where('paper_id', $v['id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
                    foreach ($my_vo as $c => $d) {
                        array_push($list[$k]['vo_id'], intval($d['pv_id']));
                    }
                }
                $pvInfo = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->select();
                $pvInfoSum = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->sum('cheat_ballot');
                $pvPeopleCount = Db::name('user_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                $pvPeopleCountTotal = ($pvInfoSum + $pvPeopleCount);
                $list[$k]['vo_count'] = $pvPeopleCountTotal;
                for ($i = 0; $i < count($pvInfo); $i++) {
                    $voters = Db::name('user_vote')->where('paper_id', $v['id'])->where('pv_id', $pvInfo[$i]['id'])->where('much_id', $data['much_id'])->count();
                    $pvInfo[$i]['voters'] = (intval($pvInfo[$i]['cheat_ballot']) + $voters);
                    $ratio = (($pvInfo[$i]['voters'] / $pvPeopleCountTotal) * 100);
                    if (is_nan($ratio)) {
                        $pvInfo[$i]['ratio'] = 0;
                    } else {
                        $pvInfo[$i]['ratio'] = $ratio;
                    }
                }
                $list[$k]['vo'] = $pvInfo;
                //判断截止时间
                if ($v['vote_deadline'] != 0) {
                    if ($v['vote_deadline'] > time()) {
                        $list[$k]['vote_deadline'] = date('Y-m-d H:i', $v['vote_deadline']);
                    } else {
                        $list[$k]['vote_deadline'] = -1;//已截止
                        $list[$k]['is_vo_check'] = 1;
                    }
                }
            }
            $rs['info'] = $list;
        } else {
            $rs['info'] = [];
        }
        $rs['version'] = $this->version;
        return $this->json_rewrite($rs);
    }

    /**
     * 超管审核帖子
     */
    public function set_paper_status()
    {
        $data = input('param.');
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg == 0) {
            $rs = ['status' => 'error', 'msg' => '您不是超管哦！'];
            return $this->json_rewrite($rs);
        }
        $util = new Util();
        $paper_info = $util->get_paper($data['paper_id']);
        // 启动事务
        Db::startTrans();
        try {
            $dd['study_status'] = $data['key'];
            $dd['prove_time'] = time();
            $dd['adapter_time'] = time();
            $dd['reject_reason'] = empty($data['reject_reason']) ? NULL : $data['reject_reason'];
            $res = Db::name('paper')->where('id', $data['paper_id'])->where('much_id', $data['much_id'])->update($dd);
            if (!$res) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '审核失败！']);
            }
            if ($data['key'] == 1) {
                $maringText = '您的发帖已通过审核！';
            } else {
                $maringText = '您的发帖未通过审核！';
            }

            Db::name('user_smail')->insert(['user_id' => $paper_info['user_id'], 'maring' => $maringText, 'clue_time' => time(), 'status' => 0, 'much_id' => $data['much_id']]);
            // 提交事务
            Db::commit();
            $tmplData = [
                'much_id' => $data['much_id'],
                'at_id' => 'YL0009',
                'user_id' => $paper_info['user_id'],
                'page' => 'yl_welore/pages/user_smail/index',
                'keyword1' => $maringText,
                'keyword2' => date('Y年m月d日 H:i:s', time())
            ];
            $tmplService = new TmplService();
            $tmplService->add_template($tmplData);
            if ($data['key'] == 1) {
                $task = $util->new_user_task(['uid' => $paper_info['user_id'], 'key' => 1, 'tory_id' => $paper_info['tory_id'], 'paper_id' => $paper_info['id'], 'much_id' => $data['much_id']]);
                if ($task['code'] == 0) {
                    return $this->json_rewrite(['status' => 'success', 'msg' => '审核成功！' . $task['msg']]);
                } else if ($task['code'] == 1) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '审核失败，请稍候重试！4']);
                }
            }
            Cache::clear("globalIndexCache_" . $data['much_id']);
            return $this->json_rewrite(['status' => 'success', 'msg' => '审核成功！']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'msg' => '审核失败！']);
        }
    }

    /**
     * 获取需要审核的评论回复
     */
    public function get_ping_hui_list_admin()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');

        $page = $data['ping_hui_page'];

        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg == 0) {
            $rs = ['status' => 'error', 'msg' => '您不是超管哦！'];
            return $this->json_rewrite($rs);
        }
        $list = Db::name('paper_reply_duplex')
            ->where('much_id', $data['much_id'])
            ->where('whether_delete', 0)
            ->where('duplex_status', 0)
            ->page($page, '10')
            ->field('user_id,id,duplex_content,duplex_time,duplex_status')
            ->select();
        if ($list) {
            $util = new Util();
            foreach ($list as $k => $v) {

                $list[$k]['user_nick_name'] = emoji_decode($util->get_uid_info($v['user_id'])['user_nick_name']);
                $list[$k]['user_head_sculpture'] = $util->get_uid_info($v['user_id'])['user_head_sculpture'];
                $list[$k]['duplex_content'] = emoji_decode($v['duplex_content']);
                $list[$k]['duplex_time'] = formatTime($v['duplex_time']);
                $list[$k]['duplex_content'] = Alternative::ExpressionHtml($list[$k]['duplex_content']);
            }
            $rs['info'] = $list;
        } else {
            $rs['info'] = [];
        }
        $rs['version'] = $this->version;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取需要审核的回复
     */
    public function get_hui_list_admin()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');

        $page = $data['hui_page'];

        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg == 0) {
            $rs = ['status' => 'error', 'msg' => '您不是超管哦！'];
            return $this->json_rewrite($rs);
        }
        $list = Db::name('paper_reply')
            ->alias('p')
            ->join('paper a', 'a.id=p.paper_id')
            ->join('user u', 'p.user_id=u.id')
            ->where('p.reply_status', '0')
            ->where('p.whether_delete', '0')
            ->where('a.whether_delete', 0)
            ->where('p.much_id', $data['much_id'])
            ->order('p.apter_time')
            ->page($page, '10')
            ->field('p.*,u.user_nick_name,u.user_head_sculpture,u.level,a.study_type')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {
                $util = new Util();
                $list[$k]['is_voice'] = false;
                $list[$k]['image_part'] = empty(json_decode($v['image_part'], true)[0]) ? '' : json_decode($v['image_part'], true)[0];
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['reply_content'] = emoji_decode($v['reply_content']);
                $list[$k]['user_vip'] = $util->get_user_vip($v['user_id']);
                $list[$k]['apter_time'] = formatTime($v['apter_time']);
                $list[$k]['level'] = $util->get_user_level($v['level'], $data['much_id'])['level_icon'];
                $list[$k]['reply_content'] = Alternative::ExpressionHtml($list[$k]['reply_content']);
                $list[$k]['reply_voice_time'] = s_to_hs($v['reply_voice_time']);
                $list[$k]['starttime'] = '00:00';
                $list[$k]['offset'] = 0;
                $list[$k]['max'] = $v['reply_voice_time'];
            }
            $rs['info'] = $list;
        } else {
            $rs['info'] = [];
        }
        $rs['version'] = $this->version;
        return $this->json_rewrite($rs);
    }

    /**
     * 超管审核评论回复
     */
    public function set_ping_hui_status()
    {
        $data = input('param.');
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg == 0) {
            $rs = ['status' => 'error', 'msg' => '您不是超管哦！'];
            return $this->json_rewrite($rs);
        }
        $util = new Util();
        $duplex = Db::name('paper_reply_duplex')->where('id', $data['ping_id'])->find();
        // 启动事务
        Db::startTrans();
        try {
            if ($data['key'] == 1) {
                $maringText = '您的回复[' . $duplex['duplex_content'] . ']已通过审核！';
            } else {
                $maringText = '您的回复[' . $duplex['duplex_content'] . ']未通过审核！';
            }
            $dd['duplex_status'] = $data['key'];
            $dd['check_opinion'] = empty($data['reject_reason']) ? $maringText : emoji_encode($data['reject_reason']);
            $res = Db::name('paper_reply_duplex')->where('id', $data['ping_id'])->where('much_id', $data['much_id'])->update($dd);
            if (!$res) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '审核失败！']);
            }

            Db::name('user_smail')->insert(['user_id' => $duplex['user_id'], 'maring' => $maringText, 'clue_time' => time(), 'status' => 0, 'much_id' => $data['much_id']]);
            // 提交事务
            Db::commit();

            $util->add_template([
                'much_id' => $data['much_id'],
                'at_id' => 'YL0009',
                'user_id' => $duplex['user_id'],
                'page' => 'yl_welore/pages/user_smail/index',
                'keyword1' => $maringText,
                'keyword2' => date('Y年m月d日 H:i:s', time())
            ]);
            return $this->json_rewrite(['status' => 'success', 'msg' => '审核成功！']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'msg' => '审核失败！' . $e->getMessage()]);
        }
    }

    /**
     * 超管审核回复
     */
    public function set_reply_status()
    {
        $data = input('param.');
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg == 0) {
            $rs = ['status' => 'error', 'msg' => '您不是超管哦！'];
            return $this->json_rewrite($rs);
        }
        $util = new Util();
        $paper_info = $util->paper_reply($data['reply_id']);
        $util->set_user_red(['much_id' => $data['much_id'], 'reply_id' => $paper_info['id'], 'id' => $paper_info['paper_id'], 'uid' => $this->user_info['id']]);
        // 启动事务
        Db::startTrans();
        try {
            $dd['reply_status'] = $data['key'];
            $dd['prove_time'] = time();
            $dd['apter_time'] = time();
            $dd['whether_reason'] = empty($data['reject_reason']) ? NULL : $data['reject_reason'];
            $res = Db::name('paper_reply')->where('id', $data['reply_id'])->where('much_id', $data['much_id'])->update($dd);
            if (!$res) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '审核失败！']);
            }
            if ($data['key'] == 1) {
                $maringText = '您的回复已通过审核！';
            } else {
                $maringText = '您的回复未通过审核！';
            }

            Db::name('user_smail')->insert(['user_id' => $paper_info['user_id'], 'maring' => $maringText, 'clue_time' => time(), 'status' => 0, 'much_id' => $data['much_id']]);
            // 提交事务
            Db::commit();

            $util->add_template($tmplData = [
                'much_id' => $data['much_id'],
                'at_id' => 'YL0009',
                'user_id' => $paper_info['user_id'],
                'page' => 'yl_welore/pages/user_smail/index',
                'keyword1' => $maringText,
                'keyword2' => date('Y年m月d日 H:i:s', time())
            ]);
            // 新人营销
            if ($data['key'] == 1) {
                $task = $util->new_user_task(['uid' => $paper_info['user_id'], 'key' => 2, 'tory_id' => $paper_info['tory_id'], 'paper_id' => $paper_info['paper_id'], 'much_id' => $data['much_id']]);
                if ($task['code'] == 0) {
                    return $this->json_rewrite(['status' => 'success', 'msg' => '审核成功！' . $task['msg']]);
                } else if ($task['code'] == 1) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '审核失败，请稍候重试！4']);
                }
            }
            return $this->json_rewrite(['status' => 'success', 'msg' => '审核成功！']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'msg' => '审核失败！' . $e->getMessage()]);
        }
    }


    /**
     * 获取首页视频数据
     */
    public function get_video_list()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $where = [];
        $util = new Util();
        $where_not_in = [];
        $page = $data['page'];
        $check_qq = 'no';
        if (isset($data['tory_id'])) {
            $where['p.tory_id'] = ['eq', $data['tory_id']];
            $where['p.topping_time'] = ['eq', 0];
            $check_qq = $util->check_qq($this->user_info['user_wechat_open_id'], $data['tory_id']);
        }
        if ($this->version == 1) {
            $where['p.study_type'] = ['in', ['0', '1', '3']];
            $where['p.is_buy'] = ['eq', 0];
        }
        $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($authority['hair_video_arbor'] == 0) {
            $where['p.study_type'] = ['in', ['0', '1', '3']];
        }

        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        //查询当前用户是否是VIP
        $user_vip = $util->get_user_vip($this->user_info['id']);
        /**
         * 第一个版本低于第二个版本的时候 return -1
         * 第一个版本等于第二个版本的时候 return 0
         * 第一个版本高于第二个版本的时候 return 1
         */
        //查询我关注的圈子
        //$user_trailing = Db::name('user_trailing')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
//        $user_trailing_id = '';
//        foreach ($user_trailing as $k => $v) {
//            $user_trailing_id .= $v['tory_id'] . ",";
//        }
//        $user_trailing_id = substr($user_trailing_id, 0, -1);

        //查询有权限的圈子
        $q_tory = Db::name('territory')->where('status', 1)->where('much_id', $data['much_id'])->select();
        $q_tory_id = '';
        $vip_tory_id = '';
        $level_tory_id = '';
        foreach ($q_tory as $k => $v) {
            if ($v['attention'] == 1) {
                $user_trailing = Db::name('user_trailing')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->where('tory_id', $v['id'])->find();
                if (empty($user_trailing)) {
                    $q_tory_id .= $v['id'] . ",";
                }
            }
            if ($v['attention'] == 2) {
                $vip_tory_id .= $v['id'] . ",";
            }
            if ($v['visit_level'] > $this->user_info['level']) {
                $level_tory_id .= $v['id'] . ",";
            }

        }
        //return $level_tory_id;
        //权限
        $q_tory_id = substr($q_tory_id, 0, -1);
        //等级
        $level_tory_id = substr($level_tory_id, 0, -1);
        //vip
        $vip_tory_id = substr($vip_tory_id, 0, -1);


        if ($check_qq == 'no') {
            if ($user_vip == 0 && $vip_tory_id && $cg == 0) {
                $q_tory_id = $q_tory_id . ',' . $vip_tory_id;
            }
            if ($level_tory_id && $cg == 0) {
                $q_tory_id = $q_tory_id . ',' . $level_tory_id;
            }
            if ($cg == 0) {
                $where['t.id'] = array('not in', $q_tory_id);
            }
        }


        //查询置顶的帖子
        $home = Db::name('home_topping')->where('much_id', $data['much_id'])->select();
        $home_id = '';
        foreach ($home as $k => $v) {
            $home_id .= $v['paper_id'] . ",";
        }
        $home_id = substr($home_id, 0, -1);
        //查询被封禁的用户
        $user_status = Db::name('user')->where('much_id', $data['much_id'])->where('status', 0)->select();
        $user_status_id = '';
        foreach ($user_status as $k => $v) {
            $user_status_id .= $v['id'] . ",";
        }
        $user_status_id = substr($user_status_id, 0, -1);

        //$where['p.essence_time'] = ['eq', 0];
        $list = Db::name('paper')
            ->alias('p')
            ->join('user u', 'p.user_id=u.id')
            ->join('territory t', 'p.tory_id=t.id')
            ->join('paper_reply r', 'r.paper_id=p.id', 'LEFT')
            ->where('p.whether_delete', '0')
            ->where('p.study_status', '1')
            ->where('p.much_id', $data['much_id'])
            ->where('t.status', 1)
            ->where('study_type', 2)
            ->where('t.is_del', 0)
            ->where($where)
            ->whereNotIn('p.id', $home_id)
            ->whereNotIn('p.user_id', $user_status_id)
            ->field('p.video_type,p.third_part_vid,p.uccid,u.user_wechat_open_id,p.study_video,p.id,p.user_id,p.tory_id,p.tg_id,p.study_title,p.study_title_color,p.study_content,p.study_type,p.is_buy,p.topping_time,p.image_part,p.study_voice,p.study_voice_time,p.study_heat,p.study_laud,p.study_repount,p.adapter_time,p.is_open,u.level,u.gender,u.wear_merit,u.user_nick_name,u.user_head_sculpture,t.realm_name,max(r.apter_time) as huifu_time')
            ->order('p.adapter_time desc')
            ->group('p.id')
            ->page($page, '5')
            ->select();

        if ($list) {
            foreach ($list as $k => $v) {

                if (isset($data['tory_id'])) {//判断是否是 圈主或者管理员

                    $list[$k]['check_qq'] = $util->check_qq($v['user_wechat_open_id'], $v['tory_id']);
                }
                $list[$k]['user_wechat_open_id'] = null;
                if (isset($v['huifu_time'])) {
                    $list[$k]['huifu_time'] = formatTime($v['huifu_time']);
                }

                //计算全部回复人数
                $huifu_all_count = Db::name('paper_reply')->where('reply_status', 1)->where('paper_id', $v['id'])->where('whether_delete=0')->where('much_id', $data['much_id'])->count();

                $huifu_hui_count = Db::name('paper_reply')->alias('p')
                    ->join('paper_reply_duplex r', 'r.reply_id=p.id')
                    ->where('p.whether_delete=0')
                    ->where('p.much_id', $data['much_id'])
                    ->where('p.paper_id', $v['id'])
                    ->count();

                $list[$k]['is_voice'] = false;
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['study_title'] = emoji_decode($v['study_title']);

                $list[$k]['image_part'] = json_decode($v['image_part']);
                $list[$k]['adapter_time'] = formatTime($v['adapter_time']);
                $list[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));
                $list[$k]['study_heat'] = formatNumber($v['study_heat']);
                $list[$k]['study_laud'] = formatNumber($v['study_laud']);
                $list[$k]['study_repount'] = formatNumber($huifu_hui_count + $huifu_all_count);
                $list[$k]['user_vip'] = $util->get_user_vip($v['user_id']);

                $sc = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
                $list[$k]['is_info_zan'] = $sc == 0 ? false : true;
                //$count = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('paper_id', $v['id'])-
                $count = $v['study_laud'];
                $list[$k]['info_zan_count'] = formatNumber($count);
                $list[$k]['info_zan_count_this'] = $count;
                //查询当前帖子是否是红包贴
                $red = Db::name('paper_red_packet')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                //查询是否购买了当前帖子
                $list[$k]['red'] = $red;
                //获取当前话题
                if ($v['tg_id'] != 0) {
                    $gambit = Db::name('gambit')->where('much_id', $data['much_id'])->where('id', $v['tg_id'])->find();
                    $list[$k]['gambit_name'] = emoji_decode($gambit['gambit_name']);
                    $list[$k]['gambit_id'] = $gambit['id'];
                }

                $list[$k]['level'] = $util->get_user_level($v['level'], $data['much_id'])['level_icon'];
                //当前用户的勋章
                $list[$k]['wear_merit'] = $util->get_medal($v['wear_merit'], $data['much_id']);
                //当前用户昵称
                $list[$k]['special'] = $util->get_user_special_nickname($v['user_id'], $data['much_id']);
                //如果是腾讯视频
                if ($v['video_type'] == 1) {
                    $list[$k]['study_video'] = $this->getVideoInfo($v['third_part_vid']);

                }
                //判断是否有身份铭牌
                if (intval($v['uccid']) != 0) {
                    //查询佩戴的身份
                    $card_info = Db::name('camouflage_card')->where('id', $v['uccid'])->where('much_id', $data['much_id'])->find();
                    $user_card = Db::name('user_camouflage_card')->where('ccid', $v['uccid'])->where('user_id', $v['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                    $list[$k]['user_head_sculpture'] = $card_info['forgery_head'];
                    $list[$k]['user_id'] = 0;
                    //$list[$k]['user_nick_name']=$card_info['forgery_name'].'-'.$user_card['id'];
                    $list[$k]['user_nick_name'] = $card_info['forgery_name'];
                    $list[$k]['attr'] = '';
                } else {
                    $attr = $util->get_att_info($v['user_id'], $data['much_id']);
                    $list[$k]['attr'] = $attr;
                }

            }

            $rs['info'] = $list;
        } else {
            $rs['info'] = [];
        }
        $rs['version'] = $this->version;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取首页有声数据
     */
    public function get_audio_list()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $where = [];
        $util = new Util();
        $where_not_in = [];
        $page = $data['page'];
        $check_qq = 'no';
        if (isset($data['tory_id'])) {
            $where['p.tory_id'] = ['eq', $data['tory_id']];
            $where['p.topping_time'] = ['eq', 0];
            $check_qq = $util->check_qq($this->user_info['user_wechat_open_id'], $data['tory_id']);
        }
        if ($this->version == 1) {
            $where['p.study_type'] = ['in', ['0', '1', '3']];
            $where['p.is_buy'] = ['eq', 0];
        }
        $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($authority['hair_video_arbor'] == 0) {
            $where['p.study_type'] = ['in', ['0', '1', '3']];
        }

        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        //查询当前用户是否是VIP
        $user_vip = $util->get_user_vip($this->user_info['id']);
        //查询有权限的圈子
        $q_tory = Db::name('territory')->where('status', 1)->where('much_id', $data['much_id'])->select();
        $q_tory_id = '';
        $vip_tory_id = '';
        $level_tory_id = '';
        foreach ($q_tory as $k => $v) {
            if ($v['attention'] == 1) {
                $user_trailing = Db::name('user_trailing')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->where('tory_id', $v['id'])->find();
                if (empty($user_trailing)) {
                    $q_tory_id .= $v['id'] . ",";
                }
            }
            if ($v['attention'] == 2) {
                $vip_tory_id .= $v['id'] . ",";
            }
            if ($v['visit_level'] > $this->user_info['level']) {
                $level_tory_id .= $v['id'] . ",";
            }

        }
        //return $level_tory_id;
        //权限
        $q_tory_id = substr($q_tory_id, 0, -1);
        //等级
        $level_tory_id = substr($level_tory_id, 0, -1);
        //vip
        $vip_tory_id = substr($vip_tory_id, 0, -1);


        if ($check_qq == 'no') {
            if ($user_vip == 0 && $vip_tory_id && $cg == 0) {
                $q_tory_id = $q_tory_id . ',' . $vip_tory_id;
            }
            if ($level_tory_id && $cg == 0) {
                $q_tory_id = $q_tory_id . ',' . $level_tory_id;
            }
            if ($cg == 0) {
                $where['t.id'] = array('not in', $q_tory_id);
            }
        }


        //查询置顶的帖子
        $home = Db::name('home_topping')->where('much_id', $data['much_id'])->select();
        $home_id = '';
        foreach ($home as $k => $v) {
            $home_id .= $v['paper_id'] . ",";
        }
        $home_id = substr($home_id, 0, -1);
        //查询被封禁的用户
        $user_status = Db::name('user')->where('much_id', $data['much_id'])->where('status', 0)->select();
        $user_status_id = '';
        foreach ($user_status as $k => $v) {
            $user_status_id .= $v['id'] . ",";
        }
        $user_status_id = substr($user_status_id, 0, -1);

        //$where['p.essence_time'] = ['eq', 0];
        $list = Db::name('paper')
            ->alias('p')
            ->join('user u', 'p.user_id=u.id')
            ->join('territory t', 'p.tory_id=t.id')
            ->join('paper_reply r', 'r.paper_id=p.id', 'LEFT')
            ->where('p.whether_delete', '0')
            ->where('p.study_status', '1')
            ->where('p.study_type', '1')
            ->where('p.much_id', $data['much_id'])
            ->where('t.status', 1)
            ->where('t.is_del', 0)
            ->where($where)
            ->whereNotIn('p.id', $home_id)
            ->whereNotIn('p.user_id', $user_status_id)
            ->field('p.uccid,u.user_wechat_open_id,p.id,p.user_id,p.tory_id,p.tg_id,p.study_title,p.study_title_color,p.study_content,p.study_type,p.is_buy,p.topping_time,p.image_part,p.study_voice,p.study_voice_time,p.study_heat,p.study_laud,p.study_repount,p.adapter_time,p.is_open,u.level,u.gender,u.wear_merit,u.user_nick_name,u.user_head_sculpture,t.realm_name,max(r.apter_time) as huifu_time')
            ->order('p.adapter_time desc')
            ->group('p.id')
            ->page($page, '10')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {

                if (isset($data['tory_id'])) {//判断是否是 圈主或者管理员

                    $list[$k]['check_qq'] = $util->check_qq($v['user_wechat_open_id'], $v['tory_id']);
                }
                $list[$k]['user_wechat_open_id'] = null;
                if (isset($v['huifu_time'])) {
                    $list[$k]['huifu_time'] = formatTime($v['huifu_time']);
                }

                //计算全部回复人数
                $huifu_all_count = Db::name('paper_reply')->where('reply_status', 1)->where('paper_id', $v['id'])->where('whether_delete=0')->where('much_id', $data['much_id'])->count();

                $huifu_hui_count = Db::name('paper_reply')->alias('p')
                    ->join('paper_reply_duplex r', 'r.reply_id=p.id')
                    ->where('p.whether_delete=0')
                    ->where('p.much_id', $data['much_id'])
                    ->where('p.paper_id', $v['id'])
                    ->count();

                $list[$k]['is_voice'] = false;
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['study_title'] = emoji_decode($v['study_title']);


                if (isset($data['tory_id'])) {
                    $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';

                    preg_match_all($preg, emoji_decode($v['study_content']), $match);

                    //$list[$k]['mmmmmmatch'] = $v['study_content'];
                    if (!empty(json_decode($v['image_part'], true))) {
                        //$list[$k]['image_part_1'] = '1';
                        $list[$k]['image_part'] = json_decode($v['image_part']);
                    } else {
                        //$list[$k]['image_part_1'] = '0';
                        //$list[$k]['image_part_2'] = $match;
                        if (!empty($match[0])) {
                            $img = array();
                            foreach ($match[1] as $a => $b) {
                                $img[$a] = htmlspecialchars_decode($b);
                            }
                            $list[$k]['image_part'] = $img;
                        } else {
                            $list[$k]['image_part'] = [];
                        }
                    }
                }

                $list[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));
                $list[$k]['study_heat'] = formatNumber($v['study_heat']);
                $list[$k]['study_laud'] = formatNumber($v['study_laud']);
                $list[$k]['study_repount'] = formatNumber($huifu_hui_count + $huifu_all_count);
                $list[$k]['user_vip'] = $util->get_user_vip($v['user_id']);

                $list[$k]['study_voice'] = link_urldecode($v['study_voice']);

                $list[$k]['study_voice_time'] = s_to_hs($v['study_voice_time']);
                $list[$k]['starttime'] = '00:00';
                $list[$k]['adapter_time'] = formatTime($v['adapter_time']);
                $list[$k]['offset'] = 0;
                $list[$k]['max'] = $v['study_voice_time'];

                $sc = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
                $list[$k]['is_info_zan'] = $sc == 0 ? false : true;
                //$count = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('paper_id', $v['id'])-
                $count = $v['study_laud'];
                $list[$k]['info_zan_count'] = formatNumber($count);
                $list[$k]['info_zan_count_this'] = $count;
                //查询当前帖子是否是红包贴
                $red = Db::name('paper_red_packet')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                //查询是否购买了当前帖子
                $list[$k]['red'] = $red;
                //获取当前话题
                if ($v['tg_id'] != 0) {
                    $gambit = Db::name('gambit')->where('much_id', $data['much_id'])->where('id', $v['tg_id'])->find();
                    $list[$k]['gambit_name'] = emoji_decode($gambit['gambit_name']);
                    $list[$k]['gambit_id'] = $gambit['id'];
                }

                $list[$k]['level'] = $util->get_user_level($v['level'], $data['much_id'])['level_icon'];
                //当前用户的勋章
                $list[$k]['wear_merit'] = $util->get_medal($v['wear_merit'], $data['much_id']);
                //当前用户昵称
                $list[$k]['special'] = $util->get_user_special_nickname($v['user_id'], $data['much_id']);

                //判断是否有身份铭牌
                if (intval($v['uccid']) != 0) {
                    //查询佩戴的身份
                    $card_info = Db::name('camouflage_card')->where('id', $v['uccid'])->where('much_id', $data['much_id'])->find();
                    $user_card = Db::name('user_camouflage_card')->where('ccid', $v['uccid'])->where('user_id', $v['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                    $list[$k]['user_head_sculpture'] = $card_info['forgery_head'];
                    $list[$k]['user_id'] = 0;
                    //$list[$k]['user_nick_name']=$card_info['forgery_name'].'-'.$user_card['id'];
                    $list[$k]['user_nick_name'] = $card_info['forgery_name'];
                    $list[$k]['attr'] = '';
                } else {
                    $attr = $util->get_att_info($v['user_id'], $data['much_id']);
                    $list[$k]['attr'] = $attr;
                }

            }

            $rs['info'] = $list;
        } else {
            $rs['info'] = [];
        }
        $rs['version'] = $this->version;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取首页活动数据
     */
    public function get_activity_list()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $where = [];
        $util = new Util();
        $where_not_in = [];
        $page = $data['page'];
        $check_qq = 'no';
        if (isset($data['tory_id'])) {
            $where['p.tory_id'] = ['eq', $data['tory_id']];
            $where['p.topping_time'] = ['eq', 0];
            $check_qq = $util->check_qq($this->user_info['user_wechat_open_id'], $data['tory_id']);
        }
        if ($this->version == 1) {
            $where['p.study_type'] = ['in', ['0', '1', '3']];
            $where['p.is_buy'] = ['eq', 0];
        }
        $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($authority['hair_video_arbor'] == 0) {
            $where['p.study_type'] = ['in', ['0', '1', '3']];
        }

        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        //查询当前用户是否是VIP
        $user_vip = $util->get_user_vip($this->user_info['id']);
        /**
         * 第一个版本低于第二个版本的时候 return -1
         * 第一个版本等于第二个版本的时候 return 0
         * 第一个版本高于第二个版本的时候 return 1
         */
        //查询我关注的圈子
        //$user_trailing = Db::name('user_trailing')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
//        $user_trailing_id = '';
//        foreach ($user_trailing as $k => $v) {
//            $user_trailing_id .= $v['tory_id'] . ",";
//        }
//        $user_trailing_id = substr($user_trailing_id, 0, -1);

        //查询有权限的圈子
        $q_tory = Db::name('territory')->where('status', 1)->where('much_id', $data['much_id'])->select();
        $q_tory_id = '';
        $vip_tory_id = '';
        $level_tory_id = '';
        foreach ($q_tory as $k => $v) {
            if ($v['attention'] == 1) {
                $user_trailing = Db::name('user_trailing')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->where('tory_id', $v['id'])->find();
                if (empty($user_trailing)) {
                    $q_tory_id .= $v['id'] . ",";
                }
            }
            if ($v['attention'] == 2) {
                $vip_tory_id .= $v['id'] . ",";
            }
            if ($v['visit_level'] > $this->user_info['level']) {
                $level_tory_id .= $v['id'] . ",";
            }

        }
        //return $level_tory_id;
        //权限
        $q_tory_id = substr($q_tory_id, 0, -1);
        //等级
        $level_tory_id = substr($level_tory_id, 0, -1);
        //vip
        $vip_tory_id = substr($vip_tory_id, 0, -1);


        if ($check_qq == 'no') {
            if ($user_vip == 0 && $vip_tory_id && $cg == 0) {
                $q_tory_id = $q_tory_id . ',' . $vip_tory_id;
            }
            if ($level_tory_id && $cg == 0) {
                $q_tory_id = $q_tory_id . ',' . $level_tory_id;
            }
            if ($cg == 0) {
                $where['t.id'] = array('not in', $q_tory_id);
            }
        }


        //查询置顶的帖子
        $home = Db::name('home_topping')->where('much_id', $data['much_id'])->select();
        $home_id = '';
        foreach ($home as $k => $v) {
            $home_id .= $v['paper_id'] . ",";
        }
        $home_id = substr($home_id, 0, -1);
        //查询被封禁的用户
        $user_status = Db::name('user')->where('much_id', $data['much_id'])->where('status', 0)->select();
        $user_status_id = '';
        foreach ($user_status as $k => $v) {
            $user_status_id .= $v['id'] . ",";
        }
        $user_status_id = substr($user_status_id, 0, -1);


        //$where['p.essence_time'] = ['eq', 0];
        $list = Db::name('paper')
            ->alias('p')
            ->join('user u', 'p.user_id=u.id')
            ->join('territory t', 'p.tory_id=t.id')
            ->join('paper_reply r', 'r.paper_id=p.id', 'LEFT')
            ->where('p.whether_delete', '0')
            ->where('p.study_status', '1')
            ->where('p.study_type', '3')
            ->where('p.much_id', $data['much_id'])
            ->where('t.status', 1)
            ->where('t.is_del', 0)
            ->where($where)
            ->whereNotIn('p.id', $home_id)
            ->whereNotIn('p.user_id', $user_status_id)
            ->field('p.uccid,u.user_wechat_open_id,p.id,p.user_id,p.tory_id,p.tg_id,p.study_title,p.study_title_color,p.study_content,p.study_type,p.is_buy,p.topping_time,p.image_part,p.study_voice,p.study_voice_time,p.study_heat,p.study_laud,p.study_repount,p.adapter_time,p.is_open,u.level,u.gender,u.wear_merit,u.user_nick_name,u.user_head_sculpture,t.realm_name,max(r.apter_time) as huifu_time')
            ->order('p.adapter_time desc')
            ->group('p.id')
            ->page($page, '10')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {

                if (isset($data['tory_id'])) {//判断是否是 圈主或者管理员

                    $list[$k]['check_qq'] = $util->check_qq($v['user_wechat_open_id'], $v['tory_id']);
                }
                $list[$k]['user_wechat_open_id'] = null;
                if (isset($v['huifu_time'])) {
                    $list[$k]['huifu_time'] = formatTime($v['huifu_time']);
                }

                //计算全部回复人数
                $huifu_all_count = Db::name('paper_reply')->where('reply_status', 1)->where('paper_id', $v['id'])->where('whether_delete=0')->where('much_id', $data['much_id'])->count();

                $huifu_hui_count = Db::name('paper_reply')->alias('p')
                    ->join('paper_reply_duplex r', 'r.reply_id=p.id')
                    ->where('p.whether_delete=0')
                    ->where('p.much_id', $data['much_id'])
                    ->where('p.paper_id', $v['id'])
                    ->count();

                $list[$k]['is_voice'] = false;
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['study_title'] = emoji_decode($v['study_title']);


                $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';

                preg_match_all($preg, emoji_decode($v['study_content']), $match);

                //$list[$k]['mmmmmmatch'] = $v['study_content'];
                if (!empty(json_decode($v['image_part'], true))) {
                    //$list[$k]['image_part_1'] = '1';
                    $list[$k]['image_part'] = json_decode($v['image_part']);
                } else {
                    //$list[$k]['image_part_1'] = '0';
                    //$list[$k]['image_part_2'] = $match;
                    if (!empty($match[0])) {
                        $img = array();
                        foreach ($match[1] as $a => $b) {
                            $img[$a] = htmlspecialchars_decode($b);
                        }
                        $list[$k]['image_part'] = $img;
                    } else {
                        $list[$k]['image_part'] = [];
                    }
                }


                //$ling = count(json_decode($list[$k]['image_part']));

                $list[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));
                $list[$k]['study_heat'] = formatNumber($v['study_heat']);
                $list[$k]['study_laud'] = formatNumber($v['study_laud']);
                $list[$k]['study_repount'] = formatNumber($huifu_hui_count + $huifu_all_count);
                $list[$k]['user_vip'] = $util->get_user_vip($v['user_id']);

                $list[$k]['study_voice'] = link_urldecode($v['study_voice']);

                $list[$k]['study_voice_time'] = s_to_hs($v['study_voice_time']);
                $list[$k]['starttime'] = '00:00';
                $list[$k]['adapter_time'] = formatTime($v['adapter_time']);
                $list[$k]['offset'] = 0;
                $list[$k]['max'] = $v['study_voice_time'];

                $sc = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
                $list[$k]['is_info_zan'] = $sc == 0 ? false : true;
                //$count = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('paper_id', $v['id'])-
                $count = $v['study_laud'];
                $list[$k]['info_zan_count'] = formatNumber($count);
                $list[$k]['info_zan_count_this'] = $count;
                //查询当前帖子是否是红包贴
                $red = Db::name('paper_red_packet')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                //查询是否购买了当前帖子
                $list[$k]['red'] = $red;
                //获取当前话题
                if ($v['tg_id'] != 0) {
                    $gambit = Db::name('gambit')->where('much_id', $data['much_id'])->where('id', $v['tg_id'])->find();
                    $list[$k]['gambit_name'] = emoji_decode($gambit['gambit_name']);
                    $list[$k]['gambit_id'] = $gambit['id'];
                }

                $list[$k]['level'] = $util->get_user_level($v['level'], $data['much_id'])['level_icon'];
                //当前用户的勋章
                $list[$k]['wear_merit'] = $util->get_medal($v['wear_merit'], $data['much_id']);
                //当前用户昵称
                $list[$k]['special'] = $util->get_user_special_nickname($v['user_id'], $data['much_id']);

                //判断是否有身份铭牌
                if (intval($v['uccid']) != 0) {
                    //查询佩戴的身份
                    $card_info = Db::name('camouflage_card')->where('id', $v['uccid'])->where('much_id', $data['much_id'])->find();
                    $user_card = Db::name('user_camouflage_card')->where('ccid', $v['uccid'])->where('user_id', $v['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                    $list[$k]['user_head_sculpture'] = $card_info['forgery_head'];
                    $list[$k]['user_id'] = 0;
                    //$list[$k]['user_nick_name']=$card_info['forgery_name'].'-'.$user_card['id'];
                    $list[$k]['user_nick_name'] = $card_info['forgery_name'];
                    $list[$k]['attr'] = '';
                } else {
                    $attr = $util->get_att_info($v['user_id'], $data['much_id']);
                    $list[$k]['attr'] = $attr;
                }

            }

            $rs['info'] = $list;
        } else {
            $rs['info'] = [];
        }
        $rs['version'] = $this->version;
        return $this->json_rewrite($rs);
    }

}