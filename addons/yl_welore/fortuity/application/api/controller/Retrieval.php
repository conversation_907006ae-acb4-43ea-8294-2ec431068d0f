<?php

namespace app\api\controller;


use app\api\service\Util;
use think\Db;

class Retrieval extends Base
{
    public function index()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('d9cbb0e0-a4ae-9ada-057b-cf08dfd91ef7', $data['much_id'])) {
            return $this->json_rewrite(['status' => 'error', 'code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $util=new Util();

        $currentTime = time(); // 获取当前时间戳
        if ($data['tab'] == 0) {
            $where['draw_time'] = ['>=', $currentTime];
        } else if($data['tab'] == 1){
            $where['draw_time'] = ['<', $currentTime];
        }else{
            $user_list=Db::name('sweepstake_participate')->where('user_id',$this->user_info['id'])->where('much_id',$data['much_id'])->group('sp_id')->select();
            $u='';
            foreach ($user_list as $k => $v) {
                $u .= $v['sp_id'] . ",";
            }
            $u = substr($u, 0, -1);
            $where['id'] = ['in',$u];
        }

        $list = Db::name('sweepstake_list')
            ->where($where)
            ->where('is_del', 0)
            ->where('much_id', $data['much_id'])->page($data['page'], 10)
            ->field('id,is_winning,prize_list,lottery_name,start_time,end_time,draw_time,is_group')
            ->order('is_winning asc,id asc')
            ->select();
        foreach ($list as $k => $v) {
            //查询当前活动是否到开奖时间
            if ($v['is_winning'] == 0) {
                $kkk = $util->insKai(['id' => $v['id'], "much_id" => $data['much_id'],'now'=>0]);
                if ($kkk['code'] == 1) {
                    $list[$k]['is_winning'] = 1;
                }
            }
            $list[$k]['start_time'] = date("Y年m月d日 H：i", $v['start_time']);
            $list[$k]['end_time'] = date("Y年m月d日 H：i", $v['end_time']);
            $list[$k]['draw_time'] = date("Y年m月d日 H：i", $v['draw_time']);
            //查询当前用户的奖号
            $user_number_list = Db::name('sweepstake_participate')->where('sp_id',$v['id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->field('lucky_number,award_categories')->order('create_time desc')->select();
            $new_number_list=[];
            foreach ($user_number_list as $a=>$b){
                if($v['is_group']==1){
                    $new_number_list[$a]['lucky_number'] = ($this->numberToLetter($b['award_categories'])) . $b['lucky_number'];
                }else{
                    $new_number_list[$a]['lucky_number'] = $b['lucky_number'];
                }
            }
            $new_number_list=array_values($new_number_list);
            $list[$k]['user_cos'] = $new_number_list;
            //查询当前奖项
            $prize = json_decode($v['prize_list'], true)[0];
            $list[$k]['prizeName'] = $prize['prizeName'];
            $list[$k]['prizeQuantity'] = $prize['prizeQuantity'];
            unset($list[$k]['prize_list']);
        }
        //查询幸运抽奖
        $sweepstake_config= Db::name('sweepstake_config')->where('much_id', $data['much_id'])->find();
        $sweepstake_config['custom_title']=empty($sweepstake_config['custom_title'])?'幸运抽奖':$sweepstake_config['custom_title'];
        return $this->json_rewrite(['msg' => '', 'code' => 0, 'list' => $list,'info'=>$sweepstake_config]);
    }

    public function get_prize_info()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('d9cbb0e0-a4ae-9ada-057b-cf08dfd91ef7', $data['much_id'])) {
            return $this->json_rewrite(['status' => 'error', 'code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $list = Db::name('sweepstake_winning')->where('sp_id', $data['id'])->where('much_id', $data['much_id'])->find();
        $prize_outcome = json_decode($list['prize_outcome'], true);
        foreach ($prize_outcome as $k => $v) {
            $user = Db::name('sweepstake_participate')->alias('p')
                ->join('user u', 'u.id=p.user_id')
                ->where('p.sp_id', $data['id'])
                ->where('p.award_level', $v['prizeLevel'])
                ->where('p.is_award', 1)
                ->field('u.user_head_sculpture,u.user_nick_name')
                ->limit(20)
                ->select();
            foreach ($user as $a=>$b){
                $user[$a]['user_nick_name']=substr_cut($b['user_nick_name']);
            }
            $prize_outcome[$k]['user_list'] = $user;
            $prize_outcome[$k]['prizeLevel'] = numToWord($v['prizeLevel']);
            unset($prize_outcome[$k]['prizeType']);
            unset($prize_outcome[$k]['extractQuantity']);
            unset($prize_outcome[$k]['luckyNumber']);
            unset($prize_outcome[$k]['prizeQuantity']);
        }

        return $this->json_rewrite($prize_outcome);
    }

    public function info()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('d9cbb0e0-a4ae-9ada-057b-cf08dfd91ef7', $data['much_id'])) {
            return $this->json_rewrite(['status' => 'error', 'code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $info = Db::name('sweepstake_list')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        if (empty($info) || $info['is_del'] == 1) {
            return $this->json_rewrite(['msg' => '当前活动已被删除！', 'code' => 0, 'info' => []]);
        }

        $prize_list = json_decode($info['prize_list'], true);
        foreach ($prize_list as $k => $v) {
            $prize_list[$k]['prizeLevel'] = numToWord($v['prizeLevel']);
            //unset($prize_list[$k]['extractQuantity']);
        }
        $new_info['prize_list'] = $prize_list;
        $new_info['id'] = $info['id'];
        $new_info['is_winning'] = $info['is_winning'];
        $new_info['lottery_name'] = $info['lottery_name'];
        $new_info['free_entry_count'] = $info['free_entry_count'];
        $new_info['video_entry_count'] = $info['video_entry_count'];
        $new_info['participant_num_limit'] = $info['participant_num_limit'];
        $new_info['start_time'] = date("Y年m月d日 H：i", $info['start_time']);
        $new_info['end_time'] = date("Y年m月d日 H：i", $info['end_time']);
        $new_info['draw_time'] = date("Y年m月d日 H：i", $info['draw_time']);
        $new_info['campaign_desc'] = emoji_decode($info['campaign_desc']);
        //查询参与头像
        $user_list = Db::name('sweepstake_participate')->alias('s')
            ->join('user u', 'u.id=s.user_id')
            ->where('s.much_id', $data['much_id'])
            ->where('s.sp_id', $data['id'])
            ->limit(7)
            ->field('u.user_head_sculpture')
            ->group('s.user_id')
            ->select();
        //查询参与人数
        $new_info['user_count'] = Db::name('sweepstake_participate')
            ->where('much_id', $data['much_id'])
            ->where('sp_id', $data['id'])
            ->group('user_id')
            ->count();

        //查询我的奖券q
        $participate = Db::name('sweepstake_participate')
            ->where('much_id', $data['much_id'])
            ->where('user_id', $this->user_info['id'])
            ->where('sp_id', $info['id'])
            ->field('lucky_number,create_time,award_categories,is_award')
            ->order('create_time desc')
            ->select();
        foreach ($participate as $k => $v) {
            $participate[$k]['create_time'] = date("Y年m月d日 H：i", $v['create_time']);
            if ($info['is_group'] == 1) {
                $participate[$k]['lucky_number'] = ($this->numberToLetter($v['award_categories'])) . $v['lucky_number'];
            }
        }
        //查询我免费抽奖次数
        $mian = Db::name('sweepstake_participate')
            ->where('much_id', $data['much_id'])
            ->where('user_id', $this->user_info['id'])
            ->where('sp_id', $info['id'])
            ->where('award_number_type', 0)
            ->count();
        //查询我免费抽奖次数
        $ji = Db::name('sweepstake_participate')
            ->where('much_id', $data['much_id'])
            ->where('user_id', $this->user_info['id'])
            ->where('sp_id', $info['id'])
            ->where('award_number_type', 1)
            ->count();
        $new_info['ji'] = $ji;
        $new_info['mian'] = $mian;
        //查询我免费抽奖次数
        $new_info['participate'] = $participate;
        //查询当前活动是否已经开奖
        $is_open = 0;//未开奖
        if ($info['end_time'] <= time()) {
            $is_open = 1;//暂停抽奖
        }
        if ($info['draw_time'] <= time()) {
            $is_open = 2;//已开奖
        }
        if ($info['start_time'] > time()) {
            $is_open = 3;//未开始
        }
        $new_info['is_open'] = $is_open;
        $new_info['user_list'] = $user_list;
        //查询幸运抽奖
        $sweepstake_config= Db::name('sweepstake_config')->where('much_id', $data['much_id'])->find();
        $new_info['custom_title']=empty($sweepstake_config['custom_title'])?'幸运抽奖':$sweepstake_config['custom_title'];
        $new_info['ad1']=$sweepstake_config['ad_1'];
        return $this->json_rewrite(['msg' => '', 'code' => 0, 'info' => $new_info]);
    }

    public function prizeDraw()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('d9cbb0e0-a4ae-9ada-057b-cf08dfd91ef7', $data['much_id'])) {
            return $this->json_rewrite(['status' => 'error', 'code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $info = Db::name('sweepstake_list')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        if (empty($info) || $info['is_del'] == 1) {
            return $this->json_rewrite(['msg' => '当前活动已被删除！', 'code' => 0, 'info' => []]);
        }
        if ($info['end_time'] <= time()) {
            return $this->json_rewrite(['msg' => '抽奖阶段已经结束啦~', 'code' => 0, 'info' => []]);
        }
        if ($info['draw_time'] <= time()) {
            return $this->json_rewrite(['msg' => '你来晚了，活动已经结束！', 'code' => 0, 'info' => []]);
        }
        if ($info['start_time'] > time()) {
            return $this->json_rewrite(['msg' => '活动还未开始！！', 'code' => 0, 'info' => []]);
        }
        //查询是否限制人数
        if (intval($info['participant_num_limit']) != 0) {
            //查询已经参与人数
            $all = Db::name('sweepstake_participate')
                ->where('much_id', $data['much_id'])
                ->where('sp_id', $info['id'])
                ->count();
            if ($all >= $info['participant_num_limit']) {
                return $this->json_rewrite(['msg' => '奖券已经发放完了！', 'code' => 0, 'info' => []]);
            }
        }
        //查询我免费抽奖次数
        $mian = Db::name('sweepstake_participate')
            ->where('much_id', $data['much_id'])
            ->where('user_id', $this->user_info['id'])
            ->where('sp_id', $info['id'])
            ->where('award_number_type', 0)
            ->count();
        //查询我激励抽奖次数
        $ji = Db::name('sweepstake_participate')
            ->where('much_id', $data['much_id'])
            ->where('user_id', $this->user_info['id'])
            ->where('sp_id', $info['id'])
            ->where('award_number_type', 1)
            ->count();
        //查询我的参入次数
        if ($data['type'] == 0) {
            if ($info['free_entry_count'] <= $mian) {
                return $this->json_rewrite(['msg' => '参与已达上限！', 'code' => 0, 'info' => []]);
            }
        } else {
            if ($info['video_entry_count'] <= $ji) {
                return $this->json_rewrite(['msg' => '参与已达上限！', 'code' => 0, 'info' => []]);
            }
        }
        //查询当前活动的最大值
        $participate = Db::name('sweepstake_participate')
            ->where('much_id', $data['much_id'])
            ->where('sp_id', $info['id'])
            ->order('id desc')
            ->find();
        if ($participate) {
            $number = $participate['lucky_number'];
            $originalLength = strlen($number);
            $number = ltrim($number, '0');
            $number = strval(intval($number) + 1);
            $numberOfDigits = str_pad($number, $originalLength, '0', STR_PAD_LEFT);
            if ($info['is_group'] == 0) {
                //查询奖号是否超出结束random_extract_range_end
                if (intval($numberOfDigits) > intval($info['random_extract_range_end'])) {
                    return $this->json_rewrite(['msg' => '奖券已经发放完了！', 'code' => 0, 'info' => []]);
                }
                $item['award_categories'] = 0;
            } else {
                //如果生成的值大于random_extract_range_end，并且有分组
                if (intval($numberOfDigits) > intval($info['random_extract_range_end'])) {
                    $numberOfDigits = str_pad($info['random_extract_range_start'] + 1, strlen(strval($info['random_extract_range_end'])), '0', STR_PAD_LEFT);
                    $item['award_categories'] = $info['award_categories'] + 1;
                } else {
                    $item['award_categories'] = 0;
                }
            }
        } else {
            $numberOfDigits = str_pad($info['random_extract_range_start'] + 1, strlen(strval($info['random_extract_range_end'])), '0', STR_PAD_LEFT);
        }
        //保存数据
        $item['user_id'] = $this->user_info['id'];
        $item['sp_id'] = $info['id'];
        $item['lucky_number'] = $numberOfDigits;
        $item['award_number_type'] = $data['type'];
        $item['create_time'] = time();
        $item['much_id'] = $data['much_id'];
        $item['award_level']=0;
         $item['award_type']=0;
        $ins_participate = Db::name('sweepstake_participate')->insert($item);
        if (!$ins_participate) {
            return $this->json_rewrite(['msg' => '活动太火爆了，请稍候重试-1！', 'code' => 0, 'info' => []]);
        }
        return $this->json_rewrite(['msg' => '抽取成功，获得一张奖券！', 'code' => 1, 'info' => []]);
    }


    public function numberToLetter($number)
    {
        $base = 26; // 字母表的基数
        $letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        $result = "";
        while ($number >= 0) {
            $remainder = $number % $base;
            $result = $letters[$remainder] . $result;
            $number = floor($number / $base) - 1;

            if ($number < 0) {
                break;
            }
        }
        return $result;
    }
}
