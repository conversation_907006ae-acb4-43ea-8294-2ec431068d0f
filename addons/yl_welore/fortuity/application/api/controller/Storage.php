<?php


namespace app\api\controller;


use app\api\service\RankingUtil;
use app\api\service\Util;
use app\common\NetDiskService;
use think\Db;

class Storage extends Base
{
    /**
     * 获取我的网盘内容
     */
    public function get_my_volume()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('2d08651c-15b9-1924-c83e-6c65680a82be', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $rank = new RankingUtil();
        //如果为0 则初始化容量
        $check = Db::name('netdisc_user_volume')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->where('exp_time', 0)
            ->find();
        if (!empty($check) && $check['use_status'] == 0) {
            return $this->json_rewrite(['code' => 1, 'msg' => '您的网盘已被封禁！']);
        }
        $netdisc_config = Db::name('netdisc_config')->where('much_id', $data['much_id'])->find();
        //获取配置
        if (empty($check)) {
            $user_ins = Db::name('netdisc_user_volume')->insert(['user_id' => $this->user_info['id'], 'quota_size' => $netdisc_config['disk_size'], 'exp_time' => 0, 'use_status' => 1, 'much_id' => $data['much_id']]);
            if (!$user_ins) {
                return $this->json_rewrite(['code' => 1, 'msg' => '网盘初始化失败！']);
            }
        }

        //我的网盘总大小
        $user_big = $rank->get_my_netdisc_big(['much_id' => $data['much_id'], 'user_id' => $this->user_info['id']]);
        $user_use = $rank->get_my_netdisc_use(['much_id' => $data['much_id'], 'user_id' => $this->user_info['id']]);

        $info['user_big_b'] = $user_big;
        $info['user_use_b'] = $user_use;

        $info['user_big'] = $this->setupSize($user_big);
        $info['user_use'] = $this->setupSize($user_use);
        $info['upload_limit'] = $this->setupSize($netdisc_config['upload_size_limit']);
        $info['upload_limit_b'] = $netdisc_config['upload_size_limit'];
        $info['netdisc_config'] = $netdisc_config['use_protocol'];
        $info['half_score'] = round($user_use / $user_big * 100, 2) . "%";

        $info['upload_type_limited'] = empty($netdisc_config['upload_type_limited']) ? [] : explode(",", $netdisc_config['upload_type_limited']);

        return $this->json_rewrite(['code' => 0, 'msg' => '获取网盘成功！', 'info' => $info]);

    }

    /**
     * 获取网盘文件
     */
    public function my_netdisc_belong()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('2d08651c-15b9-1924-c83e-6c65680a82be', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $rank = new RankingUtil();
        $page = $data['page'];
        $page_k = $data['page_k'];
        //获取首页文件(文件夹)
        $my_netdisc_belong = Db::name('netdisc_belong')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->where('parent_path_id', 0)
            ->where('is_del', 0)
            ->where('is_dir', 1)
            ->order(['add_time' => 'desc'])
            ->page($page, 15)
            ->select();
        $my_netdisc_belong_count = count($my_netdisc_belong);
        if ($my_netdisc_belong_count < 15) {
            //获取首页文件(不是文件夹的文件，不是在文件夹内的文件)
            $my_netdisc = Db::name('netdisc_belong')
                ->where('user_id', $this->user_info['id'])
                ->where('much_id', $data['much_id'])
                ->where('parent_path_id', 0)
                ->where('is_del', 0)
                ->where('is_dir', 0)
                ->order(['add_time' => 'desc'])
                ->page($page_k, 15)
                ->select();
            foreach ($my_netdisc as $k => $v) {
                array_push($my_netdisc_belong, $v);
            }
        }
        foreach ($my_netdisc_belong as $k => $v) {
            if ($v['nc_id'] != 0) {
                $netdisc = Db::name('netdisc')->where('id', $v['nc_id'])->where('much_id', $data['much_id'])->find();
                $my_netdisc_belong[$k]['file_size'] = $this->setupSize($netdisc['file_size']);
                $my_netdisc_belong[$k]['file_icon'] = $rank->fileTypeIcon($netdisc['file_suffix']);
                $my_netdisc_belong[$k]['file_url'] = $netdisc['file_address'];
                $my_netdisc_belong[$k]['file_suffix'] = $netdisc['file_suffix'];
                $my_netdisc_belong[$k]['file_status'] = $netdisc['file_status'];
                $my_netdisc_belong[$k]['suf'] = $rank->CheckfileTypeIcon($netdisc['file_suffix']);
            } else {
                $my_netdisc_belong[$k]['file_size'] = 0;
                $my_netdisc_belong[$k]['file_icon'] = $rank->fileTypeIcon('dir');
            }
            //$my_netdisc_belong[$k]['file_size'] =0;
            $my_netdisc_belong[$k]['add_time'] = date('Y-m-d H:i:s', $v['add_time']);
        }

        return $this->json_rewrite(['code' => 0, 'msg' => '获取文件列表成功！', 'page' => $my_netdisc_belong_count, 'info' => $my_netdisc_belong]);

    }

    /**
     * 修改文件名称
     */
    public function update_file_name()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('2d08651c-15b9-1924-c83e-6c65680a82be', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $util = new Util();
        $data['name'] = preg_replace('# #', '', $data['name']);
        if (empty($data['name'])) {
            return $this->json_rewrite(['code' => '1', 'msg' => '文件名不能为空!']);
        }
        $check_title = $util->get_check_msg($data['name'], $data['much_id'], $data['openid']);
        if ($check_title['status'] == 'error') {
            return $this->json_rewrite($check_title);
        }
        //执行更新
        $res = Db::name('netdisc_belong')
            ->where('id', $data['id'])
            ->where('much_id', $data['much_id'])
            ->where('user_id', $this->user_info['id'])
            ->update(['file_name' => emoji_encode($data['name'])]);
        if ($res !== false) {
            return $this->json_rewrite(['code' => '0', 'msg' => '修改成功！']);
        } else {
            return $this->json_rewrite(['code' => '1', 'msg' => '文件名修改失败！']);
        }
    }

    /**
     * 获取 文件夹
     */
    public function get_dir()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('2d08651c-15b9-1924-c83e-6c65680a82be', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $rank = new RankingUtil();
        $page = $data['dir_page'];
        //获取首页文件(文件夹)
        $my_netdisc_belong = Db::name('netdisc_belong')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->where('parent_path_id', 0)
            ->where('is_del', 0)
            ->where('is_dir', 1)
            ->order(['add_time' => 'desc'])
            ->page($page, 15)
            ->select();
        array_unshift($my_netdisc_belong, ['id' => 0, 'add_time' => 1, 'file_name' => '/']);
        foreach ($my_netdisc_belong as $k => $v) {
            $my_netdisc_belong[$k]['file_icon'] = $rank->fileTypeIcon('dir');
            $my_netdisc_belong[$k]['add_time'] = date('Y-m-d H:i:s', $v['add_time']);
        }
        return $this->json_rewrite($my_netdisc_belong);
    }

    /**
     * 移动文件
     */
    public function move_file_dir()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('2d08651c-15b9-1924-c83e-6c65680a82be', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        //判断当前文件是否是我自己的
        $check_my = Db::name('netdisc_belong')
            ->where('user_id', $this->user_info['id'])
            ->where('id', $data['id'])
            ->where('much_id', $data['much_id'])
            ->find();
        if (empty($check_my)) {
            return $this->json_rewrite(['code' => '1', 'msg' => '文件不见了！']);
        }
        if ($check_my['is_dir'] == 1) {
            return $this->json_rewrite(['code' => '1', 'msg' => '文件夹不支持移动！']);
        }
        if ($check_my['is_del'] == 1) {
            return $this->json_rewrite(['code' => '1', 'msg' => '文件不见了！']);
        }
        //执行移动
        $res = Db::name('netdisc_belong')
            ->where('id', $data['id'])
            ->where('much_id', $data['much_id'])
            ->update(['parent_path_id' => $data['move_id']]);
        if ($res !== false) {
            return $this->json_rewrite(['code' => '0', 'msg' => '移动成功！']);
        } else {
            return $this->json_rewrite(['code' => '1', 'msg' => '文件不见了！']);
        }
    }

    /**
     * 新建文件夹
     */
    public function new_folder_dir()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('2d08651c-15b9-1924-c83e-6c65680a82be', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $check = Db::name('netdisc_user_volume')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->where('exp_time', 0)
            ->find();
        if (!empty($check) && $check['use_status'] == 0) {
            return $this->json_rewrite(['code' => 1, 'msg' => '您的网盘已被封禁！']);
        }
        $data['name'] = str_replace(" ", '', $data['name']);
        if (empty($data['name'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '文件名不能为空！']);
        }
        //创建
        $d['nc_id'] = 0;
        $d['user_id'] = $this->user_info['id'];
        $d['file_name'] = emoji_encode($data['name']);
        $d['parent_path_id'] = 0;
        $d['is_dir'] = 1;
        $d['add_time'] = time();
        $d['is_del'] = 0;
        $d['much_id'] = $data['much_id'];
        $ins = Db::name('netdisc_belong')->insert($d);
        if (!$ins) {
            return $this->json_rewrite(['code' => 1, 'msg' => '创建失败！']);
        }
        return $this->json_rewrite(['code' => 0, 'msg' => '创建成功！']);
    }

    /**
     * 文件夹内的文件
     */
    public function get_inside_list()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('2d08651c-15b9-1924-c83e-6c65680a82be', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $page = $data['page'];
        $rank = new RankingUtil();
        $info = Db::name('netdisc_belong')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->where('id', $data['pid'])
            ->field('file_name,is_del')
            ->find();
        $info['count'] = Db::name('netdisc_belong')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->where('parent_path_id', $data['pid'])
            ->where('is_del', 0)
            ->count();
        $list = Db::name('netdisc_belong')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->where('parent_path_id', $data['pid'])
            ->where('is_del', 0)
            ->order('add_time desc')
            ->page($page, 15)
            ->select();
        foreach ($list as $k => $v) {
            $netdisc = Db::name('netdisc')->where('id', $v['nc_id'])->where('much_id', $data['much_id'])->find();
            $list[$k]['file_size'] = $this->setupSize($netdisc['file_size']);
            $list[$k]['file_icon'] = $rank->fileTypeIcon($netdisc['file_suffix']);
            $list[$k]['add_time'] = date('Y-m-d H:i:s', $v['add_time']);
            $list[$k]['file_url'] = $netdisc['file_address'];
            $list[$k]['file_suffix'] = $netdisc['file_suffix'];
            $list[$k]['file_status'] = $netdisc['file_status'];
            $list[$k]['suf'] = $rank->CheckfileTypeIcon($netdisc['file_suffix']);
        }
        return $this->json_rewrite(['code' => 0, 'list' => $list, 'info' => $info]);
    }

    /**
     * 搜索我的所有文件
     */
    public function get_my_search_list()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('2d08651c-15b9-1924-c83e-6c65680a82be', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $rank = new RankingUtil();
        $page = $data['page'];
        $page_k = $data['page_k'];
        $where = [];
        if (!empty($data['search'])) {
            $data['search'] = str_replace(" ", '', $data['search']);
            $data['search'] = emoji_encode($data['search']);
            $where['file_name'] = ['LIKE', "%" . $data['search'] . "%"];
        }

        //获取首页文件(文件夹)
        $my_netdisc_belong = Db::name('netdisc_belong')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->where('is_del', 0)
            ->where('is_dir', 1)
            ->where($where)
            ->order(['add_time' => 'desc'])
            ->page($page, 15)
            ->select();
        $my_netdisc_belong_count = count($my_netdisc_belong);
        if ($my_netdisc_belong_count < 15) {
            //获取首页文件(不是文件夹的文件，不是在文件夹内的文件)
            $my_netdisc = Db::name('netdisc_belong')
                ->where('user_id', $this->user_info['id'])
                ->where('much_id', $data['much_id'])
                ->where('is_del', 0)
                ->where('is_dir', 0)
                ->where($where)
                ->order(['add_time' => 'desc'])
                ->page($page_k, 15)
                ->select();
            foreach ($my_netdisc as $k => $v) {
                array_push($my_netdisc_belong, $v);
            }
        }
        foreach ($my_netdisc_belong as $k => $v) {
            if ($v['nc_id'] != 0) {
                $netdisc = Db::name('netdisc')->where('id', $v['nc_id'])->where('much_id', $data['much_id'])->find();
                $my_netdisc_belong[$k]['file_size'] = $this->setupSize($netdisc['file_size']);
                $my_netdisc_belong[$k]['file_icon'] = $rank->fileTypeIcon($netdisc['file_suffix']);
                $my_netdisc_belong[$k]['file_url'] = $netdisc['file_address'];
                $my_netdisc_belong[$k]['file_suffix'] = $netdisc['file_suffix'];
                $my_netdisc_belong[$k]['file_status'] = $netdisc['file_status'];
                $my_netdisc_belong[$k]['suf'] = $rank->CheckfileTypeIcon($netdisc['file_suffix']);
            } else {
                $my_netdisc_belong[$k]['file_size'] = 0;
                $my_netdisc_belong[$k]['file_icon'] = $rank->fileTypeIcon('dir');
            }
            //$my_netdisc_belong[$k]['file_size'] =0;
            $my_netdisc_belong[$k]['add_time'] = date('Y-m-d H:i:s', $v['add_time']);
        }

        return $this->json_rewrite(['code' => 0, 'msg' => '获取文件列表成功！', 'page' => $my_netdisc_belong_count, 'info' => $my_netdisc_belong]);

    }

    /**
     * 删除文件
     */
    public function del_folder_dir()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('2d08651c-15b9-1924-c83e-6c65680a82be', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        //查看详情
        $info = Db::name('netdisc_belong')
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->where('user_id', $this->user_info['id'])
            ->find();
        if (empty($info)) {
            return $this->json_rewrite(['code' => 1, 'msg' => '文件不见了！']);
        }
        if ($info['is_del'] == 1) {
            return $this->json_rewrite(['code' => 1, 'msg' => '文件已被删除！']);
        }
        //查询里面是否有文件
        //查看详情
//        $check = Db::name('netdisc_belong')
//            ->where('much_id', $data['much_id'])
//            ->where('parent_path_id', $data['id'])
//            ->where('user_id', $this->user_info['id'])
//            ->where('is_del', 0)
//            ->find();
//        if (!empty($check)) {
//            return $this->json_rewrite(['code' => 1, 'msg' => '文件夹内还有其他文件！']);
//        }
        //执行逻辑删除
        $del = Db::name('netdisc_belong')
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->where('user_id', $this->user_info['id'])
            ->update(['is_del' => 1]);
        if ($info['is_dir'] == 1) {
            Db::name('netdisc_belong')
                ->where('much_id', $data['much_id'])
                ->where('parent_path_id', $data['id'])
                ->where('user_id', $this->user_info['id'])
                ->update(['is_del' => 1]);
        }
        Db::name('netdisc_belong')
            ->where('much_id', $data['much_id'])
            ->where('parent_path_id', $data['id'])
            ->where('user_id', $this->user_info['id'])
            ->update(['is_del' => 1]);
        if ($del !== false) {
            return $this->json_rewrite(['code' => 0, 'msg' => '删除成功！']);
        } else {
            return $this->json_rewrite(['code' => 1, 'msg' => '删除失败！']);
        }

    }

    /**
     * 文件上传
     */
    public function file_upload()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('2d08651c-15b9-1924-c83e-6c65680a82be', $data['much_id'])) {
            return $this->json_rewrite(['code' => 1, 'msg' => '应用未开通此插件！']);
        }
        //查询是否开启网络验证
        $ins = Db::name('user_violation')->where('much_id', $data['much_id'])->find();
        if ($ins['open_network_images_offend'] == 1) {
            $res = $this->check_img($data['much_id']);
            if ($res['errcode'] == 87014) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '图片含有违法违规内容']);
            }
        }
        $rank = new RankingUtil();
        $houzhui = strtolower(substr(strrchr($data['file_name'], '.'), 1));
        //查询网盘配置
        $netdisc_config = Db::name('netdisc_config')->where('much_id', $data['much_id'])->find();
        if (bccomp($data['file_size'], $netdisc_config['upload_size_limit']) == 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '上传限制' . $this->setupSize($netdisc_config['upload_size_limit'])]);
        }
        $allowedUploadTypes = explode(",", NetDiskService::allowedUploadTypes($data['much_id']));
        if (!in_array($houzhui, $allowedUploadTypes)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '禁止上传：' . $houzhui . '文件！']);
        }
//        $upload_type_limited = empty($netdisc_config['upload_type_limited']) ? '' : explode(",", $netdisc_config['upload_type_limited']);
//        if (!empty($upload_type_limited)) {
//            if (in_array($houzhui, $upload_type_limited)) {
//                return $this->json_rewrite(['status' => 'error', 'msg' => '禁止上传：' . $houzhui . '文件！']);
//            }
//        }
        //$allowedUploadTypes = NetDiskService::allowedUploadTypes($this->much_id);
        //dump($allowedUploadTypes);exit();
        //获取我的网盘大小
        $user_big = $rank->get_my_netdisc_big(['much_id' => $data['much_id'], 'user_id' => $this->user_info['id']]);
        //已使用
        $user_use = $rank->get_my_netdisc_use(['much_id' => $data['much_id'], 'user_id' => $this->user_info['id']]);
        $zong = bcadd($user_use, $data['file_size']);
        if (bccomp($zong, $user_big) == 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '网盘容量已超限！']);
        }

        $file = request()->file('sngpic');
        $code = NetDiskService::manipulate($file, $data['much_id']);
        if ($code['code'] == 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '上传错误:code:0！']);
        }


        $file_name = str_replace(" ", '', basename($data['file_name'], "." . $houzhui));

        // 启动事务
        Db::startTrans();
        try {
            if ($code['ncId'] == 0) {
                //获取到链接之后保存数据
                $i['file_type'] = $code['fileType'];//文件类型，0本地1云存储2填写
                $i['file_md5'] = $code['fileMD5'];//md5
                $i['file_suffix'] = $code['getExt'];//后缀
                $i['file_size'] = $code['getSize'];
                $i['file_address'] = $code['url'];
                $i['file_status'] = '1';
                $i['up_user_id'] = $this->user_info['id'];
                $i['up_user_ip'] = \request()->ip();
                $i['add_time'] = time();
                $i['much_id'] = $data['much_id'];
                $ins = Db::name('netdisc')->insertGetId($i);
                if (!$ins) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '上传失败:code:1！']);
                }
            }

            $res = Db::name('netdisc_belong')->insert(
                [
                    'nc_id' => $code['ncId'] == 0 ? $ins : $code['ncId'],
                    'user_id' => $this->user_info['id'],
                    'file_name' => $file_name,
                    'parent_path_id' => $data['pid'],
                    'is_dir' => 0, 'add_time' => time(),
                    'is_sell' => 1,
                    'is_del' => 0,
                    'much_id' => $data['much_id']
                ]);
            if (!$res) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '上传失败:code:2！']);
            }
            // 提交事务
            Db::commit();
            return $this->json_rewrite(['status' => 'success', 'msg' => '上传成功!']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'msg' => $e->getMessage()]);
        }
    }

    /*
     *  发布页面获取文件详情
     */
    public function get_add_file_info()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('2d08651c-15b9-1924-c83e-6c65680a82be', $data['much_id'])) {
            return $this->json_rewrite(['status' => 'error', 'code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $rank = new RankingUtil();
        //查询是否运行二次销售
        $info = Db::name('netdisc_belong')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->where('id', $data['file_id'])
            ->find();
        if (empty($info)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '文件不见了！']);
        }
        if ($info['is_sell'] == 0 && ($this->user_info['id'] != $info['user_id'])) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '文件禁止分享！']);
        }
        if ($info['is_del'] == 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '文件已被删除！']);
        }
        if ($info['is_dir'] == 0) {
            //查询文件是否已经被屏蔽
            $netdisc = Db::name('netdisc')
                ->where('much_id', $data['much_id'])
                ->where('id', $info['nc_id'])
                ->find();
            if ($netdisc['file_status'] == 0) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '文件已被屏蔽，禁止分享！']);
            }
            $info['file_size'] = $this->setupSize($netdisc['file_size']);
            $info['file_icon'] = $rank->fileTypeIcon($netdisc['file_suffix']);
            $info['file_url'] = $netdisc['file_address'];
            $info['file_suffix'] = $netdisc['file_suffix'];
            $info['suf'] = $rank->CheckfileTypeIcon($netdisc['file_suffix']);
        } else {
            //是文件夹
            //查询文件夹内的文件数量
            $info['file_count'] = Db::name('netdisc_belong')
                ->where('user_id', $this->user_info['id'])
                ->where('much_id', $data['much_id'])
                ->where('parent_path_id', $data['file_id'])
                ->count();
            if ($info['file_count'] == 0) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '文件夹内没有文件哦！']);
            }
            $info['file_icon'] = $rank->fileTypeIcon('dir');
        }

        $info['add_time'] = date('Y-m-d H:i:s', $info['add_time']);

        return $this->json_rewrite(['status' => 'success', 'info' => $info]);
    }

    /*
 *  网盘页面获取文件详情
 */
    public function get_file_info()
    {
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('2d08651c-15b9-1924-c83e-6c65680a82be', $data['much_id'])) {
            return $this->json_rewrite(['status' => 'error', 'code' => 1, 'msg' => '应用未开通此插件！']);
        }
        $rank = new RankingUtil();
        //查询是否运行二次销售
        $info = Db::name('netdisc_belong')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->where('id', $data['file_id'])
            ->find();
        if (empty($info)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '文件不见了！']);
        }
        if ($info['is_sell'] == 0 && ($this->user_info['id'] != $info['user_id'])) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '文件禁止分享！']);
        }
        if ($info['is_del'] == 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '文件已被删除！']);
        }
        //查询文件是否已经被屏蔽
        $netdisc = Db::name('netdisc')
            ->where('much_id', $data['much_id'])
            ->where('id', $info['nc_id'])
            ->find();
        if ($netdisc['file_status'] == 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '文件已被屏蔽，禁止分享！']);
        }
        $info['file_size'] = $this->setupSize($netdisc['file_size']);
        $info['file_icon'] = $rank->fileTypeIcon($netdisc['file_suffix']);
        $info['file_url'] = $netdisc['file_address'];
        $info['file_suffix'] = $netdisc['file_suffix'];
        $info['suf'] = $rank->CheckfileTypeIcon($netdisc['file_suffix']);
        $info['add_time'] = date('Y-m-d H:i:s', $info['add_time']);
        return $this->json_rewrite(['status' => 'success', 'info' => $info]);
    }

    /**
     * 保存文件到网盘
     */
    public function inst_file_my_ng(){
        $data = input('param.');
        $plug = new Plugunit();
        if (!$plug->check_plug('2d08651c-15b9-1924-c83e-6c65680a82be', $data['much_id'])) {
            return $this->json_rewrite(['status' => 'error', 'code' => 1, 'msg' => '应用未开通此插件！']);
        }
        //查询是否已经初始化网盘
        $check=Db::name('netdisc_user_volume')->where('much_id',$data['much_id'])->where('user_id',$this->user_info['id'])->find();
        if(!$check){
            return $this->json_rewrite(['status' => 'success', 'code' => 1, 'msg' => '未开通网盘，请前往网盘页面开通后重试！']);
        }
        //查询当前帖子内的文件
        $netdisc_sell=Db::name('netdisc_sell')->where('pa_id',$data['id'])->where('much_id',$data['much_id'])->find();
        //查询是否已经保存过了
        $netdisc_belong=Db::name('netdisc_belong')
            ->where('id',$netdisc_sell['nb_id'])
            ->where('nc_id',$netdisc_sell['nc_id'])
            ->where('much_id',$data['much_id'])
            ->find();
        if(!empty($netdisc_belong)&&$netdisc_belong['is_del']==1){
            return $this->json_rewrite(['status' => 'error', 'code' => 1, 'msg' => '文件已被删除！']);
        }
        //判断网盘容量
        $rank = new RankingUtil();
        //获取我的网盘大小
        $user_big = $rank->get_my_netdisc_big(['much_id' => $data['much_id'], 'user_id' => $this->user_info['id']]);
        //已使用
        $user_use = $rank->get_my_netdisc_use(['much_id' => $data['much_id'], 'user_id' => $this->user_info['id']]);
        $file_info=Db::name('netdisc_belong')->where('id',$netdisc_sell['nb_id'])->where('much_id',$data['much_id'])->find();
        $file_size=0;
        if($file_info['is_dir']==0){
            //查询真实文件
            $netdisc=Db::name('netdisc')->where('id',$file_info['nc_id'])->where('much_id',$data['much_id'])->find();
            $file_size=$netdisc['file_size'];
        }else{
            $netdisc_list=Db::name('netdisc_belong')->where('parent_path_id',$netdisc_sell['nb_id'])->where('much_id',$data['much_id'])->select();
            foreach ($netdisc_list as $k => $v) {
                $netdisc=Db::name('netdisc')->where('id',$v['nc_id'])->where('much_id',$data['much_id'])->find();
                $file_size=bcadd($file_size, $netdisc['file_size']);
            }
        }
        $zong = bcadd($user_use, $file_size);
        if (bccomp($zong, $user_big) == 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '网盘容量已超限！']);
        }


        //保存
        $ins_my_n = Db::name('netdisc_belong')->insertGetId([
            'nc_id' => $file_info['nc_id'],
            'user_id' => $this->user_info['id'],
            'file_name' => $file_info['file_name'],
            'parent_path_id' => 0,
            'is_dir' => $file_info['is_dir'],
            'add_time' => time(),
            'is_sell' => $netdisc_sell['is_sell'],
            'is_del' => 0,
            'much_id' => $data['much_id'],
        ]);
        if (!$ins_my_n) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '保存失败！', 'error_code' => 8]);
        }
        if ($file_info['is_dir'] == 1) {
            $belong_in = Db::name('netdisc_belong')->where('parent_path_id', $file_info['id'])->where('much_id', $data['much_id'])->select();
            foreach ($belong_in as $k => $v) {
                $ins_my_in = Db::name('netdisc_belong')->insert([
                    'nc_id' => $v['nc_id'],
                    'user_id' => $this->user_info['id'],
                    'file_name' => $v['file_name'],
                    'parent_path_id' => $ins_my_n,
                    'is_dir' => $v['is_dir'],
                    'add_time' => time(),
                    'is_sell' => $netdisc_sell['is_sell'],
                    'is_del' => 0,
                    'much_id' => $v['much_id'],
                ]);
                if (!$ins_my_in) {
                    return $this->json_rewrite(['status' => 'error', 'msg' => '保存失败！', 'error_code' => 8]);
                }
            }
        }
        return $this->json_rewrite(['status' => 'success', 'msg' => '保存成功，前往网盘查看！']);
    }
}