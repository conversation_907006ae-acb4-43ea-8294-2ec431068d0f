<?php
/**
 * Created by PhpStorm.
 * User: 王创世
 * Date: 2018/11/10
 * Time: 11:33
 */

namespace app\api\controller;


use app\api\service\Util;
use app\common\FluxibleInfo;
use app\common\Gyration;
use app\common\ImageReduce;
use app\common\MagicTrick;
use app\common\Pisces;
use app\common\Suspense;
use think\Cache;
use think\Controller;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\Response;

class Base extends Controller
{
    protected $user_info = null;//用户信息
    protected $much_id = null;//多用户标识
    protected $paper_smingle = null;//帖子设置
    protected $design = null;//小程序设置
    protected $version = 0;//0:显示全部1：隐藏视频
    protected $version_code = '0';
    protected $conceal = 1;  //0：全部显示  1：隐藏

    public function __construct(Request $request = null)
    {
        //session('user_info',['id'=>4]);
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        $much_id = input('param.much_id');
        $token = input('param.token');
        $openid = input('param.openid');
        $version = input('param.version');
        $account_info = input('param.account_info');

        $unfamiliar = Suspense::silence();
        $rhododendron = MagicTrick::pinafore();
        $foreign = MagicTrick::headpiece();
        $roseate = false;
        if ($rhododendron == $foreign) {
            Cache::clear();
            $roseate = true;
        }
        if ($roseate) {
            $abnormal = Suspense::silence();
        } else {
            $abnormal = $unfamiliar;
        }
        if ($unfamiliar !== $abnormal) {
            $rhododendron = MagicTrick::pinafore();
            Pisces::slothful($much_id);
        }
        $external = MagicTrick::chapeau();
        if ($rhododendron != $external) {
            echo 'error';
            exit();
        }
        if (empty($version)) {
            $this->version = 0;//显示全部
        } else {
            $this->version_code = $version;
            //查询版本号
            $check_version = Db::name('version')->where('sign_code', $version)->where('much_id', $much_id)->find();

            //查询是否开启审核
            $chech_sh = Db::name('authority')->where('much_id', $much_id)->find();
            //审核为关闭
            if ($chech_sh['ensure_arbor'] == 0) {
                $this->version = 0;//显示全部
            } else {
                //查找不到版本号
                if (empty($check_version)) {
                    $this->version = 1;//隐藏视频
                } else {
                    //0是待审核
                    if ($check_version['status'] == 0) {
                        $this->version = 1;//隐藏视频
                    } else {
                        $this->version = 0;//显示全部
                    }
                }
            }
            $v = cache('version_overdue' . $much_id);
            if (!$v) {
                //查询最新版本号
                $v = Db::name('version_overdue')->where('much_id', $much_id)->order('id desc')->find();
                cache('version_overdue' . $much_id, $v);
                $end_time = intval($v['most_above_time']);
            } else {
                $end_time = intval($v['most_above_time']);
            }

            //正式版
            if ($account_info == 'release') {
                //新版本第一次不为空
                if ($end_time !== 0) {
                    if (version_compare($version, '1.1.39', '<') && $end_time <= time()) {
                        $response = Response::create($this->json_rewrite(['status' => 'error', 'msg' => '小程序版本过低，请删除老版本后重新访问!']));
                        throw new HttpResponseException($response);
                    }

                } else {//新版本第一次为空
                    //如果小程序版本大于等于最新版本
                    if (version_compare($version, '1.1.39', '>=')) {
                        $time = 86400 * 5 + time();
                        //更新版时间
                        Db::name('version_overdue')->where('id', $v['id'])->where('much_id', $much_id)->update(['most_above_time' => $time]);
                        $v['most_above_time'] = $time;
                        cache('version_overdue' . $much_id, $v);
                    } else {
                        //如果小程序版本小于最新版本
                        $response = Response::create($this->json_rewrite(['status' => 'error', 'msg' => '小程序版本过低，请删除老版本后重新访问!']));
                        throw new HttpResponseException($response);
                    }
                }
            } else {
                //新版本第一次不为空
                if ($end_time !== 0) {
                    //如果小程序版本小于1.1.39
                    if (version_compare($version, '1.1.39', '<') && $end_time <= time()) {
                        $response = Response::create($this->json_rewrite(['status' => 'error', 'msg' => '小程序版本过低，请删除老版本后重新访问!']));
                        throw new HttpResponseException($response);
                    }
                }
            }
        }
        $user = Db::name('user')->where('user_wechat_open_id', $openid)->where('token', $token)->find();
        if ($user) {//接口调用权限
            //$user['user_nick_name'] = emoji_decode($user['user_nick_name']);
            $this->user_info = $user;//用户信息
            if (empty($much_id)) {
                $this->much_id = $user['much_id'];//多用户标识
            } else {
                $this->much_id = $much_id;//多用户标识
            }
            //打开了 权限
            if ($chech_sh['overall_arbor'] == 1) {
                //判断用户是否绑定手机号
                if (empty($user['user_phone'])) {
                    $this->conceal = 1;
                } else {
                    $this->conceal = 0;
                }
            } else {
                $this->conceal = 0;
            }

//            if($account_info == 'develop'&&$user['user_wechat_open_id']!='o9zSf4or2F8Efn2JhsMxxcNJXhHA'){
//                $response = Response::create($this->json_rewrite(['code'=>0,'status' => 'error', 'msg' => '请不要用开发版进行浏览']));
//                throw new HttpResponseException($response);
//            }


            $paper_smingle = Db::name('paper_smingle')->where('much_id', $much_id)->find();
            $this->paper_smingle = $paper_smingle;//帖子设置
            //获取小程序设置
            $design = Db::name('design')->where('much_id', $much_id)->find();
            $this->design = $design;
            //增加IP
            $ip = \request()->ip();
            if ($user['user_access_ip'] != $ip) {
                Db::name('user')->where('id', $user['id'])->update(['user_access_ip' => $ip, 'user_last_time' => time()]);
            } else {
                Db::name('user')->where('id', $user['id'])->update(['user_access_ip' => $ip, 'user_last_time' => time()]);
            }
            if ($user['status'] == 0) {
                $response = Response::create($this->json_rewrite(['status' => 'feng', 'msg' => '您已被封禁！', 'user' => ['open_id' => $user['user_wechat_open_id'], 'forbid_prompt' => $user['forbid_prompt']]]));
                throw new HttpResponseException($response);
            }

        } else {
            if (version_compare($version, '1.0.56') == -1) {
                $response = Response::create($this->json_rewrite(['status' => 'error', 'msg' => '账户未授权!']));
                throw new HttpResponseException($response);
            }
        }

    }

    /**
     * 截取字符串
     */
    public function subtext($text, $length)
    {
        if (mb_strlen($text, 'utf8') > $length) {
            return mb_substr($text, 0, $length, 'utf8');
        }
        return $text;
    }

//腾讯视频获取实际播放放地址
    public function getVideoInfo($vid)
    {
        //$vid = "o0928sun9kq";

        $urlString = 'https://vv.video.qq.com/getinfo?platform=101001&charge=0&otype=json&defn=shd&vids=' . $vid;

        $res = $this->fopen_url($urlString);

        //字符串截取json
        $json = str_replace("QZOutputJson=", "", $res);
        $json = str_replace("}}]}};", "}}]}}", $json);
        $json = str_replace(";", "", $json);
        //json转换为数组
        $json = json_decode($json, true);

        $fileName = $json['vl']['vi'][0]['fn'];
        $fvkey = $json['vl']['vi'][0]['fvkey'];
        $host = $json['vl']['vi'][0]['ul']['ui'][2]['url'];
        $url = $host . $fileName . '?vkey=' . $fvkey;
        return $url;
    }

    /**
     * 获取远程文件内容
     *
     */
    public function fopen_url($url)
    {
        $file_content = '';
        $option = ['ssl' => ['verify_peer' => false, 'verify_peer_name' => false]];

        if (function_exists('file_get_contents')) {
            $file_content = @file_get_contents($url, false, stream_context_create($option));
        } elseif (ini_get('allow_url_fopen') && ($file = @fopen($url, 'rb'))) {
            $i = 0;
            while (!feof($file) && $i++ < 1000) {
                $file_content .= strtolower(fread($file, 4096));
            }
            fclose($file);
        } else {
            $curl_handle = curl_init();
            curl_setopt($curl_handle, CURLOPT_URL, $url);
            curl_setopt($curl_handle, CURLOPT_CONNECTTIMEOUT, 2);
            curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl_handle, CURLOPT_FAILONERROR, 1);
            curl_setopt($curl_handle, CURLOPT_USERAGENT, 'Trackback Spam Check'); //引用垃圾邮件检查
            $file_content = curl_exec($curl_handle);
            curl_close($curl_handle);
        }
        return $file_content;
    }

    /**
     * 列表加密
     */
    public function json_rewrite($arr)
    {
        if (version_compare($this->version_code, '1.1.39', '<')) {
            return json_encode($arr);
        } else {
            return base64_encode(rawurlencode(json_encode($arr)));
        }
    }

    /**
     * 手机加密
     * @param $string
     * @return string
     */
    public function cipher_text($string)
    {
        $wteh = substr($string, 0, 3);
        $yeth = substr($string, -4);
        return "{$wteh}*****{$yeth}";
    }

    /**
     * 返回两个时间的相距时间，*年*月*日*时*分*秒
     * @param int $one_time 时间戳一  大的时间戳
     * @param int $two_time 时间戳二  小的时间戳
     * @param int $return_type 默认值为0，0/不为0则拼接返回，1/*秒，2/*分*秒，3/*时*分*秒/，4/*日*时*分*秒，5/*月*日*时*分*秒，6/*年*月*日*时*分*秒
     * @param array $format_array 格式化字符，例，array('年', '月', '日', '时', '分', '秒')
     * @return String or false
     */
    public function getRemainderTime($one_time, $two_time, $return_type = 0, $format_array = array('年', '个月', '天'))
    {
        if ($return_type < 0 || $return_type > 6) {
            return false;
        }
        if (!(is_int((int)$one_time) && is_int((int)$two_time))) {
            return false;
        }
        $remainder_seconds = abs($one_time - $two_time);
        //年
        $years = 0;
        if (($return_type == 0 || $return_type == 6) && $remainder_seconds - 31536000 > 0) {
            $years = floor($remainder_seconds / (31536000));
        }
        //月
        $monthes = 0;
        if (($return_type == 0 || $return_type >= 5) && $remainder_seconds - $years * 31536000 - 2592000 > 0) {
            $monthes = floor(($remainder_seconds - $years * 31536000) / (2592000));
        }
        //日
        $days = 0;
        if (($return_type == 0 || $return_type >= 4) && $remainder_seconds - $years * 31536000 - $monthes * 2592000 - 86400 > 0) {
            $days = floor(($remainder_seconds - $years * 31536000 - $monthes * 2592000) / (86400));
        }
        $return = false;
        switch ($return_type) {
            case 0:
                if ($years > 0) {
                    $return = $years . $format_array[0] . $monthes . $format_array[1] . $days . $format_array[2];
                } else if ($monthes > 0) {
                    $return = $monthes . $format_array[1] . $days . $format_array[2];
                } else if ($days > 0) {
                    $return = $days . $format_array[2];
                }
                break;
            case 4:
                $return = $days . $format_array[2];
                break;
            case 5:
                $return = $monthes . $format_array[1] . $days . $format_array[2];
                break;
            case 6:
                $return = $years . $format_array[0] . $monthes . $format_array[1] . $days . $format_array[2];
                break;
            default:
                $return = false;
        }
        return $return;
    }

    // 安全过滤
    protected function safe_html($html)
    {
        $elements = [
            'html' => ['class', 'style'],
            'body' => ['class', 'style'],
            'a' => ['target', 'href', 'title', 'class', 'style', 'jump_type', 'data_appid'],
            'abbr' => ['title', 'class', 'style'],
            'address' => ['class', 'style'],
            'area' => ['shape', 'coords', 'href', 'alt', 'class', 'style'],
            'article' => ['class', 'style'],
            'aside' => ['class', 'style'],
            'audio' => ['autoplay', 'controls', 'loop', 'preload', 'src', 'class', 'style'],
            'b' => ['class', 'style'],
            'bdi' => ['dir', 'class', 'style'],
            'bdo' => ['dir', 'class', 'style'],
            'big' => ['class', 'style'],
            'blockquote' => ['cite', 'class', 'style'],
            'br' => ['class', 'style'],
            'caption' => ['class', 'style'],
            'center' => ['class', 'style'],
            'cite' => ['class', 'style'],
            'code' => ['class', 'style'],
            'col' => ['align', 'valign', 'span', 'width', 'class', 'style'],
            'colgroup' => ['align', 'valign', 'span', 'width', 'class', 'style'],
            'dd' => ['class', 'style'],
            'del' => ['datetime', 'class', 'style'],
            'details' => ['open', 'class', 'style'],
            'div' => ['class', 'style'],
            'dl' => ['class', 'style'],
            'dt' => ['class', 'style'],
            'em' => ['class', 'style'],
            'font' => ['color', 'size', 'face', 'class', 'style'],
            'footer' => ['class', 'style'],
            'h1' => ['class', 'style'],
            'h2' => ['class', 'style'],
            'h3' => ['class', 'style'],
            'h4' => ['class', 'style'],
            'h5' => ['class', 'style'],
            'h6' => ['class', 'style'],
            'header' => ['class', 'style'],
            'hr' => ['class', 'style'],
            'i' => ['class', 'style'],
            'img' => ['src', 'alt', 'title', 'width', 'height', 'id', 'class', 'style'],
            'ins' => ['datetime', 'class', 'style'],
            'li' => ['class', 'style'],
            'mark' => ['class', 'style'],
            'nav' => ['class', 'style'],
            'ol' => ['class', 'style'],
            'p' => ['class', 'style', 'align'],
            'pre' => ['class', 'style'],
            's' => ['class', 'style'],
            'section' => ['class', 'style'],
            'small' => ['class', 'style'],
            'span' => ['class', 'style'],
            'sub' => ['class', 'style'],
            'sup' => ['class', 'style'],
            'strong' => ['class', 'style'],
            'table' => ['width', 'border', 'align', 'valign', 'class', 'style'],
            'tbody' => ['align', 'valign', 'class', 'style'],
            'td' => ['width', 'rowspan', 'colspan', 'align', 'valign', 'class', 'style'],
            'tfoot' => ['align', 'valign', 'class', 'style'],
            'th' => ['width', 'rowspan', 'colspan', 'align', 'valign', 'class', 'style'],
            'thead' => ['align', 'valign', 'class', 'style'],
            'tr' => ['rowspan', 'align', 'valign', 'class', 'style'],
            'tt' => ['class', 'style'],
            'u' => ['class', 'style'],
            'ul' => ['class', 'style'],
            'video' => ['autoplay', 'controls', 'loop', 'preload', 'src', 'height', 'width', 'class', 'style'],
            'embed' => ['src', 'height', 'align', 'width', 'class', 'style', 'type', 'pluginspage', 'wmode', 'play', 'loop', 'menu', 'allowscriptaccess', 'allowfullscreen'],
            'source' => ['src', 'type'],
        ];
        $html = strip_tags($html, '<' . implode('><', array_keys($elements)) . '>');
        $xml = new \DOMDocument();
        libxml_use_internal_errors(true);
        if (!strlen($html)) {
            return '';
        }
        if ($xml->loadHTML('<meta http-equiv="Content-Type" content="text/html; charset=utf-8">' . $html)) {
            foreach ($xml->getElementsByTagName("*") as $element) {
                if (!isset($elements[$element->tagName])) {
                    $element->parentNode->removeChild($element);
                } else {
                    for ($k = $element->attributes->length - 1; $k >= 0; --$k) {
                        if (!in_array($element->attributes->item($k)->nodeName, $elements[$element->tagName])) {
                            $element->removeAttributeNode($element->attributes->item($k));
                        } elseif (in_array($element->attributes->item($k)->nodeName, ['href', 'src', 'style', 'background', 'size'])) {
                            $_keywords = ['javascript:', 'javascript.:', 'vbscript:', 'vbscript.:', ':expression'];
                            $find = false;
                            foreach ($_keywords as $a => $b) {
                                if (false !== strpos(strtolower($element->attributes->item($k)->nodeValue), $b)) {
                                    $find = true;
                                }
                            }
                            if ($find) {
                                $element->removeAttributeNode($element->attributes->item($k));
                            }
                        }
                    }
                }
            }
        }

        //$html = substr($xml->saveHTML($xml->documentElement), 12, -14);

        $html = strip_tags($html, '<' . implode('><', array_keys($elements)) . '>');
        return $html;
    }

    public function getMyDate($d)
    {
        $marr = array('January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December');
        $stamp = $d;
        $m = date('m', $stamp);
        return substr($marr[$m - 1], 0, 3) . ' ' . date('d', $stamp) . ', ' . date('Y', $stamp);
    }

    public function setupSize($fileSize)
    {
        $size = sprintf("%u", $fileSize);
        if ($size == 0) {
            return ("0 Bytes");
        }
        $sizename = array(" B", " KB", " MB", " GB", " TB", " PB", " EB", " ZB", " YB");
        return round($size / pow(1024, ($i = floor(log($size, 1024)))), 2) . $sizename[$i];
    }

    public function check_img($uniacid)
    {
        // 获取后台 APPID
        $util = new Util();
        $access_token = $util->getWchatAcctoken($uniacid);
        // 获取图片对象
        $file = request()->file('sngpic');
        if (!$file) {
            return ['code' => 0, 'status' => 'No file uploaded.'];
        }
        // 获取临时图片属性
        $fileInfo = $file->getInfo();
        // 获取临时图片类型
        $fileExt = explode('/', $fileInfo['type']);
        if (!in_array($fileExt[1], ['gif', 'jpg', 'jpeg', 'bmp', 'png'])) {
            return ['code' => 0, 'status' => 'Invalid image type.'];
        }
        // 获取临时图片信息
        $tempImageInfo = getimagesize($fileInfo['tmp_name']);
        $uploadDir = ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'uploads' . DS . date('Ymd');
        $tempSaveName = $uploadDir . DS . md5(Suspense::getRandomCode()) . '.' . strtolower($fileExt[1]);
        // 确保目录存在
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }
        // 判断是否需要压缩
        $shouldCompress = ($fileInfo['size'] >= 1048576 || $tempImageInfo[0] >= 750 || $tempImageInfo[1] >= 1334);
        if ($shouldCompress) {
            // 根据文件大小设置合适的裁剪比例
            if ($fileInfo['size'] > 10 * 1024 * 1024) { // 大于 10MB
                $amplitude = 0.1;
            } elseif ($fileInfo['size'] > 8 * 1024 * 1024) { // 大于 8MB 且小于等于 10MB
                $amplitude = 0.2;
            } elseif ($fileInfo['size'] > 6 * 1024 * 1024) { // 大于 6MB 且小于等于 8MB
                $amplitude = 0.3;
            } elseif ($fileInfo['size'] > 3.5 * 1024 * 1024) { // 大于 3.5MB 且小于等于 6MB
                $amplitude = 0.4;
            } elseif ($fileInfo['size'] > 1.5 * 1024 * 1024) { // 大于 1.5MB 且小于等于 3.5MB
                $amplitude = 0.5;
            } else { // 小于等于 1.5MB
                $amplitude = 0.7;
            }
            // 创建图像裁剪对象并生成临时图片
            $imageReduce = new ImageReduce($file->getInfo('tmp_name'), $amplitude);
            $imageReduce->compressImg($tempSaveName);
            // 如果临时图片仍然大于限制，则进一步裁剪
            while (true) {
                $tempImageInfo = getimagesize($tempSaveName);
                $tempImageSize = filesize($tempSaveName);
                if ($tempImageSize < 1048576 && $tempImageInfo[0] < 750 && $tempImageInfo[1] < 1334) {
                    break;
                }
                $imageReduce = new ImageReduce($tempSaveName, 0.5);
                $imageReduce->compressImg($tempSaveName);
            }
        } else {
            // 不需要压缩，直接保存原图
            //$file->move($uploadDir, basename($tempSaveName));
            // 创建图像裁剪对象 不做裁剪
            $imageReduce = new ImageReduce($file->getInfo('tmp_name'), 1);
            // 生成临时图片
            $imageReduce->compressImg($tempSaveName);
        }
        // 进行图片安全检查
        $url = "https://api.weixin.qq.com/wxa/img_sec_check?access_token=" . $access_token;
        $file_info = [
            'media' => new \CURLFile($tempSaveName),
        ];
        $result = $this->curl_form($file_info, $url, '');
        $error_json = json_decode($result, true);
        // 删除临时文件
        @unlink($tempSaveName);
        return $error_json;
    }


    public function curl_form($post_data, $sumbit_url, $http_url)
    {
        //初始化
        $ch = curl_init();
        //设置变量
        curl_setopt($ch, CURLOPT_URL, $sumbit_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);//执行结果是否被返回，0是返回，1是不返回
        curl_setopt($ch, CURLOPT_HEADER, 0);//参数设置，是否显示头部信息，1为显示，0为不显示
        curl_setopt($ch, CURLOPT_REFERER, $http_url);
        //表单数据，是正规的表单设置值为非0
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);//设置curl执行超时时间最大是多少
        //使用数组提供post数据时，CURL组件大概是为了兼容@filename这种上传文件的写法，
        //默认把content_type设为了multipart/form-data。虽然对于大多数web服务器并
        //没有影响，但是还是有少部分服务器不兼容。本文得出的结论是，在没有需要上传文件的
        //情况下，尽量对post提交的数据进行http_build_query，然后发送出去，能实现更好的兼容性，更小的请求数据包。
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        //执行并获取结果
        $output = curl_exec($ch);
        //    释放cURL句柄
        curl_close($ch);
        return $output;
    }

}