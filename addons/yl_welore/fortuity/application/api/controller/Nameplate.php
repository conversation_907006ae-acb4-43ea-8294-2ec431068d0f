<?php


namespace app\api\controller;


use app\api\service\Util;
use think\Db;

class Nameplate extends Base
{
    /**
     * 获取用户信息
     */
    public function get_user_info()
    {
        $user_info = $this->user_info;
        $user_info['user_nick_name'] = emoji_decode($user_info['user_nick_name']);
        return $this->json_rewrite(['user_info' => $user_info]);
    }

    /**
     * 获取缓存中的身份卡
     */
    public function get_one_name_card()
    {
        $data = input('param.');
        //判断是否过期
        $check = Db::name('user_camouflage_card')->where('expired_time', '> time', date('Y-m-d H:i:s', time()))->where('ccid', $data['name_id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        if (empty($check)) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '身份已过期！']);
        } else {
            $info = Db::name('camouflage_card')->where('id', $data['name_id'])->where('much_id', $data['much_id'])
                ->find();
            return $this->json_rewrite(['status' => 'success', 'info' => $info]);
        }
    }

    /**
     * 获取身份卡
     */
    public function get_nameplate()
    {
        $data = input('param.');
        $list = Db::name('camouflage_card')->where('much_id', $data['much_id'])
            ->where('is_del', 0)
            ->order('scores')
            ->select();
        foreach ($list as $k => $v) {
            $check = Db::name('user_camouflage_card')->where('expired_time', '> time', date('Y-m-d H:i:s', time()))->where('ccid', $v['id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();

            if (!empty($check)) {
                if ($check['expired_time'] > time()) {
                    $list[$k]['is_ok'] = 1;
                    $list[$k]['expired_time'] = date('Y-m-d H:i', $check['expired_time']);
                } else {
                    $list[$k]['is_ok'] = 0;
                }
            } else {
                $list[$k]['is_ok'] = 0;
            }

        }
        return $this->json_rewrite($list);
    }

    /**
     * 开通身份卡
     */
    public function ins_user_nameplate()
    {
        $util = new Util();
        $data = input('param.');
        $data['name_id'] = abs($data['name_id']);
        $info = Db::name('camouflage_card')->where('much_id', $data['much_id'])
            ->where('id', $data['name_id'])
            ->find();
        //判断 用户积分
        if (bccomp($info['unlock_fraction'], $this->user_info['fraction'], 2) == 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => $this->design['confer'] . '不足，解锁失败！']);
        }
        //计算到期时间
        $time = bcadd(($info['cost_day'] * 86400), time());
        //计算金额
        $money = bcsub($this->user_info['fraction'], $info['unlock_fraction'], 2);
        // 启动事务
        Db::startTrans();
        try {
            $res = Db::name('user_camouflage_card')->insert(['ccid' => $data['name_id'], 'user_id' => $this->user_info['id'], 'expired_time' => $time, 'much_id' => $data['much_id']]);
            if (!$res) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '解锁失败1！']);
            }
            if ((abs($info['unlock_fraction'])) != 0) {
                //增加明细
                $amo = $util->user_amount($this->user_info['id'], 2, $info['unlock_fraction'], $this->user_info['fraction'], $money, $this->user_info['conch'], $this->user_info['conch'], 1, '解锁身份牌[' . $info['forgery_name'] . ']', $data['much_id']);
                if (!$amo) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '解锁失败2！']);
                }
                //扣除积分
                $red = Db::name('user')->where('id', $this->user_info['id'])->update(['fraction' => $money]);
                if (!$red) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '解锁失败3！']);
                }

            }

            // 提交事务
            Db::commit();
            return $this->json_rewrite(['status' => 'success', 'msg' => '解锁成功！']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'msg' => '解锁失败5！']);
        }
    }

    /**
     * 检测当前圈子关注了没有/或者圈子等级/会员等级
     */
    public function check_open_id()
    {
        $data = input('param.');
        $util = new Util();
        $user_vip = $util->get_user_vip($this->user_info['id']);
        $msg = 0;
        $info = Db::name('territory')->where('id', $data['open_id'])->where('much_id', $data['much_id'])->find();
        if (empty($info)) {
            $msg = 1;
        }
        //会员权限
        if ($info['attention'] == 2 && $user_vip == 0) {
            $msg = 1;
        }
        //等级权限
        if ($info['release_level'] != 0 && ($this->user_info['level'] < $info['release_level'])) {
            $msg = 1;
        }
        //是否加入权限
        if ($info['attention'] == 1) {
            $user_trailing = Db::name('user_trailing')->where('user_id', $this->much_id)->where('tory_id', $data['open_id'])->where('much_id', $data['much_id'])->count();
            $msg = $user_trailing == 0 ? 1 : 0;
        }
        //查询超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg == 1) {
            $msg = 0;
        }
        if ($msg == 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '圈子有权限，请重新选择！']);
        } else {
            return $this->json_rewrite(['status' => 'success', 'msg' => '成功！']);
        }
    }

    /**
     * 待审核数量
     */
    public function get_quantity()
    {
        //判断当前用户是否是超管或者圈主
        $data = input('param.');
        $where = [];
//        //圈主
//        $da = Db::name('territory_learned')->alias('l')
//            ->join('territory t', 'l.tory_id=t.id')
//            ->where('l.bulord', 'like', '%' . $data['openid'] . '%')
//            ->field('t.id')
//            ->where('t.status', 1)
//            ->where('t.is_del', 0)
//            ->where('l.much_id', $data['much_id'])
//            ->where('t.much_id', $data['much_id'])
//            ->select();
//        if ($da) {
//            $da_ids = '';
//            foreach ($da as $k => $v) {
//                $da_ids .= $v['id'] . ',';
//            }
//            $da_ids = substr($da_ids, 0, -1);
//            $where['p.tory_id'] = ['in', $da_ids];
//        }
        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        if ($cg > 0) {
            $where = [];
        }
        $list = Db::name('paper')->alias('p')
            ->join('territory r', 'r.id=p.tory_id')
            ->where('p.much_id', $data['much_id'])
            ->where('p.study_status', 0)
            ->where('p.whether_delete', 0)
            ->where('p.whether_type', 0)
            ->where('r.is_del', 0)
            ->where('r.status', 1)
            ->where($where)
            ->count();
        $paper_reply = Db::name('paper')->alias('p')
            ->join('paper_reply r', 'r.paper_id=p.id')
            ->where('p.much_id', $data['much_id'])
            ->where('p.study_status', 1)
            ->where('p.whether_delete', 0)
            ->where('p.whether_type', 0)
            ->where('r.whether_delete', 0)
            ->where('r.whether_type', 0)
            ->where('r.reply_status', 0)
            ->field('r.*')
            ->where($where);
        $paper_reply_count=$paper_reply->count();
        $paper_reply_duplex = $paper_reply->select();
        $paper_reply_duplex_count = 0;
        foreach ($paper_reply_duplex as $k => $v) {
            $reply_duplex = Db::name('paper_reply_duplex')
                ->where('much_id', $data['much_id'])
                ->where('reply_id', $v['id'])
                ->where('duplex_status', 0)
                ->where('whether_delete', 0)
                ->count();
            $paper_reply_duplex_count += $reply_duplex;
        }
        //点评
        $paper_review_score = Db::name('paper_review_score')->alias('p')
            ->where('p.much_id', $data['much_id'])
            ->where('p.audit_status', 0)
            ->where($where)
            ->count();
        //认证
        $user_attest = Db::name('user_attest')
            ->where('much_id', $data['much_id'])
            ->where('adopt_status', 0)
            ->count();
        //圈子
        $territory_petition = Db::name('territory_petition')
            ->where('much_id', $data['much_id'])
            ->where('realm_status', 0)
            ->count();
        //小秘密
        $sprout = Db::name('sprout')
            ->where('much_id', $data['much_id'])
            ->where('status', 0)
            ->count();
        return $this->json_rewrite(['sprout'=>$sprout,'tie' => $list, 'hui' => $paper_reply_count, 'ping' => $paper_reply_duplex_count, 'dian' => $paper_review_score, 'ren' => $user_attest, 'quan' => $territory_petition]);
    }
}