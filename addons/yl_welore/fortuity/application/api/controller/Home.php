<?php

namespace app\api\controller;


use app\api\service\Alternative;
use app\api\service\Moderation;
use app\api\service\Scheduled;
use app\api\service\Util;
use app\common\FluxibleInfo;
use app\common\Gyration;
use app\common\ImageReduce;
use app\common\NetDiskService;
use app\common\Suspense;
use app\urge\controller\Upload;
use think\Db;

class Home extends Base
{

    /**
     * 获取用户信息
     */
    public function get_user_info()
    {
        $data = input('param.');
        $rs = ['status' => 'success'];
        $util = new Util();
        if ($this->user_info) {
            $rs['msg'] = '获取成功！';
            $info = $this->user_info;;
            $info['user_nick_name'] = emoji_decode($info['user_nick_name']);
            $info['is_vip'] = $util->get_user_vip($info['id']);
            if ($info['vip_end_time'] > time()) {
                $info['vip_end_time_tmpl'] = date('Y/m/d', $info['vip_end_time']) . '到期';
                $info['vip_end_time'] = $this->getRemainderTime(time(), $info['vip_end_time'], 4);
            } else {
                $info['vip_end_time'] = 0;
                $info['vip_end_time_tmpl'] = 0;
            }
            //关注的圈子
            $trailing = Db::name('user_trailing')->alias('u')
                ->join('territory t', 't.id=u.tory_id')
                ->where('t.status', 1)
                ->where('u.user_id', $info['id'])
                ->count();
            $info['trailing'] = formatNumber($trailing);
            //我发布的
            $paper = Db::name('paper')->where('user_id', $info['id'])->count();
            $info['paper'] = formatNumber($paper);
            //关注的人
            $user_track = Db::name('user_track')->alias('t')
                ->join('user u', 'u.id=t.qu_user_id')
                ->where('t.at_user_id', $info['id'])
                ->group('t.qu_user_id')
                ->count();
            $info['user_track'] = formatNumber($user_track);
            //粉丝
            $user_fs = Db::name('user_track')->alias('t')
                ->join('user u', 'u.id=t.at_user_id')
                ->where('t.qu_user_id', $info['id'])
                ->group('t.at_user_id')
                ->count();
            $info['user_fs'] = formatNumber($user_fs);
            $info['autograph'] = emoji_decode($info['autograph']);
            $info['is_nick_name'] = false;
            $info['is_nick_name_end'] = 0;
            //获取改名时间
            if ($info['is_vip'] == 0) {//不是vip90天
                if ($info['nick_name_time'] + 90 * 86400 > time()) {
                    $info['is_nick_name_end'] = date('Y-m-d H:i:s', $info['nick_name_time'] + 90 * 86400);
                    $info['is_nick_name'] = true;
                }
            }
            if ($info['is_vip'] == 1) {//是vip30天
                if ($info['nick_name_time'] + 30 * 86400 > time()) {
                    $info['is_nick_name_end'] = date('Y-m-d H:i:s', $info['nick_name_time'] + 30 * 86400);
                    $info['is_nick_name'] = true;
                }
            }
            //获取今日是否签到
            $check = Db::name('user_punch')->whereTime('punch_time', 'today')->where('user_id', $info['id'])->order('punch_time desc')->count();
            $info['is_sign'] = $check;
            //查询未读消息
            $user_male = Db::name('user_smail')->where('user_id', $info['id'])->where('status', 0)->count();
            $info['user_male'] = $user_male;
            //查询是否有验证码
            $user_yzm = Db::name('user_invitation_code')->where('user_id', $info['id'])->find();
            if (empty($user_yzm)) {
                $yzm = $this->get_yzm_random(6);
                Db::name('user_invitation_code')->insert(['user_id' => $info['id'], 'code' => $yzm, 'much_id' => $this->much_id]);
            }
            //查询今日贝壳兑换次数
            $ji_bei = Db::name('user_currency_conversion')
                ->where('user_id', $info['id'])
                ->where('much_id', $this->much_id)
                ->where('conver_type', 1)
                ->whereTime('conver_time', 'today')
                ->count();
            //查询今日积分兑换次数
            $bei_ji = Db::name('user_currency_conversion')
                ->where('user_id', $info['id'])
                ->where('much_id', $this->much_id)
                ->where('conver_type', 0)
                ->whereTime('conver_time', 'today')
                ->count();
            $info['ji_bei'] = $ji_bei;
            $info['bei_ji'] = $bei_ji;
            $info['version'] = $this->version;
            //获取当前用户是否是超管
            $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
            $info['admin'] = $cg;
            $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
            $authority['vip'] = $util->get_user_vip($this->user_info['id']);
            $info['authority'] = $authority;
            $info['level_info'] = $util->get_user_level($info['level'], $data['much_id']);
            //当前用户的勋章
            $info['wear_merit'] = $util->get_medal($info['wear_merit'], $data['much_id']);
            //获取是否有设置抽奖
            $check_chou = Db::name('event_raffle')->where('much_id', $data['much_id'])->where('status', 1)->find();
            $info['check_chou'] = empty($check_chou) ? 0 : 1;
            //当前用户昵称
            $info['special'] = $util->get_user_special_nickname($info['id'], $data['much_id']);
            $info['avatar_frame'] = $util->get_user_avatar_frame($info['id'], $data['much_id']);
            //获取今日@我的
            $secret = Db::name('sprout')
                ->where('at_user_id', $info['id'])
                ->where('status', 1)
                ->where('is_del', 0)
                ->where('much_id', $data['much_id'])
                ->whereTime('send_time', 'd')
                ->count();
            $info['secret'] = $secret >= 99 ? '99+' : $secret;
            if (!empty($info['user_phone'])) {
                $info['user_phone'] = $this->cipher_text($info['user_phone']);
            }
            //获取我认证的
            // $info['attest'] = $util->get_att_info($this->user_info['id'], $data['much_id'])['attest']['at_icon'];
            $info['attest_info'] = $util->get_att_info($this->user_info['id'], $data['much_id'])['attest'];
            $info['conceal'] = $this->conceal;
            //获取任务完成情况
            $info['task_count'] = Scheduled::GetTaskComplete($info['id'], $data['much_id']);
            //查询是否是店员
            //查询店员权限
            $easy_info_shop_assistant = Db::name('easy_info_shop_assistant')
                ->where('user_id', $info['id'])
                ->where('much_id', $data['much_id'])
                ->find();
            $info['assistant'] = 1;
            if (!$easy_info_shop_assistant || $easy_info_shop_assistant['status'] == 0) {
                $info['assistant'] = 0;
            }

            $rs['info'] = $info;
            FluxibleInfo::SendDetectPost([
                'type' => 3,
                'user_id' => $this->user_info['id'],
                'much_id' => $this->much_id,
            ], $this->much_id);
        } else {
            $rs['status'] = 'error';
            $rs['msg'] = '系统忙，请稍候重试！';
        }
        return $this->json_rewrite($rs);
    }

    /**
     * 文章用户信息
     */
    public function get_article_user_info()
    {
        $rs = ['status' => 'success'];
        $util = new Util();
        $data = input('param.');
        if ($this->user_info) {
            $rs['msg'] = '获取成功！';
            $info['user_head_sculpture'] = $this->user_info['user_head_sculpture'];
            $info['conch'] = $this->user_info['conch'];
            $info['fraction'] = $this->user_info['fraction'];
            $info['user_nick_name'] = emoji_decode($info['user_nick_name']);
            $info['is_vip'] = $util->get_user_vip($info['id']);
            $info['user_phone'] = ciphertext($this->user_info['user_phone']);
            $info['avatar_frame'] = $util->get_user_avatar_frame($this->user_info['id'], $data['much_id']);
            $info['attest_info'] = $util->get_att_info($this->user_info['id'], $data['much_id'])['attest'];
            $rs['info'] = $info;
        } else {
            $rs['status'] = 'error';
            $rs['msg'] = '系统忙，请稍候重试！';
        }
        return $this->json_rewrite($rs);
    }

    public function get_article_authority()
    {
        $data = input('param.');
        $info = Db::name('authority')
            ->where('much_id', $data['much_id'])
            ->field('video_download_arbor,engrave_arbor,re_force_phone_arbor,ios_pay_arbor,guard_arbor,recharge_arbor,tribute_arbor,hair_audio_arbor,video_auto_arbor')
            ->find();
        $info['version'] = $this->version;
        $info['currency'] = $this->design['currency'];
        $info['confer'] = $this->design['confer'];
        $config = Db::name('paper_review_config')->where('much_id', $data['much_id'])->find();
        $info['is_auto_audit'] = empty($config) ? 0 : $config['is_auto_audit'];
        $info['is_all_review'] = empty($config) ? 0 : $config['is_all_review'];
        return $this->json_rewrite($info);
    }

    /**
     * 用户签到
     */
    public function add_user_punch()
    {

        $data = input('param.');
        //$user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        $check = Db::name('user_punch')->whereTime('punch_time', 'today')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->order('punch_time desc')->find();
        if ($check) {
            $rs = ['status' => 'error', 'msg' => '今天已经签过，请明天再来！'];
            return $this->json_rewrite($rs);
        }
        Db::startTrans();
        //$util = new Util();
        try {
            $ins = Db::name('user_punch')
                ->insert(['user_id' => $this->user_info['id'], 'fraction' => 0, 'punch_time' => time(), 'much_id' => $data['much_id']]);
            if (!$ins) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '签到失败，请稍候重试'];
                return $this->json_rewrite($rs);
            }
            Db::commit();
            $rs = ['status' => 'success', 'msg' => '签到成功！'];
            FluxibleInfo::SendDetectPost([
                'type' => 0,
                'user_id' => $this->user_info['id'],
                'much_id' => $data['much_id'],
            ], $data['much_id']);
            return $this->json_rewrite($rs);
        } catch (\Exception $e) {
            Db::rollback();
            $rs = ['status' => 'error', 'msg' => '签到失败，请稍候重试' . $e->getMessage()];
            return $this->json_rewrite($rs);
        }
    }

    /**
     * 编辑资料
     */
    public function edit_user_info()
    {
        $rs = ['status' => 'success', 'msg' => '保存成功'];
        $data = input('param.');
        $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($authority['user_info_update_arbor'] == 0) {
            $rs = ['status' => 'error', 'msg' => '修改个人信息功能暂时不可用！'];
            return $this->json_rewrite($rs);
        }
        $user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        $user_list = Db::name('user')->where('id', '<>', $this->user_info['id'])->where('much_id', $data['much_id'])->where('user_nick_name', emoji_encode($data['nick_name']))->find();
        if ($user_list) {
            $rs = ['status' => 'error', 'msg' => '昵称已存在，换个吧'];
            return $this->json_rewrite($rs);
        }
        $util = new Util();
        //判断是否有违规词
            $check_name = $util->get_check_msg($data['nick_name'], $data['much_id'], $data['openid']);
            if ($check_name['status'] == 'error') {
                return $this->json_rewrite($check_name);
            }
            $up['user_nick_name'] =emoji_encode($data['nick_name']);
            $up['nick_name_time'] = emoji_encode($data['nick_name']) == $user_info['user_nick_name'] ? 0 : time();
        $up['user_head_sculpture'] = $data['img'];
        $up['gender'] = $data['gender'];
        $up['user_home_access_status'] = $data['user_home_access_status'] ? 1 : $data['user_home_access_status'];
        $up['autograph'] = $this->safe_html(emoji_encode($data['autograph']));
        if (!empty($up['autograph'])) {
            $check_autograph = $util->get_check_msg($up['autograph'], $data['much_id'], $data['openid']);
            if ($check_autograph['status'] == 'error') {
                return $this->json_rewrite($check_autograph);
            }
        }
        $up['user_head_sculpture'] = $data['img'];
        $up['is_enable_fans_privacy'] = $data['is_enable_fans_privacy'];
        $up['is_enable_concern_privacy'] = $data['is_enable_concern_privacy'];
        $update = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update($up);
        if ($update !== false) {
            return $this->json_rewrite($rs);
        } else {
            $rs = ['status' => 'error', 'msg' => '保存失败'];
            return $this->json_rewrite($rs);
        }
    }


    /**
     * 获取指定会员信息
     */
    public function get_user_info_my()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $user = Db::name('user')->where('much_id', $data['much_id'])->where('id', $data['uid'])
            ->field('is_enable_fans_privacy,is_enable_concern_privacy,virtual_fans_num,user_home_access_status,id,user_head_sculpture,user_nick_name,gender,level,experience,wear_af,wear_merit,wear_special_id,autograph,much_id')
            ->find();
        $is_user_info = Db::name('user')->where('much_id', $data['much_id'])->where('id', $data['this_uid'])->find();
        $util = new Util();
        $user['is_vip'] = $util->get_user_vip($user['id']);
        //关注的圈子
        $trailing = Db::name('user_trailing')->alias('u')
            ->join('territory t', 't.id=u.tory_id')
            ->where('t.status', 1)
            ->where('t.is_del', 0)
            ->where('u.user_id', $data['uid'])
            ->where('u.much_id', $data['much_id'])
            ->count();
        $user['trailing'] = formatNumber($trailing);
        //关注的人
        $user_track = Db::name('user_track')->alias('t')
            ->join('user u', 'u.id=t.qu_user_id')
            ->where('t.at_user_id', $data['uid'])
            ->group('t.qu_user_id')
            ->count();
        $user['user_track'] = formatNumber($user_track);
        //粉丝
        $user_fs = Db::name('user_track')->alias('t')
            ->join('user u', 'u.id=t.at_user_id')
            ->where('t.qu_user_id', $data['uid'])
            ->group('t.at_user_id')
            ->count();
        $user['user_fs'] = formatNumber($user_fs);
        //是否关注了此人
        $is_user = Db::name('user_track')->where('at_user_id', $data['this_uid'])->where('qu_user_id', $data['uid'])->count();
        $user['is_user'] = $is_user > 0 ? 1 : $is_user;

        $user['user_nick_name'] = emoji_decode($user['user_nick_name']);
        $user['autograph'] = emoji_decode($user['autograph']);


        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();

        $user['admin'] = $cg;
        $user['version'] = $this->version;
        $user['is_user_info'] = $is_user_info;
        $res = Db::name('user_level')->where('level_hierarchy', $user['level'] + 1)->where('much_id', $data['much_id'])->find();
        $user['level_info'] = $util->get_user_level($user['level'], $data['much_id']);

        if (!empty($user['level_info'])) {
            $user['level_info']['next_level'] = $res['need_experience'];
            $user['level_info']['percentage'] = bcmul(bcdiv($user['experience'], $res['need_experience'], 2), 100, 0);

        }
        if ($user['wear_merit'] != 0) {
            //查询佩戴的勋章
            $model = Db::name('medal')->where('much_id', $data['much_id'])->where('id', $user['wear_merit'])->find();
            $user['merit'] = $model;
        } else {
            $user['merit'] = '';
        }

        //当前用户昵称
        $user['special'] = $util->get_user_special_nickname($user['id'], $data['much_id']);
        $user['avatar_frame'] = $util->get_user_avatar_frame($user['id'], $data['much_id']);
        if (empty($is_user_info['user_phone'])) {
            $user['is_phone'] = 0;
        } else {
            $user['is_phone'] = 1;
        }
        //虚拟粉丝
        if ($user['is_enable_fans_privacy'] == 1 && $user['virtual_fans_num'] > 0) {
            $user['user_fs'] = formatNumber($user['virtual_fans_num']);
        }
        //获取我认证的
        $user['attest'] = $util->get_att_list($user['id'], $data['much_id']);
        if ($user['user_home_access_status'] == 0 && ($user['id'] != $this->user_info['id']) && $user['admin'] == 0) {
            $c['user_nick_name'] = $user['user_nick_name'];
            $c['user_head_sculpture'] = $user['user_head_sculpture'];
            $c['user_home_access_status'] = 0;
            $c['admin'] = $user['admin'];
            $rs['info'] = $c;
        } else {
            $rs['info'] = $user;
        }
        return $this->json_rewrite($rs);
    }

    /**
     * 关注取消 会员
     */
    public function get_user_cancel()
    {
        $data = input('param.');

        if ($data['type'] == 1) {
            //关注了此人，需要取消
            if ($data['is_user'] == 1) {
                $del = Db::name('user_track')->where('at_user_id', $data['this_uid'])->where('qu_user_id', $data['uid'])->where('much_id', $data['much_id'])->delete();
                if ($del) {
                    $rs = ['status' => 'success', 'msg' => '取消关注成功！'];
                    return $this->json_rewrite($rs);
                } else {
                    $rs = ['status' => 'error', 'msg' => '取消关注失败！'];
                    return $this->json_rewrite($rs);
                }
            }
            $ins = Db::name('user_track')->insert(['at_user_id' => $data['this_uid'], 'qu_user_id' => $data['uid'], 'much_id' => $data['much_id'], 'fo_time' => time()]);
            if ($ins) {
                $rs = ['status' => 'success', 'msg' => '关注成功！'];
                return $this->json_rewrite($rs);
            } else {
                $rs = ['status' => 'error', 'msg' => '关注失败！'];
                return $this->json_rewrite($rs);
            }
        } else {
            $del = Db::name('user_track')->where('at_user_id', $data['uid'])->where('qu_user_id', $data['this_uid'])->where('much_id', $data['much_id'])->delete();
            if ($del) {
                $rs = ['status' => 'success', 'msg' => '删除成功！'];
                return $this->json_rewrite($rs);
            } else {
                $rs = ['status' => 'error', 'msg' => '删除失败！'];
                return $this->json_rewrite($rs);
            }

        }

    }

    /**
     * 获取会员金币明细
     */
    public function get_user_amount()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $where = [];
        $fraction_scale = Db::name('user_punch_range')->where('much_id', $data['much_id'])->field('fraction_scale,currency_redemption_channel,fraction_redemption_channel')->find();
        $scale = 10;
        if (!empty($fraction_scale['fraction_scale'])) {
            $scale = $fraction_scale['fraction_scale'];
        }
        if ($data['evaluate'] == 'tab1') {
            $where['evaluate'] = ['eq', 0];
        } else {
            $where['evaluate'] = ['eq', 1];
        }
        $list = Db::name('user_amount')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->page($data['page'], '20')
            ->order('ruins_time desc')
            ->where($where)
            ->select();
        foreach ($list as $k => $v) {
            $list[$k]['ruins_time'] = date('Y-m-d H:i:s', $v['ruins_time']);
        }
        $raws_setting = Db::name('raws_setting')->where('much_id', $data['much_id'])->find();
        $rs['info'] = $list;
        $rs['setting'] = $raws_setting;
        $rs['scale'] = $scale;
        $rs['fraction_scale'] = $fraction_scale;
        return $this->json_rewrite($rs);
    }

    /**
     * 我收到的礼物
     */
    public function get_my_rec()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $list = Db::name('user_subsidy')->alias('s')
            ->join('user u', 's.con_user_id=u.id')
            ->where('s.much_id', $data['much_id'])
            ->where('s.sel_user_id', $this->user_info['id'])
            ->page($data['page'], 30)
            ->field('s.*,u.user_head_sculpture,u.user_nick_name')
            ->order('bute_time desc')
            ->select();
        if (!empty($list)) {
            foreach ($list as $k => $v) {
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['bute_time'] = date('Y-m-d', $v['bute_time']);
            }
        }

        $res = array();
        foreach ($list as $key => $val) {
            $res[$val['bute_time']][] = $val;
        }
        $re = [];
        foreach ($res as $ke => $va) {
            $re[]['time'] = $ke;
            foreach ($re as $a => $v) {
                foreach ($va as $key => $value) {
                    if ($v['time'] == $value['bute_time']) {
                        $re[$a]['list'] = $va;
                    }
                }
            }
        }
        foreach ($re as $k => $v) {
            $time = explode("-", $v["time"]);
            $re[$k]['year'] = $time[0];
            $re[$k]['month'] = $time[1];
            $re[$k]['day'] = $time[2];
        }

        $rs['info'] = $re;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取用户的礼物排行榜
     */
    public function get_user_guard()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $list = Db::name('user_subsidy')->alias('s')
            ->join('user u', 's.con_user_id=u.id')
            ->where('s.sel_user_id', $data['uid'])
            ->group('s.con_user_id')
            ->field('u.id,u.user_head_sculpture,u.user_nick_name,sum(s.bute_price) as sub_count')
            ->limit($data['limit'])
            ->order('sub_count desc')
            ->select();
        if (!empty($list)) {
            foreach ($list as $k => $v) {
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
            }
        }
        $count_list = Db::name('user_subsidy')->where('sel_user_id', $data['uid'])->where('much_id', $data['much_id'])->select();
        $count = 0;
        if ($count_list) {
            foreach ($count_list as $k => $v) {
                $count += $this->findNum($v['bute_name']);
            }
        }

        $rs['count'] = $count;
        $rs['info'] = $list;
        return $this->json_rewrite($rs);
    }

    public function findNum($str = '')
    {
        $str = trim($str);
        if (empty($str)) {
            return '';
        }
        $result = '';
        for ($i = 0; $i < strlen($str); $i++) {
            if (is_numeric($str[$i])) {
                $result .= $str[$i];
            }
        }
        return $result;
    }

    /**
     * 获取最高人数的几个广场
     */
    public function get_all_needle()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        if (!isset($data['page'])) {
            $data['page'] = 1;
        }
        //获取最高关注人数的 一级
        $needle = Db::name('needle')
            ->where('status', 1)
            ->where('much_id', $data['much_id'])
            ->order('scores')
            ->page($data['page'], 12)
            ->select();

        foreach ($needle as $k => $v) {
            $nex = Db::name('territory')
                ->where('status', 1)
                ->where('needle_id', $v['id'])
                ->order('scores')
                ->limit(12)
                ->select();
            foreach ($nex as $a => $b) {
                $nex[$a]['concern'] = formatNumber($b['concern']);
            }
            $needle[$k]['children'] = $nex;
            $needle[$k]['needle_count'] = Db::name('territory')->where('much_id', 3)
                ->where('needle_id', $v['id'])->sum('concern');
        }
//        foreach ($needle as $key => $row) {
//            $volume[$key] = $row['needle_count'];
//        }
//        array_multisort($volume, SORT_DESC, $needle);
        $rs['info'] = $needle;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取搜索内容
     */
    public function get_search_list()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $where = [];
        //$where['p.is_buy'] = ['eq', 0];
//        if ($this->version == 1) {
//            $where['p.study_type'] = ['in', ['0', '1']];
//        }
        //$where['p.is_buy'] = ['eq', 0];
        //$where['p.study_type'] = ['in', ['0', '1']];
        $util = new Util();
        $title = emoji_encode($data['search']);
        $where['p.study_title|p.study_content'] = ['like', '%' . $title . '%'];
        //查询当前用户是否是VIP
        $user_vip = $util->get_user_vip($this->user_info['id']);
        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        $check_qq = $util->check_qq($this->user_info['user_wechat_open_id'], $data['tory_id']);
        //查询有权限的圈子
        $q_tory = Db::name('territory')->where('status', 1)->where('attention', '<>', 0)->where('much_id', $data['much_id'])->select();
        $q_tory_id = '';
        $vip_tory_id = '';
        foreach ($q_tory as $k => $v) {
            if ($v['attention'] == 1) {
                $q_tory_id .= $v['id'] . ",";
            }
            if ($v['attention'] == 2) {
                $vip_tory_id .= $v['id'] . ",";
            }

        }
        $q_tory_id = substr($q_tory_id, 0, -1);
        $vip_tory_id = substr($vip_tory_id, 0, -1);

        if ($check_qq == 'no') {
            if ($user_vip == 0 && $vip_tory_id && $cg == 0) {
                $q_tory_id = $q_tory_id . ',' . $vip_tory_id;
            }

            if ($cg == 0) {
                $where['t.id'] = array('not in', $q_tory_id);
            }
        }
        $page = $data['page'];
        $list = Db::name('paper')->alias('p')
            ->join('user u', 'p.user_id=u.id')
            ->join('territory t', 'p.tory_id=t.id')
            ->where('p.whether_delete', '0')
            ->where('p.study_status', '1')
            ->where('p.much_id', $data['much_id'])
            ->where($where)
            ->field('p.uccid,p.video_type,p.study_video,p.third_part_vid,u.user_wechat_open_id,p.id,p.user_id,p.tory_id,p.tg_id,p.study_title,p.study_title_color,p.study_content,p.study_type,p.is_buy,p.topping_time,p.image_part,p.study_voice,p.study_voice_time,p.study_heat,p.study_laud,p.study_repount,p.adapter_time,p.vote_deadline,p.is_open,u.level,u.gender,u.wear_merit,u.user_nick_name,u.user_head_sculpture,t.realm_name')
            ->order('p.adapter_time desc')
            ->page($page, '15')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {

                if (isset($data['tory_id'])) {//判断是否是 圈主或者管理员

                    $list[$k]['check_qq'] = $util->check_qq($v['user_wechat_open_id'], $v['tory_id']);
                }
                $list[$k]['is_voice'] = false;
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['study_title'] = emoji_decode($v['study_title']);
                $list[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));

                $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
                preg_match_all($preg, emoji_decode($v['study_content']), $match);


                if (!empty(json_decode($v['image_part'], true))) {
                    $list[$k]['image_part_1'] = '1';
                    $list[$k]['image_part'] = json_decode($v['image_part']);
                } else {
                    $list[$k]['image_part_1'] = '0';
                    $list[$k]['image_part_2'] = $match;
                    if (!empty($match[0])) {
                        $list[$k]['image_part'] = $match[1];
                    } else {
                        $list[$k]['image_part'] = [];
                    }
                }

                //查询是否购买了当前帖子
                $purchase = Db::name('paper_buy_user')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();


                //判断是否是 圈主，管理，超管，自己
                $check_look = 0;
                $da_xiao = $util->check_qq($data['openid'], $v['tory_id']);
                if ($da_xiao == 'da' || $da_xiao == 'xiao') { //不是圈主或者管理
                    $check_look = 1;
                }

                if ($cg == 1) {//超管
                    $check_look = 1;

                }
                if ($v['user_id'] == $this->user_info['id']) {  //不是自己发的
                    $check_look = 1;
                }
                //return $this->user_info['id'];
                if ($purchase == 1) {//已经购买
                    $check_look = 1;
                }

                $list[$k]['check_look'] = $check_look;


                $list[$k]['study_heat'] = formatNumber($v['study_heat']);
                $list[$k]['study_laud'] = formatNumber($v['study_laud']);
                $list[$k]['study_repount'] = formatNumber($v['study_repount']);
                $list[$k]['user_vip'] = $util->get_user_vip($v['user_id']);
                $list[$k]['index6_time'] = $this->getMyDate($v['adapter_time']);
                $list[$k]['adapter_time'] = formatTime($v['adapter_time']);

                $list[$k]['study_voice'] = link_urldecode($v['study_voice']);
                $list[$k]['study_voice_time'] = s_to_hs($v['study_voice_time']);
                $list[$k]['starttime'] = '00:00';
                $list[$k]['adapter_time'] = formatTime($v['adapter_time']);
                $list[$k]['offset'] = 0;
                $list[$k]['max'] = $v['study_voice_time'];
                $list[$k]['study_content'] = Alternative::ExpressionHtml($list[$k]['study_content']);
                //如果是腾讯视频
                if ($v['video_type'] == 1) {
                    $list[$k]['study_video'] = $this->getVideoInfo($v['third_part_vid']);

                }
                $sc = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
                $list[$k]['is_info_zan'] = $sc == 0 ? false : true;
                //$count = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('paper_id', $v['id'])->count();
                $count = $v['study_laud'];
                $list[$k]['info_zan_count'] = formatNumber($count);
                $list[$k]['info_zan_count_this'] = $count;
                //查询当前帖子是否是红包贴
                $red = Db::name('paper_red_packet')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                //查询是否购买了当前帖子
                $purchase = Db::name('paper_buy_user')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                $list[$k]['purchase'] = $purchase;
                $list[$k]['red'] = $red;
                $list[$k]['level'] = $util->get_user_level($v['level'], $data['much_id'])['level_icon'];
                //当前用户的勋章
                $list[$k]['wear_merit'] = $util->get_medal($v['wear_merit'], $data['much_id']);
                //当前用户昵称
                $list[$k]['special'] = $util->get_user_special_nickname($v['user_id'], $data['much_id']);
                $list[$k]['avatar_frame'] = $util->get_user_avatar_frame($v['user_id'], $data['much_id']);
                //是否是投票贴
                $list[$k]['vo_id'] = [];
                //查询我是否投票了
                $list[$k]['is_vo_check'] = Db::name('user_vote')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                if ($list[$k]['is_vo_check'] > 0) {
                    $my_vo = Db::name('user_vote')->where('paper_id', $v['id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
                    foreach ($my_vo as $c => $d) {
                        array_push($list[$k]['vo_id'], intval($d['pv_id']));
                    }
                }
                $pvInfo = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->select();
                $pvInfoSum = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->sum('cheat_ballot');
                $pvPeopleCount = Db::name('user_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                $pvPeopleCountTotal = ($pvInfoSum + $pvPeopleCount);
                $list[$k]['vo_count'] = $pvPeopleCountTotal;
                for ($i = 0; $i < count($pvInfo); $i++) {
                    $voters = Db::name('user_vote')->where('paper_id', $v['id'])->where('pv_id', $pvInfo[$i]['id'])->where('much_id', $data['much_id'])->count();
                    $pvInfo[$i]['voters'] = (intval($pvInfo[$i]['cheat_ballot']) + $voters);
                    $ratio = (($pvInfo[$i]['voters'] / $pvPeopleCountTotal) * 100);
                    if (is_nan($ratio)) {
                        $pvInfo[$i]['ratio'] = 0;
                    } else {
                        $pvInfo[$i]['ratio'] = $ratio;
                    }
                }
                $list[$k]['vo'] = $pvInfo;
                //判断是否有身份铭牌
                if (intval($v['uccid']) != 0) {
                    //查询佩戴的身份
                    $card_info = Db::name('camouflage_card')->where('id', $v['uccid'])->where('much_id', $data['much_id'])->find();
                    $user_card = Db::name('user_camouflage_card')->where('ccid', $v['uccid'])->where('user_id', $v['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                    $list[$k]['user_head_sculpture'] = $card_info['forgery_head'];
                    $list[$k]['user_id'] = 0;
                    //$list[$k]['user_nick_name']=$card_info['forgery_name'].'-'.$user_card['id'];
                    $list[$k]['user_nick_name'] = $card_info['forgery_name'];
                    $list[$k]['attr'] = '';
                } else {
                    $attr = $util->get_att_info($v['user_id'], $data['much_id']);
                    $list[$k]['attr'] = $attr;
                }


            }
            $rs['info'] = $list;
        } else {
            $rs['info'] = [];
        }
        //圈子
        $map['realm_name'] = ['like', '%' . emoji_encode($data['search']) . '%'];
        $territory = Db::name('territory')->where($map)->where('status', 1)->where('much_id', $data['much_id'])->order('concern desc')->select();
        $rs['is_search_yes'] = 0;
        foreach ($territory as $k => $v) {
            $territory[$k]['concern'] = formatNumber($v['concern']);
            $territory[$k]['paper_count'] = formatNumber($util->get_territory_papo_count($v['id']));
            if (emoji_encode($data['search']) == $v['realm_name']) {
                $rs['is_search_yes'] = 1;
            }
        }
        $rs['territory'] = $territory;
        //用户
        $user_map['user_nick_name'] = ['like', '%' . emoji_encode($data['search']) . '%'];
        $user = Db::name('user')->where($user_map)->where('id', '<>', $this->user_info['id'])->where('status', 1)->where('much_id', $data['much_id'])->order('id')->limit(5)->select();
        $rs['user_search_yes'] = 0;
        foreach ($user as $k => $v) {
            if (emoji_encode($data['search']) == $v['user_nick_name']) {
                $rs['user_search_yes'] = 1;
            }
            $user[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
            //粉丝
            $user_fs = Db::name('user_track')->alias('t')
                ->join('user u', 'u.id=t.at_user_id')
                ->where('t.qu_user_id', $v['id'])
                ->group('t.at_user_id')
                ->count();
            //文章
            $user_paper = Db::name('paper')->alias('t')
                ->join('user u', 'u.id=t.user_id')
                ->where('t.user_id', $v['id'])
                ->count();
            $user[$k]['user_paper'] = formatNumber($user_paper);
            $user[$k]['user_fs'] = formatNumber($user_fs);
        }

        $rs['user'] = $user;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取圈子
     */
    public function get_mod_list()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $page = $data['page'];
        $user_trailing = Db::name('user_trailing')
            ->where('much_id', $data['much_id'])
            ->where('user_id', $this->user_info['id'])
            ->field('tory_id')
            ->select();
        $arr_ar = '';
        foreach ($user_trailing as $a => $b) {
            $arr_ar .= $b['tory_id'] . ",";
        }
        $arr_ar = substr($arr_ar, 0, -1);
        $list = Db::name('territory')
            ->where('status', 1)
            ->where('much_id', $data['much_id'])
            ->where('is_del', 0)
            ->whereNotIn('id', $arr_ar)
            ->order('scores asc')
            ->page($page, '15')
            ->select();
        $id_arr = [];
        foreach ($user_trailing as $a => $b) {
            $info = Db::name('territory')
                ->where('id', $b['tory_id'])
                ->where('status', 1)
                ->where('much_id', $data['much_id'])
                ->where('is_del', 0)
                ->find();
            if (!empty($info)) {
                $id_arr[$a]['realm_icon'] = $info['realm_icon'];
                $id_arr[$a]['id'] = $info['id'];
                $id_arr[$a]['realm_name'] = $info['realm_name'];
                if ($page == 1) {
                    array_unshift($list, $id_arr[$a]);
                }
            }

        }
        if ($page == 1) {
            array_unshift($list, ['id' => 0, 'realm_name' => '关注']);
        }
        $rs['info'] = $list;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取热门话题
     */
    public function get_gambit()
    {
        $data = input('param.');
        $util = new Util();
        $list = Db::name('gambit')
            ->where('much_id', $data['much_id'])
            ->where('is_del', 0)
            ->order('scores asc')
            ->limit(9)
            ->select();

        foreach ($list as $k => $v) {
            $count = 0;
            $list[$k]['gambit_name'] = emoji_decode(str_replace("#", '', $v['gambit_name']));
            //获取话题讨论人数
            $page = Db::name('paper')->alias('p')
                ->join('user u', 'p.user_id=u.id')
                ->where('p.tg_id', $v['id'])
                ->where('p.much_id', $data['much_id'])
                ->field('p.id,u.user_head_sculpture')
                ->group('u.id')
                ->select();
            foreach ($page as $a => $b) {
                $count2 = Db::name('paper_reply')->where('paper_id', $b['id'])->count();
                $count += $count2;
                // $list[$k]['img'][1] = $b['user_head_sculpture'];
                //$list[$k]['img'][2] = $b['user_head_sculpture'];
            }
            $list[$k]['img'] = array_slice($page, 0, 3);
            $count += count($page);
            $list[$k]['page_count'] = formatNumber($count);

        }
        $bbb = array();
        for ($i = 0; $i < ceil(3); $i++) {
            $bbb[] = array_slice($list, $i * 3, 3);
        }
        return $this->json_rewrite($bbb);

    }

    /**
     * 获取我关注得圈子里的内容
     */
    public function get_my_attr_list()
    {
        $data = input('param.');
        $where = [];
        $util = new Util();
        $page = $data['page'];

        if ($this->version == 1) {
            $where['p.study_type'] = ['in', ['0', '1', '3']];
            $where['p.is_buy'] = ['eq', 0];
        }
        $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($authority['hair_video_arbor'] == 0) {
            $where['p.study_type'] = ['in', ['0', '1', '3', '4', '5']];
        }

        //获取当前用户是否是超管
        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();

        if ($data['mod_list_id'] == 0) {
            $user_trailing = Db::name('user_trailing')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->field('tory_id')->select();
            $id_arr = '';
            foreach ($user_trailing as $a => $b) {
                $id_arr .= $b['tory_id'] . ",";
            }
            $id_arr = substr($id_arr, 0, -1);
            $where['p.tory_id'] = ['in', $id_arr];
        } else {
            $where['p.tory_id'] = ['eq', $data['mod_list_id']];
        }


        //$where['p.essence_time'] = ['eq', 0];
        $list = Db::name('paper')
            ->alias('p')
            ->join('user u', 'p.user_id=u.id')
            ->join('territory t', 'p.tory_id=t.id')
            ->join('paper_reply r', 'r.paper_id=p.id', 'LEFT')
            ->where('p.whether_delete', '0')
            ->where('p.study_status', '1')
            ->where('p.much_id', $data['much_id'])
            ->where('t.status', 1)
            ->where('t.is_del', 0)
            ->where($where)
            ->field('p.uccid,p.video_type,p.third_part_vid,p.study_video,u.user_wechat_open_id,p.id,p.user_id,p.tory_id,p.tg_id,p.study_title,p.study_title_color,p.study_content,p.study_type,p.is_buy,p.topping_time,p.image_part,p.study_voice,p.study_voice_time,p.study_heat,p.study_laud,p.study_repount,p.adapter_time,p.vote_deadline,p.is_open,u.level,u.gender,u.wear_merit,u.autograph,u.user_nick_name,u.user_head_sculpture,t.realm_name,max(r.apter_time) as huifu_time')
            ->order('p.adapter_time desc')
            ->group('p.id')
            ->page($page, '10')
            ->select();
        if ($list) {
            foreach ($list as $k => $v) {

                if (isset($data['tory_id'])) {//判断是否是 圈主或者管理员

                    $list[$k]['check_qq'] = $util->check_qq($v['user_wechat_open_id'], $v['tory_id']);
                }
                $list[$k]['user_wechat_open_id'] = null;
                if (isset($v['huifu_time'])) {
                    $list[$k]['huifu_time'] = formatTime($v['huifu_time']);
                }

                //计算全部回复人数
                $huifu_all_count = Db::name('paper_reply')->where('reply_status', 1)->where('paper_id', $v['id'])->where('whether_delete=0')->where('much_id', $data['much_id'])->count();

                $huifu_hui_count = Db::name('paper_reply')->alias('p')
                    ->join('paper_reply_duplex r', 'r.reply_id=p.id')
                    ->where('p.whether_delete=0')
                    ->where('p.much_id', $data['much_id'])
                    ->where('p.paper_id', $v['id'])
                    ->count();

                $list[$k]['is_voice'] = false;
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['study_title'] = emoji_decode($v['study_title']);


                //查询是否购买了当前帖子
                $purchase = Db::name('paper_buy_user')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();


                //判断是否是 圈主，管理，超管，自己
                $check_look = 0;
                $da_xiao = $util->check_qq($data['openid'], $v['tory_id']);
                if ($da_xiao == 'da' || $da_xiao == 'xiao') { //不是圈主或者管理
                    $check_look = 1;
                }

                if ($cg == 1) {//超管
                    $check_look = 1;

                }
                if ($v['user_id'] == $this->user_info['id']) {  //不是自己发的
                    $check_look = 1;
                }
                //return $this->user_info['id'];
                if ($purchase == 1) {//已经购买
                    $check_look = 1;
                }

                $list[$k]['check_look'] = $check_look;

                $modd = '/<div class="stealth_module".*>([\s\S]*)<\/div>/i';

                preg_match_all($modd, emoji_decode($v['study_content']), $moddUrl);

                if (count($moddUrl[1]) > 0) {
                    for ($i = 0; $i < count($moddUrl[1]); $i++) {
                        $moddHtml = "<div></div>";
                        $v['study_content'] = preg_replace($modd, $moddHtml, emoji_decode($v['study_content']), 1);
                    }
                    if (empty(strip_tags(emoji_decode($v['study_content'])))) {
                        $v['study_content'] = "<div>这里是隐藏文本，需要回复才能看到</div>";
                    }
                } else {
                    if ($v['is_buy'] == 1) {
                        $v['study_content'] = "<div>这里是隐藏文本，需要订阅才能看到</div>";
                    } else {
                        $v['study_content'] = emoji_decode($v['study_content']);
                    }

                }


                $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';

                preg_match_all($preg, emoji_decode($v['study_content']), $match);

                //$list[$k]['mmmmmmatch'] = $v['study_content'];
                if (!empty(json_decode($v['image_part'], true))) {
                    //$list[$k]['image_part_1'] = '1';
                    $list[$k]['image_part'] = json_decode($v['image_part']);
                } else {
                    //$list[$k]['image_part_1'] = '0';
                    //$list[$k]['image_part_2'] = $match;
                    if (!empty($match[0])) {
                        $img = array();
                        foreach ($match[1] as $a => $b) {
                            $img[$a] = htmlspecialchars_decode($b);
                        }
                        $list[$k]['image_part'] = $img;
                    } else {
                        $list[$k]['image_part'] = [];
                    }
                }


                //$ling = count(json_decode($list[$k]['image_part']));

                $list[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));
                $list[$k]['study_heat'] = formatNumber($v['study_heat']);
                $list[$k]['study_laud'] = formatNumber($v['study_laud']);
                $list[$k]['study_repount'] = formatNumber($huifu_hui_count + $huifu_all_count);
                $list[$k]['user_vip'] = $util->get_user_vip($v['user_id']);

                $list[$k]['study_voice'] = link_urldecode($v['study_voice']);

                $list[$k]['study_voice_time'] = s_to_hs($v['study_voice_time']);
                $list[$k]['starttime'] = '00:00';
                $list[$k]['index6_time'] = $this->getMyDate($v['adapter_time']);
                $list[$k]['adapter_time'] = formatTime($v['adapter_time']);
                //如果是腾讯视频
                if ($v['video_type'] == 1) {
                    $list[$k]['study_video'] = $this->getVideoInfo($v['third_part_vid']);

                }
                $list[$k]['study_content'] = Alternative::ExpressionHtml($list[$k]['study_content']);
                $list[$k]['offset'] = 0;
                $list[$k]['max'] = $v['study_voice_time'];

                $sc = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
                $list[$k]['is_info_zan'] = $sc == 0 ? false : true;
                //$count = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('paper_id', $v['id'])-
                $count = $v['study_laud'];
                $list[$k]['info_zan_count'] = formatNumber($count);
                $list[$k]['info_zan_count_this'] = $count;
                //查询当前帖子是否是红包贴
                $red = Db::name('paper_red_packet')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                //查询是否购买了当前帖子
                $list[$k]['red'] = $red;
                //获取当前话题
                if ($v['tg_id'] != 0) {
                    $gambit = Db::name('gambit')->where('much_id', $data['much_id'])->where('id', $v['tg_id'])->find();
                    $list[$k]['gambit_name'] = emoji_decode(str_replace('#', '', $gambit['gambit_name']));
                    $list[$k]['gambit_id'] = $gambit['id'];
                }
                $list[$k]['level'] = $util->get_user_level($v['level'], $data['much_id'])['level_icon'];
                //当前用户的勋章
                $list[$k]['wear_merit'] = $util->get_medal($v['wear_merit'], $data['much_id']);
                //当前用户昵称
                $list[$k]['special'] = $util->get_user_special_nickname($v['user_id'], $data['much_id']);
                //当前用户头像框
                $list[$k]['avatar_frame'] = $util->get_user_avatar_frame($v['user_id'], $data['much_id']);
                //是否是投票贴
                $list[$k]['vo_id'] = [];
                //查询我是否投票了
                $list[$k]['is_vo_check'] = Db::name('user_vote')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                if ($list[$k]['is_vo_check'] > 0) {
                    $my_vo = Db::name('user_vote')->where('paper_id', $v['id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
                    foreach ($my_vo as $c => $d) {
                        array_push($list[$k]['vo_id'], intval($d['pv_id']));
                    }
                }
                $pvInfo = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->select();
                $pvInfoSum = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->sum('cheat_ballot');
                $pvPeopleCount = Db::name('user_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                $pvPeopleCountTotal = ($pvInfoSum + $pvPeopleCount);
                $list[$k]['vo_count'] = $pvPeopleCountTotal;
                for ($i = 0; $i < count($pvInfo); $i++) {
                    $voters = Db::name('user_vote')->where('paper_id', $v['id'])->where('pv_id', $pvInfo[$i]['id'])->where('much_id', $data['much_id'])->count();
                    $pvInfo[$i]['voters'] = (intval($pvInfo[$i]['cheat_ballot']) + $voters);
                    $ratio = (($pvInfo[$i]['voters'] / $pvPeopleCountTotal) * 100);
                    if (is_nan($ratio)) {
                        $pvInfo[$i]['ratio'] = 0;
                    } else {
                        $pvInfo[$i]['ratio'] = $ratio;
                    }
                }
                $list[$k]['vo'] = $pvInfo;
                //判断截止时间
                if ($v['vote_deadline'] != 0) {
                    if ($v['vote_deadline'] > time()) {
                        $list[$k]['vote_deadline'] = date('Y-m-d H:i', $v['vote_deadline']);
                    } else {
                        $list[$k]['vote_deadline'] = -1;//已截止
                        $list[$k]['is_vo_check'] = 1;
                    }
                }
                //判断是否有身份铭牌
                if (intval($v['uccid']) != 0) {
                    //查询佩戴的身份
                    $card_info = Db::name('camouflage_card')->where('id', $v['uccid'])->where('much_id', $data['much_id'])->find();
                    $user_card = Db::name('user_camouflage_card')->where('ccid', $v['uccid'])->where('user_id', $v['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                    $list[$k]['user_head_sculpture'] = $card_info['forgery_head'];
                    $list[$k]['user_id'] = 0;
                    //$list[$k]['user_nick_name']=$card_info['forgery_name'].'-'.$user_card['id'];
                    $list[$k]['user_nick_name'] = $card_info['forgery_name'];
                    $list[$k]['attr'] = '';
                }
                //获取当前帖子的评论
                $list[$k]['reply_list'] = $util->get_paper_reply_list($v['id'], $data['much_id']);
                //else{
                //$attr=$util->get_att_info($v['user_id'],$data['much_id']);
                //$list[$k]['attr']=$attr;
                // }
            }

            $rs['info'] = $list;
        } else {
            $rs['info'] = [];
        }
        $rs['version'] = $this->version;
        return $this->json_rewrite($rs);

    }

    /**
     * 获取推荐圈子
     */
    public function get_tj_list()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $page = $data['page'];
        $user_trailing = Db::name('user_trailing')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->field('tory_id')->select();
        $id_arr = [];
        foreach ($user_trailing as $a => $b) {
            $id_arr[$a] = $b['tory_id'];
        }

        $list = Db::name('territory')->where('status', 1)->where('much_id', $data['much_id'])->where('is_del', 0)->where('id', 'not in', $id_arr)->order('scores asc')->page($page, '20')->select();

//        foreach ($list as $k => $v) {
//            $paper = Db::name('paper')
//                ->where('tory_id', $v['id'])
//                ->where('whether_type', '0')
//                ->where('study_status', '1')
//                ->field('image_part')
//                ->where('image_part', 'not NULL')
//                ->order('adapter_time desc')
//                ->limit(5)
//                ->select();
//            $img = [];
//            foreach ($paper as $a => $b) {
//
//                $img[$a] = json_decode($b['image_part'], true)[0];
//
//            }
//            $list[$k]['img'] = $img;
//        }
        $rs['info'] = $list;
        return $this->json_rewrite($rs);
    }

    /**
     * 获取关注或者粉丝
     */
    public function get_follow_fansi()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        //判断隐私
        $user_info=Db::name('user')->where('id',$data['uid'])->where('much_id',$data['much_id'])->field('user_home_access_status,is_enable_fans_privacy,is_enable_concern_privacy')->find();
        if ($data['type'] == 1) {//关注的人
            $list = Db::name('user_track')->alias('t')
                ->join('user u', 'u.id=t.qu_user_id')
                ->where('t.at_user_id', $data['uid'])
                ->where('t.much_id', $data['much_id'])
                ->order('t.fo_time desc')
                ->field('u.id,u.user_head_sculpture,u.user_nick_name,u.autograph,u.gender')
                ->page($data['page'], '20')
                ->group('t.qu_user_id')
                ->select();
            $count = Db::name('user_track')->alias('t')
                ->join('user u', 'u.id=t.qu_user_id')
                ->where('t.at_user_id', $data['uid'])
                ->where('t.much_id', $data['much_id'])
                ->group('t.qu_user_id')
                ->count();
            if(($user_info['is_enable_concern_privacy']==1||$user_info['user_home_access_status']==0)&&$data['uid']!=$this->user_info['id']){
                $list=[];
                $count=[];
            }
        }
        if ($data['type'] == 2) {//粉丝
            $list = Db::name('user_track')->alias('t')
                ->join('user u', 'u.id=t.at_user_id')
                ->where('t.qu_user_id', $data['uid'])
                ->where('t.much_id', $data['much_id'])
                ->order('t.fo_time desc')
                ->field('u.id,u.user_head_sculpture,u.user_nick_name,u.autograph,u.gender')
                ->page($data['page'], '20')
                ->group('t.at_user_id')
                ->select();
            $count = Db::name('user_track')->alias('t')
                ->join('user u', 'u.id=t.at_user_id')
                ->where('t.qu_user_id', $data['uid'])
                ->where('t.much_id', $data['much_id'])
                ->group('t.at_user_id')
                ->count();
            if(($user_info['is_enable_fans_privacy']==1||$user_info['user_home_access_status']==0)&&$data['uid']!=$this->user_info['id']){
                $list=[];
                $count=[];
            }
        }
        if (!empty($list)) {
            foreach ($list as $k => $v) {
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['autograph'] = emoji_decode($v['autograph']);
            }
        }
         $rs['info'] = $list;
        $rs['num'] = $count;
        return $this->json_rewrite($rs);

    }

    /**
     * 图片上传
     */
    public function img_upload()
    {
        $data = input('param.');
        //查询是否开启网络验证
        $ins = Db::name('user_violation')->where('much_id', $data['much_id'])->find();
        if ($ins['open_network_images_offend'] == 1) {
            $res = $this->check_img($data['much_id']);
            if ($res['errcode'] == 87014) {
                $code['msg'] = '图片含有违法违规内容！-1';
                $code['status'] = 'error';
                return $this->json_rewrite($code);
            }
        }
        $up = new Upload($data['much_id']);
        $code = $up->operate();
        if ($code['status'] == 'success') {
            $code['msg'] = '上传成功！';
            if ($ins['open_network_images_offend'] == 2) {
                $res = Moderation::Img($data['much_id'], $code['url']);
                if ($res['status'] == 'error') {
                    $msg['msg'] = '图片含有违法违规内容！-2';
                    $msg['status'] = 'error';
                    return $this->json_rewrite($msg);
                }
            }
        } else {
            $code['msg'] = $code['status'];
        }
        return $this->json_rewrite($code);
    }

    /**
     * 缓存数组排序
     */
    public function set_arr_dx()
    {
        $rs = ['status' => 'success', 'msg' => '成功！'];
        $data = input('param.');
        $rs['info'] = array_reverse(json_decode($data['arr']));
        return $this->json_rewrite($rs);
    }

    /**
     * base64
     */
    public function base64EncodeImage()
    {
        $data = input('param.');
        $img = $data['img'];
        $imageInfo = getimagesize($img);
        $option = ['ssl' => ['verify_peer' => false, 'verify_peer_name' => false]];
        $base = 'data:' . $imageInfo['mime'] . ';base64,' . chunk_split(base64_encode(file_get_contents($img, false, stream_context_create($option))));
        //获取论坛图标
        $info = Db::name('authority')->where('much_id', $data['much_id'])->field('sgraph,title')->find();
        $authority_imageInfo = getimagesize($info['sgraph']);
        $authority = 'data:' . $authority_imageInfo['mime'] . ';base64,' . chunk_split(base64_encode(file_get_contents($info['sgraph'], false, stream_context_create($option))));
        return $this->json_rewrite(['base' => $base, 'authority' => $authority, 'title' => $info['title']]);
    }

    /**
     * 获取小程序二维码（帖子）
     */
    public function qrcode()
    {
        $data = input('param.');//得到用户openid
        $util = new Util();
        $getConfig = $util->getWchatAcctoken($data['much_id']);
        //帖子ID查
        $paper_info = Db::name('paper')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        //获取后台APPID
        $url = 'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=' . $getConfig;
        $data = array(
            "page" => "yl_welore/pages/packageA/article/index",
            "scene" => $data['id'] . "-" . $paper_info['study_type'],
            "is_hyaline" => true
        );
        $result = Gyration::_requestPost($url, json_encode($data));
        return $result;
    }

    /**
     * 获取小程序二维码（邀请页面）
     */
    public function qrcode_code()
    {
        $data = input('param.');//得到用户openid
        $util = new Util();
        $getConfig = $util->getWchatAcctoken($data['much_id']);
        $url = 'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=' . $getConfig;
        $data = array(
            "page" => "yl_welore/pages/packageC/user_invitation/index",
            "scene" => $this->user_info['id'],
            "is_hyaline" => true
        );
        $result = Gyration::_requestPost($url, json_encode($data));
        return $result;

    }

    /**
     * 获取小程序二维码（圈子）
     */
    public function quan_qrcode()
    {
        $data = input('param.');//得到用户openid
        $util = new Util();
        $user_open_type = $data['user_open_type'];
        if (empty($user_open_type) || $user_open_type == 0) {
            $getConfig = $util->getWchatAcctoken($data['much_id']);
            $url = 'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=' . $getConfig;
            $data = array(
                "page" => "yl_welore/pages/packageA/circle_info/index",
                "scene" => "id=" . $data['id'],
                "is_hyaline" => false
            );
            $result = Gyration::_requestPost($url, json_encode($data));
        } else {
            $getConfig = $util->getTouTiaoAcctoken($data['much_id']);
            $url = 'https://developer.toutiao.com/api/apps/qrcode';
            $data = [
                'access_token' => $getConfig,
                'set_icon' => true,
                'appname' => 'douyin',
                "path" => urlencode("yl_welore/pages/packageA/circle_info/index?id=" . $data['id']),
            ];
            $result = $util->TouTiaoApiPost($url, $data, $data['much_id']);
        }

        return $result;
    }


    /**
     * 获取随机字符
     */
    public function get_yzm_random($len = 6)
    {
        $chars = array(
            "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R",
            "S", "T", "U", "V", "W", "X", "Y", "Z", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9"
        );

        $charsLen = count($chars) - 1;
        shuffle($chars);                            //打乱数组顺序
        $str = '';
        for ($i = 0; $i < $len; $i++) {
            $str .= $chars[mt_rand(0, $charsLen)];    //随机取出一位
        }
        $check_yzm = Db::name('user_invitation_code')->where('code', $str)->find();
        if (!empty($check_yzm)) {
            $this->get_yzm_random(6);
        }
        return $str;
    }

    /**
     * 获取用户邀请码
     */
    public function ger_user_code()
    {
        $data = input('param.');
        $info = Db::name('user_invitation_code')->where('user_id', $data['uid'])->where('much_id', $data['much_id'])->find();
        if (empty($info)) {
            $yzm = $this->get_yzm_random(6);
            Db::name('user_invitation_code')->insert(['user_id' => $data['uid'], 'code' => $yzm, 'much_id' => $data['much_id']]);
            $info = Db::name('user_invitation_code')->where('user_id', $data['uid'])->where('much_id', $data['much_id'])->find();
        }
        return $this->json_rewrite($info);
    }

    /**
     * 输入验证码提交
     */
    public function add_user_invitation()
    {
        $data = input('param.');
        $util = new Util();
        //邀请码不能为空
        if (empty($data['yzm_text'])) {
            $rs = ['status' => 'error', 'msg' => '内容不能为空！'];
            return $this->json_rewrite($rs);
        }
        //查询是否有这个验证码
        $yam_check = Db::name('user_invitation_code')->where('code', preg_replace('# #', '', $data['yzm_text']))->where('much_id', $data['much_id'])->find();
        if (empty($yam_check)) {
            $rs = ['status' => 'error', 'msg' => '邀请码错误！'];
            return $this->json_rewrite($rs);
        }
        if ($yam_check['user_id'] == $data['uid']) {
            $rs = ['status' => 'error', 'msg' => '邀请码错误！'];
            return $this->json_rewrite($rs);
        }
        //查询是否已经输入过邀请码
        $user_check_yzm = Db::name('user_respond_invitation')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        if ($user_check_yzm) {
            $rs = ['status' => 'error', 'msg' => '您已经响应过朋友啦！'];
            return $this->json_rewrite($rs);
        }

        //查询我的邀请码
        $this_user_code = Db::name('user_invitation_code')->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        $lian_check = Db::name('user_respond_invitation')
            ->where('user_id', $yam_check['user_id'])
            ->where('re_code', $this_user_code['code'])
            ->find();
        if ($lian_check) {
            $rs = ['status' => 'error', 'msg' => '不能互相邀请哦！'];
            return $this->json_rewrite($rs);
        }
        //查询邀请码的主人
        $user_info = $util->get_user_invitation(preg_replace('# #', '', $data['yzm_text']), $data['much_id']);
        //查询被邀请者信息
        $this_user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();

        //获得积分明细
        $punch_range = Db::name('user_punch_range')->where('much_id', $data['much_id'])->find();
        //随机积分
        $fraction = rand($punch_range['invite_min'] * 100, $punch_range['invite_max'] * 100) / 100;
        // 启动事务
        Db::startTrans();
        try {
            //响应表增加数据
            $respond_invitation = Db::name('user_respond_invitation')->insert([
                    'user_id' => $this->user_info['id'],
                    're_code' => preg_replace('# #', '', $data['yzm_text']),
                    'in_us_reward' => $fraction,
                    're_us_reward' => $fraction,
                    're_time' => time(),
                    'much_id' => $data['much_id']]
            );
            if (!$respond_invitation) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '验证失败'];
                return $this->json_rewrite($rs);
            }
            //货币明细表增加数据（邀请者）
            $amount['user_id'] = $user_info['id'];
            $amount['category'] = 3;
            $amount['finance'] = $fraction;

            $amount['poem_fraction'] = $user_info['fraction'];
            $amount['surplus_fraction'] = $user_info['fraction'] + $fraction;

            $amount['poem_conch'] = $user_info['conch'];//初始贝壳
            $amount['surplus_conch'] = $user_info['conch'];//后贝壳

            $amount['ruins_time'] = time();
            $amount['solution'] = '邀请好友获得' . $this->design['confer'];
            $amount['evaluate'] = 1;
            $amount['much_id'] = $data['much_id'];
            $user_amount = Db::name('user_amount')->insert($amount);
            $user_up = Db::name('user')->where('id', $user_info['id'])->where('much_id', $data['much_id'])->update(['fraction' => $amount['surplus_fraction']]);
            if (!$user_amount || !$user_up) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '验证失败'];
                return $this->json_rewrite($rs);
            }
            //货币明细表增加数据（被邀请者）
            $amount_d['user_id'] = $this->user_info['id'];
            $amount_d['category'] = 3;
            $amount_d['finance'] = $fraction;
            $amount_d['poem_fraction'] = $this_user_info['fraction'];
            $amount_d['surplus_fraction'] = $this_user_info['fraction'] + $fraction;
            $amount_d['ruins_time'] = time();
            $amount_d['solution'] = '响应好友获得' . $this->design['confer'];
            $amount_d['evaluate'] = 1;
            $amount_d['much_id'] = $data['much_id'];
            $user_amount_d = Db::name('user_amount')->insert($amount_d);
            $user_up_d = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update(['fraction' => $amount_d['surplus_fraction']]);
            if (!$user_amount_d || !$user_up_d) {
                Db::rollback();
                $rs = ['status' => 'error', 'msg' => '验证失败'];
                return $this->json_rewrite($rs);
            }
            Db::commit();
            $rs = ['status' => 'success', 'msg' => '验证成功,恭喜获得' . $fraction . $this->design['confer']];
            return $this->json_rewrite($rs);
        } catch (\Exception $e) {
            Db::rollback();
            $rs = ['status' => 'error', 'msg' => '验证失败'];
            return $this->json_rewrite($rs);
        }
    }
}