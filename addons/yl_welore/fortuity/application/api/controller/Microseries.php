<?php

namespace app\api\controller;

use app\api\service\Alternative;
use app\api\service\RedPaper;
use app\api\service\Util;
use think\Cache;
use think\Db;

class Microseries extends Base
{
    public function getIndexTypeList()
    {
        $data = input('param.');
        //类型
        $type = Db::name('micro_series_type')
            ->where('much_id', $data['much_id'])
            ->where('status', 1)
            ->where('is_del',0)
            ->order('sort')
            ->field('id,name')->select();

        return $this->json_rewrite(['type' => $type]);
    }

    public function getIndex()
    {
        $data = input('param.');
        if ($data['type_id'] == 0 || empty($data['type_id'])) {
            // 定义查询条件
            $microSeriesList = Db::name('micro_series_info_list')
                ->where('much_id', $data['much_id'])
                ->where('status', 1)
                ->where('display_status', 1)
                ->where('is_del', 0)
                ->where('title','like','%'.$data['content'].'%')
                ->field('id, title, total_episodes, poster_url')
                ->page($data['page'],12)
                ->order('create_time desc')
                ->select();
        } else {
            // 定义查询条件
            $microSeriesList = Db::name('micro_series_info_list')
                ->where('much_id', $data['much_id'])
                ->where('status', 1)
                ->where('display_status', 1)
                ->where('is_del', 0)
                ->where('title','like','%'.$data['content'].'%')
                ->page($data['page'],12)
                // 确保type字段中的1是独立的，不是其他数字的一部分
                ->where(function ($query) use ($data) {
                    $query->where('type', 'LIKE', $data['type_id'] . ',%')
                        ->whereOr('type', 'LIKE', '%,' . $data['type_id'])
                        ->whereOr('type', 'LIKE', '%,' . $data['type_id'] . ',%')
                        ->whereOr('type', 'eq', $data['type_id']);
                })
                ->field('id, title, total_episodes, poster_url')
                ->order('create_time desc')
                ->select();
        }


        //配置
        $config = Db::name('micro_series_config')
            ->where('much_id', $data['much_id'])
            ->field('custom_title')
            ->find();
        $config['custom_title'] = $config['custom_title'] ? $config['custom_title'] : '短剧视频';
        return $this->json_rewrite(['list' => $microSeriesList, 'config' => $config]);
    }

    /**
     * 获取类型信息
     * 本函数用于根据用户输入的参数，从数据库中获取微系列类型、用户已上传的剧集列表以及相关配置信息。
     * @return array 返回包含类型列表、用户剧集列表和配置信息的JSON格式数据。
     */
    public function getType()
    {
        $data = input('param.');
        //类型
        $list = Db::name('micro_series_type')->where('much_id', $data['much_id'])->where('status', 1)->order('sort')->field('id,name')->select();
        foreach ($list as $k => $v) {
            $list[$k]['is'] = 0;
        }
        //我的连载剧
        $info_list = Db::name('micro_series_info_list')
            ->where('much_id', $data['much_id'])
            ->where('upload_user_id', $this->user_info['id'])
            ->where('status', 1)
            ->where('display_status', 1)
            ->where('is_del', 0)
            ->field('id,title,total_episodes')
            ->select();
        foreach ($info_list as $k => $v) {
            $info_list[$k]['title'] = $v['title'] . '(共' . numToWord($v['total_episodes']) . '集)';
        }
        //配置
        $config = Db::name('micro_series_config')
            ->where('much_id', $data['much_id'])
            ->field('is_allow_user_charge,min_amount_charged,max_amount_charged,is_require_user_copyright')
            ->find();
        return $this->json_rewrite(['type' => $list, 'list' => $info_list, 'config' => $config]);
    }

    /**
     * 执行短剧添加操作。
     * 此函数用于处理用户提交的短剧添加请求，包括新剧集的添加和已有剧集的后续添加。
     * 它首先验证用户是否有上传权限，然后检查各项必填信息和规则，最后将数据插入数据库。
     *
     * @return array 返回一个包含操作状态和消息的数组。
     */
    public function doAdd()
    {
        $data = input('param.');
        //配置
        $config = Db::name('micro_series_config')
            ->where('much_id', $data['much_id'])
            ->find();
        if ($config['is_allow_user_upload'] == 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '系统禁止用户上传短剧！']);
        }
        $data['paid_unlocking_price'] = abs($data['paid_unlocking_price']);
        //判断是否是新剧
        if ($data['key'] == 0) {
            $data['plot_summary'] = strip_tags($data['plot_summary']);
            $data['title'] = strip_tags($data['title']);
            $data['director'] = strip_tags($data['director']);
            $data['screenwriter'] = strip_tags($data['screenwriter']);
            $data['lead_actors'] = strip_tags($data['lead_actors']);
            $data['production_country'] = strip_tags($data['production_country']);
            $data['language'] = strip_tags($data['language']);
            $data['release_date'] = strip_tags($data['release_date']);
            $data['duration_minutes'] = strip_tags($data['duration_minutes']);
            $data['alias'] = strip_tags($data['alias']);
            if (empty(preg_replace('# #', '', $data['title']))) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '短剧标题不能为空']);
            }
            if (empty(preg_replace('# #', '', $data['type']))) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '短剧类型不能为空']);
            }
            if (empty(preg_replace('# #', '', $data['plot_summary']))) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '剧情简介不能为空']);
            }
        }
        if (empty(preg_replace('# #', '', $data['poster_url']))) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '短剧封面不能为空']);
        }
        if (empty(preg_replace('# #', '', $data['msi_episode_url']))) {
            if (empty(preg_replace('# #', '', $data['url_link']))) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '短剧视频不能为空']);
            }
        }
        if ($data['key'] == 0 && $config['is_require_user_copyright'] == 1 && empty(preg_replace('# #', '', $data['user_copyright_img']))) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '请上传版权或授权证书图片']);
        }
        $util = new Util();
        //标题
        $check_content = $util->get_check_msg($data['title'], $data['much_id'], $data['openid']);
        if ($check_content['status'] == 'error') {
            return $this->json_rewrite($check_content);
        }
        //简介
        $check_content = $util->get_check_msg($data['plot_summary'], $data['much_id'], $data['openid']);
        if ($check_content['status'] == 'error') {
            return $this->json_rewrite($check_content);
        }
        if (!empty($data['director'])) {
            $check_title = $util->get_check_msg($data['director'], $data['much_id'], $data['openid']);
            if ($check_title['status'] == 'error') {
                return $this->json_rewrite($check_title);
            }
        }
        if (!empty($data['screenwriter'])) {
            $check_title = $util->get_check_msg($data['screenwriter'], $data['much_id'], $data['openid']);
            if ($check_title['status'] == 'error') {
                return $this->json_rewrite($check_title);
            }
        }
        if (!empty($data['lead_actors'])) {
            $check_title = $util->get_check_msg($data['lead_actors'], $data['much_id'], $data['openid']);
            if ($check_title['status'] == 'error') {
                return $this->json_rewrite($check_title);
            }
        }
        if (!empty($data['production_country'])) {
            $check_title = $util->get_check_msg($data['production_country'], $data['much_id'], $data['openid']);
            if ($check_title['status'] == 'error') {
                return $this->json_rewrite($check_title);
            }
        }
        if (!empty($data['language'])) {
            $check_title = $util->get_check_msg($data['language'], $data['much_id'], $data['openid']);
            if ($check_title['status'] == 'error') {
                return $this->json_rewrite($check_title);
            }
        }
        if (!empty($data['release_date'])) {
            $check_title = $util->get_check_msg($data['release_date'], $data['much_id'], $data['openid']);
            if ($check_title['status'] == 'error') {
                return $this->json_rewrite($check_title);
            }
        }
        if (!empty($data['alias'])) {
            $check_title = $util->get_check_msg($data['alias'], $data['much_id'], $data['openid']);
            if ($check_title['status'] == 'error') {
                return $this->json_rewrite($check_title);
            }
        }
        //未被允许收费
        if ($config['is_allow_user_charge'] == 0 && $data['paid_unlocking_type'] != 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '系统禁止用户收费！']);
        }
        if ($config['is_allow_user_charge'] == 1 && $data['paid_unlocking_type'] != 0) {
            if ($data['paid_unlocking_price'] < $config['min_amount_charged']) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '最小收费金额不能小于' . $config['min_amount_charged']]);
            }
            if ($data['paid_unlocking_price'] > $config['max_amount_charged']) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '最大收费金额不能大于' . $config['max_amount_charged']]);
            }
        }
        //新剧
        if ($data['key'] == 0) {
            $item['upload_user_id'] = $this->user_info['id'];
            $item['title'] = $data['title'];
            $item['type'] = $data['type'];
            $item['poster_url'] = $data['poster_url'];
            $item['director'] = $data['director'];
            $item['screenwriter'] = $data['screenwriter'];
            $item['lead_actors'] = $data['lead_actors'];
            $item['production_country'] = $data['production_country'];
            $item['language'] = $data['language'];
            $item['release_date'] = $data['release_date'];
            $item['duration_minutes'] = $data['duration_minutes'];
            $item['alias'] = $data['alias'];
            $item['plot_summary'] = $data['plot_summary'];
            $item['total_episodes'] = 1;
            $item['user_copyright_img'] = $data['user_copyright_img'];
            $item['status'] = $config['is_info_auto_review'];
            $item['display_status'] = 1;
            $item['create_time'] = time();
            $item['update_time'] = time();
            $item['is_del'] = 0;
            $item['much_id'] = $data['much_id'];
            // 启动事务
            Db::startTrans();
            try {
                $ins = Db::name('micro_series_info_list')->insertGetId($item);
                if (!$ins) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '上传短剧失败，请稍候重试！-1']);
                }
                // 新增剧集
                $episode['msi_id'] = $ins;
                $episode['upload_user_id'] = $this->user_info['id'];
                $episode['msi_episode_number'] = 1;
                $episode['msi_episode_url'] = $data['msi_episode_url'] == '' ? $data['url_link'] : $data['msi_episode_url'];
                $episode['is_allow_only_vip'] = 0;
                $episode['paid_unlocking_type'] = $data['paid_unlocking_type'];
                $episode['paid_unlocking_price'] = $data['paid_unlocking_price'];
                $episode['status'] = $config['is_content_auto_review'];
                $episode['create_time'] = time();
                $episode['much_id'] = $data['much_id'];
                $episode_ins = Db::name('micro_series_content_list')->insert($episode);
                if (!$episode_ins) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '上传短剧失败，请稍候重试！-2']);
                }
                // 提交事务
                Db::commit();
                if ($config['is_info_auto_review'] == 1 && $config['is_content_auto_review'] == 1) {
                    $msg = '上传成功！';
                } else {
                    $msg = '上传成功，审核通过后，将显示在小程序中！';
                }
                return $this->json_rewrite(['status' => 'success', 'msg' => $msg]);
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '上传短剧失败，请稍候重试！-3' . $e->getMessage()]);
            }
        } else {
            if ($data['my_video_id'] == 0) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '请选择剧集！']);
            }
            //查询当前剧集数
            $listCount = Db::name('micro_series_content_list')
                ->where('much_id', $data['much_id'])
                ->where('msi_id', $data['my_video_id'])
                ->count();
            $episode['msi_id'] = $data['my_video_id'];
            $episode['msi_episode_number'] = ($listCount + 1);
            $episode['msi_episode_url'] = $data['msi_episode_url'] == '' ? $data['url_link'] : $data['msi_episode_url'];
            $episode['is_allow_only_vip'] = 0;
            $episode['paid_unlocking_type'] = $data['paid_unlocking_type'];
            $episode['paid_unlocking_price'] = $data['paid_unlocking_price'];
            $episode['status'] = $config['is_content_auto_review'];
            $episode['create_time'] = time();
            $episode['much_id'] = $data['much_id'];
            // 启动事务
            Db::startTrans();
            try {
                //上传内容
                $episode_ins = Db::name('micro_series_content_list')->insert($episode);
                if (!$episode_ins) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '上传短剧失败，请稍候重试！-1']);
                }
                //更新集数
                $update = Db::name('micro_series_info_list')
                    ->where('much_id', $data['much_id'])
                    ->where('id', $data['my_video_id'])
                    ->inc('total_episodes');
                if (!$update) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '上传短剧失败，请稍候重试！-2']);
                }
                Db::commit();
                if ($config['is_content_auto_review'] == 1) {
                    $msg = '上传成功！';
                } else {
                    $msg = '上传成功，审核通过后，将显示在小程序中！';
                }
                return $this->json_rewrite(['status' => 'success', 'msg' => $msg]);
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '上传短剧失败，请稍候重试！-3' . $e->getMessage()]);
            }

        }
    }

    /**
     * 获取我的剧集
     */
    public function getMyseriesList()
    {
        $data = input('param.');
        //配置
        $config = Db::name('micro_series_config')
            ->where('much_id', $data['much_id'])
            ->field('is_allow_user_upload')
            ->find();
        $list = Db::name('micro_series_info_list')
            ->where('much_id', $data['much_id'])
            ->where('upload_user_id', $this->user_info['id'])
            ->where('display_status', 1)
            ->where('is_del', 0)
            ->field('id,title,poster_url,total_episodes')
            ->select();
        //查询我的所有集数
        $count = 0;
        foreach ($list as $key => $value) {
            $episode_count = Db::name('micro_series_content_list')
                ->where('much_id', $data['much_id'])
                ->where('msi_id', $value['id'])
                ->where('status', 1)
                ->where('is_del', 0)
                ->count();
            $count += $episode_count;
        }
        return $this->json_rewrite(['list' => $list, 'count' => $count, 'config' => $config]);
    }

    /**
     *  获取短剧详情，带剧集
     */
    public function getMyseriesInfo()
    {
        $data = input('param.');
        $info = Db::name('micro_series_info_list')
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->find();
        $array = explode(",", $info['type']);
        $type_list = [];
        foreach ($array as $k => $v) {
            $type = Db::name('micro_series_type')->where('id', $v)->where('much_id', $data['much_id'])->field('id,name')->find();
            $type_list[] = $type;
        }
        $info['type_list'] = $type_list;
        $list = Db::name('micro_series_content_list')
            ->where('much_id', $data['much_id'])
            ->where('msi_id', $info['id'])
            ->field('id,msi_episode_number,is_allow_only_vip,paid_unlocking_type,paid_unlocking_price,status,is_del')
            ->select();
        return $this->json_rewrite(['list' => $list, 'info' => $info]);
    }

    /**
     *  获取短剧详情
     */
    public function getInfo()
    {
        $data = input('param.');
        $info = Db::name('micro_series_info_list')
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->find();
        if ($info['status'] != 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '剧集已被下架！']);
        }
        if ($info['display_status'] != 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '剧集已被下架！']);
        }
        if ($info['is_del'] == 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '剧集已被下架！']);
        }
        $array = explode(",", $info['type']);
        $type_list = [];
        foreach ($array as $k => $v) {
            $type = Db::name('micro_series_type')->where('id', $v)->where('much_id', $data['much_id'])->field('id,name')->find();
            $type_list[] = $type;
        }
        $info['type_list'] = $type_list;
        $list = Db::name('micro_series_content_list')
            ->where('much_id', $data['much_id'])
            ->where('msi_id', $info['id'])
            ->where('status', 1)
            ->where('display_status', 1)
            ->where('is_del', 0)
            ->field('id,msi_id,msi_episode_number,is_allow_only_vip,paid_unlocking_type,paid_unlocking_price,status,is_del')
            ->select();
        foreach ($list as $k => $v) {
            $list[$k]['msi_episode_number'] = '第' . numToWord($v['msi_episode_number']) . '集';
            $list[$k]['msi_episode_number_name'] = '第' . $v['msi_episode_number'] . '集';
            $list[$k]['is_zj'] = Db::name('micro_series_user_like')
                ->where('user_id', $this->user_info['id'])
                ->where('msi_id', $v['msi_id'])
                ->where('like_type', 1)
                ->where('much_id', $data['much_id'])
                ->count();
            $list[$k]['is_xh'] = Db::name('micro_series_user_like')
                ->where('user_id', $this->user_info['id'])
                ->where('msi_id', $v['msi_id'])
                ->where('msc_id', $v['id'])
                ->where('like_type', 0)
                ->where('much_id', $data['much_id'])
                ->count();
        }
        return $this->json_rewrite(['status' => 'success', 'info' => $info, 'list' => $list]);
    }

    /**
     * 获取视频地址
     */
    public function video()
    {
        $data = input('param.');
        $util = new Util();
        $config = Db::name('micro_series_config')->where('much_id', $data['much_id'])->find();
        //查询用户是否是VIP
        $vip = $util->get_user_vip($this->user_info['id']);
        $info = Db::name('micro_series_content_list')
            ->where('much_id', $data['much_id'])
            ->where('msi_id', $data['id'])
            ->where('id', $data['epis'])
            ->field('id,status,display_status,is_del,upload_user_id,msi_id,msi_episode_url,paid_unlocking_type,paid_unlocking_price,is_allow_only_vip')
            ->find();
        if ($info['status'] == 0) {
            return $this->json_rewrite(['lock' => -1, 'msg' => '剧集已下架！']);
        }
        if ($info['is_del'] == 1) {
            return $this->json_rewrite(['lock' => -1, 'msg' => '剧集已下架！']);
        }
        if ($info['display_status'] == 0) {
            return $this->json_rewrite(['lock' => -1, 'msg' => '剧集已下架！']);
        }
        //0 已解锁 1需要VIP 2需要付费
        $lock = 0;
        //查询当前视频是否是付费类型
        if ($info['is_allow_only_vip'] == 1 && $vip == 0) {
            //VIP观看，并且不是VIP
            $lock = 1;
        }
        if ($info['paid_unlocking_type'] != 0) {
            if ($config['is_vip_free_look'] == 1 && $vip == 1) {
                $lock = 0;
            } else {
                //付费观看
                $checkLock = Db::name('micro_series_unlock_paid_content_list')
                    ->where('user_id', $this->user_info['id'])
                    ->where('msi_id', $info['msi_id'])
                    ->where('msc_id', $info['id'])
                    ->where('much_id', $data['much_id'])
                    ->count();
                //如果已解锁
                if ($checkLock > 0) {
                    $lock = 0;
                } else {
                    //查询是否开启每天免费内容次数
                    if ($config['every_day_free_look_num'] > 0) {
                        //查询今日是否有免费次数
                        $checkLock = Db::name('micro_series_unlock_paid_content_list')
                            ->where('user_id', $this->user_info['id'])
                            ->where('much_id', $data['much_id'])
                            ->whereTime('unlock_time', 'today')
                            ->where('unlock_type', 0)
                            ->count();
                        if ($checkLock >= $config['every_day_free_look_num']) {
                            //没有免费次数了
                            $lock = 2;
                        } else {
                            //有免费次数
                            $lock = 3;
                            //插入支付记录
                            $pay_ins['much_id'] = $data['much_id'];
                            $pay_ins['msi_id'] = $info['msi_id'];
                            $pay_ins['msc_id'] = $info['id'];
                            $pay_ins['user_id'] = $this->user_info['id'];
                            $pay_ins['unlock_price'] = 0;
                            $pay_ins['unlock_time'] = time();
                            $pay_ins['unlock_type'] = 0;
                            $pay_ins['charged_profit_rake_ratio'] = 0;
                            $pay_id = Db::name('micro_series_unlock_paid_content_list')->insert($pay_ins);
                            if (!$pay_id) {
                                return $this->json_rewrite(['lock' => -1, 'msg' => '系统错误，请稍候重试']);
                            }

                        }
                    } else {
                        //支付
                        $lock = 2;
                    }
                }
            }
        }
        //查询是否是我自己的剧集
        if ($this->user_info['id'] == $info['upload_user_id']) {
            $lock = 0;
        }
        if ($lock != 0 && $lock != 3) {
            $info['msi_episode_url'] = '';
        }
        //pay
        $pay['paid_unlocking_type'] = $info['paid_unlocking_type'];
        $pay['is_allow_only_vip'] = $info['is_allow_only_vip'];
        $pay['paid_unlocking_price'] = $info['paid_unlocking_price'];
        $pay['confer'] = $this->design['confer'];
        $pay['currency'] = $this->design['currency'];
        $pay['user_conch'] = $this->user_info['conch'];
        $pay['user_fraction'] = $this->user_info['fraction'];
        //1 可以看广告
        $pay['ads'] = 1;
        $pay['incentive_id'] = '';
        //开启了观看广告解锁
        if ($config['is_enabled_look_ads_unlock_paid_content'] == 1) {
            //0=无限制
            if ($config['look_ads_unlock_paid_content_max_num'] > 0) {
                //查询本日是否有次数
                $checkAdsLock = Db::name('micro_series_unlock_paid_content_list')
                    ->where('user_id', $this->user_info['id'])
                    ->where('much_id', $data['much_id'])
                    ->whereTime('unlock_time', 'today')
                    ->where('unlock_type', 3)
                    ->count();
                if ($checkAdsLock >= $config['look_ads_unlock_paid_content_max_num']) {
                    $pay['ads'] = 0;
                }
            }

        } else {
            $pay['ads'] = 0;
        }
        if ($pay['ads'] == 1) {
            $adInfo = Db::name('advertise')->where('much_id', $data['much_id'])->find();
            $pay['incentive_id'] = $adInfo['incentive_id'];
        }

        return $this->json_rewrite(['info' => $info['msi_episode_url'], 'lock' => $lock, 'pay' => $pay]);
    }

    public function get_video_reply()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $id = $data['id'];
        $page = $data['page'];
        $where = [];
        //获取一级评论
        $pl = Db::name('micro_series_info_review')->alias('r')
            ->join('user u', 'u.id=r.user_id')
            ->where('r.msi_id', $data['id'])
            ->where('r.msc_id', $data['epis'])
            ->where('r.is_del', '0')
            ->where('r.status', 1)
            ->where('rid', 0)
            ->order('r.create_time')
            ->field('r.id,r.user_id,r.comment,r.rid,r.create_time,u.user_nick_name,u.user_head_sculpture')
            ->page($page, '5')
            ->select();
        foreach ($pl as $k => $v) {
            $pl[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
            $pl[$k]['comment'] = emoji_decode($v['comment']);
            $pl[$k]['create_time'] = formatTime($v['create_time']);
            //评论回复
            $info_list = Db::name('micro_series_info_review')
                ->where('rid', $v['id'])
                ->where('status', 1)
                ->where('is_del', 0)
                ->count();
            $pl[$k]['hui_count'] = $info_list;
            $pl[$k]['expandList'] = [];
        }

        $hui_count = Db::name('micro_series_info_review')->where('status', 1)->where('is_del', 0)->where('msi_id', $data['id'])
            ->where('msc_id', $data['epis'])->where('much_id', $data['much_id'])->count();
        $rs['huifu_count'] = formatNumber($hui_count);
        $rs['huifu'] = $pl;
        return $this->json_rewrite($rs);
    }

    public function reply_do()
    {
        $data = input('param.');
        if ($this->user_info['tourist'] == 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '请登陆后回复！']);
        }
        //获取是否打开强制手机号
        $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($this->version == 0) {
            if ($authority['re_force_phone_arbor'] == 1 && empty($this->user_info['user_phone'])) {
                return $this->json_rewrite(['status' => 'error', 'code' => 1, 'id' => $this->version, 'msg' => '请绑定手机号！']);
            }
        }
        $util = new Util();
        if (!empty($data['text'])) {
            $check_title = $util->get_check_msg($data['text'], $data['much_id'], $data['openid']);
            if ($check_title['status'] == 'error') {
                return $this->json_rewrite($check_title);
            }
        }
        $config = Db::name('micro_series_config')->where('much_id', $data['much_id'])->find();
        //是否开启自动审核//1开启0关闭
        $ins['status'] = $config['is_comment_auto_review'];
        $ins['msi_id'] = $data['id'];
        $ins['user_id'] = $this->user_info['id'];
        $ins['msc_id'] = $data['msc_id'];
        $ins['comment'] = $this->safe_html(emoji_encode($data['text']));
        $ins['create_time'] = time();
        $ins['much_id'] = $data['much_id'];
        $ins['rid'] = $data['reply_id'];
        $add = Db::name('micro_series_info_review')->insertGetId($ins);
        if (!$add) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '回复失败,请稍后重试！']);
        }
        return $this->json_rewrite(['status' => 'success', 'msg' => '回复成功！']);
    }

    /**
     * 展开回复
     */
    public function get_expand_list()
    {
        $data = input('param.');

        //获取当前评论下的回复 使用分页
        $list = Db::name('micro_series_info_review')
            ->where('much_id', $data['much_id'])
            ->where('is_del', 0)
            ->where('status', 1)
            ->where('rid', $data['id'])
            ->page($data['page'], 3)
            ->field('id,user_id,comment,create_time')
            ->order('id')
            ->select();
        foreach ($list as $k => $v) {
            $list[$k]['create_time'] = formatTime($v['create_time']);
            $list[$k]['comment'] = Alternative::ExpressionHtml(emoji_decode($v['comment']));;
            //回复者用户详情
            $reply_user = Db::name('user')->where('id', $v['user_id'])->field('id,user_head_sculpture,user_nick_name')->find();
            $reply_user['user_nick_name'] = emoji_decode($reply_user['user_nick_name']);
            $list[$k]['reply_user'] = $reply_user;
        }
        return $this->json_rewrite($list);
    }

    public function like_do()
    {
        $data = input('param.');
        //查询是否有这条数据
        $info = Db::name('micro_series_user_like')
            ->where('much_id', $data['much_id'])
            ->where('msi_id', $data['id'])
            ->where('msc_id', $data['epis'])
            ->where('like_type', $data['like_type'])
            ->where('user_id', $this->user_info['id'])
            ->find();
        if ($info) {
            Db::name('micro_series_user_like')->where('id', $info['id'])->delete();
        } else {
            //添加喜欢
            $ins['much_id'] = $data['much_id'];
            $ins['msi_id'] = $data['id'];
            $ins['msc_id'] = $data['epis'];
            $ins['user_id'] = $this->user_info['id'];
            $ins['create_time'] = time();
            $ins['like_type'] = $data['like_type'];
            Db::name('micro_series_user_like')->insert($ins);
        }
        return $this->json_rewrite(['status' => 'success']);
    }

    /**
     * 获取随机视频
     */
    public function getRandomIndex()
    {
        $data = input('param.');
        $order = Db::raw('rand()');
        $list = Db::name('micro_series_content_list')
            ->alias('c')
            ->join('micro_series_info_list i', 'c.msi_id=i.id')
            ->where('i.status', 1)
            ->where('i.display_status', 1)
            ->where('i.is_del', 0)
            ->where('c.much_id', $data['much_id'])
            ->where('c.status', 1)
            ->where('c.is_del', 0)
            ->where('c.display_status', 1)
            ->where('c.is_allow_only_vip', 0)
            ->where('c.paid_unlocking_type', 0)
            ->order($order)
            ->limit(20)
            ->field('c.id,c.msi_id,i.title,i.poster_url,i.plot_summary,i.total_episodes,c.msi_episode_url')
            ->select();
        return $this->json_rewrite(['list' => $list]);
    }

    public function pay_do()
    {
        $data = input('param.');
        $util = new Util();
        //查询内容详情
        $info = Db::name('micro_series_content_list')
            ->where('much_id', $data['much_id'])
            ->where('id', $data['epis'])
            ->where('msi_id', $data['id'])
            ->find();
        if ($info['status'] != 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '购买失败，请稍候重试 -3！']);
        }
        if ($info['display_status'] == 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '购买失败，剧集已下架 -1！']);
        }
        if ($info['is_del'] == 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '购买失败，剧集已下架 -2！']);
        }
        if ($info['paid_unlocking_type'] == 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '购买失败，请稍候重试 -4！']);
        }
        //查询购买人信息
        $user = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->field('fraction,conch')->find();
        $conch = bcsub($user['conch'], $info['paid_unlocking_price'], 2);
        $fraction = bcsub($user['fraction'], $info['paid_unlocking_price'], 2);
        if ($info['paid_unlocking_type'] == 1 && $conch <= 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => $this->design['currency'] . '不足，解锁失败！']);
        }
        if ($info['paid_unlocking_type'] == 2 && $fraction <= 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => $this->design['confer'] . '不足，解锁失败！']);
        }
        //查询短剧详情
        $series_info = Db::name('micro_series_info_list')
            ->where('much_id', $data['much_id'])
            ->where('id', $data['id'])
            ->find();
        //查询配置
        $config = Db::name('micro_series_config')->where('much_id', $data['much_id'])->find();
        //计算平台抽成
        $charged_profit_rake_ratio = bcmul($config['charged_profit_rake_ratio'], $info['paid_unlocking_price'], 2);
        //计算用户所得
        $userMoney = bcsub($info['paid_unlocking_price'], $charged_profit_rake_ratio, 2);
        // 启动事务
        Db::startTrans();
        try {
            //插入支付记录
            $pay_ins['much_id'] = $data['much_id'];
            $pay_ins['msi_id'] = $data['id'];
            $pay_ins['msc_id'] = $data['epis'];
            $pay_ins['user_id'] = $this->user_info['id'];
            $pay_ins['unlock_price'] = $info['paid_unlocking_price'];
            $pay_ins['unlock_time'] = time();
            $pay_ins['unlock_type'] = $info['paid_unlocking_type'];
            $pay_ins['charged_profit_rake_ratio'] = $config['charged_profit_rake_ratio'];
            $pay_id = Db::name('micro_series_unlock_paid_content_list')->insertGetId($pay_ins);
            if (!$pay_id) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '购买失败，请稍候重试 -5！']);
            }
            $solution = '购买短剧：' . $series_info['title'] . $info['msi_episode_number'];
            //1 贝壳2 积分

            if ($info['paid_unlocking_type'] == 1) {
                //明细表增加数据
                $user_amount = $util->user_amount($this->user_info['id'], 2, $info['paid_unlocking_price'], $user['fraction'], $user['fraction'], $user['conch'], bcsub($user['conch'], $info['paid_unlocking_price'], 2), 0, $solution, $data['much_id']);
                //扣除购买人金额
                $user_jian = Db::name('user')
                    ->where('id', $this->user_info['id'])
                    ->where('much_id', $data['much_id'])
                    ->update(['conch' => $conch]);
            } else {
                //明细表增加数据
                $user_amount = $util->user_amount($this->user_info['id'], 2, $info['paid_unlocking_price'], $user['fraction'], bcsub($user['fraction'], $info['paid_unlocking_price'], 2), $user['conch'], $user['conch'], 1, $solution, $data['much_id']);
                //扣除购买人金额
                $user_jian = Db::name('user')
                    ->where('id', $this->user_info['id'])
                    ->where('much_id', $data['much_id'])
                    ->update(['fraction' => $fraction]);
            }
            if (!$user_amount || !$user_jian) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'msg' => '购买失败，请稍候重试 -6！']);
            }


            if ($info['upload_user_id'] != 0) {
                $fa_solution = '短剧：' . $series_info['title'] . $info['msi_episode_number'] . '被解锁！';
                //查询发布人信息
                $fa_user = Db::name('user')->where('id', $info['upload_user_id'])->where('much_id', $data['much_id'])->field('fraction,conch')->find();
                $fa_conch = bcadd($fa_user['conch'], $userMoney, 2);
                $fa_fraction = bcadd($fa_user['fraction'], $userMoney, 2);
                if ($info['paid_unlocking_type'] == 1) {
                    //明细表增加数据
                    $user_fa_amount = $util->user_amount($info['upload_user_id'], 3, $userMoney, $fa_user['fraction'], $fa_user['fraction'], $fa_user['conch'], bcadd($fa_user['conch'], $userMoney, 2), 0, $fa_solution, $data['much_id']);
                    //增加发布人金额
                    $user_jia = Db::name('user')
                        ->where('id', $info['upload_user_id'])
                        ->where('much_id', $data['much_id'])
                        ->update(['conch' => $fa_conch]);
                } else {
                    //明细表增加数据
                    $user_fa_amount = $util->user_amount($info['upload_user_id'], 3, $userMoney, $fa_user['fraction'], bcadd($fa_user['fraction'], $userMoney, 2), $fa_user['conch'], $user['conch'], 1, $fa_solution, $data['much_id']);
                    //增加发布人金额
                    $user_jia = Db::name('user')
                        ->where('id', $info['upload_user_id'])
                        ->where('much_id', $data['much_id'])
                        ->update(['fraction' => $fa_fraction]);
                }
                if (!$user_jia) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '购买失败，请稍候重试 -7！']);
                }
                if (!$user_fa_amount) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'msg' => '购买失败，请稍候重试 -8！']);
                }
            }
            // 提交事务
            Db::commit();
            if ($info['paid_unlocking_type'] == 1) {
                $util->add_user_smail($data['uid'],$solution,$data['much_id'],0,0);
                $tmplData = [
                    'much_id' => $data['much_id'],
                    'at_id' => 'YL0009',
                    'user_id' => $data['uid'],
                    'page' => 'yl_welore/pages/user_smail/index',
                    'keyword1' => '扣除'.$this->design['currency'].'（-'.$info['paid_unlocking_price'].'）',
                    'keyword2' => date('Y年m月d日 H:i:s', time())
                ];
                $util->add_template($tmplData);
            }
            return $this->json_rewrite(['status' => 'success', 'info' => $info['msi_episode_url'], 'msg' => '购买成功！']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 看广告解锁
     */
    public function insAdLock()
    {
        $data = input('param.');
        //查询配置
        $config = Db::name('micro_series_config')->where('much_id', $data['much_id'])->find();
        //查询是否开了激励视频解锁
        if ($config['is_enabled_look_ads_unlock_paid_content'] == 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '激励视频解锁未开启！']);
        }
        //查询是否超出本日最大次数
        $checkAdsLock = Db::name('micro_series_unlock_paid_content_list')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->whereTime('unlock_time', 'today')
            ->where('unlock_type', 3)
            ->count();
        if ($config['look_ads_unlock_paid_content_max_num'] != 0 && $checkAdsLock >= $config['look_ads_unlock_paid_content_max_num']) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '今日已看激励视频次数达到上限，请明日再试！']);
        }
        //插入支付记录
        $pay_ins['much_id'] = $data['much_id'];
        $pay_ins['msi_id'] = $data['id'];
        $pay_ins['msc_id'] = $data['epis'];
        $pay_ins['user_id'] = $this->user_info['id'];
        $pay_ins['unlock_price'] = 0;
        $pay_ins['unlock_time'] = time();
        $pay_ins['unlock_type'] = 3;
        $pay_ins['charged_profit_rake_ratio'] = 0;
        $pay_id = Db::name('micro_series_unlock_paid_content_list')->insertGetId($pay_ins);
        if (!$pay_id) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '解锁失败，请稍候重试！']);
        }
        //查询内容详情
        $info = Db::name('micro_series_content_list')
            ->where('much_id', $data['much_id'])
            ->where('id', $data['epis'])
            ->where('msi_id', $data['id'])
            ->find();
        return $this->json_rewrite(['status' => 'success', 'info' => $info['msi_episode_url']]);
    }

    /**
     * 获取我喜欢的
     */
    public function getMyLove()
    {
        $data = input('param.');
        $list = Db::name('micro_series_user_like')
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->where('like_type', $data['type'])
            ->order('create_time desc')
            ->page($data['page'], 10)
            ->select();
        foreach ($list as $k => $v) {
            $msi = Db::name('micro_series_info_list')->where('id', $v['msi_id'])->find();
            $list[$k]['title'] = $msi['title'];
            $list[$k]['poster_url'] = $msi['poster_url'];
            $list[$k]['plot_summary'] = $msi['plot_summary'];
            $list[$k]['total_episodes'] = $msi['total_episodes'];
            $msc = Db::name('micro_series_content_list')->where('id', $v['msc_id'])->find();
            $list[$k]['msc'] = $msc['msi_episode_number'];
        }
        return $this->json_rewrite(['status' => 'success', 'list' => $list]);
    }

    /**
     * 删除喜欢的
     */
    public function delDo()
    {
        $data = input('param.');
        $list = Db::name('micro_series_user_like')
            ->where('id', $data['id'])
            ->where('user_id', $this->user_info['id'])
            ->where('much_id', $data['much_id'])
            ->where('like_type', $data['type'])
            ->delete();
        if (!$list) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '删除失败！']);
        }
        return $this->json_rewrite(['status' => 'success', 'msg' => '删除成功！']);
    }

    public function getMyHistory()
    {
        $data = input('param.');
        $list = json_decode($data['list'], true);
        $new_list = [];
        foreach ($list as $k => $v) {
            $new_list[$k]['info'] = Db::name('micro_series_content_list')
                ->alias('c')
                ->join('micro_series_info_list i', 'c.msi_id=i.id')
                ->where('c.much_id', $data['much_id'])
                ->where('c.id', $v['epis'])
                ->field('i.id,i.title,i.poster_url,i.plot_summary,i.total_episodes,c.msi_episode_number,c.id as epis')
                ->find();
            $new_list[$k]['list'] = Db::name('micro_series_content_list')
                ->where('much_id', $data['much_id'])
                ->where('msi_id', $new_list[$k]['info']['id'])
                ->field('id,msi_episode_number,is_allow_only_vip,paid_unlocking_type,paid_unlocking_price,status,is_del')
                ->select();
        }
        return $this->json_rewrite(['list' => $new_list]);
    }

    /**
     * 获取用户协议或者其他
     */
    public function all_agreement()
    {
        $data = input('param.');
        $arr = [];
        //短剧的用户协议
        if ($data['type'] == 1) {
            $info = Db::name('micro_series_config')
                ->where('much_id', $data['much_id'])
                ->field('user_agreement')
                ->find();
            $arr['title'] = '用户协议';
            $arr['info'] = $info['user_agreement'];
        }
        //短剧的免责声明
        if ($data['type'] == 2) {
            $info = Db::name('micro_series_config')
                ->where('much_id', $data['much_id'])
                ->field('disclaimer_warranties')
                ->find();
            $arr['title'] = '免责声明';
            $arr['info'] = $info['disclaimer_warranties'];
        }
        return $this->json_rewrite(['list' => $arr]);
    }
}