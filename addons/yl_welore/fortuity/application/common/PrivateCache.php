<?php

namespace app\common;

use think\Cache;
use think\cache\driver\Redis;
use think\Config;

if (
    !defined('IN_IA') ||
    (class_exists('\think\Request') && strtolower(request()->controller()) === strtolower(basename(__FILE__, '.php')))
) {
    exit('Access Denied');
}

class PrivateCache
{
    /*
     * 清除项目缓存
     */
    public static function clearProjectCache()
    {

        $pathLeft = $_SERVER['DOCUMENT_ROOT'];
        $pathRight = explode("web" . DS . "index.php", $_SERVER['SCRIPT_NAME']);
        $absPath = $pathLeft . $pathRight[0] . 'fortuity' . DS . 'runtime';
        self::rmdirs($absPath);

        //  获取缓存配置
        $cacheConfig = Config::get('cache');
        //  判断是否是文件缓存
        if (strtolower($cacheConfig['type']) === 'file') {
            Cache::clear();
        } else {
            // 获取Redis实例
            $redis = new Redis($cacheConfig);
            // 获取redis->handler()
            $redis = $redis->handler();
            // 获取所有带有指定前缀的缓存键名
            $keys = $redis->keys($cacheConfig['prefix'] . '*');
            // 删除符合条件的缓存数据
            $redis->del($keys);
        }
    }

    //  删除缓存
    private static function rmdirs($absPath)
    {
        $absPath_arr = @scandir($absPath);
        foreach ($absPath_arr as $key => $val) {
            if ($val == '.' || $val == '..') {
            } else {
                if (@is_dir($absPath . DS . $val)) {
                    if (@rmdir($absPath . DS . $val) == 'true') {
                    } else {
                        @self::rmdirs($absPath . DS . $val);
                    }
                } else {
                    @unlink($absPath . DS . $val);
                }
            }
        }
    }
}