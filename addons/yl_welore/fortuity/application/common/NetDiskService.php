<?php

namespace app\common;

if (
    !defined('IN_IA') ||
    (class_exists('\think\Request') && strtolower(request()->controller()) === strtolower(basename(__FILE__, '.php')))
) {
    exit('Access Denied');
}

use app\urge\controller\Upload;
use think\Db;
use app\common\Remotely;

class NetDiskService
{

    /*
     * 允许的上传类型
     */
    public static function allowedUploadTypes($muchId)
    {
        //  获取网盘配置信息
        $ngInfo = Db::name('netdisc_config')->where('much_id', $muchId)->field('upload_type_limited')->find();
        //  默认支持上传类型
        $defaultLimitUploadType = explode(',', 'gif,jpg,jpeg,bmp,png,mp3,mpeg,wav,ogg,avi,mkv,mp4,txt,doc,docx,xls,xlsx,ppt,pptx,pdf,zip,rar,7z');
        //  禁止上传的类型
        $uploadTypeLimited = explode(',', strtolower($ngInfo['upload_type_limited']));
        //  允许的上传类型
        $allowedUploadTypes = array_diff($defaultLimitUploadType, $uploadTypeLimited);
        //  返回数据
        return implode(',', $allowedUploadTypes);
    }


    /*
     * 上传类型
     */
    public static function manipulate($file, $muchId)
    {
        if ($file) {
            //  获取临时文件的md5值
            $tempMD5 = md5_file($file->getInfo('tmp_name'));
            //  查询是否存在
            $ncInfo = Db::name('netdisc')->where('file_md5', $tempMD5)->find();
            //  如果存在直接返回
            if ($ncInfo) {
                return ['code' => 1, 'msg' => 'upload success !', 'url' => $ncInfo['file_address'], 'fileType' => $ncInfo['file_type'], 'fileMD5' => $ncInfo['file_md5'], 'getSize' => $ncInfo['file_size'], 'getExt' => $ncInfo['file_suffix'], 'ncId' => $ncInfo['id']];
            }
            //  获取网盘存储上传配置
            $systemOutlying = Upload::systemOutlying(1, $muchId);
            //  判断是否跟随远程附件设置
            if (intval($systemOutlying['quicken_type']) === -1) {
                //  获取远程附件设置
                $systemOutlying = Upload::systemOutlying(0, $muchId);
            }
            //  附件上传文件大小限制 (byte)
            $fileSizeRestrict = Db::name('netdisc_config')->where('much_id', $muchId)->value('upload_size_limit');
            //  判断是否存在
            if (!$fileSizeRestrict) {
                $fileSizeRestrict = 10485760;
            }
            //  文件上传类型
            $retainType = intval($systemOutlying['quicken_type']);
            // 检查"亚马逊S3"插件是否启用，若启用则强制覆盖上传类型
            if (Remotely::isEnableProperty(base64_decode('6YCa55So5a2Y5YKo'), $muchId)) {
                // 将存储类型强制设置为 6 (Amazon S3)
                $retainType = 6;
            }
            //  获取文件大小
            $fileGetSize = $file->getSize();
            //  获取文件名
            $fileGetName = $file->getInfo('name');
            //  获取文件后缀
            $fileExt = strtolower(pathinfo($fileGetName, PATHINFO_EXTENSION));
            //  允许的上传类型
            $allowedUploadTypes = self::allowedUploadTypes($muchId);
            //  文件上传类型
            $info = $file->validate(['size' => $fileSizeRestrict, 'ext' => $allowedUploadTypes])->move(ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'uploads');
            //  判断是否移动成功
            if ($info) {
                //  文件地址
                $absLocalRes = ROOT_PATH . '..' . DS . 'web' . DS . 'static' . DS . 'uploads' . DS . $info->getSaveName();
                //  文件md5
                $fileMD5 = md5_file($absLocalRes);
                //  返回数据
                $result = [];
                //  上传配置信息
                $config = [];
                //  判断上传类型
                switch ($retainType) {
                    case 0:
                        //  获取当前包含协议、端口的域名
                        $domain = request()->domain();
                        //  获取当前完整URL并去掉参数
                        $resourcePath = explode("index.php", request()->url());
                        //  拼接上传后的路径
                        $pactReplace = "{$domain}{$resourcePath[0]}static/uploads/{$info->getSaveName()}";
                        //  获取保存的路径信息并转义
                        $pactPath = str_replace('\\', '/', $pactReplace);
                        //  返回数据
                        $result = ['code' => 1, 'msg' => 'upload success !', 'url' => $pactPath, 'fileType' => 0];
                        break;
                    default:
                        // 定义一个映射数组，用于将存储类型ID与对应的云存储配置进行关联
                        $configMap = [
                            1 => [ // 阿里云OSS
                                'source' => 'oss_follow', // 配置来源字段
                                'url_key' => 'oss_url', // URL健名
                                'keys' => [
                                    'accessKeyId' => 'oss_access_key_id',
                                    'accessKeySecret' => 'oss_access_key_secret',
                                    'endpoint' => 'oss_endpoint',
                                    'bucket' => 'oss_bucket',
                                ]
                            ],
                            2 => [
                                'source' => 'qiniu_follow',
                                'url_key' => 'qiniu_url',
                                'keys' => [
                                    'accessKey' => 'qiniu_access_key',
                                    'secretKey' => 'qiniu_secret_key',
                                    'bucket' => 'qiniu_bucket',
                                ]
                            ],
                            3 => [
                                'source' => 'cos_follow',
                                'url_key' => 'cos_url',
                                'keys' => [
                                    'region' => 'cos_region',
                                    'appId' => 'cos_app_id',
                                    'secretId' => 'cos_secret_id',
                                    'secretKey' => 'cos_secret_key',
                                    'bucket' => 'cos_bucket',
                                ]
                            ],
                            4 => [
                                'source' => 'upyun_follow',
                                'url_key' => 'upyun_url',
                                'keys' => [
                                    'service_name' => 'upyun_service_name',
                                    'operator_name' => 'upyun_operator_name',
                                    'operator_password' => 'upyun_operator_password',
                                ]
                            ],
                            5 => [
                                'source' => 'ftp_follow',
                                'url_key' => 'ftp_url',
                                'keys' => [
                                    'ftp_host' => 'ftp_host',
                                    'ftp_username' => 'ftp_username',
                                    'ftp_password' => 'ftp_password',
                                    'ftp_port' => 'ftp_port',
                                    'ftp_pasv' => 'ftp_pasv',
                                ]
                            ],
                            6 => [ // 亚马逊AWS S3
                                'source' => 'aws_follow', // 配置来源字段
                                'url_key' => 'aws_url', // URL健名
                                'keys' => [ // 秘钥健
                                    'key' => 'aws_key', // AWS-Key
                                    'secret' => 'aws_secret', // AWS-Secret
                                    'region' => 'aws_region', // AWS-Region
                                    'endpoint' => 'aws_endpoint', // AWS-Endpoint
                                    'bucket' => 'aws_bucket', // AWS-Bucket
                                    'force_path_style' => 'aws_force_path_style', // AWS-ForcePathStyle
                                ]
                            ]
                        ];
                        // 检查当前存储类型是否存在于配置映射中
                        if (isset($configMap[$retainType])) {
                            // 获取当前类型的具体配置规则
                            $typeConfig = $configMap[$retainType];
                            // 从系统配置中提取对应的云存储数据源
                            $sourceData = $systemOutlying[$typeConfig['source']];
                            // 获取目标配置的键名
                            $targetKeys = array_keys($typeConfig['keys']);
                            // 动态获取源配置的值
                            $sourceValues = array_map(function ($sourceKey) use ($sourceData) {
                                return isset($sourceData[$sourceKey]) ? $sourceData[$sourceKey] : null;
                            }, array_values($typeConfig['keys']));
                            // 组合键和值，生成最终的配置数组
                            $config = array_combine($targetKeys, $sourceValues);
                            // 过滤掉值为null的配置项
                            $config = array_filter($config, function ($value) {
                                return !is_null($value);
                            });
                            // 向配置中添加通用的文件信息
                            // 文件扩展名
                            $config['extend'] = $info->getExtension();
                            // 本地文件路径 
                            $config['path'] = $absLocalRes;
                            // 远程访问URL
                            $config['far_url'] = $systemOutlying[$typeConfig['source']][$typeConfig['url_key']];
                        }
                        break;
                }
                //  判断是否是第三方云存储
                if ($retainType !== 0) {
                    //  创建上传实例并调用上传方法并返回结果
                    $result = Assembly::transfer($retainType, $config);
                    //  附件类型 1
                    $result['fileType'] = 1;
                }
                //  文件md5
                $result['fileMD5'] = $fileMD5;
                //  文件名称
                $result['getName'] = $fileGetName;
                //  文件大小
                $result['getSize'] = $fileGetSize;
                //  文件类型
                $result['getExt'] = $fileExt;
                //  网盘编号
                $result['ncId'] = 0;
                //  输出上传成功信息
                return $result;
            } else {
                return ['code' => 0, 'msg' => 'file move error !', 'status' => 'error'];
            }
        } else {
            return ['code' => 0, 'msg' => 'file save failed !', 'status' => 'error'];
        }
    }
}
