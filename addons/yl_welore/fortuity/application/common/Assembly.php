<?php
// 定义命名空间
namespace app\common;

if (
    !defined('IN_IA') ||
    (class_exists('\think\Request') && strtolower(request()->controller()) === strtolower(basename(__FILE__, '.php')))
) {
    exit('Access Denied');
}

use OSS\OssClient;
use Qcloud\Cos\Client;
use Qiniu\Auth;
use Qiniu\Storage\UploadManager;
use think\Log;
use Upyun\Config;
use Upyun\Upyun;

/**
 * 文件上传核心类
 * 实现了将本地文件上传到多种主流云存储服务的功能，包括阿里云、七牛云、腾讯云、又拍云、FTP和AWS S3。
 * 通过统一的接口和包装器模式，简化了上传操作并减少了代码冗余。
 */
class Assembly
{

    /**
     * 文件上传任务分发器
     * 根据指定的存储类型，调用相应的上传处理方法。
     * @param int $quickenType 存储类型 (1:OSS, 2:<PERSON><PERSON>, 3:COS, 4:Upyun, 5:FTP, 6:AWS, 7:123盘)
     * @param array $config 包含各存储类型所需的认证信息和文件路径的配置数组
     * @return array|void 返回一个包含上传结果的数组，或在类型不支持时无返回
     */
    public static function transfer($quickenType, $config)
    {
        //  判断上传类型
        switch (intval($quickenType)) {
            case 1: // 阿里云OSS
                return self::ossUpload($config);
            case 2: // 七牛云
                return self::qiniuUpload($config);
            case 3: // 腾讯云COS
                return self::cosUpload($config);
            case 4: // 又拍云
                return self::upyunUpload($config);
            case 5: // FTP
                return self::ftpUpload($config);
            case 6: // AWS S3
                return self::awsUpload($config);
            case 7: // 123盘
                return self::pan123Upload($config);
        }
    }

    /**
     * 阿里云OSS上传
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function ossUpload($config)
    {
        return self::_uploadWrapper($config, function ($object, $filePath) use ($config) {
            $accessKeyId = $config['accessKeyId'];
            $accessKeySecret = $config['accessKeySecret'];
            $endpoint = $config['endpoint'];
            $bucket = $config['bucket'];
            $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);
            $ossClient->uploadFile($bucket, $object, $filePath);
            return [];
        });
    }

    /**
     * 七牛云Kodo上传
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function qiniuUpload($config)
    {
        return self::_uploadWrapper($config, function ($object, $filePath) use ($config) {
            $accessKey = $config['accessKey'];
            $secretKey = $config['secretKey'];
            $auth = new Auth($accessKey, $secretKey);
            $token = $auth->uploadToken($config['bucket']);
            $uploadMgr = new UploadManager();
            list(, $err) = $uploadMgr->putFile($token, $object, $filePath);
            if ($err !== null) {
                // 如果七牛云返回错误对象，则抛出异常，由wrapper统一处理
                throw new \Exception('qiniu upload error: ' . $err->message());
            }
            return [];
        });
    }

    /**
     * 腾讯云COS上传
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function cosUpload($config)
    {
        return self::_uploadWrapper($config, function ($object, $filePath) use ($config) {
            $qcloudConfig = [
                'region' => $config['region'],
                'credentials' => [
                    'appId' => $config['appId'],
                    'secretId' => $config['secretId'],
                    'secretKey' => $config['secretKey']
                ]
            ];
            $cosClient = new Client($qcloudConfig);
            $cosClient->putObject([
                'Bucket' => $config['bucket'],
                'Key' => $object,
                // 以文件流方式上传
                'Body' => fopen($filePath, 'rb')
            ]);
            return [];
        });
    }

    /**
     * 又拍云USS上传
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function upyunUpload($config)
    {
        return self::_uploadWrapper($config, function ($object, $filePath) use ($config) {
            $serviceName = $config['service_name'];
            $operatorName = $config['operator_name'];
            $operatorPassword = $config['operator_password'];
            $bucketConfig = new Config($serviceName, $operatorName, $operatorPassword);
            $client = new Upyun($bucketConfig);
            $file = fopen($filePath, 'r');
            // 注意：又拍云的API要求远程路径以'/'开头
            $client->write('/' . $object, $file);
            return [];
        });
    }

    /**
     * FTP上传
     * 该方法包含独立的连接、目录创建和重试逻辑，未经过_uploadWrapper包装。
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function ftpUpload($config)
    {
        // 记录开始上传的日志
        Log::write('Starting FTP upload process', 'log', true);
        // 连接到FTP服务器，添加30秒连接超时
        $ftp = @ftp_connect($config['ftp_host'], $config['ftp_port'], 30);
        if (!$ftp) {
            Log::write('Error: Unable to connect to FTP server: ' . $config['ftp_host'] . ':' . $config['ftp_port'], 'log', true);
            return ['code' => 0, 'msg' => 'Error: Unable to connect to FTP server'];
        }
        // 设置更长的执行超时时间（120秒），避免大文件上传时PHP脚本超时
        @ftp_set_option($ftp, FTP_TIMEOUT_SEC, 120);
        // 尝试登录FTP
        $login = @ftp_login($ftp, $config['ftp_username'], $config['ftp_password']);
        if (!$login) {
            Log::write('Error: FTP login failed for user: ' . $config['ftp_username'], 'log', true);
            @ftp_close($ftp);
            return ['code' => 0, 'msg' => 'Error: FTP login failed'];
        }
        // 根据配置决定是否开启被动模式(PASV)
        if ($config['ftp_pasv'] == 1) {
            @ftp_pasv($ftp, true);
            Log::write('FTP passive mode enabled', 'log', true);
        } else {
            Log::write('FTP active mode being used', 'log', true);
        }
        // 获取当前FTP目录
        $currentFtpDir = @ftp_pwd($ftp);
        Log::write('Current FTP directory: ' . $currentFtpDir, 'log', true);
        $pathInfo = self::_generateObjectPath($config['extend']);
        $newly = $pathInfo['title'];
        $remoteFilePath = $pathInfo['path'];
        $remoteDirectory = dirname($remoteFilePath);
        // 获取本地文件路径
        $localFilePath = $config['path'];
        Log::write('Upload plan - Local: ' . $localFilePath . ' -> Remote: ' . $remoteFilePath, 'log', true);
        // 检查本地文件是否存在，防止无效上传
        if (!file_exists($localFilePath)) {
            Log::write('Error: Local file does not exist - ' . $localFilePath, 'log', true);
            @ftp_close($ftp);
            return ['code' => 0, 'msg' => 'Error: Local file does not exist'];
        }
        // 记录文件大小，便于调试
        $fileSize = filesize($localFilePath);
        Log::write('File size: ' . $fileSize . ' bytes', 'log', true);
        // ===== 远程目录创建逻辑开始 =====
        // 切换到FTP根目录，以确保从正确的起点开始创建目录
        if (!@ftp_chdir($ftp, '/')) {
            Log::write('Warning: Could not change to root directory, using current directory', 'log', true);
        }
        $directories = explode('/', $remoteDirectory);
        // 逐级创建远程目录
        foreach ($directories as $dir) {
            if (empty($dir)) {
                continue;
            }
            // 尝试进入目录，如果失败则意味着目录不存在，需要创建
            if (!@ftp_chdir($ftp, $dir)) {
                Log::write('Directory does not exist, attempting to create: ' . $dir, 'log', true);
                // 尝试创建新目录
                if (@ftp_mkdir($ftp, $dir)) {
                    Log::write('Directory created: ' . $dir, 'log', true);
                    // 创建后再次尝试进入
                    if (!@ftp_chdir($ftp, $dir)) {
                        Log::write('Error: Cannot change to newly created directory: ' . $dir, 'log', true);
                        @ftp_close($ftp);
                        @unlink($config['path']);
                        return ['code' => 0, 'msg' => 'Error: Failed to navigate to newly created directory'];
                    }
                } else {
                    // 如果创建目录失败，记录错误并中止上传
                    Log::write('Error: Failed to create directory - ' . $dir, 'log', true);
                    @ftp_close($ftp);
                    @unlink($config['path']);
                    return ['code' => 0, 'msg' => 'Error: Failed to create remote directory'];
                }
            }
        }
        Log::write('All directories created successfully, current path: ' . ftp_pwd($ftp), 'log', true);
        // ===== 远程目录创建逻辑结束 =====
        // 回到根目录，准备上传文件到指定的完整路径
        if (!@ftp_chdir($ftp, '/')) {
            Log::write('Warning: Could not return to root directory for upload, using current directory', 'log', true);
        }
        // 尝试上传文件，最多重试3次，以增加上传成功率
        $maxRetries = 3;
        $uploadSuccess = false;
        $lastError = '';
        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            Log::write('Upload attempt ' . $attempt . ' of ' . $maxRetries, 'log', true);
            // 使用二进制模式上传
            if (@ftp_put($ftp, $remoteFilePath, $localFilePath, FTP_BINARY)) {
                $uploadSuccess = true;
                Log::write('File uploaded successfully on attempt ' . $attempt, 'log', true);
                break; // 上传成功，跳出循环
            } else {
                $error = error_get_last();
                $lastError = isset($error['message']) ? $error['message'] : 'Unknown error';
                Log::write('Attempt ' . $attempt . ' failed: ' . $lastError, 'log', true);
                // 如果不是最后一次尝试，则等待1秒后重试
                if ($attempt < $maxRetries) {
                    sleep(1);
                }
            }
        }
        // 根据最终上传结果设置返回信息
        if ($uploadSuccess) {
            $result = ['code' => 1, 'status' => 'success', 'url' => $config['far_url'] . '/' . $remoteFilePath, 'title' => $newly];
            Log::write('Upload successful. URL: ' . $config['far_url'] . '/' . $remoteFilePath, 'log', true);
        } else {
            $result = ['code' => 0, 'msg' => 'Error: File upload failed after ' . $maxRetries . ' attempts. Last error: ' . $lastError];
            Log::write('Upload failed after ' . $maxRetries . ' attempts', 'log', true);
        }
        // 关闭FTP连接
        @ftp_close($ftp);
        Log::write('FTP connection closed', 'log', true);
        // 删除本地临时文件
        @unlink($config['path']);
        // 返回最终结果
        return $result;
    }

    /**
     * AWS S3 上传
     * @param array $config 上传配置
     * @return array 上传结果
     */
    private static function awsUpload($config)
    {
        // 此处为特定业务逻辑，解锁某个属性后才可使用
        if (!Remotely::isUnLockProperty(base64_decode('6YCa55So5a2Y5YKo'))) {
            return ['code' => 0, 'msg' => 'error , access denied !'];
        }
        return self::_uploadWrapper($config, function ($object, $filePath) use ($config) {
            // 按需加载AWS SDK，避免在不使用AWS上传时加载，提高性能。
            require_once "phar://" . EXTEND_PATH . "aws-sdk/aws.phar.gz/aws-autoloader.php";
            // 创建AWS SDK实例
            $sdk = new \Aws\Sdk([
                'region' => $config['region'] ?: 'automatic',
                'version' => 'latest',
                'endpoint' => $config['endpoint'],
                'credentials' => [
                    'key' => $config['key'],
                    'secret' => $config['secret'],
                ],
                'forcePathStyle' => (bool)(isset($config['force_path_style']) ? $config['force_path_style'] : 0),
            ]);
            $s3Client = $sdk->createS3();
            $s3Result = $s3Client->putObject([
                'Bucket' => $config['bucket'],
                'Key' => $object,
                'Body' => fopen($filePath, 'rb'),
                'ACL' => 'public-read' // 设置文件为公开可读
            ]);
            if ($s3Result['@metadata']['statusCode'] != 200) {
                // 如果S3返回的状态码不是200，则抛出异常
                throw new \Exception('AWS S3 upload failed with status code ' . $s3Result['@metadata']['statusCode']);
            }
            // 成功时，额外返回S3的ETag
            return ['etag' => isset($s3Result['ETag']) ? $s3Result['ETag'] : 'unknown'];
        });
    }

    /**
     * 123盘上传
     * @param array $config 上传配置，需包含client_id, client_secret等
     * @return array 上传结果
     */
    private static function pan123Upload($config)
    {
        return self::_uploadWrapper($config, function ($object, $filePath) use ($config) {
            // 按需加载 123pan SDK
            require_once EXTEND_PATH . '123pan-sdk/Upload123Pan.php';
            // 从配置中获取凭证
            $clientID = $config['client_id'];
            $clientSecret = $config['client_secret'];
            // 实例化上传 SDK
            $uploader = new \Upload123Pan($clientID, $clientSecret);
            // 获取 Access Token 缓存
            $cacheName = "123pan_accessToken_" . md5($clientID);
            $accessToken = cache($cacheName);
            // 判断 Access Token 是否有效
            if (empty($accessToken) || (isset($accessToken['expiredAt']) && strtotime($accessToken['expiredAt']) - 60 < time())) {
                // 获取 Access Token
                $tokenData = $uploader->getAccessToken();
                // 设置 Access Token
                $accessToken = [
                    'token' => $tokenData['accessToken'],
                    'expiredAt' => $tokenData['expiredAt'],
                ];
                // 设置 Access Token
                $uploader->setAccessToken($accessToken['token'], $accessToken['expiredAt']);
                // 计算缓存有效期
                $expireInSeconds = strtotime($accessToken['expiredAt']) - time() - 60;
                // 如果缓存有效，则设置 Access Token
                if ($expireInSeconds > 0) {
                    // 如果缓存有效，则设置 Access Token
                    cache($cacheName, $accessToken, $expireInSeconds);
                }
            } else {
                // 如果缓存有效，则设置 Access Token
                $uploader->setAccessToken($accessToken['token'], $accessToken['expiredAt']);
            }
            // 调用 SDK 上传文件
            $fileID = $uploader->uploadFile(
                $filePath,
                0,
                $object,
                ['containDir' => true]
            );
            // 获取文件直链, 包含错误恢复和自动开启直链空间逻辑
            try {
                $directLink = $uploader->getDirectLink($fileID);
            } catch (\Exception $e) {
                if (strpos($e->getMessage(), '目录未开启直链空间') !== false) {
                    $currentFileID = $fileID;
                    while (true) {
                        $details = $uploader->getFileDetail($currentFileID);
                        $parentFileID = $details['parentFileID'];
                        if ($parentFileID == 0) {
                            $topLevelDirID = $details['fileID'];
                            break;
                        }
                        $currentFileID = $parentFileID;
                    }
                    $uploader->enableDirectLinkSpace($topLevelDirID);
                    $directLink = $uploader->getDirectLink($fileID);
                } else {
                    throw $e;
                }
            }
            // 返回包含直链的数组，以便在 wrapper 中覆盖默认 URL
            return ['url' => $directLink];
        });
    }

    /**
     * 生成标准化的对象存储路径
     * 路径格式为 "Y/m/d/uuid.extension"
     * @param string $extension 不带点的文件扩展名
     * @return array 包含'path'和'title'的数组，'path'是完整对象路径，'title'是带扩展名的文件名
     */
    private static function _generateObjectPath($extension)
    {
        $newly = self::uuid() . '.' . $extension;
        $objectPath = date('Y/m/d') . '/' . $newly;
        return ['path' => $objectPath, 'title' => $newly];
    }

    /**
     * 通用上传包装器
     * 封装了路径生成、异常捕获、临时文件清理和结果格式化等通用逻辑。
     * 使用此方法可以极大地简化各个上传方法的实现。
     * @param array $config 上传配置数组，必须包含'path', 'extend', 'far_url'等键
     * @param callable $uploadCallback 闭包函数，用于执行特定服务的上传操作。该函数接收$object和$filePath两个参数。
     * @return array 上传结果数组
     */
    private static function _uploadWrapper(array $config, callable $uploadCallback)
    {
        $filePath = $config['path'];
        // 前置检查：确保本地文件存在
        if (!file_exists($filePath)) {
            return ['code' => 0, 'msg' => 'Error: Local file does not exist - ' . $filePath];
        }
        try {
            // 1. 生成远程文件路径
            $pathInfo = self::_generateObjectPath($config['extend']);
            $object = $pathInfo['path'];
            $newly = $pathInfo['title'];
            // 2. 执行由具体上传方法传入的回调函数，进行实际的上传操作
            $extraData = call_user_func($uploadCallback, $object, $filePath);
            // 3. 准备标准化的成功返回结果
            $result = [
                'code' => 1,
                'status' => 'success',
                'url' => rtrim($config['far_url'], '/') . '/' . $object,
                'title' => $newly
            ];
            // 4. 如果回调函数返回了额外数据（如S3的ETag），则合并到结果中
            if (is_array($extraData) && !empty($extraData)) {
                $result = array_merge($result, $extraData);
            }
            return $result;
        } catch (\Exception $e) {
            // 捕获上传过程中任何环节的异常
            Log::write('Upload error: ' . $e->getMessage(), 'log', true);
            // 返回标准化的错误信息
            return ['code' => 0, 'msg' => 'error , ' . $e->getMessage()];
        } finally {
            // 5. 无论上传成功还是失败，最终都会执行此处的代码
            // 确保本地临时文件总是被尝试删除，防止文件残留
            if (isset($filePath) && file_exists($filePath)) {
                @unlink($filePath);
            }
        }
    }

    /**
     * 生成一个基于MD5的伪UUID，用于创建唯一的文件名
     * @return string 返回32位的MD5哈希字符串
     */
    public static function uuid()
    {
        // 使用md5对随机生成的唯一ID进行哈希处理
        $str = md5(uniqid(mt_rand(), true));
        // 格式化UUID的第一部分，取哈希的前8个字符
        $uuid = substr($str, 0, 8) . '-';
        // 添加UUID的第二部分，取哈希的第9-12个字符
        $uuid .= substr($str, 8, 4) . '-';
        // 添加UUID的第三部分，取哈希的第13-16个字符
        $uuid .= substr($str, 12, 4) . '-';
        // 添加UUID的第四部分，取哈希的第17-20个字符
        $uuid .= substr($str, 16, 4) . '-';
        // 添加UUID的第五部分，取哈希的第21-32个字符
        $uuid .= substr($str, 20, 12);
        // 将UUID与随机数和当前时间戳组合并进行MD5哈希，进一步增强唯一性
        return md5($uuid . uniqid(mt_rand(), true) . time());
    }
}
