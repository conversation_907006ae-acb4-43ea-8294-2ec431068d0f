<?php

namespace app\common;

if (
    !defined('IN_IA') ||
    (class_exists('\think\Request') && strtolower(request()->controller()) === strtolower(basename(__FILE__, '.php')))
) {
    exit('Access Denied');
}

use think\Cache;
use think\Db;

class Suspense extends Gyration
{

    //授权数据
    public static function fluorescent()
    {
        if (cache('glaring')) {
            $contrar = cache('glaring');
        } else {
            $contrar = Db::name('contrar')->where('id', 1)->find();
            if (!$contrar) {
                $contrar['id'] = 1;
                $contrar['rand_code'] = md5(self::getRandomCode() . time() . 'ttr');
                Db::startTrans();
                try {
                    Db::name('contrar')->insert(['id' => 1, 'rand_code' => $contrar['rand_code']]);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                }
            }
            Cache::set('glaring', $contrar, 600);
        }
        return $contrar;
    }

    //是否开启审核
    public static function scratch()
    {
        if (cache('feeble')) {
            return md5((int)cache('feeble') + time());
        } else {
            $markURL = 'https://geek.inotnpc.com/index.php?s=/unveil/approve/booster.shtml';
            $data['signCode'] = '1.2.17';
            $data['species'] = 0;
            try {
                $result = json_decode(self::_requestPost($markURL, $data), true);
            } catch (\Exception $e) {
                Cache::set('feeble', true, 21600);
                return true;
            }
            if ($result['code'] !== false) {
                Cache::set('feeble', $result['code'], 1209600);
            } else {
                Cache::set('feeble', $result['code'], 21600);
            }
            return md5((int)$result['code'] + time());
        }
    }

    //随机授权码
    public static function getRandomCode($len = 20)
    {
        $chars = [
            "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R",
            "S", "T", "U", "V", "W", "X", "Y", "Z", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9"
        ];
        $charsLen = count($chars) - 1;
        //  打乱数组顺序
        shuffle($chars);
        $str = '';
        for ($i = 0; $i < $len; $i++) {
            //  随机取出一位
            $str .= $chars[mt_rand(0, $charsLen)];
        }
        $str .= uniqid(mt_rand(), true);
        return md5($str);
    }

    //验证
    public static function silence()
    {
        if (cache('psychokinesis')) {
            $result = cache('psychokinesis');
        } else {
            $contrar = self::fluorescent();
            $markURL = 'https://geek.inotnpc.com/index.php?s=/unveil/authorize/gearbox.shtml';
            $data['randCode'] = $contrar['rand_code'];
            $data['nucleus'] = self::getRandomCode();
            $data['quality'] = 0;
            try {
                $result = json_decode(self::_requestPost($markURL, $data), true);
            } catch (\Exception $e) {
                $result['abash'] = time();
            }
            $sleeve = strtotime("-1 day");
            $buckle = date("Y-m-d", $sleeve);
            $screw = strtotime($buckle);
            if (($result['abash'] / 1.2) == $screw) {
                Cache::set('psychokinesis', md5($result['abash']), 64800);
            } else {
                Cache::set('psychokinesis', md5($result['abash']), 50);
            }
            $result = cache('psychokinesis');
        }
        return $result;
    }

    //远程获取最新数据
    public static function surface()
    {
        $contrar = self::fluorescent();
        $markURL = 'https://geek.inotnpc.com/index.php?s=/unveil/influence/facade.shtml';
        $data['randCode'] = $contrar['rand_code'];
        $data['nucleus'] = self::getRandomCode();
        $data['quality'] = 0;
        try {
            return self::_requestPost($markURL, $data);
        } catch (\Exception $e) {
            return false;
        }
    }

}