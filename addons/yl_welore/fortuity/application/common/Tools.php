<?php

namespace app\common;

if (
    !defined('IN_IA') ||
    (class_exists('\think\Request') && strtolower(request()->controller()) === strtolower(basename(__FILE__, '.php')))
) {
    exit('Access Denied');
}

class Tools
{
    /**
     * 驼峰/下划线 互转
     * @param array $params 待转换数组
     * @param integer $type 默认 0: 驼峰 -》下划线；1：下划线 -》驼峰
     * @return array
     */
    public static function HumpUnderlineConversion($params, $type = 0)
    {
        $newArr = [];
        if (!is_array($params) || empty($params)) return $newArr;
        foreach ($params as $key => $val) {
            if ($type == 1) {
                $newKey = preg_replace_callback('/([-_]+([a-z]{1}))/i', function ($matches) {
                    return strtoupper($matches[2]);
                }, $key);
            } else {
                $newKey = $key;
                if (!strstr($key, '_')) {
                    $key = str_replace("_", "", $key);
                    $key = preg_replace_callback('/([A-Z]{1})/', function ($matches) {
                        return '_' . strtolower($matches[0]);
                    }, $key);
                    $newKey = ltrim($key, "_");
                }
            }
            $newArr[$newKey] = is_array($val) ? self::HumpUnderlineConversion($val, $type) : $val;
        }
        return $newArr;
    }

    /*
     * 安全过滤
     */
    public static function safeHtml($data)
    {
        // 快速返回空数据
        if (empty($data)) {
            return $data;
        }

        // 如果不包含HTML，快速处理
        if (strpos($data, '<') === false && strpos($data, '>') === false) {
            return emoji_encode($data);
        }

        // 使用strtr替代多次str_replace
        $data = strtr($data, [
            '&amp;' => '&amp;amp;',
            '&lt;' => '&amp;lt;',
            '&gt;' => '&amp;gt;'
        ]);

        // 合并类似的正则操作
        $data = preg_replace('/(&#*\w+)[\x00-\x20]+;/u', '$1;', $data);
        $data = preg_replace('/(&#x*[0-9A-F]+);*/iu', '$1;', $data);
        $data = html_entity_decode($data, ENT_COMPAT, 'UTF-8');

        // 移除危险属性
        $data = preg_replace('#(<[^>]+?[\x00-\x20"\'])(?:on|xmlns)[^>]*+>#iu', '$1>', $data);

        // 协议替换 - 可提取为单独函数提高可读性
        $protocolPatterns = [
            '#([a-z]*)[\x00-\x20]*=[\x00-\x20]*([`\'"]*)[\x00-\x20]*j[\x00-\x20]*a[\x00-\x20]*v[\x00-\x20]*a[\x00-\x20]*s[\x00-\x20]*c[\x00-\x20]*r[\x00-\x20]*i[\x00-\x20]*p[\x00-\x20]*t[\x00-\x20]*:#iu' => '$1=$2nojavascript...',
            '#([a-z]*)[\x00-\x20]*=([\'"]*)[\x00-\x20]*v[\x00-\x20]*b[\x00-\x20]*s[\x00-\x20]*c[\x00-\x20]*r[\x00-\x20]*i[\x00-\x20]*p[\x00-\x20]*t[\x00-\x20]*:#iu' => '$1=$2novbscript...',
            '#([a-z]*)[\x00-\x20]*=([\'"]*)[\x00-\x20]*-moz-binding[\x00-\x20]*:#u' => '$1=$2nomozbinding...'
        ];

        foreach ($protocolPatterns as $pattern => $replacement) {
            $data = preg_replace($pattern, $replacement, $data);
        }

        // 安全过滤CSS样式属性，而不是完全移除
        $data = preg_replace_callback('#(<[^>]+?)style\s*=\s*(["\'])(.*?)\2#is', function ($matches) {
            // $matches[1] 是标签开始部分
            // $matches[2] 是引号类型
            // $matches[3] 是样式内容

            $style = $matches[3];

            // 过滤危险的CSS表达式
            $style = preg_replace('/expression\s*\(.*?\)/is', '', $style);
            $style = preg_replace('/behavior\s*:.*?/is', '', $style);
            $style = preg_replace('/-moz-binding\s*:.*?/is', '', $style);

            // 过滤可能包含JavaScript的URL
            $style = preg_replace('/url\s*\(\s*(["\']*)javascript:.*?\1\s*\)/is', 'url(blocked)', $style);

            // 过滤其他危险的CSS属性
            $style = preg_replace('/(document|window|eval|setTimeout|setInterval|alert|confirm|prompt)\s*(\.|:)/is', '', $style);

            // 如果没有样式内容，返回标签开始部分
            if (empty(trim($style))) {
                return $matches[1];
            }

            // 返回安全过滤后的样式
            return $matches[1] . 'style=' . $matches[2] . $style . $matches[2];
        }, $data);

        // Remove namespaced elements (we do not need them)
        $data = preg_replace('#</*\w+:\w[^>]*+>#i', '', $data);

        // 修改过滤规则，确保iframe被过滤
        $dangerousTags = 'applet|b(?:ase|gsound|link)|embed|frame(?:set)?|i(?:frame|layer)|l(?:ayer|ink)|meta|object|s(?:cript|tyle)|title|xml';

        // Remove really unwanted tags, 包括iframe但保留其他媒体标签
        do {
            $old_data = $data;
            $data = preg_replace('#</*(?:' . $dangerousTags . ')[^>]*+>#i', '', $data);
        } while ($old_data !== $data);

        // 额外过滤可能隐藏的js代码，但保留视频和图片链接
        $data = preg_replace('/<([^>]*)(?:data|href)\s*=\s*(["\']?)javascript:.*?\2([^>]*)>/i', '<$1$3>', $data);

        // 修改：不再移除空的src和href属性，而是确保它们不包含javascript
        $data = preg_replace('/<([^>]*)(?:src|href)\s*=\s*(["\']?)javascript:.*?\2([^>]*)>/i', '<$1$3>', $data);

        // 视频标签安全处理 - 允许video标签但过滤危险属性
        $data = preg_replace_callback('#<(video)([^>]*?)>(.*?)</video>#is', function ($matches) {
            $tag = $matches[1];
            $attributes = $matches[2];
            $content = $matches[3];

            // 过滤危险属性
            $cleanAttributes = preg_replace('/\s(on\w+|javascript:|data:|vbscript:)\s*=\s*(["\']).*?\2/i', '', $attributes);

            return "<{$tag}{$cleanAttributes}>{$content}</{$tag}>";
        }, $data);

        // 对img标签进行特别处理，确保src属性得到保留
        $data = preg_replace_callback('#<(img)([^>]*?)/?>#is', function ($matches) {
            $tag = $matches[1];
            $attributes = $matches[2];

            // 过滤危险属性，但保留src
            $cleanAttributes = preg_replace('/\s(on\w+|javascript:|data:script|vbscript:)\s*=\s*(["\']).*?\2/i', '', $attributes);

            // 允许data:image的情况（常见于base64编码的图片）
            if (preg_match('/\ssrc\s*=\s*(["\'])(data:image\/[^;]+;base64,)[^"\']+\1/i', $cleanAttributes)) {
                return "<{$tag}{$cleanAttributes}/>";
            }

            // 其他图片保持不变
            return "<{$tag}{$cleanAttributes}/>";
        }, $data);

        // 移除注释中可能隐藏的代码
        $data = preg_replace('/<!--.*?-->/s', '', $data);

        // we are done...
        return emoji_encode($data);
    }
}