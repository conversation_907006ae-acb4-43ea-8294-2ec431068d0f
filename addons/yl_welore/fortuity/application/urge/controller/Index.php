<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\common\Playful;
use app\common\PrivateCache;
use app\common\Remotely;
use app\common\Suspense;
use think\Cache;
use think\Db;
use think\exception\HttpResponseException;
use think\View;

#后台首页
class Index extends Base
{


    //  首页
    public function index()
    {
        //  版本号
        $this->issue();

        //  今日新增用户
        $new_user_today = cache('new_user_today_' . $this->much_id);
        if (!$new_user_today) {
            $new_user_today = Db::name('user')->where('tourist', 0)->where('uvirtual', 0)->where('much_id', $this->much_id)->whereTime('user_reg_time', 'today')->count();
            Cache::set('new_user_today_' . $this->much_id, $new_user_today, 90);
        }
        $this->assign('new_user_today', $new_user_today);

        //  今日申请创建圈子数量
        $new_toryon_today = cache('new_toryon_today_' . $this->much_id);
        if (!$new_toryon_today) {
            $new_toryon_today = Db::name('territory_petition')->where('much_id', $this->much_id)->whereTime('found_lasting', 'today')->count();
            Cache::set('new_toryon_today_' . $this->much_id, $new_toryon_today, 90);
        }
        $this->assign('new_toryon_today', $new_toryon_today);

        //  今日发帖数量
        $new_paper_today = cache('new_paper_today_' . $this->much_id);
        if (!$new_paper_today) {
            $new_paper_today = Db::name('paper')->where('much_id', $this->much_id)->where('whether_delete', 0)->whereTime('adapter_time', 'today')->count();
            Cache::set('new_paper_today_' . $this->much_id, $new_paper_today, 90);
        }
        $this->assign('new_paper_today', $new_paper_today);

        //  今日签到人数
        $new_punch_today = cache('new_punch_today_' . $this->much_id);
        if (!$new_punch_today) {
            $new_punch_today = Db::name('user_punch')->where('much_id', $this->much_id)->whereTime('punch_time', 'today')->count();
            Cache::set('new_punch_today_' . $this->much_id, $new_punch_today, 90);
        }
        $this->assign('new_punch_today', $new_punch_today);

        //  今日商品订单
        $new_sorder_today = cache('new_sorder_today_' . $this->much_id);
        if (!$new_sorder_today) {
            $new_sorder_today = Db::name('shop_order')
                ->where(function ($query) {
                    $query->where(function ($childrenQueryOne) {
                        $childrenQueryOne->where('pay_type', '<>', 2)->where('pay_status', '>=', 0);
                    })->whereOr(function ($childrenQueryTwo) {
                        $childrenQueryTwo->where('pay_type', '=', 2)->where('pay_status', '=', 2);
                    });
                })
                ->where('much_id', $this->much_id)
                ->whereTime('buy_time', 'today')
                ->count();
            Cache::set('new_sorder_today_' . $this->much_id, $new_sorder_today, 90);
        }
        $this->assign('new_sorder_today', $new_sorder_today);

        //  今日充值收益
        $new_userial_today = cache('new_userial_today_' . $this->much_id);
        if (!$new_userial_today) {
            $new_userial_today = Db::name('user_serial')->where('much_id', $this->much_id)->where('status', 1)->whereTime('add_time', 'today')->sum('pay_money');
            Cache::set('new_userial_today_' . $this->much_id, $new_userial_today, 90);
        }
        $this->assign('new_userial_today', $new_userial_today);

        //  今日送礼总金额 ( 税后 )
        $new_subsicont_today = cache('new_subsicont_today_' . $this->much_id);
        if (!$new_subsicont_today) {
            $new_subsicont_today = Db::name('user_subsidy')->where('much_id', $this->much_id)->whereTime('bute_time', 'today')->sum('bute_price');
            Cache::set('new_subsicont_today_' . $this->much_id, $new_subsicont_today, 90);
        }
        $this->assign('new_subsicont_today', $new_subsicont_today);

        //  今日礼物扣除手续费
        $new_subsidy_today = cache('new_subsidy_today_' . $this->much_id);
        if (!$new_subsidy_today) {
            $new_subsidy_today = Db::name('user_subsidy')->where('much_id', $this->much_id)->whereTime('bute_time', 'today')->field('sum(bute_price * (1 - allow_scale)) as deduction')->find();
            Cache::set('new_subsidy_today_' . $this->much_id, $new_subsidy_today, 90);
        }
        $this->assign('new_subsidy_today', number_format($new_subsidy_today['deduction'], 2));

        //  本周发帖活跃用户信息
        $large_user = cache('large_user_' . $this->much_id);
        if (!$large_user) {
            $large_user = Db::name('paper')
                ->alias('per')
                ->join('user us', 'us.id=per.user_id', 'left')
                ->where('per.much_id', $this->much_id)
                ->where('per.whether_delete', 0)
                ->where('us.uvirtual', 0)
                ->whereTime('per.adapter_time', 'week')
                ->field('us.id as uid,us.user_nick_name, us.user_head_sculpture ,us.user_wechat_open_id , per.user_id, count(per.user_id) as hasty')
                ->group('per.user_id')
                ->order(['hasty' => 'desc', 'uid' => 'asc'])
                ->limit(6)
                ->select();
            Cache::set('large_user_' . $this->much_id, $large_user, 90);
        }
        //  统计本周发帖活跃用户的贴子数
        $large_total = 0;
        foreach ($large_user as $key => $value) {
            $large_total += (int)$large_user[$key]['hasty'];
        }
        foreach ($large_user as $key => $value) {
            $large_user[$key]['percentage'] = round((int)$large_user[$key]['hasty'] / $large_total / 0.01, 2);
        }
        $this->assign('large_total', $large_total);
        $this->assign('large_user', $large_user);
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        // ============================== 授权检测 Start ==============================
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, [], 'view'));
        }
        // ============================== 授权检测 End ==============================
        return $this->fetch();
    }

    //  用户统计
    public function userCount()
    {
        $data = cache('indexUserCount_' . $this->much_id);
        if (!$data) {
            $sigma = date("t");
            $first_day = date("Y-m", time());
            for ($i = 1; $i <= $sigma; $i++) {
                $data[] = Db::name('user')->where('tourist', 0)->where('uvirtual', 0)->where('much_id', $this->much_id)->whereTime('user_reg_time', 'between', [$first_day . "-" . $i, $first_day . "-" . $i . " 23:59:59"])->count();
            }
            Cache::set('indexUserCount_' . $this->much_id, $data, 90);
        }
        return $data;
    }


    //  小程序版本号
    private function issue()
    {
        $version = Db::name('version')->where('sign_code', $this->sign_code)->where('much_id', $this->much_id)->find();
        if (!$version) {
            Db::name('version')->insert(['sign_code' => $this->sign_code, 'status' => 0, 'much_id' => $this->much_id]);
            PrivateCache::clearProjectCache();
        }
        $versionOverdue = Db::name('version_overdue')->where('sign_code', $this->sign_code)->where('much_id', $this->much_id)->find();
        if (!$versionOverdue) {
            $mostAboveTime = 0;
            $versionMax = Db::name('version_overdue')->where('much_id', $this->much_id)->order(['id' => 'desc'])->find();
            if ($versionMax) {
                $mostAboveTime = $versionMax['most_above_time'];
            }
            Db::name('version_overdue')->insert(['sign_code' => $this->sign_code, 'update_time' => time(), 'most_above_time' => $mostAboveTime, 'much_id' => $this->much_id]);
        }
    }

    /*
     * 通知网页跳转
     */
    private function webPageTurn($capriole, $tyid)
    {
        switch ($capriole) {
            case 1:
                //  申请圈主 申请管理
                $url = url('compass/dominator') . '&tyid=' . $tyid;
                break;
            case 2:
                //  申请关注
                $url = url('compass/savour') . '&hazy_bering=' . $tyid;
                break;
            case 3:
                //  圈子审核
                $url = url('compass/solicit');
                break;
            case 4:
                //  帖子审核
                $url = url('essay/index');
                break;
            case 5:
                //  回复审核
                $url = url('essay/reply');
                break;
            case 6:
                //  帖子举报
                $url = url('journal/report');
                break;
            case 7:
                //  帖子申诉
                $url = url('journal/appeal');
                break;
            case 8:
                //  投诉圈子
                $url = url('journal/spread');
                break;
            case 9:
                //  投诉管理
                $url = url('journal/safety');
                break;
            case 10:
                //  投诉用户
                $url = url('journal/usmur');
                break;
            case 11:
                //  购买商品 申请退款
                $url = url('marketing/sorder');
                break;
            case 12:
                //  提现通知
                $url = url('rawls/stand');
                break;
            case 13:
                //  抽奖中奖通知
                $url = url('unlawful/cheer') . '&erid=' . $tyid;
                break;
            case 14:
                //  小秘密审核
                $url = url('stealth/softly');
                break;
            case 15:
                //  小秘密回复审核
                $url = url('stealth/janitor');
                break;
            case 16:
                //   认证列表审核
                $url = url('depend/acquire');
                break;
        }
        return $url;
    }

    //  系统提醒
    public function awake()
    {
        if (request()->isPost() && request()->isAjax()) {
            $uetype = request()->post('uetype');
            if ($uetype == 0) {
                Db::startTrans();
                try {
                    Db::name('prompt_msg')->where('type', 0)->where('much_id', $this->much_id)->update(['status' => 1]);
                    $notices = Db::name('prompt_msg')->where('status', 0)->where('type', 0)->where('much_id', $this->much_id)->count('*');
                    cache('notices_' . $this->much_id, $notices);
                    $vacants = Db::name('prompt_msg')->where('status', 0)->where('type', 1)->where('much_id', $this->much_id)->count('*');
                    cache('vacants_' . $this->much_id, $vacants);
                    Db::name('prompt_count')->where('much_id', $this->much_id)->cache('preCount_' . $this->much_id)->update(['barg' => ($notices + $vacants)]);
                    $result = true;
                    Db::commit();
                } catch (\Exception $e) {
                    $result = false;
                    Db::rollback();
                }
                if ($result !== false) {
                    return json(['code' => 1, 'msg' => '全部标为已读成功']);
                } else {
                    return json(['code' => 0, 'msg' => '全部标为已读失败']);
                }
            } else {
                $usid = request()->post('usid');
                Db::startTrans();
                try {
                    if ($uetype == 1) {
                        Db::name('prompt_msg')->where('id', $usid)->where('type', 0)->where('much_id', $this->much_id)->update(['status' => 1]);
                    } elseif ($uetype == 2) {
                        Db::name('prompt_msg')->where('id', $usid)->where('type', 0)->where('much_id', $this->much_id)->delete();
                    }
                    $notices = Db::name('prompt_msg')->where('status', 0)->where('type', 0)->where('much_id', $this->much_id)->count('*');
                    cache('notices_' . $this->much_id, $notices);
                    $vacants = Db::name('prompt_msg')->where('status', 0)->where('type', 1)->where('much_id', $this->much_id)->count('*');
                    cache('vacants_' . $this->much_id, $vacants);
                    Db::name('prompt_count')->where('much_id', $this->much_id)->cache('preCount_' . $this->much_id)->update(['barg' => ($notices + $vacants)]);
                    $result = true;
                    Db::commit();
                } catch (\Exception $e) {
                    $result = false;
                    Db::rollback();
                }
                if ($result !== false) {
                    return json(['code' => 1, 'msg' => $uetype == 1 ? '标为已读成功' : '删除提醒成功']);
                } else {
                    return json(['code' => 0, 'msg' => $uetype == 1 ? '标为已读失败' : '删除提醒失败']);
                }
            }
        }
        $webPageTurn = function ($capriole, $tyid) {
            return $this->webPageTurn($capriole, $tyid);
        };
        $this->assign('webPageTurn', $webPageTurn);
        $list = Db::name('prompt_msg')->where('type', 0)->where('much_id', $this->much_id)->order('msg_time', 'desc')->paginate(10, false, ['query' => ['s' => $this->defaultQuery()]]);
        $this->assign('list', $list);
        return $this->fetch();
    }

    //  系统消息
    public function message()
    {
        if (request()->isPost() && request()->isAjax()) {
            $uetype = request()->post('uetype');
            if ($uetype == 0) {
                Db::startTrans();
                try {
                    Db::name('prompt_msg')->where('type', 1)->where('much_id', $this->much_id)->update(['status' => 1]);
                    $notices = Db::name('prompt_msg')->where('status', 0)->where('type', 0)->where('much_id', $this->much_id)->count('*');
                    cache('notices_' . $this->much_id, $notices);
                    $vacants = Db::name('prompt_msg')->where('status', 0)->where('type', 1)->where('much_id', $this->much_id)->count('*');
                    cache('vacants_' . $this->much_id, $vacants);
                    Db::name('prompt_count')->where('much_id', $this->much_id)->cache('preCount_' . $this->much_id)->update(['barg' => ($notices + $vacants)]);
                    $result = true;
                    Db::commit();
                } catch (\Exception $e) {
                    $result = false;
                    Db::rollback();
                }
                if ($result !== false) {
                    return json(['code' => 1, 'msg' => '全部标为已读成功']);
                } else {
                    return json(['code' => 0, 'msg' => '全部标为已读失败']);
                }
            } else {
                $usid = request()->post('usid');
                Db::startTrans();
                try {
                    if ($uetype == 1) {
                        Db::name('prompt_msg')->where('id', $usid)->where('type', 1)->where('much_id', $this->much_id)->update(['status' => 1]);
                    } elseif ($uetype == 2) {
                        Db::name('prompt_msg')->where('id', $usid)->where('type', 1)->where('much_id', $this->much_id)->delete();
                    }
                    $notices = Db::name('prompt_msg')->where('status', 0)->where('type', 0)->where('much_id', $this->much_id)->count('*');
                    cache('notices_' . $this->much_id, $notices);
                    $vacants = Db::name('prompt_msg')->where('status', 0)->where('type', 1)->where('much_id', $this->much_id)->count('*');
                    cache('vacants_' . $this->much_id, $vacants);
                    Db::name('prompt_count')->where('much_id', $this->much_id)->cache('preCount_' . $this->much_id)->update(['barg' => ($notices + $vacants)]);
                    $result = true;
                    Db::commit();
                } catch (\Exception $e) {
                    $result = false;
                    Db::rollback();
                }
                if ($result !== false) {
                    return json(['code' => 1, 'msg' => $uetype == 1 ? '标为已读成功' : '删除消息成功']);
                } else {
                    return json(['code' => 0, 'msg' => $uetype == 1 ? '标为已读失败' : '删除消息失败']);
                }
            }
        }
        $webPageTurn = function ($capriole, $tyid) {
            return $this->webPageTurn($capriole, $tyid);
        };
        $this->assign('webPageTurn', $webPageTurn);
        $list = Db::name('prompt_msg')->where('type', 1)->where('much_id', $this->much_id)->order('msg_time', 'desc')->paginate(10, false, ['query' => ['s' => $this->defaultQuery()]]);
        $this->assign('list', $list);
        return $this->fetch();
    }

    //  修复缺失字段
    public function repairMissing()
    {
        if ($this->M['role'] == 'founder' && request()->isPost() && request()->isAjax()) {
            $renovate = Suspense::surface();
            $ultimate = json_decode($renovate, true);
            if (intval($ultimate['code']) !== 0) {
                if (trim($ultimate['data']) !== '') {
                    $ultimateSQL = explode(';', $ultimate['data']);
                    foreach ($ultimateSQL as $exhaust) {
                        $grotesque = function ($sql) {
                            Db::startTrans();
                            try {
                                Db::execute($sql);
                                Db::commit();
                            } catch (\Exception $e) {
                                Db::rollback();
                            }
                        };
                        $grotesque("{$exhaust};");
                    }
                }
                $this->purgeCache();
                return true;
            } else {
                return false;
            }
        } else {
            $this->redirect('index/index');
        }
    }

    //  返回系统
    public function logout()
    {
        $this->M = null;
        $this->much = null;
        $this->much_id = null;

        //    销毁cookie
        $cookies = ['yl_welore_session_token' => ''];
        foreach ($cookies as $name => $value) {
            $cookieHeader = "{$name}={$value}; Path=/; SameSite=None; Secure; HttpOnly; Expires=" . gmdate('D, d M Y H:i:s \G\M\T', time() - 3600);
            header("Set-Cookie: {$cookieHeader}", false);
        }

        $keySymbol = "addons/yl_welore/web/index.php";
        $pathLeft = explode($keySymbol, $_SERVER['SCRIPT_NAME']);
        $pathRight = 'web/#/index/home';
        //    判断是否为第三方版本
        if ($this->accessType === 1) {
            $pathRight = 'web/index.php?c=home&a=welcome&do=mc';
        }
        $absPath = $pathLeft[0] . $pathRight;
        $this->redirect($absPath);
    }

    //  清空缓存
    public function purgeCache()
    {
        PrivateCache::clearProjectCache();
        return 1;
    }

}
