<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\api\service\Alternative;
use app\common\Playful;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

#举报反馈
class Journal extends Base
{

    //帖子举报
    public function report()
    {
        if (request()->isGet() && request()->isAjax()) {
            $data = Db::name('paper_complaint')->where('id', request()->get('ksin'))->where('much_id', $this->much_id)->field('tale_content as content')->find();
            if ($data) {
                return json(emoji_decode(strip_tags($data['content'])));
            } else {
                return 'error';
            }
        }
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $where = [
            'us.user_nick_name' => ['like', "%$hazy_name%"],
            'pait.much_id' => $this->much_id
        ];
        $inquire = [
            's' => $url,
            'hazy_name' => $hazy_name
        ];
        if (!$hazy_name) {
            unset($where["us.user_nick_name"]);
            unset($inquire['hazy_name']);
        }

        $pritList = Db::name('paper_complaint')
            ->alias('pait')
            ->join('territory tory', 'pait.tory_id=tory.id', 'left')
            ->join('user us', 'pait.user_id=us.id', 'left')
            ->where($where)
            ->where(function ($query) {
                $query->where('pait.tale_type', 0)->whereOr('pait.tale_type', 1);
            })
            ->order('acceptance_status')
            ->order('transact_time', 'desc')
            ->order('petition_time')
            ->field('pait.*,us.user_nick_name,us.user_wechat_open_id,us.uvirtual,tory.realm_name')
            ->paginate(10, false, ['query' => $inquire])
            ->each(function ($item, $key) {
                if ($item['tale_type'] == 0) {
                    $item['satisfy'] = Db::name('paper')
                        ->alias('per')
                        ->join('user us', 'per.user_id=us.id', 'left')
                        ->where('per.id', $item['paper_id'])
                        ->where('per.much_id', $this->much_id)
                        ->field('per.*,us.user_nick_name,us.user_wechat_open_id,us.uvirtual')
                        ->find();
                } elseif ($item['tale_type'] == 1) {
                    $item['satisfy'] = Db::name('paper_reply')
                        ->alias('eply')
                        ->join('user us', 'eply.user_id=us.id', 'left')
                        ->where('eply.id', $item['prely_id'])
                        ->where('eply.much_id', $this->much_id)
                        ->field('eply.*,us.user_nick_name,us.user_wechat_open_id,us.uvirtual')
                        ->find();
                }
                return $item;
            });
        $this->assign('list', $pritList);
        $page = request()->get('page', 1);
        $pageCount = $pritList->count();
        if ($pageCount < 1 && $page != 1) {
            $this->emptyDataRedirect(['hazy_name' => $hazy_name]);
        }
        $this->assign('page', $page);
        return $this->fetch();
    }

    //删除帖子举报
    public function deljuper()
    {
        if (request()->isPost() && request()->isAjax()) {
            $rid = request()->post('rid');
            Db::startTrans();
            try {
                Db::name('paper_complaint')->where('id', $rid)->where('much_id', $this->much_id)->delete();
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result == true) {
                return json(['code' => 1, 'msg' => '删除成功']);
            }
        } else {
            $this->error('参数错误', 'journal/report');
        }
    }

    //举报处理
    public function adjudic()
    {
        if (request()->isPost() && request()->isAjax()) {
            $sngid = request()->post('sngid');
            $setale = request()->post('setale');
            $nstruct = request()->post('nstruct');
            $sfell = request()->post('sfell');
            $nvgid = request()->post('nvgid');
            $nvame = $setale == 0 ? 'paper' : 'paper_reply';
            // 启动事务
            Db::startTrans();
            try {
                //帖子/回复信息
                $uname = Db::name($nvame)->where('id', $nvgid)->where('much_id', $this->much_id)->find();
                //回执消息
                if ($setale == 0) {
                    if ($uname['study_title']) {
                        $msguer = $uname['study_title'];
                    } else {
                        $msguer = '帖子 ' . subtext($uname['study_content'], 10) . ' ';
                    }
                } else {
                    $msguer = '回复 ' . subtext($uname['reply_content'], 10);
                }
                //举报信息
                $pemit = Db::name('paper_complaint')->where('id', $sngid)->where('much_id', $this->much_id)->find();
                if ($sfell == 1) {
                    Db::name($nvame)
                        ->where('id', $nvgid)
                        ->where('much_id', $this->much_id)
                        ->update([
                            'whether_type' => 1,
                            'whether_reason' => '用户举报删帖',
                            'whether_delete' => 1,
                            'whetd_time' => time(),
                            'token' => md5(time())
                        ]);
                    Db::name('user_smail')->insert([
                        'user_id' => $uname['user_id'],
                        'maring' => "您的 {$msguer} 被大量用户投诉，管理员已经进行删帖，请注意您的言行举止！",
                        'clue_time' => time(),
                        'status' => 0,
                        'much_id' => $this->much_id
                    ]);
                    if ($setale == 0) {
                        //举报帖子/回复字段
                        $factor = trim('paper_id');
                        //举报帖子/回复编号
                        $prelyId = trim($pemit['paper_id']);
                    } else {
                        //举报帖子/回复字段
                        $factor = trim('prely_id');
                        //举报帖子/回复编号
                        $prelyId = trim($pemit['prely_id']);
                    }
                    //多用户举报
                    $pulet = Db::name('paper_complaint')->where($factor, $prelyId)->where('much_id', $this->much_id)->select();
                    foreach ($pulet as $key => $value) {
                        Db::name('user_smail')->insert([
                            'user_id' => $value['user_id'],
                            'maring' => "您举报的 {$msguer} 已成功，管理员已经进行删帖，感谢您对平台做出的努力与贡献，谢谢！",
                            'clue_time' => time(),
                            'status' => 0,
                            'much_id' => $this->much_id
                        ]);
                    }
                    Db::name('paper_complaint')
                        ->where($factor, $prelyId)
                        ->where('much_id', $this->much_id)
                        ->update([
                            'acceptance_status' => 1,
                            'transact_time' => time(),
                            'tale_instruct' => $nstruct,
                            'is_strike' => $sfell
                        ]);
                } else {
                    Db::name('paper_complaint')
                        ->where('id', $sngid)
                        ->where('much_id', $this->much_id)
                        ->update([
                            'acceptance_status' => 1,
                            'transact_time' => time(),
                            'tale_instruct' => $nstruct,
                            'is_strike' => $sfell
                        ]);
                    Db::name('user_smail')->insert([
                        'user_id' => $pemit['user_id'],
                        'maring' => "很抱歉，您举报的 {$msguer} 失败，举报理由不成立！",
                        'clue_time' => time(),
                        'status' => 0,
                        'much_id' => $this->much_id
                    ]);
                }
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                $result = false;
                Db::rollback();
            }
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '处理成功']);
            } else {
                return json(['code' => 0, 'msg' => '处理失败']);
            }
        } else {
            $this->redirect('report');
        }
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, [], 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    //举报处理撤销
    public function rejudic()
    {
        if (request()->isPost() && request()->isAjax()) {
            $sngid = request()->post('sngid');
            $setale = request()->post('setale');
            $sfell = request()->post('sfell');
            $nvgid = request()->post('nvgid');
            $nvame = $setale == 0 ? 'paper' : 'paper_reply';
            // 启动事务
            Db::startTrans();
            try {
                if ($sfell == 1) {
                    Db::name($nvame)
                        ->where('id', $nvgid)
                        ->where('much_id', $this->much_id)
                        ->update([
                            'whether_type' => 0,
                            'whether_reason' => null,
                            'whether_delete' => 0,
                            'whetd_time' => null,
                            'token' => null
                        ]);
                }
                Db::name('paper_complaint')
                    ->where('id', $sngid)
                    ->where('much_id', $this->much_id)
                    ->update([
                        'acceptance_status' => 0,
                        'transact_time' => null,
                        'tale_instruct' => null,
                        'is_strike' => 0
                    ]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                $result = false;
                Db::rollback();
            }
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '处理成功']);
            } else {
                return json(['code' => 0, 'msg' => '处理失败']);
            }

        } else {
            $this->redirect('report');
        }
    }


    //申诉恢复
    public function appeal()
    {
        if (request()->isGet() && request()->isAjax()) {
            $data = Db::name('paper_complaint')
                ->where('id', request()->get('ksin'))
                ->where('much_id', $this->much_id)
                ->field('tale_content as content')
                ->find();
            if ($data) {
                return json(emoji_decode(strip_tags($data['content'])));
            } else {
                return 'error';
            }
        }
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $where = [
            'us.user_nick_name' => ['like', " % $hazy_name % "],
            'pait.much_id' => $this->much_id
        ];
        $inquire = [
            's' => $url,
            'hazy_name' => $hazy_name
        ];
        if (!$hazy_name) {
            unset($where["us.user_nick_name"]);
            unset($inquire['hazy_name']);
        }

        $pritList = Db::name('paper_complaint')
            ->alias('pait')
            ->join('territory tory', 'pait.tory_id=tory.id', 'left')
            ->join('user us', 'pait.user_id=us.id', 'left')
            ->where($where)
            ->where(function ($query) {
                $query->where('pait.tale_type', 2)->whereOr('pait.tale_type', 3);
            })
            ->order('acceptance_status')
            ->order('transact_time', 'desc')
            ->order('petition_time')
            ->field('pait.*,us.user_nick_name,tory.realm_name')
            ->paginate(10, false, ['query' => $inquire])
            ->each(function ($item, $key) {
                if ($item['tale_type'] == 2) {
                    $item['satisfy'] = Db::name('paper')
                        ->where('id', $item['paper_id'])
                        ->where('much_id', $this->much_id)
                        ->find();
                } elseif ($item['tale_type'] == 3) {
                    $item['satisfy'] = Db::name('paper_reply')
                        ->where('id', $item['prely_id'])
                        ->where('much_id', $this->much_id)
                        ->find();
                }
                return $item;
            });
        $this->assign('list', $pritList);
        $page = request()->get('page', 1);
        $pageCount = $pritList->count();
        if ($pageCount < 1 && $page != 1) {
            $this->emptyDataRedirect(['hazy_name' => $hazy_name]);
        }
        $this->assign('page', $page);
        return $this->fetch();
    }

    //删除帖子申诉
    public function delsuar()
    {
        if (request()->isPost() && request()->isAjax()) {
            $rid = request()->post('rid');
            Db::startTrans();
            try {
                Db::name('paper_complaint')
                    ->where('id', $rid)
                    ->where('much_id', $this->much_id)
                    ->delete();
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result == true) {
                return json(['code' => 1, 'msg' => '删除成功']);
            }
        } else {
            $this->error('参数错误', 'journal/report');
        }
    }


    //申诉恢复撤销处理
    public function rejupeal()
    {
        if (request()->isPost() && request()->isAjax()) {
            $sngid = request()->post('sngid');
            $setale = request()->post('setale');
            $nstruct = request()->post('nstruct');
            $sfell = request()->post('sfell');
            $nvgid = request()->post('nvgid');
            if ($setale == 2) {
                $nvame = 'paper';
            } else {
                $nvame = 'paper_reply';
            }
            // 启动事务
            Db::startTrans();
            try {
                if ($sfell == 0) {
                    $pult = Db::name($nvame)
                        ->where('id', $nvgid)
                        ->where('much_id', $this->much_id)
                        ->update([
                            'whether_type' => 0,
                            'whether_reason' => null,
                            'whether_delete' => 0,
                            'whetd_time' => null,
                            'token' => null
                        ]);
                    if ($pult === false) {
                        Db::rollback();
                        return json(['code' => 0, 'msg' => '处理失败']);
                    }
                    if ($setale == 2) {
                        $snvame = 'paper_id';
                        $setale = 0;
                    } else {
                        $snvame = 'prely_id';
                        $setale = 1;
                    }
                    $repall = Db::name('paper_complaint')
                        ->where($snvame, $nvgid)
                        ->where('tale_type', $setale)
                        ->whereNotIn('id', $sngid)
                        ->where('much_id', $this->much_id)
                        ->delete();
                    if ($repall === false) {
                        Db::rollback();
                        return json(['code' => 0, 'msg' => '处理失败']);
                    }
                }
                $result = Db::name('paper_complaint')
                    ->where('id', $sngid)
                    ->where('much_id', $this->much_id)
                    ->update([
                        'acceptance_status' => 1,
                        'transact_time' => time(),
                        'tale_instruct' => $nstruct,
                        'is_strike' => $sfell
                    ]);
                if ($result !== false) {
                    // 提交事务
                    Db::commit();
                    return json(['code' => 1, 'msg' => '处理成功']);
                } else {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => '处理失败']);
                }

            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                return json(['code' => 0, 'msg' => '处理失败']);
            }
        } else {
            $this->redirect('report');
        }
    }

    //圈子投诉
    public function spread()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $hazy_egon = request()->get('egon', 0);
        $where = [
            'tory.realm_name|us.user_nick_name' => ['like', "%$hazy_name%"],
            'la.status' => $hazy_egon == 0 ? null : ($hazy_egon - 1),
            'la.ment_type' => 0,
            'la.much_id' => $this->much_id,
        ];
        $inquire = [
            's' => $url,
            'egon' => $hazy_egon,
            'hazy_name' => $hazy_name
        ];
        if (!$hazy_name) {
            unset($where["tory.realm_name|us.user_nick_name"]);
            unset($inquire['hazy_name']);
        }
        if (!is_numeric($where["la.status"])) {
            unset($where["la.status"]);
            unset($inquire['egon']);
        }
        $list = Db::name('lament')
            ->alias('la')
            ->join('user us', 'la.proof_id=us.id', 'left')
            ->join('territory tory', 'la.tory_id=tory.id', 'left')
            ->where($where)
            ->field('la.*,tory.realm_name,us.user_nick_name')
            ->order('la.ment_time')
            ->paginate(10, false, ['query' => $inquire]);
        $this->assign('list', $list);
        $this->assign('egon', $hazy_egon);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //圈子管理投诉
    public function safety()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $hazy_egon = request()->get('egon', 0);
        $where = [
            'tory.realm_name|us.user_nick_name' => ['like', "%$hazy_name%"],
            'la.status' => $hazy_egon == 0 ? null : ($hazy_egon - 1),
            'la.ment_type' => 1,
            'la.much_id' => $this->much_id,
        ];
        $inquire = [
            's' => $url,
            'egon' => $hazy_egon,
            'hazy_name' => $hazy_name
        ];
        if (!$hazy_name) {
            unset($where["tory.realm_name|us.user_nick_name"]);
            unset($inquire['hazy_name']);
        }
        if (!is_numeric($where["la.status"])) {
            unset($where["la.status"]);
            unset($inquire['egon']);
        }
        $list = Db::name('lament')
            ->alias('la')
            ->join('user us', 'la.proof_id=us.id', 'left')
            ->join('territory tory', 'la.tory_id=tory.id', 'left')
            ->where($where)
            ->field('la.*,tory.realm_name,us.user_nick_name')
            ->order('la.ment_time')
            ->paginate(10, false, ['query' => $inquire])
            ->each(function ($item, $key) {
                $user = Db::name('user')->where('uvirtual', 0)->where('id', $item['user_id'])->find();
                $item['username'] = $user['user_nick_name'];
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('egon', $hazy_egon);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //用户投诉
    public function usmur()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $hazy_egon = request()->get('egon', 0);
        $where = [
            'us.user_nick_name' => ['like', "%$hazy_name%"],
            'la.status' => $hazy_egon == 0 ? null : ($hazy_egon - 1),
            'la.ment_type' => 2,
            'la.much_id' => $this->much_id,
        ];
        $inquire = [
            's' => $url,
            'egon' => $hazy_egon,
            'hazy_name' => $hazy_name
        ];
        if (!$hazy_name) {
            unset($where["us.user_nick_name"]);
            unset($inquire['hazy_name']);
        }
        if (!is_numeric($where["la.status"])) {
            unset($where["la.status"]);
            unset($inquire['egon']);
        }
        $list = Db::name('lament')
            ->alias('la')
            ->join('user us', 'la.proof_id=us.id', 'left')
            ->where($where)
            ->field('la.*,us.user_nick_name')
            ->order('la.ment_time')
            ->paginate(10, false, ['query' => $inquire])
            ->each(function ($item, $key) {
                $user = Db::name('user')->where('id', $item['user_id'])->find();
                $item['username'] = $user['user_nick_name'];
                $item['userType'] = $user['uvirtual'];
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('egon', $hazy_egon);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //提醒次数计算
    protected function sflock()
    {
        $preCount = Db::name('prompt_msg')->where('status', 0)->where('much_id', $this->much_id)->count('*');
        return $preCount;
    }

    //投诉已查看
    public function jaureak()
    {
        if (request()->isPost() && request()->isAjax()) {
            $suid = request()->post('suid');
            $mopid = request()->post('moid');
            Db::startTrans();
            try {
                Db::name('prompt_msg')
                    ->where('id', $mopid)
                    ->where('type', 1)
                    ->where('much_id', $this->much_id)
                    ->cache('vacants_' . $this->much_id)
                    ->update(['status' => 1]);
                $result = Db::name('lament')
                    ->where('id', $suid)
                    ->where('much_id', $this->much_id)
                    ->update(['status' => 1]);
                if ($result !== false) {
                    Db::commit();
                    $barg = $this->sflock();
                    Db::name('prompt_count')->where('much_id', $this->much_id)->cache('preCount_' . $this->much_id)->update(['barg' => $barg]);
                    return json(['code' => 1, 'msg' => '标记成功']);
                } else {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => '标记失败 ']);
                }
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => '标记失败 ']);
            }
        }
    }

    //投诉删除
    public function sprelint()
    {
        if (request()->isPost() && request()->isAjax()) {
            $suid = request()->post('suid');
            $mopid = request()->post('moid');
            Db::startTrans();
            try {
                Db::name('prompt_msg')
                    ->where('id', $mopid)
                    ->where('much_id', $this->much_id)
                    ->cache('vacants_' . $this->much_id)
                    ->delete();
                $result = Db::name('lament')
                    ->where('id', $suid)
                    ->where('much_id', $this->much_id)
                    ->delete();
                if ($result !== false) {
                    Db::commit();
                    $barg = $this->sflock();
                    Db::name('prompt_count')->where('much_id', $this->much_id)->cache('preCount_' . $this->much_id)->update(['barg' => $barg]);
                    return json(['code' => 1, 'msg' => '删除成功']);
                } else {
                    return json(['code' => 0, 'msg' => '删除失败 ']);
                }
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => '删除失败 ']);
            }
        }
    }

    //用户私信记录
    public function letterRecord()
    {
        $url = $this->defaultQuery();
        $hazy_name = trim(request()->get('hazy_name', ''));
        $hazy_name != '' && $hazy_name = emoji_encode($hazy_name);
        //发送用户
        $seUserId = input('get.rid');
        //接收用户
        $reUserId = input('get.uid');;
        //查询数据
        $list = Db::name('user_leave_word')
            ->where(function ($query) use ($seUserId, $reUserId) {
                $query->where('se_user_id', $seUserId)->whereOr('se_user_id', $reUserId);
            })
            ->where(function ($query) use ($seUserId, $reUserId) {
                $query->where('re_user_id', $seUserId)->whereOr('re_user_id', $reUserId);
            })
            ->where(function ($query) use ($hazy_name) {
                $query->where('le_content', 'like', "%{$hazy_name}%")->whereOr('le_content', 'like', "%\"title\":\"{$hazy_name}\"%");
            })
            ->where('much_id', $this->much_id)
            ->order(['id' => 'desc'])
            ->paginate(10, false, ['query' => ['s' => $url, 'rid' => $seUserId, 'uid' => $reUserId, 'hazy_name' => $hazy_name]])
            ->each(function ($item, $key) {
                $item['user_head_sculpture'] = Db::name('user')->where('id', $item['se_user_id'])->where('much_id', $this->much_id)->value('user_head_sculpture');
                $item['user_nick_name'] = Db::name('user')->where('id', $item['se_user_id'])->where('much_id', $this->much_id)->value('user_nick_name');
                $item['user_wechat_open_id'] = Db::name('user')->where('id', $item['se_user_id'])->where('much_id', $this->much_id)->value('user_wechat_open_id');
                if (intval($item['le_type']) === 1) {
                    $leContent = json_decode($item['le_content'], true);
                    $item['paid'] = $leContent['paid'];
                    $item['content'] = $leContent['title'];
                } else {
                    $item['content'] = $item['le_content'];
                }
                return $item;
            });
        $expressionHtml = function ($val) {
            return Alternative::ExpressionHtml($val);
        };
        $this->assign('expressionHtml', $expressionHtml);
        $this->assign('list', $list);
        $this->assign('hazy_name', emoji_decode($hazy_name));
        $this->assign('rid', $seUserId);
        $this->assign('uid', $reUserId);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }
}