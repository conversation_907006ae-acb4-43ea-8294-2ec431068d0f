<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\api\service\WxCompany;
use app\common\Playful;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

class Rawls extends Base
{

    public function setting()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data['open_withdrawals'] = request()->post('openWithdrawals');
            $data['open_offline_payment'] = request()->post('openOfflinePayment');
            $data['auto_review_payment'] = request()->post('autoReviewPayment');
            $data['lowest_money'] = request()->post('lowestMoney', 1);
            $data['payment_tariff'] = request()->post('paymentTariff', 0) * 0.01;
            $data['notice'] = request()->post('notice');
            Db::startTrans();
            try {
                db('raws_setting')->where('much_id', $this->much_id)->update($data);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                $result = false;
                Db::rollback();
            }
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '保存成功']);
            } else {
                return json(['code' => 0, 'msg' => '保存失败']);
            }
        }
        $rwasList = $this->defaultRawsSetting();
        $this->assign('list', $rwasList);
        return $this->fetch();
    }

    //初始化提现设置
    private function defaultRawsSetting()
    {
        $defaultRawsSetting = db('raws_setting')->where('much_id', $this->much_id)->find();
        if (!$defaultRawsSetting) {
            $defaultRawsSetting = [
                'open_withdrawals' => 0,
                'open_offline_payment' => 0,
                'auto_review_payment' => 0,
                'payment_tariff' => 0,
                'lowest_money' => 1,
                'much_id' => $this->much_id
            ];

            Db::startTrans();
            try {
                $defaultRawsSetting['id'] = db('raws_setting')->insertGetId($defaultRawsSetting);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
            }
        }
        return $defaultRawsSetting;
    }

    //提现列表
    public function stand()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $hazy_egon = request()->get('egon', 0);
        switch ($hazy_egon) {
            case 0:
                $where = [];
                break;
            case 1:
                $where['uwm.status'] = 0;
                break;
            case 2:
                $where['uwm.status'] = 1;
                break;
            case 3:
                $where['uwm.status'] = 2;
                break;
        }
        $list = Db::name('user_withdraw_money')
            ->alias('uwm')
            ->join('user us', 'uwm.user_id=us.id', 'left')
            ->where('us.user_nick_name', 'like', "%{$hazy_name}%")
            ->where($where)
            ->where('uwm.much_id', $this->much_id)
            //->order('status', 'asc')
            //->order('uwm.seek_time', 'asc')
            ->orderRaw('uwm.status <> 0 asc,case when uwm.status = 0 then uwm.id end asc,case when uwm.status <> 0 then uwm.id end desc')
            ->field('uwm.*,us.user_nick_name,us.user_wechat_open_id,us.uvirtual')
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $hazy_egon, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('egon', $hazy_egon);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, [], 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    //提现详情
    public function austive()
    {
        $defaultNavigate = $this->defaultNavigate();
        if (request()->isPost() && request()->isAjax()) {
            $suid = request()->post('suid');
            $thesis = request()->post('thesis');
            if ($thesis == 1) {
                Db::startTrans();
                try {
                    $uwmInfo = db('user_withdraw_money')->where('id', $suid)->where('much_id', $this->much_id)->find();
                    if ($uwmInfo['status'] == 0) {
                        if ($uwmInfo['withdraw_type'] == 0) {
                            $userInfo = db('user')->where('id', $uwmInfo['user_id'])->where('much_id', $this->much_id)->find();
                            $wxcom = new WxCompany();
                            $company = $wxcom->companyToPocket($userInfo['user_wechat_open_id'], $uwmInfo['actual_amount'], '', '提现', '', $this->much_id);
                            if ($company['status'] == 0) {
                                //更改状态
                                db('user_withdraw_money')->where('id', $suid)->where('much_id', $this->much_id)->update(['status' => 1, 'verify_time' => time()]);
                                //站内信
                                db('user_smail')->insert([
                                    'user_id' => $uwmInfo['user_id'],
                                    'maring' => "提现成功，提现资金已付款到您的微信钱包，请注意查收！",
                                    'clue_time' => time(),
                                    'status' => 0,
                                    'much_id' => $this->much_id
                                ]);
                                $result = true;
                            } else {
                                $result = false;
                                $res = $company;
                            }
                            $apiclientCert = EXTEND_PATH . 'Wxpay' . DS . 'Cert' . DS . 'apiclient_cert_' . $this->much_id . '.pem';
                            $apiclientKey = EXTEND_PATH . 'Wxpay' . DS . 'Cert' . DS . 'apiclient_key_' . $this->much_id . '.pem';
                            @unlink($apiclientCert);        //删除证书
                            @unlink($apiclientKey);
                        } else {
                            //更改状态
                            db('user_withdraw_money')->where('id', $suid)->where('much_id', $this->much_id)->update(['status' => 1, 'verify_time' => time()]);
                            //站内信
                            db('user_smail')->insert([
                                'user_id' => $uwmInfo['user_id'],
                                'maring' => "提现成功，提现资金已转账到您的相关账户，请注意查收！",
                                'clue_time' => time(),
                                'status' => 0,
                                'much_id' => $this->much_id
                            ]);
                            $result = true;
                        }
                    } else {
                        $result = false;
                    }
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => '提现失败' . $e->getMessage()]);
                }
                if ($result !== false) {
                    if ($uwmInfo['withdraw_type'] == 0) {
                        return json(['code' => 1, 'msg' => "提现成功，资金已付款到用户微信账户上！"]);
                    } else {
                        return json(['code' => 1, 'msg' => "提现成功！"]);
                    }
                } else {
                    if ($res != '') {
                        return json(['code' => 0, 'msg' => "提现失败，{$res['msg']}"]);
                    } else {
                        return json(['code' => 0, 'msg' => "提现状态不正确，请刷新页面后重试！"]);
                    }
                }
            } else {
                $argument = request()->post('argument');
                Db::startTrans();
                try {
                    $uwmInfo = db('user_withdraw_money')->where('id', $suid)->where('much_id', $this->much_id)->find();
                    if ($uwmInfo['status'] == 0) {
                        //更改状态
                        db('user_withdraw_money')->where('id', $suid)->where('much_id', $this->much_id)->update(['status' => 2, 'verify_time' => time()]);
                        //退还货币
                        $userBefore = db('user')->where('id', $uwmInfo['user_id'])->where('much_id', $this->much_id)->find();
                        db('user')->where('id', $uwmInfo['user_id'])->where('much_id', $this->much_id)->setInc('conch', $uwmInfo['display_money']);
                        $userRear = db('user')->where('id', $uwmInfo['user_id'])->where('much_id', $this->much_id)->find();
                        db('user_amount')->insert([
                            'user_id' => $uwmInfo['user_id'],
                            'category' => 0,
                            'finance' => $uwmInfo['display_money'],
                            'poem_fraction' => $userBefore['fraction'],
                            'poem_conch' => $userBefore['conch'],
                            'surplus_fraction' => $userRear['fraction'],
                            'surplus_conch' => $userRear['conch'],
                            'ruins_time' => time(),
                            'solution' => "提现失败，返还{$defaultNavigate['currency']}",
                            'evaluate' => 0,
                            'much_id' => $this->much_id
                        ]);
                        //站内信
                        db('user_smail')->insert([
                            'user_id' => $uwmInfo['user_id'],
                            'maring' => "提现失败，管理员拒绝了您的提现申请，拒绝理由如下：{$argument}",
                            'clue_time' => time(),
                            'status' => 0,
                            'much_id' => $this->much_id
                        ]);
                        $result = true;
                    } else {
                        $result = false;
                    }
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => '拒绝失败' . $e->getMessage()]);
                }
                if ($result !== false) {
                    return json(['code' => 1, 'msg' => "拒绝成功，提现时扣除的{$defaultNavigate['currency']}已返还给用户"]);
                } else {
                    return json(['code' => 0, 'msg' => "提现状态不正确，请刷新页面后重试！"]);
                }
            }
        }
        $renum = request()->get('renum');
        if ($renum) {
            $getUwm = db('user_withdraw_money')
                ->alias('uwm')
                ->join('user us', 'uwm.user_id=us.id', 'left')
                ->where('uwm.id', $renum)
                ->where('uwm.much_id', $this->much_id)
                ->field('uwm.*,us.user_head_sculpture,us.user_nick_name,us.gender,us.user_wechat_open_id,us.conch,us.fraction,us.status as uats')
                ->find();
            if ($getUwm) {
                $this->assign('defaultNavigate', $defaultNavigate);
                $this->assign('list', $getUwm);
                return $this->fetch();
            } else {
                $this->redirect('rawls/stand');
            }
        } else {
            $this->redirect('rawls/stand');
        }
    }

    /*
     * 重写fputcsv
     */
    private function fputcsv2($handle, array $fields, $delimiter = ",", $enclosure = '"', $escape_char = "\\")
    {
        foreach ($fields as $k => $v) {
            $fields[$k] = iconv("UTF-8", "GB2312//IGNORE", "$v\t");  // 这里将UTF-8转为GB2312编码
        }
        fputcsv($handle, $fields, $delimiter, $enclosure, $escape_char);
    }

    /*
     * 导出充值明细
     */
    public function export_the_top_up_details()
    {
        $egon = intval(input('get.egon', 0));
        header('Content-Encoding: UTF-8');
        header("Content-type:application/vnd.ms-excel;charset=UTF-8");
        header('Content-Disposition: attachment;');
        //  打开php标准输出流
        $fp = fopen('php://output', 'a');
        //  添加导出标题
        $this->fputcsv2($fp, ['订单号', '用户UID', '用户昵称', '用户性别', '用户状态', '当前贝壳', '当前积分', '订单金额', '实付金额', '订单状态', '订单创建时间']);
        //  获取总数量
        $countNums = Db::name('user_serial')->where('much_id', $this->much_id)->count();
        //  每次导出数量
        $nums = 500;
        //  循环次数
        $step = $countNums / $nums;
        //  用户性别
        $userGender = function ($gender) {
            if ($gender === 1) {
                return '男';
            } elseif ($gender === 2) {
                return '女';
            } else {
                return '未知';
            }
        };
        $when = [];
        if ($egon === 1) {
            $when = ['status' => 0];
        }
        if ($egon === 2) {
            $when = ['status' => 1];
        }
        //  循环查询
        for ($i = 0; $i < $step; $i++) {
            //  组合结果
            $result = [];
            //  充值列表
            $userSerialList = Db::name('user_serial')->where($when)->limit(($i * $nums), $nums)->where('much_id', $this->much_id)->order(['id' => 'asc'])->select();
            //  循环查询信息
            foreach ($userSerialList as $item) {
                $userInfo = Db::name('user')->where('id', $item['user_id'])->where('much_id', $this->much_id)->find();
                $result[] = [
                    'order_number' => $item['single_mark'],
                    'user_id' => $item['user_id'],
                    'user_name' => emoji_decode($userInfo['user_nick_name']),
                    'user_gender' => $userGender($userInfo['gender']),
                    'user_status' => $userInfo['status'] === 1 ? '正常' : '封禁',
                    'user_conch' => $userInfo['conch'],
                    'user_fraction' => $userInfo['fraction'],
                    'order_money' => $item['money'],
                    'order_pay_money' => $item['pay_money'],
                    'order_status' => $item['status'] === 1 ? '已支付' : '未支付',
                    'create_time' => date('Y-m-d H:i:s', $item['add_time'])
                ];
            }
            foreach ($result as $item) {
                $this->fputcsv2($fp, $item);
                ob_flush();
            }
            flush();
        }
    }

    /*
     * 导出提现信息
     */
    public function export_payout_information()
    {
        $egon = intval(input('get.egon', 0));
        header('Content-Encoding: UTF-8');
        header("Content-type:application/vnd.ms-excel;charset=UTF-8");
        header('Content-Disposition: attachment;');
        //  打开php标准输出流
        $fp = fopen('php://output', 'a');
        //  添加导出标题
        $this->fputcsv2($fp, ['UID', '用户昵称', '用户性别', '用户状态', '所剩贝壳', '所剩积分', '提现金额 ( 税前 )', '提现税率', '实际金额 ( 税后 ) ', '提现类型', '提现状态', '提现申请时间', '提现审核时间']);
        //  获取总数量
        $countNums = Db::name('user_withdraw_money')->where('much_id', $this->much_id)->count();
        //  每次导出数量
        $nums = 500;
        //  循环次数
        $step = $countNums / $nums;
        //  用户性别
        $userGender = function ($gender) {
            if ($gender === 1) {
                return '男';
            } elseif ($gender === 2) {
                return '女';
            } else {
                return '未知';
            }
        };
        $withdrawStatus = function ($status) {
            if ($status === 1) {
                return '提现通过';
            } elseif ($status === 2) {
                return '提现拒绝';
            } else {
                return '待审核';
            }
        };
        $when = [];
        if ($egon === 1) {
            $when = ['status' => 0];
        }
        if ($egon === 2) {
            $when = ['status' => 1];
        }
        if ($egon === 3) {
            $when = ['status' => 2];
        }
        //  循环查询
        for ($i = 0; $i < $step; $i++) {
            //  组合结果
            $result = [];
            //  提现列表
            $userWithdrawMoneyList = Db::name('user_withdraw_money')->where($when)->limit(($i * $nums), $nums)->where('much_id', $this->much_id)->order(['id' => 'asc'])->select();
            //  循环查询信息
            foreach ($userWithdrawMoneyList as $item) {
                $userInfo = Db::name('user')->where('id', $item['user_id'])->where('much_id', $this->much_id)->find();
                $result[] = [
                    'user_id' => $item['user_id'],
                    'user_name' => emoji_decode($userInfo['user_nick_name']),
                    'user_gender' => $userGender($userInfo['gender']),
                    'user_status' => $userInfo['status'] === 1 ? '正常' : '封禁',
                    'user_conch' => $userInfo['conch'],
                    'user_fraction' => $userInfo['fraction'],
                    'display_money' => $item['display_money'],
                    'withdrawal_tax_rate' => ($item['tariff'] * 100) . '%',
                    'actual_amount' => $item['actual_amount'],
                    'withdraw_type' => $item['withdraw_type'] === 0 ? '微信支付' : '线下打款',
                    'withdraw_status' => $withdrawStatus($item['status']),
                    'seek_time' => date('Y-m-d H:i:s', $item['seek_time']),
                    'verify_time' => $item['verify_time'] !== null ? date('Y-m-d H:i:s', $item['verify_time']) : '-'
                ];
            }
            foreach ($result as $item) {
                $this->fputcsv2($fp, $item);
                ob_flush();
            }
            flush();
        }
    }
}