<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\api\service\TmplService;
use app\common\Playful;
use app\common\Remotely;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;
use WxPayApi;
use WxPayRefund;

#营销设置
class Marketing extends Base
{

    //  礼物列表
    public function friendly()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $list = Db::name('tribute')
            ->where('tr_name', 'like', "%{$hazy_name}%")
            ->where('much_id', $this->much_id)
            ->order('scores', 'asc')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //  礼物列表排序
    public function sfrieng()
    {
        if (request()->isPost() && request()->isAjax()) {
            $syid = request()->post('asyId');
            $scores = request()->post('dalue');
            $result = Db::name('tribute')
                ->where('id', $syid)
                ->where('much_id', $this->much_id)
                ->update(['scores' => $scores]);
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '保存成功']);
            } else {
                return json(['code' => 0, 'msg' => '保存失败']);
            }
        }
    }


    //  新增礼物
    public function rufriendly()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data['tr_name'] = request()->post('name');
            $data['tr_icon'] = request()->post('icon');
            $data['tr_conch'] = request()->post('conch');
            $data['status'] = request()->post('status');
            $data['scores'] = request()->post('scores');
            $buteData = Db::name('tribute')
                ->where('tr_name', $data['tr_name'])
                ->where('much_id', $this->much_id)
                ->find();
            if (empty($buteData) || !isset($buteData)) {
                $data['much_id'] = $this->much_id;
                $result = Db::name('tribute')->insert($data);
                if ($result != false) {
                    return json(['code' => 1, 'msg' => '保存成功']);
                } else {
                    return json(['code' => 0, 'msg' => '保存失败']);
                }
            } else {
                return json(['code' => 0, 'msg' => '保存失败，礼物名已存在']);
            }
        }
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        return $this->fetch();
    }

    //  编辑礼物
    public function upfriendly()
    {
        if (request()->isPost() && request()->isAjax()) {
            $suplid = request()->post('uplid');
            $data['tr_name'] = request()->post('name');
            $data['tr_icon'] = request()->post('icon');
            $data['tr_conch'] = request()->post('conch');
            $data['status'] = request()->post('status');
            $data['scores'] = request()->post('scores');
            $buteData = Db::name('tribute')
                ->where('tr_name', $data['tr_name'])
                ->where('id', '<>', $suplid)
                ->where('much_id', $this->much_id)
                ->find();
            if (empty($buteData) || !isset($buteData)) {
                $result = Db::name('tribute')
                    ->where('id', $suplid)
                    ->where('much_id', $this->much_id)
                    ->update($data);
                if ($result !== false) {
                    return json(['code' => 1, 'msg' => '保存成功']);
                } else {
                    return json(['code' => 0, 'msg' => '保存失败']);

                }
            } else {
                return json(['code' => 0, 'msg' => '保存失败，礼物名已存在']);
            }
        }
        $uplid = request()->get('uplid', '');
        if ($uplid) {
            $buteList = Db::name('tribute')
                ->where('id', $uplid)
                ->where('much_id', $this->much_id)
                ->find();
            if ($buteList) {
                $defaultNavigate = $this->defaultNavigate();
                $this->assign('defaultNavigate', $defaultNavigate);
                $this->assign('list', $buteList);
                return $this->fetch();
            } else {
                $this->redirect('friendly');
            }
        } else {
            $this->redirect('friendly');
        }
    }

    //  删除礼物
    public function defriendly()
    {
        if (request()->isPost() && request()->isAjax()) {
            $suplid = request()->post('ecid');

            Db::startTrans();
            try {
                Db::name('tribute')->where('id', $suplid)->where('much_id', $this->much_id)->delete();
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result === true) {
                return json(['code' => 1, 'msg' => '删除成功']);
            }
        }
    }

    //  会员设置
    public function fabulous()
    {
        if (request()->isPost() && \request()->isAjax()) {
            $getData = input('post.getData');
            if ($getData) {
                $defaultNavigate = $this->defaultNavigate();
                $map[0] = ['confer' => $defaultNavigate['confer'], 'currency' => $defaultNavigate['currency']];
                $list = $this->defaultHonorablePrice($this->much_id);
                $list['discount_scale'] *= 100;
                unset($list['much_id']);
                $map[1] = $list;
                return json($map);
            }
            $setData = input('post.');
            $fillingId = $setData['id'];
            $fillingData['chop_type'] = $setData['chop_type'];
            $fillingData['hono_price'] = $setData['hono_price'];
            $fillingData['first_discount'] = $setData['first_discount'];
            $fillingData['discount_scale'] = ($setData['discount_scale'] * 0.01);
            if (intval($setData['define_price'][0]['time']) == 0) {
                $fillingData['define_price'] = null;
            } else {
                $fillingData['define_price'] = json_encode($setData['define_price'], JSON_FORCE_OBJECT + JSON_UNESCAPED_UNICODE);
            }
            Db::startTrans();
            try {
                Db::name('user_honorary')->where('id', $fillingId)->where('much_id', $this->much_id)->update($fillingData);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result === true) {
                return json(['code' => 1, 'msg' => '保存成功']);
            }
        } else {
            return $this->fetch();
        }
    }

    //  礼物税率
    public function taxing()
    {
        if (request()->isPost() && request()->isAjax()) {
            $usid = request()->post('usid');
            $data['taxing'] = request()->post('taxing') * 0.01;
            Db::startTrans();
            try {
                Db::name('tribute_taxation')->where('id', $usid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        }
        $taxaList = Db::name('tribute_taxation')->where('much_id', $this->much_id)->find();
        if (!$taxaList) {
            Db::startTrans();
            try {
                $taxaList = ['taxing' => 1.00, 'much_id' => $this->much_id];
                $taxaList['id'] = Db::name('tribute_taxation')->insertGetId($taxaList);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
            }
        }
        $this->assign('list', $taxaList);
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $exchangeRate = Db::name('user_punch_range')->where('much_id', $this->much_id)->find();
        $this->assign('exchangeRate', $exchangeRate['fraction_scale']);
        return $this->fetch();
    }

    //  商品分类
    public function stype()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $list = Db::name('shop_type')
            ->where('name', 'like', "%{$hazy_name}%")
            ->where('much_id', $this->much_id)
            ->order('scores', 'asc')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //  添加商品分类
    public function rustype()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data = request()->post();
            $data['stype_time'] = time();
            $data['much_id'] = $this->much_id;
            $getStype = Db::name('shop_type')->where('name', $data['name'])->where('much_id', $this->much_id)->find();
            if ($getStype) {
                return json(['code' => 0, 'msg' => '保存失败，分类名称已存在！']);
            }
            Db::startTrans();
            try {
                Db::name('shop_type')->insert($data);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                $result = false;
                Db::rollback();
            }
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '保存成功']);
            } else {
                return json(['code' => 0, 'msg' => '保存失败']);
            }
        }
        return $this->fetch();
    }

    //  编辑分类
    public function upstype()
    {
        if (request()->isPost() && request()->isAjax()) {
            $usid = request()->post('usid');
            $data['name'] = request()->post('name');
            $data['status'] = request()->post('status');
            $data['scores'] = request()->post('scores');
            $getStype = Db::name('shop_type')
                ->where('id', '<>', $usid)
                ->where('name', $data['name'])
                ->where('much_id', $this->much_id)
                ->find();
            if ($getStype) {
                return json(['code' => 0, 'msg' => '保存失败，保存的分类名称存在重复！']);
            }
            Db::startTrans();
            try {
                Db::name('shop_type')
                    ->where('id', $usid)
                    ->where('much_id', $this->much_id)
                    ->update($data);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                $result = false;
                Db::rollback();
            }
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '保存成功']);
            } else {
                return json(['code' => 0, 'msg' => '保存失败']);
            }
        }
        $uplid = request()->get('uplid', '');
        if ($uplid) {
            $stypeList = Db::name('shop_type')->where('id', $uplid)->where('much_id', $this->much_id)->find();
            if ($stypeList) {
                $this->assign('list', $stypeList);
                return $this->fetch();
            } else {
                $this->redirect('marketing/stype');
            }
        } else {
            $this->redirect('marketing/stype');
        }
    }

    //  删除商品分类
    public function stypelint()
    {
        if (request()->isPost() && request()->isAjax()) {
            $usid = request()->post('ecid');
            Db::startTrans();
            try {
                $getShop = Db::name('shop')->where('product_type', $usid)->where('much_id', $this->much_id)->find();
                if (!$getShop) {
                    Db::name('shop_type')->where('id', $usid)->where('much_id', $this->much_id)->delete();
                    $result = true;
                    Db::commit();
                } else {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => '删除失败，分类下存在关联商品']);
                }
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '删除成功']);
            }
        }
    }

    //  商品列表
    public function shop()
    {
        $url = $this->defaultQuery();
        $egon = request()->get('egon', 0);
        $sid = intval(request()->get('sid', 0));
        $name = trim(request()->get('name', ''));
        $type = trim(request()->get('type', ''));

        $where = [];
        if ($sid !== 0) {
            $where['sp.id'] = $sid;
        } else {
            $sid = '';
        }
        if ($type !== '') {
            $where['stype.name'] = $type;
        }
        switch ($egon) {
            case 0:
                $where['sp.trash'] = 0;
                break;
            case 1:
                $where['sp.trash'] = 0;
                $where['sp.status'] = 1;
                break;
            case 2:
                $where['sp.trash'] = 0;
                $where['sp.status'] = 0;
                break;
            case 3:
                $where['sp.trash'] = 0;
                $where['sp.product_inventory'] = 0;
                break;
            case 4:
                $where['sp.trash'] = 0;
                $where['sp.noble_exclusive'] = 1;
                break;
            case 5:
                $where['sp.trash'] = 1;
                break;
        }
        $list = Db::name('shop')
            ->alias('sp')
            ->join('shop_type stype', 'sp.product_type=stype.id', 'left')
            ->where('sp.product_name', 'like', "%{$name}%")
            ->where($where)
            ->where('sp.much_id', $this->much_id)
            ->order('sp.scores', 'asc')
            ->order('sp.id', 'asc')
            ->field('sp.*,stype.name as tpname')
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $egon, 'sid' => $sid, 'name' => $name, 'type' => $type]])
            ->each(function ($item) {
                //  商品属性
                $svInfo = Db::name('shop_vested')->where('sp_id', $item['id'])->where('much_id', $this->much_id)->find();
                if ($svInfo) {
                    $saList = json_decode($svInfo['sa_list'], true);
                    //  商品库存
                    $saListInventory = 0;
                    //  商品价格
                    $saListPrice = 0.00;
                    for ($i = 0; $i < count($saList); $i++) {
                        $saListInventory += intval($saList[$i]['inventory_count']);
                        if ($saList[$i]['price'] > 0) {
                            $saListPrice = $saList[$i]['price'];
                        }
                    }
                    $item['product_inventory'] = $saListInventory;
                    $item['product_price'] = $saListPrice;
                }
                return $item;
            });
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $this->assign('list', $list);
        $this->assign('egon', $egon);
        $this->assign('sid', $sid);
        $this->assign('name', $name);
        $this->assign('type', $type);
        $autoDelivery = Remotely::isUnLockProperty(base64_decode('5Y2h5a+G5YiX6KGo'));
        $this->assign('autoDelivery', $autoDelivery);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //  添加商品
    public function rushop()
    {
        if (request()->isPost() && request()->isAjax()) {
            $uercive = request()->post();
            $data['product_name'] = $uercive['name'];
            $data['product_type'] = $uercive['type'];
            $data['product_synopsis'] = $uercive['synopsis'];
            $data['product_detail'] = $this->safe_html($uercive['detail']);
            $data['product_img'] = json_encode($uercive['multipleImg'], 320);
            $data['product_inventory'] = $uercive['inventory'];
            $data['product_restrict'] = $uercive['restrict'];
            $data['product_price'] = $uercive['price'];
            $data['pay_type'] = $uercive['payType'];
            $data['noble_exclusive'] = $uercive['exclusive'];
            $data['open_discount'] = $uercive['opdiscount'];
            $data['noble_discount'] = ($uercive['nodiscount'] * 0.01);
            $data['noble_rebate'] = $uercive['rebate'];
            $data['sales_volume'] = $uercive['volume'];


            //  商品属性
            $vestedData = [];
            if (intval($uercive['multipleSpecs']) === 1) {
                $vestedData['sa_name'] = $uercive['saName'];
                $vestedData['sa_list'] = json_encode($uercive['saList'], 320);
                $vestedData['create_time'] = time();
                $vestedData['much_id'] = $this->much_id;
            }

            $data['is_offline'] = intval($uercive['isOffline']);
            //  判断是否为商家商品
            if ($data['is_offline'] === 1) {
                $data['auto_delivery'] = 0;
            } else {
                $data['auto_delivery'] = intval($uercive['autoDelivery']);
            }
            $data['status'] = $uercive['status'];
            $data['scores'] = $uercive['scores'];
            $data['trash'] = 0;
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                $vestedData['sp_id'] = Db::name('shop')->insertGetId($data);
                //  商品属性
                if (intval($uercive['multipleSpecs']) === 1) {
                    Db::name('shop_vested')->insert($vestedData);
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $stypeList = Db::name('shop_type')->where('status', 1)->where('much_id', $this->much_id)->select();
            $this->assign('stypeList', $stypeList);
            $defaultNavigate = $this->defaultNavigate();
            $this->assign('defaultNavigate', $defaultNavigate);
            //  线下自提
            $offlinePickup = Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'));
            $this->assign('offlinePickup', $offlinePickup);
            //  自动发货
            $autoDelivery = Remotely::isUnLockProperty(base64_decode('5Y2h5a+G5YiX6KGo'));
            $this->assign('autoDelivery', $autoDelivery);

            return $this->fetch();
        }
    }

    //  编辑商品
    public function upshop()
    {
        if (request()->isPost() && request()->isAjax()) {
            $uercive = request()->post();
            $usid = $uercive['usid'];
            $data['product_name'] = $uercive['name'];
            $data['product_type'] = $uercive['type'];
            $data['product_synopsis'] = $uercive['synopsis'];
            $data['product_detail'] = $this->safe_html($uercive['detail']);
            $data['product_img'] = json_encode($uercive['multipleImg'], 320);
            $data['product_inventory'] = $uercive['inventory'];
            $data['product_restrict'] = $uercive['restrict'];
            $data['product_price'] = $uercive['price'];
            $data['pay_type'] = $uercive['payType'];
            $data['noble_exclusive'] = $uercive['exclusive'];
            $data['open_discount'] = $uercive['opdiscount'];
            $data['noble_discount'] = ($uercive['nodiscount'] * 0.01);
            $data['noble_rebate'] = $uercive['rebate'];
            $data['sales_volume'] = $uercive['volume'];

            //  商品属性
            $vestedData = [];
            $svInfo = null;
            if (intval($uercive['multipleSpecs']) === 1) {
                $vestedData['sa_name'] = $uercive['saName'];
                $vestedData['sa_list'] = json_encode($uercive['saList'], 320);
                $svInfo = Db::name('shop_vested')->where('sp_id', $usid)->where('much_id', $this->much_id)->find();
                if (!$svInfo) {
                    $vestedData['sp_id'] = $usid;
                    $vestedData['create_time'] = time();
                    $vestedData['much_id'] = $this->much_id;
                }
            }

            $data['is_offline'] = intval($uercive['isOffline']);
            //  判断是否为商家商品
            if ($data['is_offline'] === 1) {
                $data['auto_delivery'] = 0;
            } else {
                $data['auto_delivery'] = intval($uercive['autoDelivery']);
            }

            $data['status'] = $uercive['status'];
            $data['scores'] = $uercive['scores'];
            Db::startTrans();
            try {
                Db::name('shop')->where('id', $usid)->where('much_id', $this->much_id)->update($data);
                //  商品属性
                if (intval($uercive['multipleSpecs']) === 1) {
                    //  判断商品属性是否存在
                    if ($svInfo) {
                        Db::name('shop_vested')->where('sp_id', $usid)->where('much_id', $this->much_id)->update($vestedData);
                    } else {
                        Db::name('shop_vested')->insert($vestedData);
                    }
                } else {
                    //  设置单属性时，删除多属性数据
                    Db::name('shop_vested')->where('sp_id', $usid)->where('much_id', $this->much_id)->delete();
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        }
        $usid = request()->get('usid', '');
        if ($usid) {
            $shopList = Db::name('shop')->where('id', $usid)->where('trash', 0)->where('much_id', $this->much_id)->find();
            if ($shopList) {
                $stypeList = Db::name('shop_type')->where('status', 1)->where('much_id', $this->much_id)->select();
                $shopList['product_img'] = json_decode($shopList['product_img'], true);

                //  商品属性
                $svInfo = Db::name('shop_vested')->where('sp_id', $usid)->where('much_id', $this->much_id)->find();
                if ($svInfo) {
                    $svInfo['sa_list'] = base64_encode(rawurlencode($svInfo['sa_list']));
                    $this->assign('svInfo', $svInfo);
                    $this->assign('svInfoExist', 1);
                } else {
                    $this->assign('svInfoExist', 0);
                }

                //  线下自提
                $offlinePickup = Remotely::isUnLockProperty(base64_decode('5ZCM5Z+O5L+h5oGv'));
                $this->assign('offlinePickup', $offlinePickup);

                //  自动发货
                $autoDelivery = Remotely::isUnLockProperty(base64_decode('5Y2h5a+G5YiX6KGo'));
                $this->assign('autoDelivery', $autoDelivery);

                $this->assign('list', $shopList);
                $this->assign('stypeList', $stypeList);
                $defaultNavigate = $this->defaultNavigate();
                $this->assign('defaultNavigate', $defaultNavigate);

                return $this->fetch();
            } else {
                $this->redirect('marketing/shop');
            }
        } else {
            $this->redirect('marketing/shop');
        }
    }

    //  商品删除
    public function shoplint()
    {
        if (request()->isPost() && request()->isAjax()) {
            $usid = request()->post('suid');
            $thorough = request()->post('thorough');
            Db::startTrans();
            try {
                if ($thorough == 0) {
                    Db::name('shop')->where('id', $usid)->where('much_id', $this->much_id)->update(['status' => 0, 'trash' => 1]);
                } else {
                    Db::name('shop')->where('id', $usid)->where('much_id', $this->much_id)->delete();
                }
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                $result = false;
                Db::rollback();
            }
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '删除成功']);
            } else {
                return json(['code' => 0, 'msg' => '删除失败']);
            }
        }
    }

    //  恢复商品
    public function resume()
    {
        if (request()->isPost() && request()->isAjax()) {
            $usid = request()->post('suid');
            Db::startTrans();
            try {
                Db::name('shop')->where('id', $usid)->where('much_id', $this->much_id)->update(['trash' => 0]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                $result = false;
                Db::rollback();
            }
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '恢复成功']);
            } else {
                return json(['code' => 0, 'msg' => '恢复失败']);
            }
        }
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, [], 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    //  商品排序
    public function dopslue()
    {
        if (request()->isPost() && request()->isAjax()) {
            $syid = request()->post('asyId');
            $scores = request()->post('dalue');
            Db::startTrans();
            try {
                Db::name('shop')
                    ->where('id', $syid)
                    ->where('much_id', $this->much_id)
                    ->update(['scores' => $scores]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                $result = false;
                Db::rollback();
            }
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '排序成功']);
            } else {
                return json(['code' => 0, 'msg' => '排序失败']);
            }
        }
    }

    //  商品订单
    public function sorder()
    {
        $url = $this->defaultQuery();
        $orderNumber = trim(input('get.orderNumber'));
        $name = trim(input('get.name'));
        $egon = intval(input('get.egon', 0));
        $uid = intval(input('get.uid', 0));
        $where = [];

        if ($orderNumber !== '') {
            $where[] = function ($query) use ($orderNumber) {
                $query->where('sord.order_number', 'like', "%{$orderNumber}%");
            };
        }
        if ($uid !== 0) {
            $where['us.id'] = $uid;
        } else {
            $uid = '';
        }
        switch ($egon) {
            case 1:
                $where[] = function ($query) {
                    $query->where(function ($childrenQueryOne) {
                        $childrenQueryOne->where('sord.pay_type', '<>', 2)->where('sord.pay_status', '>=', 0);
                    })->whereOr(function ($childrenQueryTwo) {
                        $childrenQueryTwo->where('sord.pay_type', '=', 2)->where('sord.pay_status', '=', 1);
                    });
                };
                $where['sord.status'] = 0;
                break;
            case 2:
                $where['sord.status'] = 1;
                break;
            case 3:
                $where['sord.status'] = 2;
                break;
            case 4:
                $where['sord.status'] = 4;
                break;
            case 5:
                $where[] = function ($query) {
                    $query->where(function ($childrenQueryOne) {
                        $childrenQueryOne->where('sord.pay_type', '<>', 2)->where('sord.pay_status', '>=', 0);
                    })->whereOr(function ($childrenQueryTwo) {
                        $childrenQueryTwo->where('sord.pay_type', '=', 2)->where('sord.pay_status', '=', 2);
                    });
                };
                $where['sord.status'] = 3;
                break;
            case 6:
                $where[] = function ($query) {
                    $query->where(function ($childrenQueryOne) {
                        $childrenQueryOne->where('sord.pay_type', '=', 2)->where('sord.pay_status', '=', 0);
                    });
                };
                $where['sord.status'] = 3;
                break;
        }
        $list = Db::name('shop_order')
            ->alias('sord')
            ->join('user us', 'sord.user_id=us.id', 'left')
            ->where('sord.product_name', 'like', "%{$name}%")
            ->where($where)
            ->where('sord.much_id', $this->much_id)
            //->order('sord.buy_time', 'asc')
            ->orderRaw('CASE WHEN sord.pay_status = 1 AND sord.status = 2 THEN 0 WHEN sord.pay_status = 1 AND sord.status = 0 THEN 1 WHEN sord.pay_status = 1 AND sord.status = 1 THEN 2 WHEN sord.status = 2 THEN 2 WHEN sord.status = 0 THEN 3 WHEN sord.status = 1 THEN 4 WHEN sord.status IN (3, 4) THEN 5 ELSE 6 END,sord.buy_time ASC,sord.id DESC')
            ->field('sord.*,us.user_nick_name,us.user_wechat_open_id')
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $egon, 'orderNumber' => $orderNumber, 'name' => $name, 'uid' => $uid]]);
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $this->assign('list', $list);
        $this->assign('egon', $egon);
        $this->assign('orderNumber', $orderNumber);
        $this->assign('name', $name);
        $this->assign('uid', $uid);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //  订单详情
    public function seorder()
    {
        $usid = request()->get('usid', '');
        if ($usid) {
            $sorderList = Db::name('shop_order')
                ->alias('sord')
                ->join('user us', 'sord.user_id=us.id', 'left')
                ->where('sord.id', $usid)
                ->where('sord.much_id', $this->much_id)
                ->field('sord.*,us.user_nick_name,us.user_wechat_open_id')
                ->find();
            if ($sorderList) {
                //  商品属性
                if ($sorderList['vested_attribute']) {
                    $sorderList['attrInfo'] = json_decode($sorderList['vested_attribute'], true);
                }
                $defaultNavigate = $this->defaultNavigate();
                $this->assign('defaultNavigate', $defaultNavigate);
                $this->assign('list', $sorderList);
                return $this->fetch();
            } else {
                $this->redirect('marketing/sorder');
            }
        } else {
            $this->redirect('marketing/sorder');
        }
    }

    //  订单发货
    public function shiporder()
    {
        if (request()->isPost() && request()->isAjax()) {
            $usid = request()->post('usid');
            $data['buyer_name'] = request()->post('buyer_name');
            $data['buyer_phone'] = request()->post('buyer_phone');
            $data['buyer_address'] = request()->post('buyer_address');
            $data['shipment'] = request()->post('shipment');
            $data['ship_time'] = time();
            $data['status'] = 1;
            Db::startTrans();
            try {
                $sorder = Db::name('shop_order')->where('id', $usid)->where('much_id', $this->much_id)->find();
                if ($sorder['status'] == 0) {
                    Db::name('shop_order')->where('id', $usid)->where('much_id', $this->much_id)->update($data);
                    if ($sorder['status'] == 2) {
                        Db::name('user_smail')->insert([
                            'user_id' => $sorder['user_id'],
                            'maring' => "您申请的订单号为 : {$sorder['order_number']} 的退款已被管理员拒绝，如有疑问请联系在线客服！",
                            'clue_time' => time(),
                            'status' => 0,
                            'much_id' => $this->much_id
                        ]);
                    }
                    Db::name('user_smail')->insert([
                        'user_id' => $sorder['user_id'],
                        'maring' => "您兑换 订单号为 : {$sorder['order_number']} 的商品已发货，请及时关注物流信息，确认无误后再收货！",
                        'clue_time' => time(),
                        'status' => 0,
                        'much_id' => $this->much_id
                    ]);
                    $result = true;
                    Db::commit();
                } else {
                    $result = false;
                }
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result == true) {
                $tmplData = [
                    'much_id' => $this->much_id,
                    'at_id' => 'YL0009',
                    'user_id' => $sorder['user_id'],
                    'page' => 'yl_welore/pages/packageA/user_order/index',
                    'keyword1' => '您在商城中兑换的物品已发货，点击查看详情',
                    'keyword2' => date('Y年m月d日 H:i:s', time())
                ];
                $tmplService = new TmplService();
                $tmplService->add_template($tmplData);
                return json(['code' => 1, 'msg' => '发货成功']);
            }
        } else {
            $this->redirect('marketing/sorder');
        }
    }

    //  编辑订单物流信息
    public function unshier()
    {
        if (request()->isPost() && request()->isAjax()) {
            $usid = request()->post('usid');
            $data['shipment'] = request()->post('shipment');
            Db::startTrans();
            try {
                Db::name('shop_order')->where('id', $usid)->where('much_id', $this->much_id)->update($data);
                $sorder = Db::name('shop_order')->where('id', $usid)->where('much_id', $this->much_id)->find();
                Db::name('user_smail')->insert([
                    'user_id' => $sorder['user_id'],
                    'maring' => "您订单号为 : {$sorder['order_number']} 的商品物流信息已被重新编辑，请及时查看，确认无误后再收货！",
                    'clue_time' => time(),
                    'status' => 0,
                    'much_id' => $this->much_id
                ]);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result !== false) {
                $tmplData = [
                    'much_id' => $this->much_id,
                    'at_id' => 'YL0009',
                    'user_id' => $sorder['user_id'],
                    'page' => 'yl_welore/pages/packageA/user_order/index',
                    'keyword1' => '您在商城中兑换物品的物流信息已被重新编辑',
                    'keyword2' => date('Y年m月d日 H:i:s', time())
                ];
                $tmplService = new TmplService();
                $tmplService->add_template($tmplData);
                return json(['code' => 1, 'msg' => '修改成功']);
            }
        } else {
            $this->redirect('marketing/sorder');
        }
    }


    //  订单收货
    public function charger()
    {
        if (request()->isPost() && request()->isAjax()) {
            $usid = request()->post('usid');
            Db::startTrans();
            try {
                $sorder = Db::name('shop_order')->where('id', $usid)->where('much_id', $this->much_id)->find();
                //  判断订单状态
                if ($sorder['status'] == 1) {
                    if ($sorder['is_noble'] == 1 && $sorder['product_rebate'] != 0) {
                        $userEL = Db::name('user')->where('uvirtual', 0)->where('id', $sorder['user_id'])->where('much_id', $this->much_id)->find();
                        Db::name('user')->where('uvirtual', 0)->where('id', $sorder['user_id'])->where('much_id', $this->much_id)->setInc('fraction', $sorder['product_rebate']);
                        $userER = Db::name('user')->where('uvirtual', 0)->where('id', $sorder['user_id'])->where('much_id', $this->much_id)->find();
                        Db::name('user_amount')->insert([
                            'user_id' => $sorder['user_id'],
                            'category' => 3,
                            'finance' => $sorder['actual_price'],
                            'poem_fraction' => $userEL['fraction'],
                            'poem_conch' => $userEL['conch'],
                            'surplus_fraction' => $userER['fraction'],
                            'surplus_conch' => $userER['conch'],
                            'ruins_time' => time(),
                            'solution' => '商品订单完成 ( 赠送积分 )',
                            'evaluate' => 1,
                            'much_id' => $this->much_id
                        ]);
                    }
                    Db::name('shop_order')->where('id', $usid)->where('much_id', $this->much_id)->update(['status' => 4]);
                    Db::name('user_smail')->insert([
                        'user_id' => $sorder['user_id'],
                        'maring' => "您订单号为：{$sorder['order_number']}的商品已完成收货，感谢您的惠顾，欢迎再次光临！",
                        'clue_time' => time(),
                        'status' => 0,
                        'much_id' => $this->much_id
                    ]);
                    $result = true;
                    Db::commit();
                } else {
                    $result = false;
                }
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result !== false) {
                $tmplData = [
                    'much_id' => $this->much_id,
                    'at_id' => 'YL0009',
                    'user_id' => $sorder['user_id'],
                    'page' => 'yl_welore/pages/packageA/user_order/index',
                    'keyword1' => '您在商城中兑换的物品已完成收货，谢谢惠顾',
                    'keyword2' => date('Y年m月d日 H:i:s', time())
                ];
                $tmplService = new TmplService();
                $tmplService->add_template($tmplData);
                return json(['code' => 1, 'msg' => '收货成功']);
            } else {
                return json(['code' => 0, 'msg' => '确认收货失败，请刷新页面后重试！']);
            }
        } else {
            $this->redirect('marketing/sorder');
        }
    }

    //  订单取消
    public function canlorder()
    {
        if (request()->isPost() && request()->isAjax()) {
            $usid = request()->post('usid');
            Db::startTrans();
            try {
                $sorder = Db::name('shop_order')->where('id', $usid)->where('much_id', $this->much_id)->find();
                $userEL = Db::name('user')->where('uvirtual', 0)->where('id', $sorder['user_id'])->where('much_id', $this->much_id)->find();

                //  是否为商家商品订单
                if ($sorder['is_offline'] == 1) {
                    Db::name('easy_info_shop_order')->where('so_id', $sorder['id'])->where('much_id', $this->much_id)->update(['order_status' => 0]);
                }

                if (intval($sorder['pay_type']) < 2 && intval($sorder['status']) != 3 && intval($sorder['status']) != 4) {
                    $defaultNavigate = $this->defaultNavigate();
                    $moneyName = '';    //  货币名称
                    switch (intval($sorder['pay_type'])) {
                        case 0:
                            //  退款贝壳
                            $moneyName = $defaultNavigate['currency'];
                            Db::name('user')->where('uvirtual', 0)->where('id', $sorder['user_id'])->where('much_id', $this->much_id)->setInc('conch', $sorder['actual_price']);
                            break;
                        case 1:
                            //  退款积分
                            $moneyName = $defaultNavigate['confer'];
                            Db::name('user')->where('uvirtual', 0)->where('id', $sorder['user_id'])->where('much_id', $this->much_id)->setInc('fraction', $sorder['actual_price']);
                            break;
                    }
                    $userER = Db::name('user')->where('uvirtual', 0)->where('id', $sorder['user_id'])->where('much_id', $this->much_id)->find();
                    Db::name('user_amount')->insert([
                        'user_id' => $sorder['user_id'],
                        'category' => 3,
                        'finance' => $sorder['actual_price'],
                        'poem_fraction' => $userEL['fraction'],
                        'poem_conch' => $userEL['conch'],
                        'surplus_fraction' => $userER['fraction'],
                        'surplus_conch' => $userER['conch'],
                        'ruins_time' => time(),
                        'solution' => "商品订单取消 ( 退还{$moneyName} )",
                        'evaluate' => intval($sorder['pay_type']),
                        'much_id' => $this->much_id
                    ]);
                    Db::name('shop_order')->where('id', $usid)->where('much_id', $this->much_id)->update(['status' => 3]);
                    Db::name('user_smail')->insert([
                        'user_id' => $sorder['user_id'],
                        'maring' => "您订单编号为：{$sorder['order_number']}的订单已被取消，兑换消耗的{$moneyName}已返还到您的账户上，对您造成的不便我们深感抱歉，如有疑问请联系在线客服！",
                        'clue_time' => time(),
                        'status' => 0,
                        'much_id' => $this->much_id
                    ]);
                    $result = true;
                    Db::commit();
                } elseif (intval($sorder['pay_type']) === 2 && intval($sorder['pay_status']) === 1 && $sorder['status'] != 3 && $sorder['status'] != 4) {
                    $data['openid'] = $userEL['user_wechat_open_id'];
                    $data['order_id'] = $sorder['order_number'];
                    $data['refund_price'] = Db::name('user_serial')->where('single_mark', $sorder['order_number'])->where('much_id', $this->much_id)->value('pay_money');
                    $resultInfo = $this->wxPayRefund($data);
                    if ($resultInfo['code'] === 1) {
                        Db::name('user_amount')->insert([
                            'serial_id' => $sorder['order_number'],
                            'user_id' => $sorder['user_id'],
                            'category' => 4,
                            'finance' => $sorder['actual_price'],
                            'poem_fraction' => $userEL['fraction'],
                            'poem_conch' => $userEL['conch'],
                            'surplus_fraction' => $userEL['fraction'],
                            'surplus_conch' => $userEL['conch'],
                            'ruins_time' => time(),
                            'solution' => "商品订单取消 ( 退款 )",
                            'evaluate' => intval($sorder['pay_type']),
                            'much_id' => $this->much_id
                        ]);
                        Db::name('shop_order')->where('id', $usid)->where('much_id', $this->much_id)->update(['pay_status' => 2, 'status' => 3]);
                        Db::name('user_smail')->insert([
                            'user_id' => $sorder['user_id'],
                            'maring' => "您订单编号为：{$sorder['order_number']}的订单已被取消，付款金额已原路还到您的账户上，对您造成的不便我们深感抱歉，如有疑问请联系在线客服！",
                            'clue_time' => time(),
                            'status' => 0,
                            'much_id' => $this->much_id
                        ]);
                        $result = true;
                        Db::commit();
                    } else {
                        return json($resultInfo);
                    }
                } elseif (intval($sorder['pay_type']) === 2 && intval($sorder['pay_status']) === 0 && $sorder['status'] != 3 && $sorder['status'] != 4) {
                    Db::name('shop_order')->where('id', $usid)->where('much_id', $this->much_id)->update(['status' => 3]);
                    Db::name('user_smail')->insert([
                        'user_id' => $sorder['user_id'],
                        'maring' => "您订单编号为：{$sorder['order_number']}的订单因超时未付款已被取消，对您造成的不便我们深感抱歉，如有疑问请联系在线客服！",
                        'clue_time' => time(),
                        'status' => 0,
                        'much_id' => $this->much_id
                    ]);
                    $result = true;
                    Db::commit();
                } else {
                    $result = false;
                }
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result) {
                $tmplData = [
                    'much_id' => $this->much_id,
                    'at_id' => 'YL0009',
                    'user_id' => $sorder['user_id'],
                    'page' => 'yl_welore/pages/user_smail/index',
                    'keyword1' => '您在商城中兑换的物品已被取消订单',
                    'keyword2' => date('Y年m月d日 H:i:s', time())
                ];
                $tmplService = new TmplService();
                $tmplService->add_template($tmplData);
                return json(['code' => 1, 'msg' => '取消成功']);
            } else {
                return json(['code' => 0, 'msg' => '意外的错误，未知异常！']);
            }
        } else {
            $this->redirect('marketing/sorder');
        }
    }

    //  申请退款
    public function refuntreat()
    {
        if (request()->isPost() && request()->isAjax()) {
            $usid = request()->post('usid');
            $uentreat = intval(request()->post('uentreat'));
            Db::startTrans();
            try {
                $sorder = Db::name('shop_order')->where('id', $usid)->where('much_id', $this->much_id)->find();
                if ($uentreat === 0) {
                    if ($sorder['shipment'] != '') {
                        $data['status'] = 1;
                    } else {
                        $data['status'] = 0;
                    }
                    Db::name('shop_order')->where('id', $usid)->where('much_id', $this->much_id)->update($data);
                    $maring = "您申请的订单号为 : {$sorder['order_number']} 的退款已被拒绝，如有疑问请联系在线客服！";
                    Db::name('user_smail')->insert([
                        'user_id' => $sorder['user_id'],
                        'maring' => $maring,
                        'clue_time' => time(),
                        'status' => 0,
                        'much_id' => $this->much_id
                    ]);
                } else {
                    $userEL = Db::name('user')->where('uvirtual', 0)->where('id', $sorder['user_id'])->where('much_id', $this->much_id)->find();
                    if (intval($sorder['pay_type']) < 2 && $sorder['status'] != 3 && $sorder['status'] != 4) {
                        $defaultNavigate = $this->defaultNavigate();
                        $moneyName = '';    //  货币名称
                        switch (intval($sorder['pay_type'])) {
                            case 0:
                                //  退款贝壳
                                $moneyName = $defaultNavigate['currency'];
                                Db::name('user')->where('uvirtual', 0)->where('id', $sorder['user_id'])->where('much_id', $this->much_id)->setInc('conch', $sorder['actual_price']);
                                break;
                            case 1:
                                //  退款积分
                                $moneyName = $defaultNavigate['confer'];
                                Db::name('user')->where('uvirtual', 0)->where('id', $sorder['user_id'])->where('much_id', $this->much_id)->setInc('fraction', $sorder['actual_price']);
                                break;
                        }
                        $userER = Db::name('user')->where('uvirtual', 0)->where('id', $sorder['user_id'])->where('much_id', $this->much_id)->find();
                        Db::name('user_amount')->insert([
                            'user_id' => $sorder['user_id'],
                            'category' => 3,
                            'finance' => $sorder['actual_price'],
                            'poem_fraction' => $userEL['fraction'],
                            'poem_conch' => $userEL['conch'],
                            'surplus_fraction' => $userER['fraction'],
                            'surplus_conch' => $userER['conch'],
                            'ruins_time' => time(),
                            'solution' => "商品订单取消 ( 退还{$moneyName} )",
                            'evaluate' => intval($sorder['pay_type']),
                            'much_id' => $this->much_id
                        ]);
                        Db::name('shop_order')->where('id', $usid)->where('much_id', $this->much_id)->update(['status' => 3]);
                        Db::name('user_smail')->insert([
                            'user_id' => $sorder['user_id'],
                            'maring' => "您申请的订单号为 : {$sorder['order_number']} 的退款已成功，兑换消耗的{$moneyName}已返还到您的账户上，如有疑问请联系在线客服！",
                            'clue_time' => time(),
                            'status' => 0,
                            'much_id' => $this->much_id
                        ]);
                    } elseif (intval($sorder['pay_type']) === 2 && intval($sorder['pay_status']) === 1 && $sorder['status'] != 3 && $sorder['status'] != 4) {
                        $data['openid'] = $userEL['user_wechat_open_id'];
                        $data['order_id'] = $sorder['order_number'];
                        $data['refund_price'] = Db::name('user_serial')->where('single_mark', $sorder['order_number'])->where('much_id', $this->much_id)->value('pay_money');
                        $resultInfo = $this->wxPayRefund($data);
                        if ($resultInfo['code'] === 1) {
                            Db::name('user_amount')->insert([
                                'serial_id' => $sorder['order_number'],
                                'user_id' => $sorder['user_id'],
                                'category' => 4,
                                'finance' => $sorder['actual_price'],
                                'poem_fraction' => $userEL['fraction'],
                                'poem_conch' => $userEL['conch'],
                                'surplus_fraction' => $userEL['fraction'],
                                'surplus_conch' => $userEL['conch'],
                                'ruins_time' => time(),
                                'solution' => "商品订单取消 ( 退款 )",
                                'evaluate' => intval($sorder['pay_type']),
                                'much_id' => $this->much_id
                            ]);
                            Db::name('shop_order')->where('id', $usid)->where('much_id', $this->much_id)->update(['pay_status' => 2, 'status' => 3]);
                            Db::name('user_smail')->insert([
                                'user_id' => $sorder['user_id'],
                                'maring' => "您申请的订单号为 : {$sorder['order_number']} 的退款已成功，付款金额已原路还到您的账户上，如有疑问请联系在线客服！",
                                'clue_time' => time(),
                                'status' => 0,
                                'much_id' => $this->much_id
                            ]);
                        } else {
                            return json($resultInfo);
                        }
                    }
                    //  同城信息商家产品订单关联取消
                    Db::name('easy_info_shop_order')->where('so_id', $usid)->where('much_id', $this->much_id)->update(['order_status' => 0]);
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            $tmplData = [
                'much_id' => $this->much_id,
                'at_id' => 'YL0009',
                'user_id' => $sorder['user_id'],
                'page' => 'yl_welore/pages/user_smail/index',
                'keyword1' => $uentreat == 0 ? '您在商城中兑换的物品申请退款已被拒绝' : '您在商城中兑换的物品申请退款已成功',
                'keyword2' => date('Y年m月d日 H:i:s', time())
            ];
            $tmplService = new TmplService();
            $tmplService->add_template($tmplData);
            return json(['code' => 1, 'msg' => $uentreat == 0 ? '拒绝退款成功' : '同意退款成功']);
        } else {
            $this->redirect('marketing/sorder');
        }
    }

    /*
     * 微信支付退款
     */
    private function wxPayRefund($data)
    {
        require_once EXTEND_PATH . "Wxpay/WxPay.Api.php";
        $refundConfig = Db::name('config')->where('much_id', $this->much_id)->find();
        foreach ($refundConfig as $key => $value) {
            if ($key != 'id' && $key != 'pay_react' && $key != 'much_id') {
                $refundConfig[$key] = authcode($refundConfig[$key]);
            }
        }
        $createCatalogPath = EXTEND_PATH . 'Wxpay' . DS . 'Cert';
        $createPath = iconv('UTF-8', 'GBK', $createCatalogPath);
        if (!file_exists($createPath)) {
            @mkdir($createPath, 0777, true);
        }
        $apiclientCert = EXTEND_PATH . 'Wxpay' . DS . 'Cert' . DS . 'apiclient_cert_' . $this->much_id . '.pem';
        $apiclientKey = EXTEND_PATH . 'Wxpay' . DS . 'Cert' . DS . 'apiclient_key_' . $this->much_id . '.pem';

        @file_put_contents($apiclientCert, $refundConfig['apiclient_cert']);      //生成证书
        @file_put_contents($apiclientKey, $refundConfig['apiclient_key']);


        $wxPayRefund = new WxPayRefund();

        define('APPID', $refundConfig['app_id']);
        define('MCHID', $refundConfig['app_mchid']);
        define('KEY', $refundConfig['app_key']);
        define("SSLCERT_PATH", $apiclientCert);
        define("SSLKEY_PATH", $apiclientKey);

        $wxPayRefund->SetOp_user_id($data['openid']);
        $wxPayRefund->SetOut_trade_no($data['order_id']);
        $wxPayRefund->SetOut_refund_no($data['order_id']);
        $wxPayRefund->SetTotal_fee($data['refund_price'] * 100);
        $wxPayRefund->SetRefund_fee($data['refund_price'] * 100);
        try {
            $result = WxPayApi::refund($wxPayRefund);
        } catch (\Exception $e) {
            $result['return_msg'] = $e->getMessage();
        }
        @unlink($apiclientCert);        //删除证书
        @unlink($apiclientKey);
        if ($result['result_code'] === 'SUCCESS') {
            return ['code' => 1, 'msg' => $result['return_msg']];
        } else {
            return ['code' => 0, 'msg' => $result['err_code_des']];
        }
    }
}