<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\api\service\TmplService;
use app\api\service\UserService;
use app\common\Playful;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

#违规词汇
class Unlawful extends Base
{

    //违规关键词设置
    public function words()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data['open_network_images_offend'] = intval(request()->post('openNetworkImagesOffend'));
            $data['open_network_nickname_offend'] = intval(request()->post('openNetworkNicknameOffend'));
            $data['open_network_content_offend'] = intval(request()->post('openNetworkContentOffend'));
            $data['nickname_offend'] = trim(request()->post('nicknameOffend'));
            $data['content_offend'] = trim(request()->post('contentOffend'));

            if ($data['open_network_images_offend'] === 2 || $data['open_network_nickname_offend'] === 2 || $data['open_network_content_offend'] === 2) {

                $tencentCloudContentSecurityConfigInfoJson = Db::name('user_violation')->where('much_id', $this->much_id)->value('tencent_cloud_content_security_config');
                $tencentCloudContentSecurityConfigInfo = json_decode($tencentCloudContentSecurityConfigInfoJson, true);

                $tencentCloudContentSecurityConfig = request()->post('tencentCloudContentSecurityConfig/a');

                $tencentCloudContentSecurityConfig['secretId'] = trim($tencentCloudContentSecurityConfig['secretId']);
                if ($tencentCloudContentSecurityConfig['secretId'] !== '') {
                    $tencentCloudContentSecurityConfig['secretId'] = authcode($tencentCloudContentSecurityConfig['secretId'], 'ENCODE', 'YuluoNetwork');
                } else {
                    $tencentCloudContentSecurityConfig['secretId'] = $tencentCloudContentSecurityConfigInfo['secretId'];
                }

                $tencentCloudContentSecurityConfig['secretKey'] = trim($tencentCloudContentSecurityConfig['secretKey']);
                if ($tencentCloudContentSecurityConfig['secretKey'] !== '') {
                    $tencentCloudContentSecurityConfig['secretKey'] = authcode($tencentCloudContentSecurityConfig['secretKey'], 'ENCODE', 'YuluoNetwork');
                } else {
                    $tencentCloudContentSecurityConfig['secretKey'] = $tencentCloudContentSecurityConfigInfo['secretKey'];
                }

                $tencentCloudContentSecurityConfig['region'] = trim($tencentCloudContentSecurityConfig['region']);

                $data['tencent_cloud_content_security_config'] = json_encode($tencentCloudContentSecurityConfig, 320);
            }

            Db::startTrans();
            try {
                Db::name('user_violation')->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $verificationList = Db::name('user_violation')->where('much_id', $this->much_id)->find();

            if (!$verificationList) {
                Db::name('user_violation')->insert(['open_network_images_offend' => 1, 'open_network_nickname_offend' => 1, 'open_network_content_offend' => 1, 'much_id' => $this->much_id]);
            }

            $verificationList['nickname_offend'] = base64_encode(rawurlencode($verificationList['nickname_offend']));
            $verificationList['content_offend'] = base64_encode(rawurlencode($verificationList['content_offend']));

            $tencentCloudContentSecurityConfig = json_decode($verificationList['tencent_cloud_content_security_config'], true);
            $tencentCloudContentSecurityConfig['secretId'] = authcode($tencentCloudContentSecurityConfig['secretId'], 'DECODE', 'YuluoNetwork');
            $tencentCloudContentSecurityConfig['secretKey'] = authcode($tencentCloudContentSecurityConfig['secretKey'], 'DECODE', 'YuluoNetwork');

            $verificationList['tencent_cloud_content_security_config'] = $tencentCloudContentSecurityConfig;

            $this->assign('verificationList', $verificationList);

            return $this->fetch();
        }
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, [], 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    //搜索话题
    public function loadGambit()
    {
        $wd = request()->get('wd');
        $gambitInfo = Db::name('gambit')
            ->where('gambit_name', 'like', "%{$wd}%")
            ->where('is_del', 0)
            ->where('much_id', $this->much_id)
            ->order('scores')
            ->limit(10)
            ->field('gambit_name as s')
            ->select();
        foreach ($gambitInfo as &$info) {
            $info['s'] = preg_replace('/#([^#]+)#/', '$1', $info['s']);
        }
        return json($gambitInfo);
    }

    //邀请明细
    public function salute()
    {
        $url = $this->defaultQuery();
        $hazy_name = trim(request()->get('hazy_name', ''));
        $hazy_name != '' && $hazy_name = emoji_encode($hazy_name);
        $page = request()->get('page', 1);
        $list = Db::name('user_respond_invitation')
            ->alias('uri')
            ->join('user us', 'uri.user_id = us.id')
            ->where('uri.re_code', 'like', "%{$hazy_name}%")
            ->where('uri.much_id', $this->much_id)
            ->order(['uri.id' => 'desc'])
            ->field('us.*,uri.re_code,uri.in_us_reward,uri.re_us_reward,uri.re_time')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]])
            ->each(function ($item, $key) {
                $userInfo = Db::name('user_invitation_code')->alias('uic')->join('user us', 'uic.user_id = us.id')->where('uic.code', $item['re_code'])->where('uic.much_id', $this->much_id)->field('us.*')->find();
                $item['in_user_head_img'] = $userInfo['user_head_sculpture'];
                $item['in_user_nickname'] = emoji_decode($userInfo['user_nick_name']);
                $item['in_user_openid'] = $userInfo['user_wechat_open_id'];
                return $item;
            });
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $this->assign('list', $list);
        $this->assign('hazy_name', emoji_decode($hazy_name));
        $this->assign('page', $page);
        return $this->fetch();
    }

    //推送消息给所有用户
    public function messagePush()
    {
        return $this->fetch();
    }

    //执行消息推送命令
    public function executeDestroyPlan()
    {
        if (request()->isPost() && request()->isAjax()) {
            //用户数据区间左
            $userIntervalLeft = request()->post('userIntervalLeft');
            //用户数据区间右
            $userIntervalRight = request()->post('userIntervalRight');
            //消息内容
            $messageContent = request()->post('messageContent');
            //模板消息开关
            $tmplMutual = request()->post('tmplMutual', 0);
            //是否出错
            $symbol = true;
            Db::name('user')
                ->where('id', '>=', $userIntervalLeft)
                ->where('id', '<=', $userIntervalRight)
                ->where('tourist', 0)
                ->where('uvirtual', 0)
                ->where('much_id', $this->much_id)
                ->field('id')
                ->order(['id' => 'asc'])
                ->chunk(10, function ($users) use ($messageContent, $tmplMutual, &$symbol) {
                    foreach ($users as $user) {
                        Db::startTrans();
                        try {
                            Db::name('user_smail')->insert(['user_id' => $user['id'], 'maring' => $messageContent, 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                            $breakpoint = true;
                            Db::commit();
                        } catch (\Exception $e) {
                            $breakpoint = false;
                            Db::rollback();
                        }
                        if ($breakpoint) {
                            if ($tmplMutual) {
                                $tmplData = [
                                    'much_id' => $this->much_id,
                                    'at_id' => 'YL0009',
                                    'user_id' => $user['id'],
                                    'page' => 'yl_welore/pages/user_smail/index',
                                    'keyword1' => '您收到一条系统发送的通知',
                                    'keyword2' => date('Y年m月d日 H:i:s', time())
                                ];
                                $tmplService = new TmplService();
                                $tmplService->add_template($tmplData);
                            }
                        } else {
                            $symbol = false;
                        }
                    }
                });
            if ($symbol) {
                return json(['code' => 1]);
            } else {
                return json(['code' => 0]);
            }
        } else {
            $this->redirect('unlawful/messagePush');
        }
    }

    //采集微信公众号文章
    public function gathering()
    {
        if (request()->isAjax()) {
            $content_url = trim(request()->param('url'));
            if (!$content_url) {
                return json(['code' => 0, 'msg' => '请提供有效的文章链接']);
            }
            
            // 验证 URL 格式
            if (!filter_var($content_url, FILTER_VALIDATE_URL)) {
                return json(['code' => 0, 'msg' => 'URL 格式不正确']);
            }
            
            // 提取域名并验证白名单（只允许微信域名）
            $urlParts = parse_url($content_url);
            $allowedDomains = ['mp.weixin.qq.com', 'weixin.qq.com', 'wx.qq.com'];
            if (!isset($urlParts['host']) || !in_array($urlParts['host'], $allowedDomains)) {
                return json(['code' => 0, 'msg' => '仅支持微信公众号文章']);
            }
            
            // 使用 cURL 替代 file_get_contents
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $content_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 10秒超时
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5); // 5秒连接超时
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
            $html = curl_exec($ch);
            
            $error = null;
            if ($html === false) {
                $error = curl_error($ch);
            }
            curl_close($ch);
            
            if ($error) {
                return json(['code' => 0, 'msg' => '获取文章内容失败: ' . $error]);
            }
            
            // 解析文章内容
            preg_match_all("/id=\"js_content\"[\\s\\S]*>([\\s\\S]*)<script/iUs", $html, $content, PREG_PATTERN_ORDER);

            if (!empty($content[1][0]) && '' != $content[1][0]) {
                $content = "<div id='js_content'>" . $content[1][0];
                $content = str_replace("data-src", "src", $content);
                $content = str_replace("preview.html", "player.html", $content);
            } else {
                $content = '';
            }

            // 移除潜在危险内容
            $content = preg_replace('/<iframe.*?><\/iframe>/si', '', $content);
            $content = preg_replace('/<script.*?>.*?<\/script>/si', '', $content);

            // 提取文章信息
            $title = '';
            preg_match_all('/var msg_title = [\"\'](.*?)[\"|\']/si', $html, $titleMatches, PREG_PATTERN_ORDER);
            if (!empty($titleMatches[1][0])) {
                $title = htmlspecialchars_decode($titleMatches[1][0]); // 文章标题
            }

            $short_title = '';
            preg_match_all('/var msg_desc = [\"\'](.*?)[\"\']/si', $html, $short_titleMatches, PREG_PATTERN_ORDER);
            if (!empty($short_titleMatches[1][0])) {
                $short_title = htmlspecialchars_decode($short_titleMatches[1][0]); // 文章副标题
            }

            $nickname = '';
            preg_match_all('/var nickname = [\"\'](.*?)[\"\']/si', $html, $nicknameMatches);
            if (!empty($nicknameMatches[1][0])) {
                $nickname = htmlspecialchars_decode($nicknameMatches[1][0]); // 公众号昵称
            }

            $msg_img = '';
            preg_match_all('/var msg_cdn_url = [\"\'](.*?)[\"\']/si', $html, $imgMatches);
            if (!empty($imgMatches[1][0])) {
                $msg_img = htmlspecialchars_decode($imgMatches[1][0]); // 封面图
            }
            
            // 构建返回数据
            $data = [
                'code' => 1,
                'title' => trim($title),
                'short_title' => trim($short_title),
                'nickname' => trim($nickname),
                'msg_img' => trim($msg_img),
                'content' => trim($content)
            ];
            
            // 验证是否成功获取内容
            if (empty($data['title']) || empty($data['content'])) {
                return json(['code' => 0, 'msg' => '无法解析文章内容，请确认链接是否正确']);
            }
            
            return json($data);
        } else {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }
    }

    //抽奖活动
    public function lottery()
    {
        if (request()->post() && request()->isAjax()) {
            $getData = input('post.getData');
            if ($getData) {
                $defaultNavigate = $this->defaultNavigate();
                $map[0] = ['confer' => $defaultNavigate['confer'], 'currency' => $defaultNavigate['currency']];
                $list = Db::name('event_raffle')->where('much_id', $this->much_id)->find();
                if (!$list) {
                    $list = [
                        'er_name' => '福利抽奖',
                        'start_time' => time(),
                        'end_time' => time(),
                        'deplete_type' => 1,
                        'deplete_score' => 20,
                        'illustrate' => '抽奖说明',
                        'turning_speed' => 30,
                        'turntable_style' => 0,
                        'free_chance' => 0,
                        'free_ad_valve' => 0,
                        'draw_restrictions' => 0,
                        'define_time' => time(),
                        'status' => 0,
                        'much_id' => $this->much_id
                    ];
                    $domain = explode(':', $_SERVER['HTTP_HOST']);
                    $absAddress = explode("index.php", $_SERVER['SCRIPT_NAME']);
                    $defaultPrizeImg = "https://{$domain[0]}{$absAddress[0]}static/disappear/prize.png";
                    $list['prize_content'] = json_encode([
                        '0' => ['odds' => 12.50, 'choose' => 0, 'prize_img' => $defaultPrizeImg, 'prize_name' => '再接再厉', 'score' => 0, 'reserve' => 999999999],
                        '1' => ['odds' => 12.50, 'choose' => 0, 'prize_img' => $defaultPrizeImg, 'prize_name' => '再接再厉', 'score' => 0, 'reserve' => 999999999],
                        '2' => ['odds' => 12.50, 'choose' => 0, 'prize_img' => $defaultPrizeImg, 'prize_name' => '再接再厉', 'score' => 0, 'reserve' => 999999999],
                        '3' => ['odds' => 12.50, 'choose' => 0, 'prize_img' => $defaultPrizeImg, 'prize_name' => '再接再厉', 'score' => 0, 'reserve' => 999999999],
                        '4' => ['odds' => 12.50, 'choose' => 0, 'prize_img' => $defaultPrizeImg, 'prize_name' => '再接再厉', 'score' => 0, 'reserve' => 999999999],
                        '5' => ['odds' => 12.50, 'choose' => 0, 'prize_img' => $defaultPrizeImg, 'prize_name' => '再接再厉', 'score' => 0, 'reserve' => 999999999],
                        '6' => ['odds' => 12.50, 'choose' => 0, 'prize_img' => $defaultPrizeImg, 'prize_name' => '再接再厉', 'score' => 0, 'reserve' => 999999999],
                        '7' => ['odds' => 12.50, 'choose' => 0, 'prize_img' => $defaultPrizeImg, 'prize_name' => '再接再厉', 'score' => 0, 'reserve' => 999999999]
                    ], JSON_FORCE_OBJECT + JSON_UNESCAPED_UNICODE);
                    Db::startTrans();
                    try {
                        $list['id'] = Db::name('event_raffle')->insertGetId($list);
                        Db::commit();
                    } catch (\Exception $e) {
                        Db::rollback();
                    }
                }
                unset($list['turntable_style']);
                unset($list['define_time']);
                unset($list['much_id']);
                $map[1] = $list;
                return json($map);
            }
            $setData = input('post.');
            $fillingId = $setData['id'];
            $fillingData['er_name'] = $setData['er_name'];
            $fillingData['start_time'] = $setData['start_time'];
            $fillingData['end_time'] = $setData['end_time'];
            $fillingData['deplete_type'] = $setData['deplete_type'];
            $fillingData['deplete_score'] = $setData['deplete_score'];
            $fillingData['illustrate'] = $setData['illustrate'];
            foreach ($setData['prize_content'] as $key => $value) {
                if (intval($setData['prize_content'][$key]['choose']) == 0) {
                    $setData['prize_content'][$key]['reserve'] = 999999999;
                }
            }
            $fillingData['prize_content'] = json_encode($setData['prize_content'], JSON_FORCE_OBJECT + JSON_UNESCAPED_UNICODE);
            $fillingData['turning_speed'] = $setData['turning_speed'];
            $fillingData['free_chance'] = $setData['free_chance'];
            $fillingData['free_ad_valve'] = $setData['free_ad_valve'];
            $fillingData['draw_restrictions'] = $setData['draw_restrictions'];
            $fillingData['status'] = $setData['status'];
            Db::startTrans();
            try {
                Db::name('event_raffle')->where('id', $fillingId)->where('much_id', $this->much_id)->update($fillingData);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            return $this->fetch();
        }
    }

    //抽奖中奖
    public function cheer()
    {
        $url = $this->defaultQuery();
        $erInfo = Db::name('event_raffle')->where('much_id', $this->much_id)->find();
        $egon = request()->get('egon', 0);
        $hazyName = request()->get('hazyName', '');
        $egon != 0 && $where['urr.delivery_status'] = $egon - 1;
        $page = request()->get('page', 1);
        $list = Db::name('user_raffle_records')
            ->alias('urr')
            ->join('user us', 'us.id = urr.user_id')
            ->where($where)
            ->where('urr.er_id', $erInfo['id'])
            ->where('urr.record_number|us.user_nick_name|us.user_wechat_open_id', 'like', "%{$hazyName}%")
            ->where('urr.much_id', $this->much_id)
            ->field('urr.*,us.user_nick_name,us.user_wechat_open_id,us.uvirtual')
            ->order(['urr.delivery_status' => 'asc', 'urr.see_time' => 'desc'])
            ->paginate(10, false, ['query' => ['s' => $url, 'erid' => $erInfo['id'], 'egon' => $egon, 'hazyName' => $hazyName]]);
        $this->assign('list', $list);
        $defaultNavigate = $this->defaultNavigate();
        $mize = ['confer' => $defaultNavigate['confer'], 'currency' => $defaultNavigate['currency']];
        $this->assign('mize', $mize);
        $this->assign('erid', $erInfo['id']);
        $this->assign('egon', $egon);
        $this->assign('hazyName', $hazyName);
        $this->assign('page', $page);
        return $this->fetch();
    }

    public function promulgate()
    {
        if (request()->post() && request()->isAjax()) {
            $getData = input('post.getData');
            if ($getData) {
                $urrid = input('post.urrid');
                $defaultNavigate = $this->defaultNavigate();
                $map[0] = ['confer' => $defaultNavigate['confer'], 'currency' => $defaultNavigate['currency']];
                $list = Db::name('user_raffle_records')->where('id', $urrid)->where('much_id', $this->much_id)->find();
                unset($list['er_id']);
                unset($list['much_id']);
                $map[1] = $list;
                return json($map);
            }
            $arbData = input('post.');
            $arbId = intval($arbData['id']);
            $hirData['address_details'] = json_encode(['name' => $arbData['name'], 'address' => $arbData['address'], 'phone' => $arbData['phone']], JSON_FORCE_OBJECT + JSON_UNESCAPED_UNICODE);
            $hirData['delivery_status'] = $arbData['status'];
            $hirData['courier_convert'] = trim($arbData['postal']);
            $urrInfo = Db::name('user_raffle_records')->where('id', $arbId)->where('much_id', $this->much_id)->find();
            if (intval($urrInfo['see_time']) == 0 && $hirData['courier_convert'] != '') {
                $hirData['see_time'] = time();
            }
            Db::startTrans();
            try {
                Db::name('user_raffle_records')->where('id', $arbId)->where('much_id', $this->much_id)->update($hirData);
                if (intval($urrInfo['see_time']) == 0 && $hirData['courier_convert'] != '') {
                    Db::name('user_smail')->insert([
                        'user_id' => $arbData['uid'],
                        'maring' => "您在抽奖中获得的物品 {$arbData['prizeName']} 奖励已派发，请到抽奖活动中查看已获得的奖品信息！",
                        'clue_time' => time(),
                        'status' => 0,
                        'much_id' => $this->much_id
                    ]);
                }
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result === true) {
                $tmplData = [
                    'much_id' => $this->much_id,
                    'at_id' => 'YL0009',
                    'user_id' => $arbData['uid'],
                    'page' => 'yl_welore/pages/user_smail/index',
                    'keyword1' => "您在抽奖活动中获得的奖品已派发",
                    'keyword2' => date('Y年m月d日 H:i:s', time())
                ];
                $tmplService = new TmplService();
                $tmplService->add_template($tmplData);
                return json(['code' => 1, 'msg' => '保存修改成功']);
            }
        } else {
            $urrid = input('get.urrid');
            $urrInfo = Db::name('user_raffle_records')->where('id', $urrid)->where('much_id', $this->much_id)->find();
            if ($urrInfo) {
                $this->assign('urrid', $urrid);
                return $this->fetch();
            } else {
                $this->error('参数错误', 'unlawful/lottery');
            }
        }
    }

    /*
     * 榜单排行
     */
    public function annunciation()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $list = Db::name('user_leaderboard')
            ->where('ranking_name', 'like', "%{$hazy_name}%")
            ->where('much_id', $this->much_id)
            ->order(['scores' => 'asc', 'id' => 'asc'])
            ->field('wont_sort', true)
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 榜单排序
     */
    public function annunciationSort()
    {
        if (request()->isPost() && request()->isAjax()) {
            $syid = request()->post('asyId');
            $scores = request()->post('dalue');
            Db::startTrans();
            try {
                Db::name('user_leaderboard')->where('id', $syid)->where('much_id', $this->much_id)->update(['scores' => $scores]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '排序成功']);
        }
    }

    /*
     * 新增榜单
     */
    public function ruannunciation()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data['ranking_name'] = trim(input('post.name'));
            $sortCondition = input('post.sortCondition/a');
            $data['wont_sort'] = json_encode($sortCondition, true);
            $data['scores'] = intval(input('post.scores'));
            $data['status'] = intval(input('post.status'));
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('user_leaderboard')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            return $this->fetch();
        }
    }

    /*
    * 编辑榜单
    */
    public function upannunciation()
    {
        if (request()->isPost() && request()->isAjax()) {
            $uplid = intval(input('post.uplid'));
            $data['ranking_name'] = trim(input('post.name'));
            $sortCondition = input('post.sortCondition/a');
            $data['wont_sort'] = json_encode($sortCondition, true);
            $data['scores'] = intval(input('post.scores'));
            $data['status'] = intval(input('post.status'));
            Db::startTrans();
            try {
                Db::name('user_leaderboard')->where('id', $uplid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $usid = input('get.usid', 0);
            if ($usid) {
                $ulInfo = Db::name('user_leaderboard')->where('id', $usid)->where('much_id', $this->much_id)->find();
                if ($ulInfo) {
                    $ulInfo['wont_sort'] = json_decode($ulInfo['wont_sort'], true);
                    $this->assign('list', $ulInfo);
                    return $this->fetch();
                } else {
                    $this->redirect('unlawful/annunciation');
                }
            } else {
                $this->redirect('unlawful/annunciation');
            }
        }
    }

    /*
     * 删除榜单
     */
    public function delAnnunciation()
    {
        if (request()->isPost() && request()->isAjax()) {
            $ecid = request()->post('ecid', 0);
            Db::startTrans();
            try {
                Db::name('user_leaderboard')->where('id', $ecid)->where('much_id', $this->much_id)->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '删除成功']);
        }
    }

    /*
     * 新人营销
     */
    public function attract()
    {
        if (request()->isPost() && request()->isAjax()) {
            $uplid = request()->post('uplid');
            $data['reward_status'] = intval(input('post.rewardStatus'));
            $data['reg_less_day'] = intval(input('post.regLessDay'));
            $data['reward_type'] = intval(input('post.rewardType'));
            $data['reward_code'] = floatval(input('post.rewardCode'));
            $data['reward_count'] = intval(input('post.rewardCount'));
            $data['tory_ids'] = trim(input('post.toryIds'));
            $data['paper_ids'] = trim(input('post.paperIds'));
            Db::startTrans();
            try {
                Db::name('new_user_task')->where('id', $uplid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $nutList = Db::name('new_user_task')->where('much_id', $this->much_id)->find();
            if (!$nutList) {
                $nutList = [
                    'reg_less_day' => 7,
                    'tory_ids' => '',
                    'paper_ids' => '',
                    'reward_type' => 0,
                    'reward_code' => 0.01,
                    'reward_count' => 1,
                    'reward_status' => 0,
                    'much_id' => $this->much_id
                ];
                Db::startTrans();
                try {
                    $nutList['id'] = Db::name('new_user_task')->insertGetId($nutList);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                }
            }
            $this->assign('list', $nutList);
            return $this->fetch();
        }
    }

    /*
     * 任务完成记录
     */
    public function attractRecord()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $list = Db::name('new_user_task_record')
            ->alias('nutar')
            ->join('user us', 'nutar.user_id=us.id', 'left')
            ->where('us.user_nick_name|us.user_wechat_open_id', 'like', "%{$hazy_name}%")
            ->where('nutar.much_id&us.much_id', $this->much_id)
            ->group('us.id')
            ->field('nutar.*,us.user_nick_name,us.user_head_sculpture,us.user_wechat_open_id,us.uvirtual')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 拦截手机
     */
    public function blockingPhone()
    {
        $egon = request()->get('egon', 0);
        $inType = request()->get('inType', 0);
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $where = [];
        switch ($egon) {
            case 1:
                $where['block_type'] = 0;
                break;
            case 2:
                $where['block_type'] = 1;
                break;
        }
        switch ($inType) {
            case 1:
                $where['intercept_type'] = 0;
                break;
            case 2:
                $where['intercept_type'] = 1;
                break;
        }
        $list = Db::name('phone_blacklist')
            ->where('block_rule', 'like', "%{$hazy_name}%")
            ->where($where)
            ->where('much_id', $this->much_id)
            ->order(['id' => 'desc'])
            ->paginate(10, false, ['query' => ['s' => $this->defaultQuery(), 'egon' => $egon, 'inType' => $inType, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('egon', $egon);
        $this->assign('inType', $inType);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 新增拦截规则
     */
    public function newBlockingPhone()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data['block_type'] = input('post.blockType');
            $data['block_rule'] = input('post.blockRule');
            $data['intercept_type'] = input('post.interceptType');
            $data['create_time'] = time();
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                Db::name('phone_blacklist')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error ,' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        }
    }

    /*
     * 删除拦截规则
     */
    public function delBlockingPhone()
    {
        if (request()->isPost() && request()->isAjax()) {
            $fid = intval(input('post.fid', 0));
            Db::startTrans();
            try {
                Db::name('phone_blacklist')->where('id', $fid)->where('much_id', $this->much_id)->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error ,' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     * 测试是否被拦截
     */
    public function testMatchPhone()
    {
        $phone = input('post.phone');
        $userService = new UserService();
        $result = $userService->BlockingPhone($phone, $this->much_id);
        if ($result > 0) {
            return json(['code' => 0, 'msg' => '手机号码已被拦截！']);
        } else {
            return json(['code' => 1, 'msg' => '手机号码状态正常！']);
        }
    }
}