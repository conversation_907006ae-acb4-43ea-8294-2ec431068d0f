<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\api\service\TmplService;
use app\api\service\UserService;
use app\common\Playful;
use app\common\Remotely;
use app\common\Suspense;
use think\Cache;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\View;

#用户管理
class User extends Base
{

    //  用户列表
    public function index()
    {
        $url = $this->defaultQuery();
        $hazy_egon = request()->get('egon', 0);
        $page = request()->get('page', 1);

        $uid = intval(input('get.uid'));
        $userName = trim(input('get.userName'));
        $openid = trim(input('get.openid'));
        $phone = trim(input('get.phone'));

        $when = [];

        if ($uid !== 0) {
            $when[] = function ($query) use ($uid) {
                $query->where('id', '=', $uid);
            };
        } else {
            $uid = '';
        }
        $userName !== '' && $when[] = function ($query) use ($userName) {
            $likeUserName = emoji_encode($userName);
            $query->where('user_nick_name', 'like', "%{$likeUserName}%");
        };
        $openid !== '' && $when[] = function ($query) use ($openid) {
            $query->where('user_wechat_open_id', 'like', "%{$openid}%");
        };
        $phone !== '' && $when[] = function ($query) use ($phone) {
            $query->where('user_phone', 'like', "%{$phone}%");
        };

        $when[] = function ($query) use ($hazy_egon) {
            switch ($hazy_egon) {
                case 1:
                    $query->where('status', '=', 1);
                    break;
                case 2:
                    $query->where('status', '=', 0);
                    break;
            }
        };

        $list = Db::name('user')
            ->where($when)
            ->where('tourist', 0)
            ->where('uvirtual', 0)
            ->where('much_id', $this->much_id)
            ->order('id', 'desc')
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $hazy_egon, 'uid' => $uid, 'userName' => $userName, 'openid' => $openid, 'phone' => $phone]]);
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $this->assign('list', $list);
        $this->assign('egon', $hazy_egon);
        $this->assign('uid', $uid);
        $this->assign('userName', $userName);
        $this->assign('openid', $openid);
        $this->assign('phone', $phone);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //  用户资料
    public function material()
    {
        $usid = request()->get('usid', '');
        if ($usid) {
            $userInfo = Db::name('user')->where('uvirtual', 0)->where('id', $usid)->where('much_id', $this->much_id)->find();
            if ($userInfo) {
                $uicInfo = Db::name('user_invitation_code')->where('user_id', $userInfo['id'])->where('much_id', $this->much_id)->find();
                $userInfo['code'] = $uicInfo['code'];
                $defaultNavigate = $this->defaultNavigate();
                $this->assign('defaultNavigate', $defaultNavigate);
                $this->assign('userInfo', $userInfo);
                $userLevel = Db::name('user_level')->where('much_id', $this->much_id)->select();
                $this->assign('userLevel', $userLevel);
                $userLevelNext = Db::name('user_level')->where('level_hierarchy', '>', $userInfo['level'])->order('level_hierarchy', 'asc')->where('much_id', $this->much_id)->find();
                $nextExperience = $userLevelNext['need_experience'];
                $nextExperience == '' && $nextExperience = 0;
                $this->assign('nextExperience', $nextExperience);
                if ($userInfo['wear_af'] != '0') {
                    $userAF = Db::name('avatar_frame')->where('id', $userInfo['wear_af'])->where('much_id', $this->much_id)->find();
                    $this->assign('userAF', $userAF);
                } else {
                    $this->assign('userAF', '');
                }
                if ($userInfo['wear_merit'] != '0') {
                    $userMedal = Db::name('medal')->where('id', $userInfo['wear_merit'])->where('much_id', $this->much_id)->find();
                    $this->assign('userMedal', $userMedal);
                } else {
                    $this->assign('userMedal', '');
                }
                $uatInfo = Db::name('user_attest')->where('user_id', $userInfo['id'])->where('much_id', $this->much_id)->value('adopt_status');
                $this->assign('uatInfo', $uatInfo);
                $userNameStyle = Db::name('special_nickname')->where('id', $userInfo['wear_special_id'])->where('much_id', $this->much_id)->value('special_style');
                $this->assign('userNameStyle', $userNameStyle);
                return $this->fetch();
            } else {
                $this->redirect('user/index');
            }
        } else {
            $this->redirect('user/index');
        }
    }

    //  用户资料更新
    public function editMaterial()
    {
        if (request()->isPost() && request()->isAjax()) {
            $slightly = request()->post();
            $data['user_nick_name'] = emoji_encode($slightly['name']);
            $data['user_head_sculpture'] = $slightly['avatar'];
            $data['level'] = $slightly['level'];
            $data['wear_merit'] = intval($slightly['medal']);
            $data['gender'] = $slightly['gender'];
            $data['autograph'] = $slightly['autograph'];
            /*
            $data['experience'] = $slightly['exp'];
            $data['honor_point'] = $slightly['glory'];
            */
            if ($slightly['phone'] != '0') {
                $data['user_phone'] = $slightly['phone'];
            }
            if (intval($slightly['resetVipEndTime']) == 1) {
                $data['vip_end_time'] = 0;
            } else {
                $data['vip_end_time'] = strtotime($slightly['vipEndTime']);
                if ($data['vip_end_time'] < 0) {
                    $data['vip_end_time'] = 0;
                }
            }
            $userOldInfo = Db::name('user')->where('id', $slightly['uid'])->where('much_id', $this->much_id)->find();
            if ($userOldInfo['user_nick_name'] != emoji_encode($slightly['name'])) {
                $userIterate = Db::name('user')->where('id', '<>', $slightly['uid'])->where('user_nick_name', emoji_encode($slightly['name']))->where('much_id', $this->much_id)->find();
                if ($userIterate) {
                    return json(['code' => 0, 'msg' => '保存失败，新更新的昵称已存在']);
                }
            }
            $data['is_enable_concern_privacy'] = intval($slightly['isEnableConcernPrivacy']);
            $data['is_enable_fans_privacy'] = intval($slightly['isEnableFansPrivacy']);
            $data['virtual_fans_num'] = intval($slightly['virtualFansNum']);
            Db::startTrans();
            try {
                /*
                $nowUserInfo = Db::name('user')->where('id', $slightly['uid'])->where('much_id', $this->much_id)->find();
                if (bcsub($slightly['exp'], $nowUserInfo['experience']) != 0) {
                    $naughtA['user_id'] = $slightly['uid'];
                    $naughtA['type'] = 0;
                    $naughtA['dot_before'] = $nowUserInfo['experience'];
                    $tempSubA = bcsub($slightly['exp'], $nowUserInfo['experience']);
                    $naughtA['points'] = abs($tempSubA);
                    $naughtA['dot_after'] = $slightly['exp'];
                    if ($tempSubA > 0) {
                        $naughtA['cypher'] = 0;
                        $naughtA['dot_cap'] = "系统赠送经验值：{$naughtA['points']}";
                    } else {
                        $naughtA['cypher'] = 1;
                        $naughtA['dot_cap'] = "系统扣除经验值：{$naughtA['points']}";
                    }
                    $naughtA['receive_time'] = time();
                    $naughtA['much_id'] = $this->much_id;
                    Db::name('user_exp_glory_logger')->insert($naughtA);
                }
                if (bcsub($slightly['glory'], $nowUserInfo['honor_point']) != 0) {
                    $naughtB['user_id'] = $slightly['uid'];
                    $naughtB['type'] = 1;
                    $naughtB['dot_before'] = $nowUserInfo['honor_point'];
                    $tempSubB = bcsub($slightly['glory'], $nowUserInfo['honor_point']);
                    $naughtB['points'] = abs($tempSubB);
                    $naughtB['dot_after'] = $slightly['glory'];
                    if ($tempSubB > 0) {
                        $naughtB['cypher'] = 0;
                        $naughtB['dot_cap'] = "系统赠送荣誉点：{$naughtB['points']}";
                    } else {
                        $naughtB['cypher'] = 1;
                        $naughtB['dot_cap'] = "系统扣除荣誉点：{$naughtB['points']}";
                    }
                    $naughtB['receive_time'] = time();
                    $naughtB['much_id'] = $this->much_id;
                    Db::name('user_exp_glory_logger')->insert($naughtB);
                }
                */
                if ($data['wear_merit'] !== 0) {
                    $umInfo = Db::name('user_medal')->where('user_id', $slightly['uid'])->where('medal_id', $data['wear_merit'])->where('much_id', $this->much_id)->find();
                    if (!$umInfo) {
                        Db::name('user_medal')->insert(['user_id' => $slightly['uid'], 'medal_id' => $data['wear_merit'], 'unlock_outlay' => 0, 'unlock_time' => time(), 'much_id' => $this->much_id]);
                    }
                }
                Db::name('user')->where('id', $slightly['uid'])->where('much_id', $this->much_id)->update($data);
                $result = true;
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            if ($result === true) {
                return json(['code' => 1, 'msg' => '保存成功']);
            }
        } else {
            $uid = request()->get('uid');
            if ($uid) {
                $userInfo = Db::name('user')->where('id', $uid)->where('much_id', $this->much_id)->find();
                if ($userInfo) {
                    $this->assign('list', $userInfo);
                    $userLevel = Db::name('user_level')->where('much_id', $this->much_id)->select();
                    $this->assign('userLevel', $userLevel);
                    $medalInfo = Db::name('medal')->where('much_id', $this->much_id)->where('status', 1)->select();
                    $this->assign('medalInfo', $medalInfo);
                    return $this->fetch();
                } else {
                    $this->error('参数错误', 'user/index');
                }
            } else {
                $this->error('参数错误', 'user/index');
            }
        }
    }

    //  钱包明细
    public function wallet()
    {
        $usid = request()->get('usid', false);
        if ($usid) {
            $userInfo = Db::name('user')->where('tourist', 0)->where('uvirtual', 0)->where('id', $usid)->where('much_id', $this->much_id)->find();
            if ($userInfo) {
                $url = $this->defaultQuery();
                $searchDetail = request()->get('searchDetail', '');
                $page = request()->get('page', 1);
                $userWalletList = Db::name('user_amount')
                    ->where('user_id', $usid)
                    ->where('solution', 'like', "%{$searchDetail}%")
                    ->where('much_id', $this->much_id)
                    ->order('id', 'desc')
                    ->field('solution,ruins_time,finance,evaluate')
                    ->paginate(15, false, ['query' => ['s' => $url, 'usid' => $usid, 'searchDetail' => $searchDetail]]);
                $this->assign('userInfo', $userInfo);
                $this->assign('list', $userWalletList);
                $defaultNavigate = $this->defaultNavigate();
                $this->assign('defaultNavigate', $defaultNavigate);
                $this->assign('searchDetail', $searchDetail);
                $pageCount = $userWalletList->count();
                if ($pageCount < 1 && $page != 1) {
                    $this->emptyDataRedirect(['usid' => $usid, 'searchDetail' => $searchDetail]);
                }
                $this->assign('page', $page);
                return $this->fetch();
            } else {
                $this->redirect('user/index');
            }
        } else {
            $this->redirect('user/index');
        }
    }

    //  修改用户额度
    public function alterunt()
    {
        if (request()->isPost() && request()->isAjax()) {
            $usid = request()->post('usid');
            $genus = request()->post('genus');
            $cipher = request()->post('cipher');
            $solution = request()->post('solution');
            Db::startTrans();
            try {
                switch ($genus) {
                    case 0:
                        $userA = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->setInc('conch', $cipher);
                        $userB = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        $amountData = [
                            'user_id' => $usid,
                            'category' => 3,
                            'finance' => $cipher,
                            'poem_fraction' => $userA['fraction'],
                            'poem_conch' => $userA['conch'],
                            'surplus_fraction' => $userB['fraction'],
                            'surplus_conch' => $userB['conch'],
                            'ruins_time' => time(),
                            'solution' => $solution,
                            'evaluate' => 0,
                            'much_id' => $this->much_id
                        ];
                        Db::name('user_amount')->insert($amountData);
                        break;
                    case 1:
                        $userA = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->setDec('conch', $cipher);
                        $userB = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        $amountData = [
                            'user_id' => $usid,
                            'category' => 2,
                            'finance' => -$cipher,
                            'poem_fraction' => $userA['fraction'],
                            'poem_conch' => $userA['conch'],
                            'surplus_fraction' => $userB['fraction'],
                            'surplus_conch' => $userB['conch'],
                            'ruins_time' => time(),
                            'solution' => $solution,
                            'evaluate' => 0,
                            'much_id' => $this->much_id
                        ];
                        Db::name('user_amount')->insert($amountData);
                        break;
                    case 2:
                        $userA = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->setInc('fraction', $cipher);
                        $userB = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        $amountData = [
                            'user_id' => $usid,
                            'category' => 3,
                            'finance' => $cipher,
                            'poem_fraction' => $userA['fraction'],
                            'poem_conch' => $userA['conch'],
                            'surplus_fraction' => $userB['fraction'],
                            'surplus_conch' => $userB['conch'],
                            'ruins_time' => time(),
                            'solution' => $solution,
                            'evaluate' => 1,
                            'much_id' => $this->much_id
                        ];
                        Db::name('user_amount')->insert($amountData);
                        break;
                    case 3:
                        $userA = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->setDec('fraction', $cipher);
                        $userB = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        $amountData = [
                            'user_id' => $usid,
                            'category' => 2,
                            'finance' => -$cipher,
                            'poem_fraction' => $userA['fraction'],
                            'poem_conch' => $userA['conch'],
                            'surplus_fraction' => $userB['fraction'],
                            'surplus_conch' => $userB['conch'],
                            'ruins_time' => time(),
                            'solution' => $solution,
                            'evaluate' => 1,
                            'much_id' => $this->much_id
                        ];
                        Db::name('user_amount')->insert($amountData);
                        break;
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => ($genus == 0 || $genus == 2) ? '充值成功' : '扣除成功']);
        }
    }

    /*
     * 修改经验荣誉获得记录
     */
    public function update_experience_and_honor_point()
    {
        if (request()->isPost() && request()->isAjax()) {
            $usid = request()->post('usid');
            $genus = request()->post('genus');
            $cipher = request()->post('cipher');
            $solution = request()->post('solution');
            Db::startTrans();
            try {
                switch ($genus) {
                    case 0:
                        $userA = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->setInc('experience', $cipher);
                        $userB = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        $amountData = [
                            'user_id' => $usid,
                            'type' => 0,
                            'cypher' => 0,
                            'dot_before' => $userA['experience'],
                            'points' => $cipher,
                            'dot_after' => $userB['experience'],
                            'dot_cap' => $solution,
                            'receive_time' => time(),
                            'much_id' => $this->much_id
                        ];
                        Db::name('user_exp_glory_logger')->insert($amountData);
                        break;
                    case 1:
                        $userA = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->setDec('experience', $cipher);
                        $userB = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        $amountData = [
                            'user_id' => $usid,
                            'type' => 0,
                            'cypher' => 1,
                            'dot_before' => $userA['experience'],
                            'points' => $cipher,
                            'dot_after' => $userB['experience'],
                            'dot_cap' => $solution,
                            'receive_time' => time(),
                            'much_id' => $this->much_id
                        ];
                        Db::name('user_exp_glory_logger')->insert($amountData);
                        break;
                    case 2:
                        $userA = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->setInc('honor_point', $cipher);
                        $userB = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        $amountData = [
                            'user_id' => $usid,
                            'type' => 1,
                            'cypher' => 0,
                            'dot_before' => $userA['honor_point'],
                            'points' => $cipher,
                            'dot_after' => $userB['honor_point'],
                            'dot_cap' => $solution,
                            'receive_time' => time(),
                            'much_id' => $this->much_id
                        ];
                        Db::name('user_exp_glory_logger')->insert($amountData);
                        break;
                    case 3:
                        $userA = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->setDec('honor_point', $cipher);
                        $userB = Db::name('user')->where('id', $usid)->where('uvirtual', 0)->where('much_id', $this->much_id)->find();
                        $amountData = [
                            'user_id' => $usid,
                            'type' => 1,
                            'cypher' => 1,
                            'dot_before' => $userA['honor_point'],
                            'points' => $cipher,
                            'dot_after' => $userB['honor_point'],
                            'dot_cap' => $solution,
                            'receive_time' => time(),
                            'much_id' => $this->much_id
                        ];
                        Db::name('user_exp_glory_logger')->insert($amountData);
                        break;
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    //  封禁用户
    public function pulate()
    {
        if (request()->isAjax() && request()->isPost()) {
            $nuid = request()->post('nuid');
            $ntheir = request()->post('ntheir');
            $data['status'] = $ntheir == 1 ? 1 : 0;
            $cause = trim(request()->post('cause', ''));
            $data['forbid_prompt'] = $cause != '' ? $cause : null;
            Db::startTrans();
            try {
                Db::name('user')->where('id', $nuid)->where('uvirtual', 0)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => $ntheir == 1 ? '解封成功' : '封禁成功']);
        }
    }

    //  游客列表
    public function traveler()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $list = Db::name('user')
            ->where('user_nick_name|user_wechat_open_id', 'like', "%{$hazy_name}%")
            ->where('tourist', 1)
            ->where('uvirtual', 0)
            ->where('much_id', $this->much_id)
            ->order('id', 'desc')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }


    //  超级管理员
    public function inspect()
    {
        $url = $this->defaultQuery();
        $list = Db::name('user_maker')
            ->alias('usme')
            ->join('user us', 'usme.user_open_id=us.user_wechat_open_id', 'left')
            ->where('usme.much_id', $this->much_id)
            ->order('usme.scores', 'asc')
            ->order('usme.id', 'asc')
            ->field('usme.*,us.user_head_sculpture,us.user_nick_name,us.user_wechat_open_id,us.uvirtual')
            ->paginate(10, false, ['query' => ['s' => $url]]);
        $this->assign('list', $list);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //  超级管理员排序
    public function slpect()
    {
        if (request()->isPost() && request()->isAjax()) {
            $syid = request()->post('asyId');
            $scores = request()->post('dalue');
            $result = Db::name('user_maker')
                ->where('id', $syid)
                ->where('much_id', $this->much_id)
                ->update(['scores' => $scores]);
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '保存成功']);
            } else {
                return json(['code' => 0, 'msg' => '保存失败']);
            }
        }
    }

    //  新增超级管理员
    public function ruinspect()
    {
        if (request()->isPost() && request()->isAjax()) {
            $data = request()->post();
            $usmaker = Db::name('user_maker')
                ->where('user_open_id', $data['user_open_id'])
                ->where('much_id', $this->much_id)
                ->find();
            if (!$usmaker) {
                $data['found_time'] = time();
                $data['much_id'] = $this->much_id;
                $result = Db::name('user_maker')->insert($data);
                if ($result != false) {
                    return json(['code' => 1, 'msg' => '保存成功']);
                } else {
                    return json(['code' => 0, 'msg' => '保存失败']);
                }
            } else {
                return json(['code' => 0, 'msg' => '保存失败，该用户已是超级管理员']);
            }
        }
        return $this->fetch();
    }

    //  更改超级管理员状态
    public function slpust()
    {
        $usid = request()->post('usid');
        $status = request()->post('status');
        $result = Db::name('user_maker')
            ->where('id', $usid)
            ->where('much_id', $this->much_id)
            ->update(['status' => $status]);
        if ($result !== false) {
            return json(['code' => 1, 'msg' => $status == 0 ? '状态已更改为禁用' : '状态已更改为正常']);
        } else {
            return json(['code' => 0, 'msg' => '状态更改失败']);
        }
    }

    //  删除超级管理员
    public function spectlint()
    {
        if (request()->isPost() && request()->isAjax()) {
            $usid = request()->post('ecid');
            $result = Db::name('user_maker')
                ->where('id', $usid)
                ->where('much_id', $this->much_id)
                ->delete();
            if ($result !== false) {
                return json(['code' => 1, 'msg' => '删除成功']);
            } else {
                return json(['code' => 0, 'msg' => '删除失败']);
            }
        }
    }

    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, [], 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    //  虚拟用户
    public function theoretic()
    {
        $url = $this->defaultQuery();
        $hazy_name = trim(input('get.hazy_name', ''));
        $searchName = emoji_encode($hazy_name);
        //  虚拟用户openid随机生成
        $userRejectNull = Db::name('user')
            ->where('uvirtual', 1)
            ->where('user_wechat_open_id', null)
            ->column('id');
        if ($userRejectNull) {
            for ($i = 0; $i < count($userRejectNull); $i++) {
                Db::startTrans();
                try {
                    Db::name('user')->where('id', $userRejectNull[$i])->update(['user_wechat_open_id' => Suspense::getRandomCode()]);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                }
            }
        }
        $list = Db::name('user')
            ->where('user_nick_name', 'like', "%{$searchName}%")
            ->where('uvirtual', 1)
            ->where('much_id', $this->much_id)
            ->order('id', 'desc')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $tribute = Db::name('tribute')->where('much_id', $this->much_id)->order('scores')->select();
        $this->assign('tribute', $tribute);
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $this->assign('hazy_name', $hazy_name);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //  新增虚拟用户
    public function rutheoretic()
    {
        if (request()->isPost() && request()->isAjax()) {
            $slightly = request()->post();
            $data['user_nick_name'] = emoji_encode($slightly['name']);
            $data['user_head_sculpture'] = $slightly['avatar'];
            $data['user_wechat_open_id'] = Suspense::getRandomCode();
            $data['level'] = $slightly['level'];
            $data['uvirtual'] = 1;
            $data['gender'] = $slightly['gender'];
            $data['user_reg_time'] = time();
            $data['vip_end_time'] = strtotime($slightly['vipEndTime']);
            $data['virtual_fans_num'] = intval($slightly['virtualFansNum']);
            $data['autograph'] = $slightly['autograph'];
            $data['much_id'] = $this->much_id;
            $userIterate = Db::name('user')->where('user_nick_name', emoji_encode($slightly['name']))->where('much_id', $this->much_id)->find();
            if ($userIterate) {
                return json(['code' => 0, 'msg' => '保存失败，用户昵称已存在']);
            }
            Db::startTrans();
            try {
                Db::name('user')->insert($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $userLevel = Db::name('user_level')->where('much_id', $this->much_id)->select();
            $this->assign('userLevel', $userLevel);
            return $this->fetch();
        }
    }

    //  虚拟用户发布帖子
    public function reticraphic()
    {
        //  拨打电话
        $callPhonePluginKey = Remotely::isUnLockProperty(base64_decode('5LiA6ZSu5ouo5Y+3'));
        //  网络云盘
        $netDiscPluginKey = Remotely::isUnLockProperty(base64_decode('572R55uY5YiX6KGo'));
        //  视频解析
        $videoParsePluginKey = Remotely::isUnLockProperty(base64_decode('6KeG6aKR6Kej5p6Q'));
        if (request()->isPost() && request()->isAjax()) {
            $many = request()->post();
            $rectify = $many['postData'];
            $data['user_id'] = $rectify['userid'];
            $data['tory_id'] = intval($rectify['toryid']);
            if ($data['tory_id'] === 0) {
                return json(['code' => 0, 'msg' => '当前未选择圈子，无法发布内容！']);
            }
            $data['study_title'] = emoji_encode($rectify['title']);
            if (trim($rectify['titleColor']) === '') {
                $rectify['titleColor'] = '#000000';
            }
            $data['study_title_color'] = $rectify['titleColor'];
            $data['is_buy'] = intval($rectify['isBuy']);
            if ($data['is_buy'] > 1 && $netDiscPluginKey) {
                if (intval($rectify['fileId']) === 0) {
                    return json(['code' => 0, 'msg' => '请选择付费文件！']);
                }
            }
            $data['buy_price_type'] = intval($rectify['buyPriceType']);
            $data['buy_price'] = $rectify['buyPrice'];
            if ($callPhonePluginKey) {
                $data['call_phone'] = $rectify['callPhone'];
            } else {
                $data['call_phone'] = null;
            }
            $data['img_show_type'] = intval($rectify['imgShowType']);
            $data['study_content'] = $this->safe_html(emoji_encode($rectify['content']));
            $data['study_type'] = intval($rectify['patype']);
            $data['video_type'] = $rectify['videoType'];
            $data['topping_time'] = 0;
            switch ($data['study_type']) {
                case 0:
                    //  图文帖
                    $data['study_video'] = null;
                    $data['study_voice'] = null;
                    $data['study_voice_time'] = 0;
                    if ($rectify['multipleImg']) {
                        $imagePart = json_encode($rectify['multipleImg'], 320);
                        $data['image_part'] = $imagePart != '[""]' ? $imagePart : '[]';
                    } else {
                        $data['image_part'] = '[]';
                    }
                    break;
                case 1:
                    //  语音帖
                    $data['study_voice'] = trim($rectify['voice']) != '' ? $rectify['voice'] : null;
                    $data['study_voice_time'] = $rectify['voiceTime'] != 0 ? $rectify['voiceTime'] : 1;
                    $data['study_video'] = null;
                    $data['image_part'] = '[]';
                    break;
                case 2:
                    //  视频帖
                    $data['study_voice'] = null;
                    $data['study_voice_time'] = 0;
                    switch (intval($data['video_type'])) {
                        case 0:
                            $data['study_video'] = $rectify['video'];
                            $data['third_part_vid'] = null;
                            break;
                        case 1:
                        case 2:
                            $data['study_video'] = null;
                            $data['third_part_vid'] = $rectify['tencentVideoVid'];
                            break;
                    }
                    $data['image_part'] = "[\"{$rectify['videoImg']}\"]";
                    break;
                case 3:
                case 4:
                case 5:
                    //  活动帖 投票帖
                    $data['study_video'] = null;
                    $data['study_voice'] = null;
                    $data['study_voice_time'] = 0;
                    if ($rectify['multipleImg']) {
                        $imagePart = json_encode($rectify['multipleImg'], 320);
                        $data['image_part'] = $imagePart != '[""]' ? $imagePart : '[]';
                    } else {
                        $data['image_part'] = '[]';
                    }
                    $data['vote_deadline'] = strtotime(date($rectify['voteDeadline']));
                    break;
                case 6:
                    $feedToken = trim($rectify['feedToken']);
                    break;
            }
            $adapterTime = trim($rectify['adapterTime']);
            if ($adapterTime) {
                $adapterTimeFormat = intval(floatval($rectify['adapterTime']) / 1000);
            } else {
                $adapterTimeFormat = time();
            }
            $data['adapter_time'] = $adapterTimeFormat;
            $data['prove_time'] = $adapterTimeFormat;
            $data['study_status'] = 1;
            $data['is_open'] = 1;
            $data['much_id'] = $this->much_id;
            Db::startTrans();
            try {
                if ($rectify['gambit'] != '') {
                    //    查询是否有此话题
                    $gambitInfo = Db::name('gambit')->where('gambit_name', $rectify['gambit'])->where('much_id', $this->much_id)->find();
                    //  如果有且已经删除则改变状态
                    if ($gambitInfo && $gambitInfo['is_del'] == 1) {
                        Db::name('gambit')->where('id', $gambitInfo['id'])->where('gambit_name', $rectify['gambit'])->where('much_id', $this->much_id)->update(['add_time' => time(), 'is_del' => 0]);
                    }
                    //  没有话题则新增一个话题
                    if (!$gambitInfo) {
                        $data['tg_id'] = Db::name('gambit')->insertGetId(['gambit_name' => $rectify['gambit'], 'add_time' => time(), 'scores' => 0, 'is_del' => 0, 'much_id' => $this->much_id]);
                    }
                    //  话题编号
                    if (!$data['tg_id']) {
                        $data['tg_id'] = $gambitInfo['id'];
                    }
                } else {
                    $data['tg_id'] = 0;
                }
                $paperId = Db::name('paper')->insertGetId($data);
                if ($data['is_buy'] > 1 && $netDiscPluginKey) {
                    $netDiscSellData = ['pa_id' => $paperId, 'nc_id' => intval($rectify['ncId']), 'nb_id' => intval($rectify['fileId']), 'is_sell' => intval($rectify['fileIsSell']), 'much_id' => $this->much_id];
                    Db::name('netdisc_sell')->insert($netDiscSellData);
                }
                //  是否是活动帖
                if ($data['study_type'] == 3) {
                    Db::name('brisk_team')->insert([
                        'paper_id' => $paperId,
                        'is_approve' => $rectify['briskApprove'],
                        'brisk_address' => $rectify['briskAddress'],
                        'brisk_address_latitude' => $rectify['briskAddressLatitude'],
                        'brisk_address_longitude' => $rectify['briskAddressLongitude'],
                        'start_time' => strtotime(date($rectify['dateStartTime'])),
                        'end_time' => strtotime(date($rectify['dateEndTime'])),
                        'number_of_people' => $rectify['numberOfPeople'],
                        'much_id' => $this->much_id
                    ]);
                }
                if ($data['study_type'] === 4 || $data['study_type'] === 5) {
                    for ($i = 0; $i < count($many['shocked']['votes']); $i++) {
                        Db::name('paper_vote')->insert([
                            'paper_id' => $paperId,
                            'ballot_name' => trim($many['shocked']['votes'][$i]),
                            'cheat_ballot' => intval($many['shocked']['pretends'][$i]),
                            'much_id' => $this->much_id
                        ]);
                    }
                }
                if ($data['study_type'] === 6) {
                    Db::name('paper_wechat_channel_video')->insert([
                        'paper_id' => $paperId,
                        'feed_token' => $feedToken,
                        'create_time' => time(),
                        'much_id' => $this->much_id
                    ]);
                }

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            Cache::clear("globalIndexCache_{$this->much_id}");
            return json(['code' => 1, 'msg' => '发布成功']);
        }

        $usid = request()->get('usid', '');
        if ($usid) {
            $getUser = Db::name('user')->where('id', $usid)->where('uvirtual', 1)->where('much_id', $this->much_id)->find();
            if ($getUser) {
                $this->assign('userInfo', $getUser);
                $toryInfo = Db::name('territory')->where('status', 1)->where('much_id', $this->much_id)->order('scores')->select();
                $this->assign('toryInfo', $toryInfo);
                $defaultNavigate = $this->defaultNavigate();
                $this->assign('defaultNavigate', $defaultNavigate);
                $this->assign('callPhonePluginKey', $callPhonePluginKey);
                $this->assign('netDiscPluginKey', $netDiscPluginKey);
                $this->assign('videoParsePluginKey', $videoParsePluginKey);
                return $this->fetch();
            } else {
                $this->error('参数错误', 'user/theoretic');
            }
        } else {
            $this->error('参数错误', 'user/theoretic');
        }
    }


    //  虚拟用户回复帖子
    public function reticrpaper()
    {
        if (request()->isPost() && request()->isAjax()) {
            //  回复帖子编号
            $data['paper_id'] = request()->post('paperId');
            $paperInfo = Db::name('paper')->where('id', $data['paper_id'])->where('study_status', 1)->where('whether_delete', 0)->where('much_id', $this->much_id)->find();
            if ($paperInfo) {
                //  虚拟用户编号
                $data['user_id'] = request()->post('userId');
                //  回复帖子类型
                $data['reply_type'] = 0;
                //  当前楼层
                $phase = Db::name('paper_reply')->where('much_id', $this->much_id)->where('paper_id', $data['paper_id'])->max('phase');
                if ($phase == 0) {
                    $data['phase'] = 2;
                } else {
                    $data['phase'] = $phase + 1;
                }
                //  回复内容
                $data['reply_content'] = emoji_encode(request()->post('content'));
                //  回复图片
                $image_part[] = request()->post('multipleImg', '');
                if ($image_part[0] != '') {
                    $data['image_part'] = json_encode($image_part, true);
                } else {
                    $data['image_part'] = '[]';
                }
                $apterTime = trim(input('post.apterTime'));
                if ($apterTime) {
                    $apterTimeFormat = intval(floatval($apterTime) / 1000);
                } else {
                    $apterTimeFormat = time();
                }
                //  回复时间
                $data['apter_time'] = $apterTimeFormat;
                //  审核时间
                $data['prove_time'] = time();
                //  多用户标识
                $data['much_id'] = $this->much_id;
                Db::startTrans();
                try {
                    $replyId = Db::name('paper_reply')->insertGetId($data);
                    Db::name('paper')->where('id', $data['paper_id'])->where('much_id', $this->much_id)->setInc('study_repount', 1);
                    $page_title = $paperInfo['study_title'] == '' ? subtext($paperInfo['study_content'], 10) : subtext($paperInfo['study_title'], 10);
                    $fa_info = Db::name('paper_reply')->where('id', $replyId)->where('much_id', $this->much_id)->find();
                    //  回复详情
                    $hui_title = subtext($fa_info['reply_content'], 10);
                    if (empty($hui_title)) {
                        if ($fa_info['reply_type'] == 0) {
                            $hui_title = '[一张图片]';
                        }
                        if ($fa_info['reply_type'] == 1) {
                            $hui_title = '[一段语音]';
                        }
                    }
                    if (empty($page_title)) {
                        if ($paperInfo['study_type'] == 0) {
                            $page_title = '[图片帖子]';
                        }
                        if ($paperInfo['study_type'] == 1) {
                            $page_title = '[语音帖子]';
                        }
                        if ($paperInfo['study_type'] == 2) {
                            $page_title = '[视频帖子]';
                        }
                    }
                    $getUser = Db::name('user')->where('id', $data['user_id'])->where('uvirtual', 1)->where('much_id', $this->much_id)->find();
                    $tmplData = [
                        'much_id' => $this->much_id,
                        'at_id' => 'YL0001',
                        'user_id' => $paperInfo['user_id'],
                        'page' => 'yl_welore/pages/packageA/article/index?id=' . $paperInfo['id'] . '&type=' . $paperInfo['study_type'],
                        'keyword1' => emoji_encode($page_title),
                        'keyword2' => emoji_encode($getUser['user_nick_name']),
                        'keyword3' => $hui_title,
                        'keyword4' => date('Y年m月d日 H:i:s', time()),
                    ];
                    $tmplService = new TmplService();
                    $tmplService->add_template($tmplData);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                }
                Cache::clear("globalIndexCache_{$this->much_id}");
                return json(['code' => 1, 'msg' => '回贴成功，请在小程序里查看回复内容！']);
            } else {
                return json(['code' => 0, 'msg' => '帖子ID填写有误']);
            }
        } else {
            $usid = request()->get('usid', '');
            if ($usid) {
                $getUser = Db::name('user')->where('id', $usid)->where('uvirtual', 1)->where('much_id', $this->much_id)->find();
                if ($getUser) {
                    $this->assign('userInfo', $getUser);
                    $toryInfo = Db::name('territory')->where('much_id', $this->much_id)->order('scores')->select();
                    $this->assign('toryInfo', $toryInfo);
                    return $this->fetch();
                } else {
                    $this->error('参数错误', 'user/theoretic');
                }
            } else {
                $this->error('参数错误', 'user/theoretic');
            }
        }
    }


    //  用户充值明细
    public function water()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $hazy_egon = request()->get('egon', 0);
        switch ($hazy_egon) {
            case 0:
                $where = [];
                break;
            case 1:
                $where['ul.status'] = 0;
                break;
            case 2:
                $where['ul.status'] = 1;
                break;
        }
        $list = Db::name('user_serial')
            ->alias('ul')
            ->join('user us', 'ul.user_id=us.id', 'left')
            ->where('ul.single_mark|us.user_nick_name|us.user_wechat_open_id', 'like', "%$hazy_name%")
            ->where('us.uvirtual', 0)
            ->where($where)
            ->where('ul.much_id', $this->much_id)
            ->order('ul.id', 'desc')
            ->field('ul.*,us.user_nick_name,us.user_wechat_open_id')
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $hazy_egon, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('egon', $hazy_egon);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //  邀请排行
    public function engage()
    {
        $url = $this->defaultQuery();
        $hazy_name = trim(request()->get('hazy_name', ''));
        $hazy_name != '' && $hazy_name = emoji_encode($hazy_name);
        $hazy_egon = request()->get('egon', 0);
        switch ($hazy_egon) {
            //  全部时间
            case 0:
                $where = [];
                break;
            //  本周排行
            case 1:
                $where['uri.re_time'] = ['>=', $this->this_monday()];
                break;
            //  本月排行
            case 2:
                $where['uri.re_time'] = ['>=', $this->month_firstday()];
                break;
            //  本年排行
            case 3:
                $threeYear = mktime(0, 0, 0, 1, 1, date('Y'));
                $where['uri.re_time'] = ['>=', $threeYear];
                break;
        }
        $list = Db::name('user_invitation_code')
            ->alias('uic')
            ->join('user us', 'uic.user_id=us.id', 'left')
            ->join('user_respond_invitation uri', 'uic.code=uri.re_code', 'left')
            ->where('us.user_nick_name|us.user_wechat_open_id|uic.code', 'like', "%{$hazy_name}%")
            ->where($where)
            ->where('us.tourist', 0)
            ->where('us.uvirtual', 0)
            ->where('uic.much_id', $this->much_id)
            ->group('uri.re_code', 'us.id')
            ->field('us.user_nick_name,us.user_wechat_open_id,uic.code,count(uri.re_code) as uri_people,sum(uri.in_us_reward) as uri_reward')
            ->having('count(uri.re_code) > 0')
            ->order(['uri_people' => 'desc', 'us.id' => 'asc'])
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $hazy_egon, 'hazy_name' => $hazy_name]]);
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $this->assign('list', $list);
        $this->assign('hazy_name', emoji_decode($hazy_name));
        $this->assign('egon', $hazy_egon);
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //  虚拟用户赠送礼物
    public function virtualSendGifts()
    {
        if (request()->isPost() && request()->isAjax()) {
            //    礼物编号
            $data['li_id'] = request()->post('tributeNumber');
            //    赠送者编号
            $data['uid'] = request()->post('virtualUser');
            //    获赠者openid
            $userOpenId = request()->post('userOpenid');
            //    获赠着信息
            $getUserInfo = Db::name('user')->where('user_wechat_open_id', $userOpenId)->where('much_id', $this->much_id)->find();
            if ($getUserInfo) {
                //    获赠着编号
                $data['user_id'] = $getUserInfo['id'];
                //    礼物数量
                $data['num'] = request()->post('tributeQuantity');
                //    多用户标识
                $data['much_id'] = $this->much_id;
                //    创建接口
                $userServer = new UserService();
                //    虚拟用户给真实用户送礼
                $result = $userServer->user_reward($data);
                if ($result['status'] == 'success') {
                    return json(['code' => 1, 'msg' => $result['msg']]);
                } else {
                    return json(['code' => 0, 'msg' => $result['msg']]);
                }
            } else {
                return json(['code' => 0, 'msg' => 'openid输入错误']);
            }
        } else {
            $this->redirect('user/theoretic');
        }
    }

    //  积分排行榜
    public function fraction_ranking()
    {
        $url = $this->defaultQuery();
        $hazy_name = trim(request()->get('hazy_name', ''));
        $hazy_name != '' && $hazy_name = emoji_encode($hazy_name);
        $hazy_egon = request()->get('egon', 0);
        $page = request()->get('page', 1);
        switch ($hazy_egon) {
            //  全部时间
            case 0:
                $where = [];
                break;
            //  今日排行
            case 1:
                $where['ua.ruins_time'] = ['between time', ['today', 'tomorrow']];
                break;
            //  本周排行
            case 2:
                $where['ua.ruins_time'] = ['>=', $this->this_monday()];
                break;
            //  本月排行
            case 3:
                $where['ua.ruins_time'] = ['>=', $this->month_firstday()];
                break;
            //  本年排行
            case 4:
                $threeYear = mktime(0, 0, 0, 1, 1, date('Y'));
                $where['ua.ruins_time'] = ['>=', $threeYear];
                break;
        }
        $subQuery = Db::name('user_amount')
            ->alias('ua')
            ->join('user us', 'ua.user_id = us.id', 'right')
            ->where($where)
            ->where('ua.evaluate', 1)
            ->where('user_nick_name|user_wechat_open_id', 'like', "%{$hazy_name}%")
            ->where('us.tourist', 0)
            ->where('us.uvirtual', 0)
            ->where('us.much_id', $this->much_id)
            ->order(['ua.ruins_time' => 'desc'])
            ->field('us.*,ua.id as uaid,ua.user_id,ua.surplus_fraction')
            ->buildSql();
        $list = Db::table($subQuery . ' a')
            ->group('a.id')
            ->order(['a.surplus_fraction' => 'desc', 'uaid' => 'asc'])
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $hazy_egon, 'hazy_name' => $hazy_name]])
            ->each(function ($item, $key) use ($hazy_egon) {
                switch ($hazy_egon) {
                    //  全部时间
                    case 0:
                        $where = [];
                        break;
                    //  今日排行
                    case 1:
                        $where['ruins_time'] = ['between time', ['today', 'tomorrow']];
                        break;
                    //  本周排行
                    case 2:
                        $where['ruins_time'] = ['>=', $this->this_monday()];
                        break;
                    //  本月排行
                    case 3:
                        $where['ruins_time'] = ['>=', $this->month_firstday()];
                        break;
                    //  本年排行
                    case 4:
                        $threeYear = mktime(0, 0, 0, 1, 1, date('Y'));
                        $where['ruins_time'] = ['>=', $threeYear];
                        break;
                }
                $uaInfo = Db::name('user_amount')->where('user_id', $item['id'])->where($where)->where('evaluate', 1)->where('much_id', $this->much_id)->field('sum(finance) as sance')->find();
                $item['sance'] = $uaInfo['sance'];
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('egon', $hazy_egon);
        $this->assign('hazy_name', emoji_decode($hazy_name));
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //  贝壳排行榜
    public function conch_ranking()
    {
        $url = $this->defaultQuery();
        $hazy_name = trim(request()->get('hazy_name', ''));
        $hazy_name != '' && $hazy_name = emoji_encode($hazy_name);
        $hazy_egon = request()->get('egon', 0);
        $page = request()->get('page', 1);
        switch ($hazy_egon) {
            //  全部时间
            case 0:
                $where = [];
                break;
            //  今日排行
            case 1:
                $where['ua.ruins_time'] = ['between time', ['today', 'tomorrow']];
                break;
            //  本周排行
            case 2:
                $where['ua.ruins_time'] = ['>=', $this->this_monday()];
                break;
            //  本月排行
            case 3:
                $where['ua.ruins_time'] = ['>=', $this->month_firstday()];
                break;
            //  本年排行
            case 4:
                $threeYear = mktime(0, 0, 0, 1, 1, date('Y'));
                $where['ua.ruins_time'] = ['>=', $threeYear];
                break;
        }
        $subQuery = Db::name('user_amount')
            ->alias('ua')
            ->join('user us', 'ua.user_id = us.id', 'right')
            ->where($where)
            ->where('ua.evaluate', 0)
            ->where('user_nick_name|user_wechat_open_id', 'like', "%{$hazy_name}%")
            ->where('us.tourist', 0)
            ->where('us.uvirtual', 0)
            ->where('us.much_id', $this->much_id)
            ->order(['ua.ruins_time' => 'desc'])
            ->field('us.*,ua.id as uaid,ua.user_id,ua.surplus_conch')
            ->buildSql();
        $list = Db::table($subQuery . ' a')
            ->group('a.id')
            ->order(['a.surplus_conch' => 'desc', 'uaid' => 'asc'])
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $hazy_egon, 'hazy_name' => $hazy_name]])
            ->each(function ($item, $key) use ($hazy_egon) {
                switch ($hazy_egon) {
                    //  全部时间
                    case 0:
                        $where = [];
                        break;
                    //  今日排行
                    case 1:
                        $where['ruins_time'] = ['between time', ['today', 'tomorrow']];
                        break;
                    //  本周排行
                    case 2:
                        $where['ruins_time'] = ['>=', $this->this_monday()];
                        break;
                    //  本月排行
                    case 3:
                        $where['ruins_time'] = ['>=', $this->month_firstday()];
                        break;
                    //  本年排行
                    case 4:
                        $threeYear = mktime(0, 0, 0, 1, 1, date('Y'));
                        $where['ruins_time'] = ['>=', $threeYear];
                        break;
                }
                $uaInfo = Db::name('user_amount')->where('user_id', $item['id'])->where($where)->where('evaluate', 0)->where('much_id', $this->much_id)->field('sum(finance) as sance')->find();
                $item['sance'] = $uaInfo['sance'];
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('egon', $hazy_egon);
        $this->assign('hazy_name', emoji_decode($hazy_name));
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //  获取本周一的时间戳
    private function this_monday($timestamp = 0, $is_return_timestamp = true)
    {
        static $cache;
        $id = $timestamp . $is_return_timestamp;
        if (!isset($cache[$id])) {
            if (!$timestamp)
                $timestamp = time();
            $monday_date = date('Y-m-d', $timestamp - 86400 * date('w', $timestamp) + (date('w', $timestamp) > 0 ? 86400 : -/*6*86400*/518400));
            if ($is_return_timestamp) {
                $cache[$id] = strtotime($monday_date);
            } else {
                $cache[$id] = $monday_date;
            }
        }
        return $cache[$id];
    }

    //  获取本月的第一天
    private function month_firstday($timestamp = 0, $is_return_timestamp = true)
    {
        static $cache;
        $id = $timestamp . $is_return_timestamp;
        if (!isset($cache[$id])) {
            if (!$timestamp)
                $timestamp = time();
            $firstday = date('Y-m-d', mktime(0, 0, 0, date('m', $timestamp), 1, date('Y', $timestamp)));
            if ($is_return_timestamp) {
                $cache[$id] = strtotime($firstday);
            } else {
                $cache[$id] = $firstday;
            }
        }
        return $cache[$id];
    }

    //  送礼排行榜
    public function gift_ranking()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $hazy_egon = request()->get('egon', 0);
        $page = request()->get('page', 1);
        switch ($hazy_egon) {
            //  全部时间
            case 0:
                $where = [];
                break;
            //  本周排行
            case 1:
                $where['sub.bute_time'] = ['>=', $this->this_monday()];
                break;
            //  本月排行
            case 2:
                $where['sub.bute_time'] = ['>=', $this->month_firstday()];
                break;
            //  本年排行
            case 3:
                $threeYear = mktime(0, 0, 0, 1, 1, date('Y'));
                $where['sub.bute_time'] = ['>=', $threeYear];
                break;
        }
        $list = Db::name('user')
            ->alias('us')
            ->join('user_subsidy sub', 'us.id=sub.con_user_id')
            ->where('user_nick_name|user_wechat_open_id', 'like', "%$hazy_name%")
            ->where($where)
            ->where('us.much_id', $this->much_id)
            ->field('us.*,count(sub.con_user_id) cid,sum(bute_price) as sprice')
            ->group('sub.con_user_id')
            ->order('sprice', 'desc')
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $hazy_egon, 'hazy_name' => $hazy_name]]);
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $this->assign('list', $list);
        $this->assign('egon', $hazy_egon);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }

    //  收礼排行榜
    public function receiving_ranking()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $hazy_egon = request()->get('egon', 0);
        $page = request()->get('page', 1);
        switch ($hazy_egon) {
            //  全部时间
            case 0:
                $where = [];
                break;
            //  本周排行
            case 1:
                $where['sub.bute_time'] = ['>=', $this->this_monday()];
                break;
            //  本月排行
            case 2:
                $where['sub.bute_time'] = ['>=', $this->month_firstday()];
                break;
            //  本年排行
            case 3:
                $threeYear = mktime(0, 0, 0, 1, 1, date('Y'));
                $where['sub.bute_time'] = ['>=', $threeYear];
                break;
        }
        $list = Db::name('user')
            ->alias('us')
            ->join('user_subsidy sub', 'us.id=sub.sel_user_id')
            ->where('user_nick_name|user_wechat_open_id', 'like', "%$hazy_name%")
            ->where($where)
            ->where('us.much_id', $this->much_id)
            ->field('us.*,count(sub.sel_user_id) cid,sum(bute_price * allow_scale) as sprice')
            ->group('sub.sel_user_id')
            ->order('sprice', 'desc')
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $hazy_egon, 'hazy_name' => $hazy_name]]);
        $defaultNavigate = $this->defaultNavigate();
        $this->assign('defaultNavigate', $defaultNavigate);
        $this->assign('list', $list);
        $this->assign('egon', $hazy_egon);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }


    //  礼物明细
    public function giftUnderstand()
    {
        $type = input('param.type');
        $carton = input('param.carton', 0);
        $reid = input('param.reid');
        $page = input('param.page', 1);
        $jetty = input('param.jetty', 0);
        if ($type == 0) {
            switch ($carton) {
                case 0:
                    $where = [];
                    break;
                case 1:
                    $where['sub.bute_time'] = ['>=', $this->this_monday()];
                    break;
                case 2:
                    $where['sub.bute_time'] = ['>=', $this->month_firstday()];
                    break;
                case 3:
                    $threeYear = mktime(0, 0, 0, 1, 1, date('Y'));
                    $where['sub.bute_time'] = ['>=', $threeYear];
                    break;
            }
            $list = Db::name('user_subsidy')
                ->alias('sub')
                ->join('user us', 'sub.sel_user_id=us.id')
                ->where('sub.con_user_id', $reid)
                ->where($where)
                ->where('sub.much_id', $this->much_id)
                ->field('sub.bute_name,sub.bute_price,sub.exchange_rate,sub.allow_scale,sub.bute_time,us.user_nick_name,us.user_head_sculpture,us.user_wechat_open_id,us.uvirtual')
                ->order('sub.id', 'desc')
                ->page($page, 20)
                ->select();
        } else {
            switch ($carton) {
                case 0:
                    $where = [];
                    break;
                case 1:
                    $where['sub.bute_time'] = ['>=', $this->this_monday()];
                    break;
                case 2:
                    $where['sub.bute_time'] = ['>=', $this->month_firstday()];
                    break;
                case 3:
                    $threeYear = mktime(0, 0, 0, 1, 1, date('Y'));
                    $where['sub.bute_time'] = ['>=', $threeYear];
                    break;
            }
            $list = Db::name('user_subsidy')
                ->alias('sub')
                ->join('user us', 'sub.con_user_id=us.id')
                ->where('sub.sel_user_id', $reid)
                ->where($where)
                ->where('sub.much_id', $this->much_id)
                ->field('sub.bute_name,sub.bute_price,sub.allow_scale,sub.bute_time,us.user_nick_name,us.user_head_sculpture,us.user_wechat_open_id,us.uvirtual')
                ->order('sub.id', 'desc')
                ->page($page, 20)
                ->select();
        }
        foreach ($list as $key => $value) {
            preg_match('|(\d+)|', $list[$key]['bute_name'], $num);
            $list[$key]['num'] = $num[1];
        }
        if ($jetty != 0) {
            foreach ($list as $key => $value) {
                $list[$key]['user_nick_name'] = emoji_decode($list[$key]['user_nick_name']);
                $list[$key]['bute_time'] = date('Y-m-d H:i:s', $list[$key]['bute_time']);
            }
            return json($list);
        } else {
            $defaultNavigate = $this->defaultNavigate();
            $this->assign('defaultNavigate', $defaultNavigate);
            $this->assign('type', $type);
            $this->assign('list', $list);
            return $this->fetch();
        }
    }

    //  发帖排行榜
    public function speak_ranking()
    {
        $url = $this->defaultQuery();
        $hazy_name = request()->get('hazy_name', '');
        $hazy_egon = request()->get('egon', 0);
        $page = request()->get('page', 1);
        switch ($hazy_egon) {
            case 0:
                $where = [];
                break;
            case 1:
                $where['per.adapter_time'] = ['between time', ['today', 'tomorrow']];
                break;
            case 2:
                $where['per.adapter_time'] = ['>=', $this->this_monday()];
                break;
            case 3:
                $where['per.adapter_time'] = ['>=', $this->month_firstday()];
                break;
            case 4:
                $threeYear = mktime(0, 0, 0, 1, 1, date('Y'));
                $where['per.adapter_time'] = ['>=', $threeYear];
                break;
        }
        $list = Db::name('paper')
            ->alias('per')
            ->join('user us', 'us.id=per.user_id', 'left')
            ->where('us.user_nick_name|us.user_wechat_open_id', 'like', "%{$hazy_name}%")
            ->where($where)
            ->where('per.much_id', $this->much_id)
            ->group('us.id')
            ->order('cuid', 'desc')
            ->field('us.*,count(per.user_id) as cuid')
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $hazy_egon, 'hazy_name' => $hazy_name]]);

        $this->assign('list', $list);
        $this->assign('egon', $hazy_egon);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }
}
