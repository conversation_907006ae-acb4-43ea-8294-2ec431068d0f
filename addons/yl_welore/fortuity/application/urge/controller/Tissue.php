<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\api\service\Alternative;
use app\api\service\TmplService;
use app\api\service\Util;
use app\common\Gyration;
use app\common\Playful;
use think\Db;
use think\exception\HttpResponseException;
use think\Request;
use think\Response;
use think\View;

class Tissue extends Base
{
    /*
     * 纸条列表
     */
    public function wedge()
    {
        $url = $this->defaultQuery();
        $hazy_name = emoji_encode(request()->get('hazy_name', ''));
        $hazy_egon = request()->get('egon', 0);
        $when = [];
        switch ($hazy_egon) {
            case 1:
                $when['fg.check_status'] = 0;
                break;
            case 2:
                $when['fg.check_status'] = 1;
                break;
            case 3:
                $when['fg.check_status'] = 2;
                break;
        }
        $list = Db::name('feeling')
            ->alias('fg')
            ->join('user us', 'fg.user_id = us.id')
            ->where('fg.hedge_content|us.user_nick_name|us.user_wechat_open_id', 'like', "%$hazy_name%")
            ->where($when)
            ->where('is_del', 0)
            ->where('fg.much_id&us.much_id', $this->much_id)
            ->field('us.user_nick_name,us.user_wechat_open_id,us.uvirtual,fg.*')
            ->orderRaw('fg.check_status <> 0 asc,case when fg.check_status = 0 then fg.id end asc,case when fg.check_status <> 0 then fg.id end desc')
            ->paginate(10, false, ['query' => ['s' => $url, 'egon' => $hazy_egon, 'hazy_name' => $hazy_name]]);
        $this->assign('list', $list);
        $this->assign('egon', $hazy_egon);
        if ($hazy_name) {
            $this->assign('hazy_name', emoji_decode($hazy_name));
        }
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 纸条详情
     */
    public function wedgeInfo()
    {
        $acid = intval(input('get.acid', 0));
        $feelingInfo = Db::name('feeling')->where('id', $acid)->where('much_id', $this->much_id)->find();
        $feelingInfo['ufCount'] = Db::name('user_feeling')->where('fg_id', $feelingInfo['id'])->where('much_id', $this->much_id)->count();
        $feelingInfo['user'] = Db::name('user')->where('id', $feelingInfo['user_id'])->where('much_id', $this->much_id)->field('user_nick_name,user_wechat_open_id,uvirtual')->find();
        $this->assign('list', $feelingInfo);
        return $this->fetch();
    }

    /*
     * 纸条抽取详情
     */
    public function wedgeSmoke()
    {
        $url = $this->defaultQuery();
        $acid = intval(input('get.acid', 0));
        $list = Db::name('user_feeling')
            ->alias('uf')
            ->join('user us', 'uf.user_id = us.id')
            ->where('uf.fg_id', $acid)
            ->where('uf.much_id&us.much_id', $this->much_id)
            ->field('us.user_nick_name,us.user_wechat_open_id,us.uvirtual,uf.*')
            ->order(['id' => 'desc'])
            ->paginate(10, false, ['query' => ['s' => $url, 'acid' => $acid]]);
        $this->assign('list', $list);
        return $this->fetch();
    }

    /*
     * 纸条审核
     */
    public function trialWedge()
    {
        if (request()->isPost() && request()->isAjax()) {
            $acid = input('post.acid/a', []);
            $status = intval(input('post.status', 0));
            $opinion = trim(input('post.opinion', ''));
            if ($status === 0) {
                return json(['code' => 0, 'msg' => '数据提交错误！']);
            }
            if ($opinion === '') {
                $opinion = null;
            }
            Db::startTrans();
            try {
                Db::name('feeling')->whereIn('id', $acid)->where('check_status', 0)->where('much_id', $this->much_id)->update(['check_status' => $status, 'check_time' => time(), 'check_opinion' => $opinion]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }


    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, [], 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    /*
     * 删除纸条
     */
    public function delWedge()
    {
        if (request()->isPost() && request()->isAjax()) {
            $acid = input('post.acid/a', []);
            Db::startTrans();
            try {
                Db::name('feeling')->whereIn('id', $acid)->where('is_del', 0)->where('much_id', $this->much_id)->update(['is_del' => 1]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     * 纸条设置
     */
    public function choked()
    {
        if (request()->isPost() && request()->isAjax()) {
            $uplid = request()->post('uplid');
            $data['custom_title'] = trim(request()->post('customTitle'));
            $data['bare_location'] = intval(request()->post('bareLocation'));
            $data['bare_direction'] = intval(request()->post('bareDirection'));
            $data['direction_bottom'] = intval(request()->post('directionBottom'));
            $data['bare_img_url'] = trim(request()->post('bareImgUrl'));
            $data['auto_careful'] = intval(request()->post('autoCareful'));
            $data['throw_price_male'] = floatval(request()->post('throwPriceMale'));
            $data['throw_price_female'] = floatval(request()->post('throwPriceFemale'));
            $data['pick_price_male'] = floatval(request()->post('pickPriceMale'));
            $data['pick_price_female'] = floatval(request()->post('pickPriceFemale'));
            $data['pay_type'] = intval(request()->post('payType'));
            $data['throw_limit'] = intval(request()->post('throwLimit'));
            $data['pick_limit'] = intval(request()->post('pickLimit'));
            $data['ten_local_key'] = trim(request()->post('tenLocalKey'));
            $data['notice'] = $this->safe_html(request()->post('notice'));
            Db::startTrans();
            try {
                Db::name('feeling_stipulate')->where('id', $uplid)->where('much_id', $this->much_id)->update($data);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            $tuaList = Db::name('feeling_stipulate')->where('much_id', $this->much_id)->find();
            if (!$tuaList) {
                $domain = explode(':', $_SERVER['HTTP_HOST']);
                $absAddress = explode("index.php", $_SERVER['SCRIPT_NAME']);
                $absRes = "https://{$domain[0]}{$absAddress[0]}static/mineIcon/material/tape_img.png";
                $tuaList = [
                    'custom_title' => '小纸条',  //自定义名称
                    'bare_location' => 0,       //展示位置
                    'bare_direction' => 0,      //展示方向
                    'direction_bottom' => 20,   //距离位置
                    'bare_img_url' => $absRes,  //显示图片
                    'auto_careful' => 0,        //自动审核
                    'throw_price_male' => 0,    //放价格-1
                    'throw_price_female' => 0,  //放价格-2
                    'pick_price_male' => 0,     //抽价格-1
                    'pick_price_female' => 0,   //抽价格-2
                    'pay_type' => 0,            //支付方式
                    'throw_limit' => 0,         //放入限制
                    'pick_limit' => 0,          //抽取限制
                    'ten_local_key' => '',      //定位key
                    'notice' => '',             //须知
                    'much_id' => $this->much_id //标识
                ];
                Db::startTrans();
                try {
                    $tuaList['id'] = Db::name('feeling_stipulate')->insertGetId($tuaList);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                }
            }
            $this->assign('list', $tuaList);
            return $this->fetch();
        }
    }

    /*
     * 生成二维码
     */
    public function createQrCode()
    {
        //  获取page
        $queryPath = trim(input('get.pagePath', ''));
        //  获取参数
        $param = explode('@', $queryPath);
        //  页面page
        $pagePath = $param[0];
        //  获取后台APPID
        $getConfig = cache('fatal_' . $this->much_id);
        if (!$getConfig) {
            $getConfig = Db::name('config')->where('much_id', $this->much_id)->find();
            if ($getConfig) {
                foreach ($getConfig as $key => $value) {
                    if ($key != 'id' && $key != 'pay_react' && $key != 'much_id') {
                        $getConfig[$key] = authcode($getConfig[$key], 'DECODE', 'YuluoNetwork', 0);
                    }
                }
                cache('fatal_' . $this->much_id, $getConfig);
            }
        }
        $option = ['ssl' => ['verify_peer' => false, 'verify_peer_name' => false]];
        $acc = cache('access_token_' . $this->much_id);
        if ($acc) {
            if ($acc['expires_in'] < time()) {
                $url_access_token = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=' . $getConfig['app_id'] . '&secret=' . $getConfig['app_secret'];
                $json_access_token = file_get_contents($url_access_token, false, stream_context_create($option));
                $arr_access_token = json_decode($json_access_token, true);
                $arr_access_token['expires_in'] = $arr_access_token['expires_in'] + time();
                cache('access_token_' . $this->much_id, $arr_access_token);
                $access_token = $arr_access_token['access_token'];
                $url = 'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=' . $access_token;
            } else {
                $url = 'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=' . $acc['access_token'];
            }
        } else {
            $url_access_token = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=' . $getConfig['app_id'] . '&secret=' . $getConfig['app_secret'];
            $json_access_token = file_get_contents($url_access_token, false, stream_context_create($option));
            $arr_access_token = json_decode($json_access_token, true);
            $arr_access_token['expires_in'] = $arr_access_token['expires_in'] + time();
            cache('access_token_' . $this->much_id, $arr_access_token);
            $access_token = $arr_access_token['access_token'];
            $url = 'https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=' . $access_token;
        }
        $data = array(
            "page" => $pagePath,
            "scene" => trim($param[1]) != '' ? $param[1] : $this->much_id,
            "is_hyaline" => true
        );
        $result = Gyration::_requestPost($url, json_encode($data));
        //  创建跳转地址
        $responsePng = Response::create($result)->contentType('image/png');
        //  强制跳转
        throw new HttpResponseException($responsePng);
    }

    /*
     * 评论回复
     */
    public function discuss()
    {
        $url = $this->defaultQuery();
        $hazy_name = emoji_encode(request()->get('hazy_name', ''));
        $list = Db::name('paper_reply_duplex')
            ->alias('prd')
            ->join('user us', 'prd.user_id=us.id', 'left')
            ->where('prd.duplex_content|us.user_nick_name', 'like', "%{$hazy_name}%")
            ->where('prd.much_id', $this->much_id)
            ->orderRaw('prd.duplex_status <> 0 asc,case when prd.duplex_status = 0 then prd.id end asc,case when prd.duplex_status <> 0 then prd.id end desc')
            ->field('prd.*,us.user_nick_name,us.user_wechat_open_id')
            ->paginate(10, false, ['query' => ['s' => $url, 'hazy_name' => $hazy_name]])
            ->each(function ($item) {
                $item['token'] = Db::name('paper_reply')->where('id', $item['reply_id'])->where('much_id', $this->much_id)->value('token');
                return $item;
            });
        $expressionHtml = function ($val) {
            return Alternative::ExpressionHtml($val);
        };
        $this->assign('expressionHtml', $expressionHtml);
        $this->assign('list', $list);
        $this->assign('hazy_name', emoji_decode($hazy_name));
        $page = request()->get('page', 1);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 评论审核
     */
    public function trialDiscuss()
    {
        if (request()->isPost() && request()->isAjax()) {
            $acid = input('post.acid/a', []);
            $status = intval(input('post.status', 0));
            $opinion = trim(input('post.opinion', ''));
            if ($status === 0) {
                return json(['code' => 0, 'msg' => '数据提交错误！']);
            }
            if ($opinion === '') {
                $opinion = null;
            }
            Db::startTrans();
            try {
                //  站内信
                for ($i = 0; $i < count($acid); $i++) {
                    $prdInfo = Db::name('paper_reply_duplex')->where('id', $acid[$i])->where('duplex_status', 0)->where('much_id', $this->much_id)->find();
                    if ($prdInfo) {
                        $maringText = "您的回复[{$prdInfo['duplex_content']}]";
                        if ($status === 1) {
                            $maringText .= "已通过审核！";
                        }
                        if ($status === 2) {
                            $maringText .= "未通过审核！";
                        }
                        //    模板消息
                        $tmplData = [
                            'much_id' => $this->much_id,
                            'at_id' => 'YL0009',
                            'user_id' => $prdInfo['user_id'],
                            'page' => 'yl_welore/pages/user_smail/index',
                            'keyword1' => $maringText,
                            'keyword2' => date('Y年m月d日 H:i:s', time())
                        ];
                        $tmplService = new TmplService();
                        $tmplService->add_template($tmplData);
                        //  站内信
                        Db::name('user_smail')->insert(['user_id' => $prdInfo['user_id'], 'maring' => $maringText, 'clue_time' => time(), 'status' => 0, 'much_id' => $this->much_id]);
                    }
                }
                //  审核状态
                Db::name('paper_reply_duplex')->whereIn('id', $acid)->where('duplex_status', 0)->where('much_id', $this->much_id)->update(['duplex_status' => $status, 'check_opinion' => $opinion]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     * 删除评论
     */
    public function delDiscuss()
    {
        if (request()->isPost() && request()->isAjax()) {
            $acid = input('post.acid/a', []);
            Db::startTrans();
            try {
                Db::name('paper_reply_duplex')->whereIn('id', $acid)->where('much_id', $this->much_id)->delete();
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            return json(['code' => 1, 'msg' => '操作成功']);
        }
    }

    /*
     * 私信列表
     */
    public function privateLetter()
    {
        $page = request()->get('page', 1);
        $sendUid = trim(input('get.sendUid'));
        $receiveUid = trim(input('get.receiveUid'));
        $content = trim(input('get.content'));
        $when = [];
        if ($sendUid !== '') {
            $when[] = function ($query) use ($sendUid) {
                $query->where('se_user_id', '=', $sendUid);
            };
        }
        if ($receiveUid !== '') {
            $when[] = function ($query) use ($receiveUid) {
                $query->where('re_user_id', '=', $receiveUid);
            };
        }
        $list = Db::name('user_leave_word')
            ->where($when)
            ->where(function ($query) use ($content) {
                $likeContent = emoji_encode($content);
                $query->where('le_content', 'like', "%{$likeContent}%")->whereOr('le_content', 'like', "%\"title\":\"{$likeContent}\"%");
            })
            ->where('much_id', $this->much_id)
            ->order(['id' => 'desc'])
            ->paginate(10, false, ['query' => ['s' => $this->defaultQuery(), 'sendUid' => $sendUid, 'receiveUid' => $receiveUid, 'content' => $content]])
            ->each(function ($item) {
                $item['se_user'] = Db::name('user')->where('id', $item['se_user_id'])->where('much_id', $this->much_id)->field('user_head_sculpture,user_nick_name,user_wechat_open_id,uvirtual')->find();
                $item['re_user'] = Db::name('user')->where('id', $item['re_user_id'])->where('much_id', $this->much_id)->field('user_head_sculpture,user_nick_name,user_wechat_open_id,uvirtual')->find();
                if (intval($item['le_type']) === 1) {
                    $leContent = json_decode($item['le_content'], true);
                    $item['paid'] = $leContent['paid'];
                    $item['content'] = $leContent['title'];
                } else {
                    $item['content'] = $item['le_content'];
                }
                return $item;
            });
        $expressionHtml = function ($val) {
            return Alternative::ExpressionHtml($val);
        };
        $this->assign('expressionHtml', $expressionHtml);
        $this->assign('list', $list);
        $this->assign('sendUid', $sendUid);
        $this->assign('receiveUid', $receiveUid);
        $this->assign('content', $content);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /*
     * 发送私信
     */
    public function send_private_messages()
    {
        if (request()->isPost() && request()->isAjax()) {
            $sendUid = intval(input('post.sendUid'));
            $receiveUid = intval(input('post.receiveUid'));
            if ($sendUid === $receiveUid) {
                return json(['code' => 0, 'msg' => '发送用户UID和接收用户UID不能相同']);
            }
            $sendInfo = Db::name('user')->where('id', $sendUid)->where('much_id', $this->much_id)->field('id,user_nick_name')->find();
            if (!$sendInfo) {
                return json(['code' => 0, 'msg' => '请检查发送用户UID是否填写正确']);
            }
            $receiveInfo = Db::name('user')->where('id', $receiveUid)->where('much_id', $this->much_id)->field('id')->find();
            if (!$receiveInfo) {
                return json(['code' => 0, 'msg' => '请检查接收用户UID是否填写正确']);
            }
            $content = trim(input('post.content'));
            Db::startTrans();
            try {
                Db::name('user_leave_word')->insert([
                    'se_user_id' => $sendUid,
                    're_user_id' => $receiveUid,
                    'le_content' => $content,
                    'le_type' => 0,
                    'le_read_status' => 0,
                    'se_user_del' => 0,
                    're_user_del' => 0,
                    'le_time' => time(),
                    'much_id' => $this->much_id
                ]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            $util = new Util();
            $util->add_template([
                'at_id' => 'YL0006',
                'user_id' => $receiveUid,
                'page' => 'yl_welore/pages/packageB/private_letter/index?id=' . $sendUid,
                'keyword1' => emoji_decode($content),
                'keyword2' => date('Y年m月d日 H:i', time()),
                'keyword3' => emoji_decode($sendInfo['user_nick_name']),
                'much_id' => $this->much_id
            ]);
            return json(['code' => 1, 'msg' => '操作成功']);
        } else {
            abort(404);
        }
    }

    /*
     * 截屏记录
     */
    public function peeping()
    {
        $hazy_name = request()->get('hazy_name', '');
        $page = request()->get('page', 1);
        $list = Db::name('user_screenshot')
            ->where('much_id', $this->much_id)
            ->order(['id' => 'desc'])
            ->paginate(10, false, ['query' => ['s' => $this->defaultQuery(), 'hazy_name' => $hazy_name]])
            ->each(function ($item) {
                $item['user'] = Db::name('user')->where('id', $item['user_id'])->where('much_id', $this->much_id)->field('user_head_sculpture,user_nick_name,user_wechat_open_id,uvirtual')->find();
                return $item;
            });
        $this->assign('list', $list);
        $this->assign('hazy_name', $hazy_name);
        $this->assign('page', $page);
        return $this->fetch();
    }
}