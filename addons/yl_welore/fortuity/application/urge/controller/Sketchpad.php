<?php

namespace app\urge\controller;

defined('IN_IA') or exit('Access Denied');

use app\common\Playful;
use app\common\Remotely;
use think\Cache;
use think\Db;
use think\exception\HttpResponseException;
use think\paginator\driver\Bootstrap;
use think\Request;
use think\View;

/**
 * Class Sketchpad
 * @package app\urge\controller
 *
 * 模板市场与插件市场控制器
 */
class Sketchpad extends Base
{
    /**
     * Sketchpad constructor.
     * @param Request|null $request
     */
    public function __construct(Request $request = null)
    {
        parent::__construct($request);
        $this->_initialize();
    }

    /**
     * 初始化方法，执行授权检测
     */
    public function _initialize()
    {
        // ============================== 授权检测 Start ==============================
        parent::_initialize();
        $lovely = Playful::lovely($this->much_id);
        if ($lovely['dried'] !== md5(time() * pi())) {
            $view = new View();
            $view->share('depressed', $lovely['randCode']);
            throw new HttpResponseException(response('error/legalization', 200, [], 'view'));
        }
        // ============================== 授权检测 End ==============================
    }

    /**
     * 模板市场列表页
     * @return string|\think\Response
     * @throws \think\Exception
     */
    public function market()
    {
        $combinationInfo = cache('market_' . $this->much_id);
        if (!$combinationInfo) {
            $combinationInfo = Db::name('combination')->where('much_id', $this->much_id)->find();
            if (!$combinationInfo) {
                $combinationInfo = [
                    'home' => Remotely::templetEncode('0'),
                    'plaza' => Remotely::templetEncode('0'),
                    'goods' => Remotely::templetEncode('0'),
                    'user' => Remotely::templetEncode('0'),
                    'much_id' => $this->much_id
                ];
                Db::startTrans();
                try {
                    $combinationInfo['id'] = Db::name('combination')->insertGetId($combinationInfo);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return $e->getMessage();
                }
            }
            cache('market_' . $this->much_id, $combinationInfo);
        }
        $page = request()->get('page', 1);
        $egon = request()->get('egon', 0);
        $waveName = request()->get('waveName', '');
        $surge = [
            'listRows' => 6,
            'page' => $page,
            'url' => $this->defaultQuery(),
            'waveName' => $waveName,
            'type' => $egon,
            'home' => $combinationInfo['home'],
            'plaza' => $combinationInfo['plaza'],
            'goods' => $combinationInfo['goods'],
            'user' => $combinationInfo['user']
        ];

        $list = cache("p{$page}_w{$waveName}_t{$egon}_{$this->much_id}");
        if (!$list) {
            $channel = Remotely::channel($surge);
            $list = unserialize($channel['listTemplet']);

            if ($list) {
                // 抽取出来的图片处理逻辑
                $this->_processMarketImages($list);
            } else {
                $list = new Bootstrap([], 0, 1, 0, false, ['path' => 'index.php', 'query' => ['s' => $this->defaultQuery()]]);
            }
            Cache::tag('pwtCache')->set("p{$page}_w{$waveName}_t{$egon}_{$this->much_id}", $list, 172800);
        }

        $this->assign('list', $list);
        $this->assign('waveName', $waveName);
        $this->assign('egon', $egon);
        $this->assign('page', $page);
        return $this->fetch();
    }

    /**
     * [辅助方法] 处理模板市场列表的图片，下载并替换为本地URL
     * @param $list
     */
    private function _processMarketImages(&$list)
    {
        // 保存目录
        $detectContents = ROOT_PATH . '..' . DS . 'web' . DS . 'assets' . DS . 'img' . DS . 'temp';
        // 判断目录是否存在
        if (!is_dir($detectContents)) {
            @mkdir($detectContents, 0777, true);
        }
        $list->each(function ($item, $key) use ($detectContents) {
            // 获取文件名称
            $fileName = basename($item['narrowImage']);
            // 忽略证书错误
            $option = ['ssl' => ['verify_peer' => false, 'verify_peer_name' => false]];
            // 保存地址
            $savePath = $detectContents . DS . $fileName;
            // 判断本地文件是否存在
            if (!file_exists($savePath)) {
                // 获取图片并保存到本地
                @file_put_contents($savePath, @file_get_contents($item['narrowImage'], false, stream_context_create($option)));
            }
            // 获取当前包含协议、端口的域名
            $domain = request()->domain();
            // 获取当前完整URL并去掉参数
            $resourcePath = explode("/index.php", request()->url());
            // 获取时间
            $time = time();
            // 文件路径
            $filePath = "{$domain}{$resourcePath[0]}/assets/img/temp/{$fileName}?time={$time}";
            // 图片地址重定向
            $item['narrowImage'] = $filePath;
            $imageInfo = getimagesize($savePath);
            $item['narrowImageWidth'] = $imageInfo[0];
            $item['narrowImageHeight'] = $imageInfo[1];
            return $item;
        });
    }

    /**
     * AJAX接口：选择并保存指定模板
     */
    public function chooseTemplet()
    {
        if (request()->isPost() && request()->isAjax()) {
            $narrowId = request()->post('narrowId');
            $narrowType = request()->post('narrowType');
            $straight = Remotely::straight(['narrowId' => $narrowId]);
            $hurdle = '';
            switch ($narrowType) {
                case 0:
                    $hurdle = 'home';
                    break;
                case 1:
                    $hurdle = 'plaza';
                    break;
                case 2:
                    $hurdle = 'goods';
                    break;
                case 3:
                    $hurdle = 'user';
                    break;
            }
            $modOriginal = cache('market_' . $this->much_id);
            $modOriginal['much_id'] = $this->much_id;
            $modInfo = $modOriginal;
            $modInfo[$hurdle] = $straight['narrowMark'];
            Db::startTrans();
            try {
                Db::name('combination')->where('much_id', $this->much_id)->update([$hurdle => $straight['narrowMark']]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                cache('market_' . $this->much_id, $modOriginal);
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }
            cache('market_' . $this->much_id, $modInfo);
            Cache::clear('pwtCache');
            return json(['code' => 1, 'msg' => '保存成功']);
        } else {
            abort(404);
        }
    }

    /**
     * 插件市场列表页
     * @return string|\think\Response
     * @throws \think\Exception
     */
    public function plugin()
    {
        //  插件类型
        $egon = intval(request()->get('egon', 0));
        //  查询插件名称
        $searchName = trim(input('get.searchName'));
        //  当前页数
        $page = intval(input('get.page', 1));
        //  每页数据
        $listRows = 8;
        //  从缓存中获取数据
        $notchList = cache(md5("notchList-{$egon}-{$searchName}-{$page}"));
        //  判断缓存数据是否失效
        if (!$notchList) {
            //  获取插件列表数据
            $notchList = Remotely::notchList(['egon' => $egon, 'searchName' => $searchName, 'page' => $page, 'listRows' => $listRows]);
            //  把插件列表数据存到缓存中 有效期两天
            Cache::tag('marketConflict')->set(md5("notchList-{$egon}-{$searchName}-{$page}"), $notchList, 172800);
        }
        //  列表数据解密
        $pluginList = json_decode(authcode($notchList, 'DECODE', base64_decode('WXVMdW9QbHVnaW4=')), true);
        //  健壮性检查：如果解码失败或结构不正确，则创建一个安全的空列表结构
        if (!is_array($pluginList) || !isset($pluginList['data'])) {
            $pluginList = ['data' => [], 'per_page' => $listRows, 'current_page' => $page, 'total' => 0];
        }
        //  更新插件列表数据 (调用 Remotely 中重构后的核心方法)
        Remotely::compareNotch($pluginList, $this->much_id);
        //  显示分页
        $list = new Bootstrap($pluginList['data'], $pluginList['per_page'], $pluginList['current_page'], $pluginList['total'], false, ['path' => 'index.php', 'query' => ['s' => $this->defaultQuery(), 'egon' => $egon, 'searchName' => $searchName]]);
        $this->assign('egon', $egon);
        $this->assign('page', $page);
        $this->assign('searchName', $searchName);
        $this->assign('list', $list);
        return $this->fetch();
    }

    /**
     * AJAX接口：刷新插件数据 (强制云端同步)
     */
    public function refreshPlugin()
    {
        if (request()->isPost() && request()->isAjax()) {
            if ($this->M['role'] === 'founder') {
                //  清除插件缓存
                Cache::clear('marketConflict');
                //  本机IP
                $positionByIp = Remotely::getPositionByIp();
                //  加密后的IP数据
                $prometheus = md5("YuLuoClothes-{$positionByIp}");
                //  读取数据库信息
                $tsInfo = Db::name('template_slot')->where('prometheus', $prometheus)->find();

                if ($tsInfo) {
                    $updateData = [];
                    // 1. 准备无效数据标记，强制刷新，这是每次都必须执行的
                    $poisonPill = authcode(json_encode(['exp_time' => 0, 'is_poison' => true], true), 'ENCODE', base64_decode('WXVMdW9TbG90UGx1Z2lu'));
                    $updateData['apollo'] = $poisonPill;

                    // 2. 检查历史记录是否需要被初始化 (仅在为空时执行一次)
                    if (empty($tsInfo['apollo_history'])) {
                        // 在写入历史前，必须验证当前数据是否有效
                        $currentApolloDecoded = Remotely::unNotchStream($tsInfo['apollo']);
                        if (is_array($currentApolloDecoded) && isset($currentApolloDecoded['exp_time']) && $currentApolloDecoded['exp_time'] > time()) {
                            // 验证通过，将有效的当前数据作为历史基线加入更新数组
                            $updateData['apollo_history'] = $tsInfo['apollo'];
                        }
                    }

                    // 3. 无论更新一个字段还是两个，都使用事务来确保操作的原子性和安全性
                    Db::startTrans();
                    try {
                        Db::name('template_slot')->where('id', $tsInfo['id'])->update($updateData);
                        Db::commit();
                        // 事务成功后，再清理缓存
                        Cache::rm($prometheus);
                    } catch (\Exception $e) {
                        Db::rollback();
                        return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
                    }
                }
            }
            return json(['code' => 1, 'msg' => '更新成功']);
        }
    }

    /**
     * AJAX接口：切换插件的启用/禁用状态 (已重构)
     * @return \think\response\Json
     */
    public function togglePluginSwitch()
    {
        if (request()->isPost() && request()->isAjax()) {
            $secMark = trim(input('post.secMark'));
            $objValue = intval(input('post.objValue')); // 1 for enable, 0 for disable
            $narrowMark = authcode($secMark, 'DECODE', base64_decode('WXVMdW9QbHVnaW4='));

            // 1. 开启插件前，执行冲突检测
            if ($objValue === 1) {
                $conflictCheck = Remotely::checkConflicts($narrowMark, $this->much_id);
                if ($conflictCheck['conflict']) {
                    return json(['code' => 0, 'msg' => $conflictCheck['message']]);
                }
            }

            // 2. 核心逻辑: 使用 Remotely 中的权威方法处理数据
            // a. 获取当前授权上下文
            $readNotchResult = Remotely::readNotch();
            // b. 查找或创建用户记录
            $tsvInfo = Remotely::findOrCreateUserValve($this->much_id);
            // c. 如有需要，执行数据迁移 (引用传递)
            Remotely::migrateUserValveIfNeeded($tsvInfo, $readNotchResult);
            // d. 清理并获取当前已启用的插件列表
            $enabledPlugins = Remotely::sanitizeUserPetalData($tsvInfo, $readNotchResult['current_unlocked']);

            // 3. 应用开关操作
            if ($objValue === 1) { // 开启插件
                if (!in_array($narrowMark, $enabledPlugins)) {
                    $enabledPlugins[] = $narrowMark;
                }
            } else { // 关闭插件
                $enabledPlugins = array_diff($enabledPlugins, [$narrowMark]);
            }

            // 4. 保存最终结果
            $petalDataEncoded = authcode(json_encode(array_values($enabledPlugins)), 'ENCODE', base64_decode('WXVMdW9QbHVnaW4='));

            Db::startTrans();
            try {
                // 这里使用 much_id 更新，因为 tsvInfo 可能是一个新建的数组，没有 id
                Db::name('template_slot_valve')->where('much_id', $this->much_id)->update(['petal_data' => $petalDataEncoded]);
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                return json(['code' => 0, 'msg' => 'error , ' . $e->getMessage()]);
            }

            return json(['code' => 1, 'msg' => $objValue ? '开启成功' : '关闭成功']);
        }
    }
}
