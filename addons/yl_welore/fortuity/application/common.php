<?php

defined('IN_IA') or exit('Access Denied');

/**
 * 对emoji表情转义
 * @param $str
 * @return string
 */
function emoji_encode($str)
{
    $strEncode = '';
    $length = mb_strlen($str, 'utf-8');
    for ($i = 0; $i < $length; $i++) {
        $_tmpStr = mb_substr($str, $i, 1, 'utf-8');
        if (strlen($_tmpStr) >= 4) {
            $strEncode .= '[[EMOJI:' . rawurlencode($_tmpStr) . ']]';
        } else {
            $strEncode .= $_tmpStr;
        }
    }
    return $strEncode;
}

/**
 * 对emoji表情转反义
 * @param $str
 * @return array|string|string[]|null
 */
function emoji_decode($str)
{
    $strDecode = preg_replace_callback('|\[\[EMOJI:(.*?)\]\]|', function ($matches) {
        return rawurldecode($matches[1]);
    }, $str);
    return $strDecode;
}

/**
 * 数字转中文数字
 * @param [num] $num [数字]
 * @return [string] [string]
 * 把数字1-1亿换成汉字表述，如：123->一百二十三
 */
function numToWord($num)
{
    $chiNum = array('', '一', '二', '三', '四', '五', '六', '七', '八', '九');
    $chiUni = array('', '十', '百', '千', '万', '亿', '十', '百', '千');
    $chiStr = '';
    $num_str = (string)(int)$num;
    $count = strlen($num_str);
    $last_flag = true; //上一个 是否为0
    $zero_flag = true; //是否第一个
    $temp_num = null; //临时数字
    $chiStr = '';//拼接结果
    if ($count == 2) {//两位数
        $temp_num = $num_str[0];
        $chiStr = $temp_num == 1 ? $chiUni[1] : $chiNum[$temp_num] . $chiUni[1];
        $temp_num = $num_str[1];
        $chiStr .= $temp_num == 0 ? '' : $chiNum[$temp_num];
    } else if ($count > 2) {
        $index = 0;
        for ($i = $count - 1; $i >= 0; $i--) {
            $temp_num = $num_str[$i];
            if ($temp_num == 0) {
                if (!$zero_flag && !$last_flag) {
                    $chiStr = $chiNum[$temp_num] . $chiStr;
                    $last_flag = true;
                }
            } else {
                $chiStr = $chiNum[$temp_num] . $chiUni[$index % 9] . $chiStr;
                $zero_flag = false;
                $last_flag = false;
            }
            $index++;
        }
    } else {
        $chiStr = $chiNum[$num_str[0]];
    }
    return $chiStr;
}

function countdays($d)
{
    $olddate = substr($d, 4);
    $newdate = date('Y') . "" . $olddate;
    $nextyear = date('Y') + 1 . "" . $olddate;
    if ($newdate > date("Y-m-d")) {
        $start_ts = strtotime($newdate);
        $end_ts = strtotime(date("Y-m-d"));
        $diff = $end_ts - $start_ts;
        $n = round($diff / 86400);
        $return = substr($n, 1);
        return $return;
    } else {
        $start_ts = strtotime($nextyear);
        $end_ts = strtotime(date("Y-m-d"));
        $diff = $end_ts - $start_ts;
        $n = round($diff / 86400);
        $return = substr($n, 1);
        return $return;
    }
}

/**
 * @param $date
 * @return string
 * 时间格式化
 */
function formatTime($date)
{
    $str = '';
    $timer = $date;
    $diff = $_SERVER['REQUEST_TIME'] - $timer;
    $day = floor($diff / 86400);
    $free = $diff % 86400;
    if ($day > 3) {
        $year = date("Y", $date);
        $year_this = date("Y", time());
        if ($year < $year_this) {
            return date("Y-m-d", $date);
        }
        return date("m-d", $date);
    }
    if ($day > 0) {
        return $day . "天前";
    } else {
        if ($free > 0) {
            $hour = floor($free / 3600);
            $free = $free % 3600;
            if ($hour > 0) {
                return $hour . "小时前";
            } else {
                if ($free > 0) {
                    $min = floor($free / 60);
                    $free = $free % 60;
                    if ($min > 0) {
                        return $min . "分钟前";
                    } else {
                        if ($free > 0) {
                            return $free . "秒前";
                        } else {
                            return '刚刚';
                        }
                    }
                } else {
                    return '刚刚';
                }
            }
        } else {
            return '刚刚';
        }
    }
}

/**
 * @param $number
 * @return float|string
 * 格式化数字
 */
function formatNumber($number)
{
    if (empty($number) || !is_numeric($number)) return $number;
    $unit = "";
    if ($number > 10000) {
        $leftNumber = floor($number / 10000);
        $rightNumber = round(($number % 10000) / 10000, 2);
        // $rightNumber = bcmul(($number % 10000) / 10000, '1', 2);
        $number = floatval($leftNumber + $rightNumber);
        $unit = "W";
    } else {
        $decimals = $number > 1 ? 2 : 6;
        $number = (float)number_format($number, $decimals, '.', '');
    }
    return (string)$number . $unit;
}

/**
 * 获取图片地址
 */
function get_images_from_html($content)
{
    $pattern = "/<img.*?src=[\'|\"](.*?)[\'|\"].*?[\/]?>/";
    preg_match_all($pattern, htmlspecialchars_decode($content), $match);
    if (!empty($match[1])) {
        return $match[1];
    }
    return null;
}

/**
 * 对URL进行编码
 * @param $url
 * @return string
 */
function link_urldecode($url)
{
    $uri = '';
    $cs = unpack('C*', $url);
    $len = count($cs);
    for ($i = 1; $i <= $len; $i++) {
        $uri .= $cs[$i] > 127 ? '%' . strtoupper(dechex($cs[$i])) : $url{$i - 1};
    }
    return $uri;
}

/**
 * 过滤emoji表情
 */
function filter_emoji($str)
{
    $str = preg_replace_callback(
        '/./u',
        function (array $match) {
            return strlen($match[0]) >= 4 ? '' : $match[0];
        },
        $str);
    return $str;
}

/**
 * 只保留字符串首尾字符，隐藏中间用*代替（两个字符时只显示第一个）
 * @param string $user_name 姓名
 * @return string 格式化后的姓名
 */
function substr_cut($user_name)
{
    $strlen = mb_strlen($user_name, 'utf-8');
    $firstStr = mb_substr($user_name, 0, 1, 'utf-8');
    $lastStr = mb_substr($user_name, -1, 1, 'utf-8');
    return $strlen == 2 ? $firstStr . str_repeat('*', mb_strlen($user_name, 'utf-8') - 1) : $firstStr . str_repeat("*", $strlen - 2) . $lastStr;
}

/**
 * 将秒转换为 分:秒
 * s int 秒数
 */
function s_to_hs($s = 0)
{
    //计算分钟
    //算法：将秒数除以60，然后下舍入，既得到分钟数
    $h = floor($s / 60);
    //计算秒
    //算法：取得秒%60的余数，既得到秒数
    $s = $s % 60;
    //如果只有一位数，前面增加一个0
    $h = (strlen($h) == 1) ? '0' . $h : $h;
    $s = (strlen($s) == 1) ? '0' . $s : $s;
    return $h . ':' . $s;
}

/**
 * 截取字符串
 */
function subtext($text, $length)
{
    if (mb_strlen($text, 'utf8') > $length) {
        return mb_substr($text, 0, $length, 'utf8') . '…';
    }
    return $text;
}

/**
 * 文本加密
 */
function authcode($string, $operation = 'DECODE', $key = '', $expiry = 0)
{
    $ckey_length = 4;
    $key = md5($key ? $key : 'YuluoNetwork');
    $keya = md5(substr($key, 0, 16));
    $keyb = md5(substr($key, 16, 16));
    $keyc = $ckey_length ? ($operation == 'DECODE' ? substr($string, 0, $ckey_length) :
        substr(md5(microtime()), -$ckey_length)) : '';
    $cryptkey = $keya . md5($keya . $keyc);
    $key_length = strlen($cryptkey);
    $string = $operation == 'DECODE' ? base64_decode(substr($string, $ckey_length)) :
        sprintf('%010d', $expiry ? $expiry + time() : 0) . substr(md5($string . $keyb), 0, 16) . $string;
    $string_length = strlen($string);
    $result = '';
    $box = range(0, 255);
    $rndkey = array();
    for ($i = 0; $i <= 255; $i++) {
        $rndkey[$i] = ord($cryptkey[$i % $key_length]);
    }
    for ($j = $i = 0; $i < 256; $i++) {
        $j = ($j + $box[$i] + $rndkey[$i]) % 256;
        $tmp = $box[$i];
        $box[$i] = $box[$j];
        $box[$j] = $tmp;
    }
    for ($a = $j = $i = 0; $i < $string_length; $i++) {
        $a = ($a + 1) % 256;
        $j = ($j + $box[$a]) % 256;
        $tmp = $box[$a];
        $box[$a] = $box[$j];
        $box[$j] = $tmp;
        $result .= chr(ord($string[$i]) ^ ($box[($box[$a] + $box[$j]) % 256]));
    }
    if ($operation == 'DECODE') {
        if ((substr($result, 0, 10) == 0 || substr($result, 0, 10) - time() > 0) &&
            substr($result, 10, 16) == substr(md5(substr($result, 26) . $keyb), 0, 16)) {
            return substr($result, 26);
        } else {
            return '';
        }
    } else {
        return $keyc . str_replace('=', '', base64_encode($result));
    }
}

/**
 * 字符串获取前三位后四位
 */
function ciphertext($string)
{
    $wteh = substr($string, 0, 3);
    $yeth = substr($string, -4);
    return $wteh . '********' . $yeth;
}

/**
 * 获取商品列表第一张图
 */
function athumbnail($data)
{
    $data = json_decode($data, true);
    return $data[0];
}

/**
 * 发货时间是否大于7天
 */
function contract($time)
{
    if ($time == '' || empty($time)) {
        return 0;
    }
    $nowTime = time();
    $timeOut = $nowTime - $time;
    return ($timeOut >= 604800) ? 1 : 0;
}

/**
 * 小程序导航栏默认图片地址
 */
function urlBridging($url)
{
    $domain = explode(':', $_SERVER['HTTP_HOST']);
    $absAddress = explode("index.php", $_SERVER['SCRIPT_NAME']);
    $absRessReplace = 'https://' . $domain[0] . $absAddress[0];
    $absRess = str_replace('\\', '/', $absRessReplace);
    return $absRess . $url;
}