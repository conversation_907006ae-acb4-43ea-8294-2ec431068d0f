<?php
defined('IN_IA') or define('IN_IA', true);
defined('IA_ROOT') or define('IA_ROOT', str_replace("\\", '/', dirname(__FILE__)));
try {
    // 定义配置信息
    $config = [];
    // 读取数据库配置
    require __DIR__ . '/../../../../data/config.php';
    // 定义数据库信息
    $database = [];
    // 判断主库是否为空
    if (empty($config['db']['master'])) {
        // 获取默认配置
        $database = $config['db'];
    } else {
        // 获取主库配置
        $database = $config['db']['master'];
    }
    // 定义数据库部署方式
    $deploy = 0;
    // 数据库读写是否分离
    $rwSeparate = false;
    //  判断主从状态变量是否存在
    if (isset($config['db']['slave_status'])) {
        // 获取主从变量状态
        $deploy = intval($config['db']['slave_status']);
        // 判断从库状态是否开启
        if ($config['db']['slave_status']) {
            // 设置读写分离
            $rwSeparate = true;
            // 整合设置主从配置
            for ($i = 1; $i <= count($config['db']['slave']); $i++) {
                // 设置主从配置服务器地址
                $database['host'] .= ",{$config['db']['slave'][$i]['host']}";
                // 设置主从配置用户名
                $database['username'] .= ",{$config['db']['slave'][$i]['username']}";
                // 设置主从配置密码
                $database['password'] .= ",{$config['db']['slave'][$i]['password']}";
                // 设置主从配置端口号
                $database['port'] .= ",{$config['db']['slave'][$i]['port']}";
            }
        }
    }
} catch (\Exception $e) {
    header('Content-type: text/html; charset=utf-8');
    echo '读取数据库配置文件失败，需开发者协助处理。错误信息：' . $e->getMessage();
    die;
}

return [
    // 数据库类型
    'type' => 'mysql',
    // 服务器地址
    'hostname' => $database['host'],
    // 数据库名
    'database' => $database['database'],
    // 用户名
    'username' => $database['username'],
    // 密码
    'password' => $database['password'],
    // 端口
    'hostport' => $database['port'],
    // 连接dsn
    'dsn' => '',
    // 数据库连接参数
    'params' => [],
    // 数据库编码默认采用utf8
    'charset' => $database['charset'],
    // 数据库表前缀
    'prefix' => 'yl_welore_',
    // 数据库调试模式
    'debug' => true,
    // 数据库部署方式:0 集中式(单一服务器),1 分布式(主从服务器)
    'deploy' => $deploy,
    // 数据库读写是否分离 主从式有效
    'rw_separate' => $rwSeparate,
    // 读写分离后 主服务器数量
    'master_num' => 1,
    // 指定从服务器序号
    'slave_no' => '',
    // 自动读取主库数据
    'read_master' => true,
    // 是否严格检查字段是否存在
    'fields_strict' => true,
    // 数据集返回类型
    'resultset_type' => 'array',
    // 自动写入时间戳字段
    'auto_timestamp' => false,
    // 时间字段取出后的默认时间格式
    'datetime_format' => 'Y-m-d H:i:s',
    // 是否需要进行SQL性能分析
    'sql_explain' => false,
];