<?php

require_once __DIR__ . DIRECTORY_SEPARATOR . 'partake.php';

#v1.0.2_2018-12-24
if (manual_indexexists('yl_welore_territory', 'realm_name')) {
    pdo_query("ALTER TABLE yl_welore_territory DROP INDEX realm_name;");
}

#v1.0.4_2018-12-26
if (!manual_tableexists('yl_welore_gallery_classify')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_gallery_classify` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(500) NOT NULL,
            `status` int(11) unsigned NOT NULL DEFAULT '1',
            `scores` int(11) unsigned NOT NULL DEFAULT '0',
            `much_id` int(11) unsigned DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `much_id` (`much_id`),
            KEY `scores` (`scores`),
            KEY `status` (`status`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.4_2018-12-26
if (!manual_tableexists('yl_welore_gallery')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_gallery` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `classify_id` int(11) unsigned DEFAULT NULL,
            `img_title` varchar(1000) DEFAULT NULL,
            `img_url` varchar(1000) DEFAULT NULL,
            `much_id` int(11) unsigned DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `classify_id` (`classify_id`),
            KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.5_2018-12-28
if (!manual_tableexists('yl_welore_design')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_design` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `confer` varchar(500) DEFAULT NULL,
              `currency` varchar(500) DEFAULT NULL,
              `landgrave` varchar(500) DEFAULT NULL,
              `home_title` varchar(500) DEFAULT NULL,
              `pattern_data` longtext,
              `much_id` int(10) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.5_2018-12-28
if (!manual_fieldexists('yl_welore_authority', 'prevent_duplication')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `prevent_duplication` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1' ;");
}

#v1.0.8_2019-1-2
if (!manual_fieldexists('yl_welore_authority', 'noble_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `noble_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1' ;");
}

#v1.0.8_2019-1-2
if (!manual_fieldexists('yl_welore_authority', 'wallet_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `wallet_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1' ;");
}

#v1.0.9_2019-1-3
if (!manual_tableexists('yl_welore_raws_setting')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_raws_setting` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `open_withdrawals` int(11) unsigned NOT NULL DEFAULT '0',
              `open_offline_payment` int(11) unsigned NOT NULL DEFAULT '0',
              `auto_review_payment` int(11) unsigned NOT NULL DEFAULT '0',
              `lowest_money` decimal(18,2) unsigned NOT NULL DEFAULT '1.00',
              `payment_tariff` decimal(19,3) unsigned NOT NULL DEFAULT '0.000',
              `notice` longtext,
              `much_id` int(11) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`),
              KEY `open_withdrawals` (`open_withdrawals`),
              KEY `open_offline_payment` (`open_offline_payment`),
              KEY `auto_review_payment` (`auto_review_payment`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}


#v1.0.9_2019-1-3
if (!manual_tableexists('yl_welore_user_withdraw_money')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_withdraw_money` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned DEFAULT NULL,
              `user_account` varchar(500) DEFAULT NULL,
              `display_money` decimal(18,2) NOT NULL DEFAULT '0.00',
              `tariff` decimal(19,3) unsigned NOT NULL DEFAULT '0.000',
              `actual_amount` decimal(18,2) NOT NULL DEFAULT '0.00',
              `withdraw_type` int(11) unsigned NOT NULL DEFAULT '0',
              `seek_time` int(11) unsigned DEFAULT NULL,
              `verify_time` int(11) unsigned DEFAULT NULL,
              `status` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `much_id` (`much_id`),
              KEY `user_id` (`user_id`),
              KEY `withdraw_type` (`withdraw_type`),
              KEY `seek_time` (`seek_time`),
              KEY `verify_time` (`verify_time`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}


#v1.0.11_2019-1-9
/* 已废弃
if (!manual_tableexists('yl_welore_mouldboard')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_mouldboard` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `prototype_data` longtext,
              `much_id` int(11) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}*/


#v1.0.11_2019-1-9
/* 已废弃
if (!manual_tableexists('yl_welore_user_form_info')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_form_info` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned DEFAULT NULL,
              `open_id` varchar(500) DEFAULT NULL,
              `formid` varchar(1000) DEFAULT NULL,
              `create_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}*/


#v1.0.11_2019-1-9
/* 已废弃
if (!manual_tableexists('yl_welore_user_templet_history')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_templet_history` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `paper_id` int(11) DEFAULT NULL,
              `send_user_id` int(11) unsigned DEFAULT NULL,
              `accept_user_id` int(11) unsigned DEFAULT NULL,
              `archetype_id` varchar(1000) DEFAULT NULL,
              `send_time` int(11) unsigned DEFAULT NULL,
              `much_id` int(10) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `send_user_id` (`send_user_id`),
              KEY `accept_user_id` (`accept_user_id`),
              KEY `much_id` (`much_id`),
              KEY `send_time` (`send_time`),
              KEY `paper_id` (`paper_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}*/


#v1.0.11_2019-1-16
if (!manual_fieldexists('yl_welore_territory', 'attention')) {
    pdo_query("ALTER TABLE  `yl_welore_territory` ADD  `attention` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.11_2019-1-16
if (!manual_fieldexists('yl_welore_territory_petition', 'attention')) {
    pdo_query("ALTER TABLE  `yl_welore_territory_petition` ADD  `attention` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.11_2019-1-16
if (!manual_tableexists('yl_welore_territory_interest')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_territory_interest` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned DEFAULT NULL,
              `tory_id` int(11) unsigned DEFAULT NULL,
              `sult_time` int(11) unsigned DEFAULT NULL,
              `rest_time` int(11) unsigned DEFAULT NULL,
              `status` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(10) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `tory_id` (`tory_id`),
              KEY `sult_time` (`sult_time`),
              KEY `rest_time` (`rest_time`),
              KEY `status` (`status`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.13_2019-1-23
if (!manual_fieldexists('yl_welore_territory', 'atence')) {
    pdo_query("ALTER TABLE  `yl_welore_territory` ADD  `atence` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.13_2019-1-23
if (!manual_fieldexists('yl_welore_territory', 'atcipher')) {
    pdo_query("ALTER TABLE  `yl_welore_territory` ADD  `atcipher` LONGTEXT NULL ;");
}

#v1.0.13_2019-1-23
if (!manual_fieldexists('yl_welore_territory_interest', 'reason')) {
    pdo_query("ALTER TABLE  `yl_welore_territory_interest` ADD  `reason` LONGTEXT NULL ;");
}

#v1.0.13_2019-1-23
if (!manual_fieldexists('yl_welore_user_punch_range', 'invite_min')) {
    pdo_query("ALTER TABLE  `yl_welore_user_punch_range` ADD  `invite_min` DECIMAL( 18, 2 ) NOT NULL DEFAULT  '0';");
}

#v1.0.13_2019-1-23
if (!manual_fieldexists('yl_welore_user_punch_range', 'invite_max')) {
    pdo_query("ALTER TABLE  `yl_welore_user_punch_range` ADD  `invite_max` DECIMAL( 18, 2 ) NOT NULL DEFAULT  '0';");
}


#v1.0.13_2019-1-23
if (!manual_tableexists('yl_welore_user_invitation_code')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_invitation_code` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned DEFAULT NULL,
              `code` varchar(1000) DEFAULT NULL,
              `much_id` int(11) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.13_2019-1-23
if (!manual_tableexists('yl_welore_user_respond_invitation')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_respond_invitation` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned DEFAULT NULL,
              `re_code` varchar(1000) DEFAULT NULL,
              `in_us_reward` decimal(18,2) NOT NULL DEFAULT '0.00',
              `re_us_reward` decimal(18,2) NOT NULL DEFAULT '0.00',
              `re_time` int(11) unsigned DEFAULT NULL,
              `much_id` int(11) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.14_2019-1-26
if (!manual_fieldexists('yl_welore_authority', 'ensure_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `ensure_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0' ;");
}

#v1.0.14_2019-1-26
if (!manual_tableexists('yl_welore_version')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_version` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `sign_code` varchar(500) DEFAULT NULL,
            `status` int(11) unsigned NOT NULL DEFAULT '0',
            `much_id` int(11) unsigned DEFAULT NULL,
            PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.17_2019-2-22
if (!manual_fieldexists('yl_welore_authority', 'video_setting')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `video_setting` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '60';");
}

#v1.0.19_2019-2-27
if (!manual_tableexists('yl_welore_paper_reply_duplex')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_paper_reply_duplex` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned DEFAULT NULL,
              `reply_id` int(11) unsigned DEFAULT NULL,
              `reply_user_id` int(11) unsigned DEFAULT NULL,
              `duplex_content` longtext,
              `duplex_time` int(11) unsigned DEFAULT NULL,
              `much_id` int(11) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `reply_id` (`reply_id`),
              KEY `duplex_time` (`duplex_time`),
              KEY `much_id` (`much_id`),
              KEY `reply_user_id` (`reply_user_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.19_2019-2-27
if (!manual_tableexists('yl_welore_contrar')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_contrar` (
              `id` int(10) unsigned NOT NULL,
              `rand_code` varchar(1000) NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
EOT;
    pdo_run($sql);
}


#v1.0.20_2019-3-4
if (!manual_tableexists('yl_welore_copyright')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_copyright` (
              `id` int(11) NOT NULL,
              `hermit` int(11) unsigned NOT NULL DEFAULT '0',
              PRIMARY KEY (`id`),
              KEY `hermit` (`hermit`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
EOT;
    pdo_run($sql);
}


#v1.0.20_2019-3-4
if (!manual_tableexists('yl_welore_paper_red_packet')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_paper_red_packet` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `paper_id` int(11) unsigned DEFAULT NULL,
              `initial_fraction` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
              `surplus_fraction` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
              `initial_quantity` int(11) unsigned NOT NULL DEFAULT '0',
              `surplus_quantity` int(11) unsigned NOT NULL DEFAULT '0',
              `red_type` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `paper_id` (`paper_id`),
              KEY `red_type` (`red_type`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.20_2019-3-4
if (!manual_tableexists('yl_welore_user_red_packet')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_red_packet` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned DEFAULT NULL,
              `red_packet_id` int(11) unsigned DEFAULT NULL,
              `obtain_fraction` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
              `obtain_time` int(11) unsigned DEFAULT NULL,
              `much_id` int(11) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `red_packet_id` (`red_packet_id`),
              KEY `obtain_time` (`obtain_time`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.20_2019-3-4
/* 已废弃
if (!manual_tableexists('yl_welore_shaky_fission')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_shaky_fission` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `release_single` int(11) unsigned NOT NULL DEFAULT '0',
              `release_fraction` decimal(18,2) NOT NULL DEFAULT '0.00',
              `reply_single` int(11) unsigned NOT NULL DEFAULT '0',
              `reply_fraction` decimal(18,2) NOT NULL DEFAULT '0.00',
              `packet_single` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}
*/


#v1.0.24_2019-3-18
if (!manual_fieldexists('yl_welore_authority', 'title_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `title_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}


#v1.0.27_2019-3-21
if (!manual_fieldexists('yl_welore_authority', 'video_member')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `video_member` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.27_2019-3-21
if (!manual_fieldexists('yl_welore_authority', 'voice_member')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `voice_member` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.27_2019-3-21
if (!manual_fieldexists('yl_welore_authority', 'graffiti_member')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `graffiti_member` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.27_2019-3-21
if (!manual_tableexists('yl_welore_home_topping')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_home_topping` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `paper_id` int(11) unsigned DEFAULT NULL,
              `top_time` int(11) unsigned DEFAULT NULL,
              `much_id` int(11) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `paper_id` (`paper_id`),
              KEY `top_time` (`top_time`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.27_2019-3-26
if (!manual_fieldexists('yl_welore_authority', 'recharge_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `recharge_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.27_2019-3-26
if (!manual_fieldexists('yl_welore_authority', 'receipt_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `receipt_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.27_2019-3-26
if (!manual_fieldexists('yl_welore_user', 'forbid_prompt')) {
    pdo_query("ALTER TABLE  `yl_welore_user` ADD  `forbid_prompt` TEXT NULL ;");
}

#v1.0.27_2019-3-27
if (!manual_fieldexists('yl_welore_paper', 'is_buy')) {
    pdo_query("ALTER TABLE  `yl_welore_paper` ADD  `is_buy` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.27_2019-3-27
if (!manual_fieldexists('yl_welore_paper', 'buy_price')) {
    pdo_query("ALTER TABLE  `yl_welore_paper` ADD  `buy_price` DECIMAL( 18, 2 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.27_2019-3-27
if (!manual_tableexists('yl_welore_paper_buy_user')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_paper_buy_user` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `paper_id` int(11) unsigned NOT NULL,
              `user_id` int(11) unsigned NOT NULL,
              `buy_price` decimal(18,2) unsigned NOT NULL DEFAULT '0.00',
              `buy_taxing` decimal(18,2) NOT NULL,
              `buy_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.27_2019-3-27
if (!manual_fieldexists('yl_welore_paper_smingle', 'buy_paper_taxing')) {
    pdo_query("ALTER TABLE  `yl_welore_paper_smingle` ADD  `buy_paper_taxing` DECIMAL( 18, 2 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.27_2019-3-28
if (!manual_tableexists('yl_welore_user_currency_conversion')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_currency_conversion` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `conver_type` int(11) unsigned NOT NULL,
              `conver_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.27_2019-3-26
/* 已废弃
if (!manual_fieldexists('yl_welore_authority', 'fraction_convert')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `fraction_convert` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}
*/

#v1.0.27_2019-3-26
/* 已废弃
if (!manual_fieldexists('yl_welore_authority', 'conch_convert')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `conch_convert` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}
*/

#v1.0.27_2019-3-26
if (!manual_fieldexists('yl_welore_authority', 'force_phone_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `force_phone_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.27_2019-3-31
if (!manual_fieldexists('yl_welore_user', 'user_access_ip')) {
    pdo_query("ALTER TABLE  `yl_welore_user` ADD  `user_access_ip` VARCHAR( 500 ) NULL ;");
}

#v1.0.27_2019-3-31
if (!manual_fieldexists('yl_welore_user', 'user_phone')) {
    pdo_query("ALTER TABLE  `yl_welore_user` ADD  `user_phone` VARCHAR( 500 ) NULL ;");
}

#v1.0.31_2019-4-16
if (!manual_fieldexists('yl_welore_authority', 'force_phone_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `title_input_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.32_2019-4-18
if (!manual_fieldexists('yl_welore_paper', 'address_name')) {
    pdo_query("ALTER TABLE  `yl_welore_paper` ADD  `address_name` VARCHAR( 500 ) NULL ;");
}

#v1.0.32_2019-4-18
if (!manual_fieldexists('yl_welore_paper', 'address_details')) {
    pdo_query("ALTER TABLE  `yl_welore_paper` ADD  `address_details` VARCHAR( 1000 ) NULL ;");
}

#v1.0.32_2019-4-18
if (!manual_fieldexists('yl_welore_authority', 'shop_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `shop_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.32_2019-4-18
if (!manual_fieldexists('yl_welore_user_red_packet', 'reply_id')) {
    pdo_query("ALTER TABLE  `yl_welore_user_red_packet` ADD  `reply_id` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.34_2019-4-23
if (!manual_fieldexists('yl_welore_user', 'user_last_time')) {
    pdo_query("ALTER TABLE  `yl_welore_user` ADD  `user_last_time` INT( 11 ) UNSIGNED NULL ;");
}

#v1.0.34_2019-4-23
if (!manual_fieldexists('yl_welore_paper', 'address_latitude')) {
    pdo_query("ALTER TABLE  `yl_welore_paper` ADD  `address_latitude` VARCHAR( 500 ) NULL ;");
}

#v1.0.34_2019-4-23
if (!manual_fieldexists('yl_welore_paper', 'address_longitude')) {
    pdo_query("ALTER TABLE  `yl_welore_paper` ADD  `address_longitude` VARCHAR( 500 ) NULL ;");
}

#v1.0.36_2019-4-26
if (!manual_fieldexists('yl_welore_paper_smingle', 'buy_paper_auto_review')) {
    pdo_query("ALTER TABLE  `yl_welore_paper_smingle` ADD  `buy_paper_auto_review` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.36_2019-4-26
if (!manual_fieldexists('yl_welore_paper_smingle', 'buy_paper_number_limit')) {
    pdo_query("ALTER TABLE  `yl_welore_paper_smingle` ADD  `buy_paper_number_limit` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.36_2019-4-26
if (!manual_fieldexists('yl_welore_authority', 'buy_paper_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `buy_paper_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.36_2019-4-26
if (!manual_fieldexists('yl_welore_authority', 'buy_paper_member')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `buy_paper_member` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.36_2019-4-29
if (!manual_fieldexists('yl_welore_paper_reply_duplex', 'whetd_time')) {
    pdo_query("ALTER TABLE  `yl_welore_paper_reply_duplex` ADD  `whetd_time` INT( 11 ) UNSIGNED NULL ;");
}

#v1.0.39_2019-5-9
if (!manual_fieldexists('yl_welore_paper_reply_duplex', 'whether_delete')) {
    pdo_query("ALTER TABLE  `yl_welore_paper_reply_duplex` ADD  `whether_delete` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.39_2019-5-9
if (!manual_fieldexists('yl_welore_paper_reply_duplex', 'whether_reason')) {
    pdo_query("ALTER TABLE  `yl_welore_paper_reply_duplex` ADD  `whether_reason` VARCHAR( 500 ) NULL ;");
}

#v1.0.39_2019-5-9
if (!manual_fieldexists('yl_welore_home_topping', 'style_type')) {
    pdo_query("ALTER TABLE  `yl_welore_home_topping` ADD  `style_type` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.40_2019-5-13
if (!manual_fieldexists('yl_welore_territory', 'is_del')) {
    pdo_query("ALTER TABLE  `yl_welore_territory` ADD  `is_del` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.42_2019-5-24
if (!manual_fieldexists('yl_welore_authority', 'tory_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `tory_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.42_2019-5-24
if (!manual_fieldexists('yl_welore_authority', 'welfare_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `welfare_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.42_2019-5-24
if (!manual_tableexists('yl_welore_user_violation')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_violation` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `nickname_offend` longtext,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.42_2019-5-24
if (!manual_tableexists('yl_welore_login_checking')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_login_checking` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_name` varchar(500) NOT NULL,
              `role` varchar(500) NOT NULL,
              `uniacid` int(11) unsigned NOT NULL,
              `login_ip` varchar(500) NOT NULL,
              `login_time` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.44_2019-6-6
if (!manual_fieldexists('yl_welore_authority', 'tribute_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `tribute_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.44_2019-6-6
if (!manual_fieldexists('yl_welore_authority', 'guard_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `guard_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.44_2019-6-6
if (!manual_fieldexists('yl_welore_authority', 'hair_audio_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `hair_audio_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.44_2019-6-6
if (!manual_fieldexists('yl_welore_authority', 'hair_graffiti_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `hair_graffiti_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.44_2019-6-6
if (!manual_fieldexists('yl_welore_authority', 'hair_video_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `hair_video_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.44_2019-6-10
if (!manual_fieldexists('yl_welore_design', 'elect_sheathe')) {
    pdo_query("ALTER TABLE  `yl_welore_design` ADD  `elect_sheathe` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.44_2019-6-10
if (!manual_fieldexists('yl_welore_design', 'ios_pay_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `ios_pay_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.44_2019-6-11
if (!manual_tableexists('yl_welore_combination')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_combination` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `home` longtext NOT NULL,
              `plaza` longtext NOT NULL,
              `goods` longtext NOT NULL,
              `user` longtext NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.48_2019-6-18
if (!manual_fieldexists('yl_welore_authority', 'reprint_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `reprint_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.49_2019-6-20
if (!manual_fieldexists('yl_welore_paper_smingle', 'reply_auto_review')) {
    pdo_query("ALTER TABLE  `yl_welore_paper_smingle` ADD  `reply_auto_review` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.49_2019-6-20
if (!manual_fieldexists('yl_welore_paper_smingle', 'reply_number_limit')) {
    pdo_query("ALTER TABLE  `yl_welore_paper_smingle` ADD  `reply_number_limit` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.49_2019-6-20
if (!manual_fieldexists('yl_welore_paper_reply', 'reply_status')) {
    pdo_query("ALTER TABLE  `yl_welore_paper_reply` ADD  `reply_status` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.49_2019-6-20
if (!manual_fieldexists('yl_welore_paper_reply', 'prove_time')) {
    pdo_query("ALTER TABLE  `yl_welore_paper_reply` ADD  `prove_time` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.52_2019-6-27
if (!manual_fieldexists('yl_welore_user_punch_range', 'fraction_scale')) {
    pdo_query("ALTER TABLE  `yl_welore_user_punch_range` ADD  `fraction_scale` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '10';");
}

#v1.0.52_2019-6-27
if (!manual_fieldexists('yl_welore_user_subsidy', 'exchange_rate')) {
    pdo_query("ALTER TABLE  `yl_welore_user_subsidy` ADD  `exchange_rate` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '10';");
}

#v1.0.54_2019-7-2
if (!manual_fieldexists('yl_welore_paper', 'tg_id')) {
    pdo_query("ALTER TABLE  `yl_welore_paper` ADD  `tg_id` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.54_2019-7-2
if (!manual_tableexists('yl_welore_gambit')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_gambit` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `gambit_name` varchar(500) NOT NULL,
              `add_time` int(11) unsigned NOT NULL,
              `scores` int(11) unsigned NOT NULL DEFAULT '0',
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.55_2019-7-4
if (!manual_fieldexists('yl_welore_prompt_msg', 'capriole')) {
    pdo_query("ALTER TABLE  `yl_welore_prompt_msg` ADD  `capriole` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.55_2019-7-4
if (!manual_fieldexists('yl_welore_prompt_msg', 'tyid')) {
    pdo_query("ALTER TABLE  `yl_welore_prompt_msg` ADD  `tyid` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.55_2019-7-4
if (!manual_fieldexists('yl_welore_user', 'tourist')) {
    pdo_query("ALTER TABLE  `yl_welore_user` ADD  `tourist` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.56_2019-7-10
if (!manual_fieldexists('yl_welore_user_smail', 'skip_type')) {
    pdo_query("ALTER TABLE  `yl_welore_user_smail` ADD  `skip_type` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.56_2019-7-10
if (!manual_fieldexists('yl_welore_user_smail', 'paper_id')) {
    pdo_query("ALTER TABLE  `yl_welore_user_smail` ADD  `paper_id` VARCHAR( 255 ) NULL ;");
}

#v1.0.56_2019-7-10
if (manual_fieldmatch('yl_welore_user', 'user_wechat_open_id', 'VARCHAR', 255) == -1) {
    pdo_query("ALTER TABLE  `yl_welore_user` CHANGE  `user_wechat_open_id`  `user_wechat_open_id` VARCHAR( 255 ) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;");
}

#v1.0.56_2019-7-10
if (!manual_indexexists('yl_welore_user', 'user_wechat_open_id')) {
    pdo_query("ALTER TABLE  `yl_welore_user` ADD UNIQUE (`user_wechat_open_id`);");
}

#v1.0.62_2019-7-26
if (!manual_fieldexists('yl_welore_authority', 'speech_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `speech_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.62_2019-7-26
if (!manual_tableexists('yl_welore_user_blacklist')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_blacklist` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `da_user_id` int(11) unsigned NOT NULL,
              `pu_user_id` int(11) unsigned NOT NULL,
              `bl_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `da_user_id` (`da_user_id`),
              KEY `pu_user_id` (`pu_user_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.62_2019-7-26
if (!manual_tableexists('yl_welore_user_leave_word')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_leave_word` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `se_user_id` int(11) unsigned NOT NULL,
              `re_user_id` int(11) unsigned NOT NULL,
              `le_content` longtext NOT NULL,
              `se_user_del` int(11) unsigned NOT NULL DEFAULT '0',
              `re_user_del` int(11) unsigned NOT NULL DEFAULT '0',
              `le_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `se_user_id` (`se_user_id`),
              KEY `re_user_id` (`re_user_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.62_2019-7-26
if (!manual_tableexists('yl_welore_user_recent_contacts')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_recent_contacts` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `chat_user_id` int(11) unsigned NOT NULL,
              `recent_user_id` int(11) unsigned NOT NULL,
              `blatter_time` int(11) unsigned NOT NULL,
              `last_msg` longtext NOT NULL,
              `blatter_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `chat_user_id` (`chat_user_id`),
              KEY `recent_user_id` (`recent_user_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.64_2019-8-5
if (!manual_fieldexists('yl_welore_paper', 'buy_price_type')) {
    pdo_query("ALTER TABLE  `yl_welore_paper` ADD  `buy_price_type` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.64_2019-8-5
if (!manual_fieldexists('yl_welore_paper_buy_user', 'buy_type')) {
    pdo_query("ALTER TABLE  `yl_welore_paper_buy_user` ADD  `buy_type` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.64_2019-8-5
if (!manual_fieldexists('yl_welore_paper_red_packet', 'initial_type')) {
    pdo_query("ALTER TABLE  `yl_welore_paper_red_packet` ADD  `initial_type` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.64_2019-8-5
if (!manual_fieldexists('yl_welore_paper_red_packet', 'initial_conch')) {
    pdo_query("ALTER TABLE  `yl_welore_paper_red_packet` ADD  `initial_conch` DECIMAL( 18, 2 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.64_2019-8-5
if (!manual_fieldexists('yl_welore_paper_red_packet', 'surplus_conch')) {
    pdo_query("ALTER TABLE  `yl_welore_paper_red_packet` ADD  `surplus_conch` DECIMAL( 18, 2 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.64_2019-8-5
if (!manual_fieldexists('yl_welore_user_red_packet', 'obtain_conch')) {
    pdo_query("ALTER TABLE  `yl_welore_user_red_packet` ADD  `obtain_conch` DECIMAL( 18, 2 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.67_2019-8-14
if (!manual_fieldexists('yl_welore_user_violation', 'content_offend')) {
    pdo_query("ALTER TABLE  `yl_welore_user_violation` ADD  `content_offend` LONGTEXT NULL;");
}

#v1.0.70_2019-8-20
if (!manual_fieldexists('yl_welore_paper', 'essence_time')) {
    pdo_query("ALTER TABLE  `yl_welore_paper` ADD  `essence_time` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.70_2019-8-20
if (!manual_fieldexists('yl_welore_user_violation', 'open_network_nickname_offend')) {
    pdo_query("ALTER TABLE  `yl_welore_user_violation` ADD  `open_network_nickname_offend` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.70_2019-8-20
if (!manual_fieldexists('yl_welore_user_violation', 'open_network_content_offend')) {
    pdo_query("ALTER TABLE  `yl_welore_user_violation` ADD  `open_network_content_offend` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.70_2019-8-20
if (!manual_fieldexists('yl_welore_authority', 'home_random_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `home_random_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.70_2019-8-20
if (!manual_fieldexists('yl_welore_authority', 'home_release_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `home_release_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.71_2019-8-31
if (!manual_fieldexists('yl_welore_polling', 'ad_type')) {
    pdo_query("ALTER TABLE  `yl_welore_polling` ADD  `ad_type` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.73_2019-9-11
if (!manual_fieldexists('yl_welore_authority', 'brisk_member')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `brisk_member` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.73_2019-9-11
if (!manual_fieldexists('yl_welore_authority', 'hair_brisk_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `hair_brisk_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.73_2019-9-11
if (!manual_tableexists('yl_welore_brisk_team')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_brisk_team` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `paper_id` int(11) unsigned NOT NULL,
              `is_approve` int(11) unsigned NOT NULL DEFAULT '0',
              `brisk_address` varchar(1000) NOT NULL,
              `brisk_address_latitude` varchar(500) NOT NULL,
              `brisk_address_longitude` varchar(500) NOT NULL,
              `start_time` int(11) unsigned NOT NULL,
              `end_time` int(11) unsigned NOT NULL,
              `number_of_people` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `paper_id` (`paper_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.73_2019-9-11
if (!manual_tableexists('yl_welore_user_brisk_team')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_brisk_team` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `paper_id` int(11) unsigned NOT NULL,
              `brisk_id` int(11) unsigned NOT NULL,
              `user_id` int(11) unsigned NOT NULL,
              `rand_captcha` varchar(500) NOT NULL,
              `partake_time` int(11) unsigned NOT NULL,
              `is_write_off` int(11) unsigned NOT NULL DEFAULT '0',
              `write_off_time` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.75_2019-9-23
if (!manual_fieldexists('yl_welore_paper', 'video_type')) {
    pdo_query("ALTER TABLE  `yl_welore_paper` ADD  `video_type` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.75_2019-9-23
if (!manual_fieldexists('yl_welore_paper', 'third_part_vid')) {
    pdo_query("ALTER TABLE  `yl_welore_paper` ADD  `third_part_vid` VARCHAR( 1000 ) NULL ;");
}

#v1.0.75_2019-9-24
if (!manual_fieldexists('yl_welore_user_violation', 'open_network_images_offend')) {
    pdo_query("ALTER TABLE  `yl_welore_user_violation` ADD  `open_network_images_offend` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.75_2019-9-24
if (!manual_fieldexists('yl_welore_authority', 'user_info_update_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `user_info_update_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '1';");
}

#v1.0.84_2019-10-31
if (!manual_tableexists('yl_welore_task')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_task` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `task_name` varchar(500) NOT NULL,
              `task_type` int(11) unsigned NOT NULL,
              `task_cycle` int(11) unsigned NOT NULL,
              `task_reward_type` int(11) unsigned NOT NULL,
              `task_frequency` int(11) unsigned NOT NULL,
              `poor_task_salary` decimal(22,0) unsigned NOT NULL,
              `rich_task_salary` decimal(22,0) unsigned NOT NULL DEFAULT '0',
              `scores` int(11) unsigned NOT NULL DEFAULT '0',
              `channel_time` int(11) unsigned NOT NULL,
              `is_being` int(11) unsigned NOT NULL DEFAULT '0',
              `death_time` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `much_id` (`much_id`),
              KEY `scores` (`scores`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.84_2019-10-31
if (!manual_tableexists('yl_welore_task_logger')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_task_logger` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `task_id` int(11) unsigned NOT NULL,
              `user_id` int(11) unsigned NOT NULL,
              `is_rich_person` int(11) unsigned NOT NULL,
              `task_salary` decimal(22,0) unsigned NOT NULL,
              `complete_description` varchar(1000) NOT NULL,
              `complete_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `task_id` (`task_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.84_2019-10-31
if (!manual_tableexists('yl_welore_user_level')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_level` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `level_hierarchy` int(11) unsigned NOT NULL,
              `level_name` varchar(500) NOT NULL,
              `level_icon` varchar(1000) NOT NULL,
              `need_experience` decimal(22,0) unsigned NOT NULL,
              `honor_point` decimal(22,0) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.84_2019-10-31
if (!manual_tableexists('yl_welore_medal')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_medal` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `merit_icon` varchar(1000) NOT NULL,
              `merit_name` varchar(1000) NOT NULL,
              `merit_annotate` text NOT NULL,
              `unlock_outlay` decimal(22,0) unsigned NOT NULL,
              `prepare_time` int(11) unsigned NOT NULL,
              `status` int(11) unsigned NOT NULL DEFAULT '1',
              `scores` int(11) unsigned NOT NULL DEFAULT '0',
              `purge_time` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `much_id` (`much_id`),
              KEY `scores` (`scores`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.84_2019-10-31
if (!manual_tableexists('yl_welore_user_medal')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_medal` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `medal_id` int(11) unsigned NOT NULL,
              `unlock_outlay` decimal(22,0) unsigned NOT NULL,
              `unlock_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `medal_id` (`medal_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.84_2019-10-31
if (!manual_tableexists('yl_welore_user_forwarded')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_forwarded` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `paper_id` int(11) unsigned NOT NULL,
              `user_id` int(11) unsigned NOT NULL,
              `fulfill_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `paper_id` (`paper_id`),
              KEY `user_id` (`user_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.84_2019-10-31
if (!manual_tableexists('yl_welore_user_better_logger')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_better_logger` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `duration` int(11) unsigned NOT NULL,
              `buy_price` decimal(18,2) unsigned NOT NULL,
              `buy_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.84_2019-10-31
if (!manual_tableexists('yl_welore_user_exp_glory_logger')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_exp_glory_logger` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `type` int(11) unsigned NOT NULL,
              `cypher` int(11) unsigned NOT NULL,
              `dot_before` decimal(22,0) unsigned NOT NULL,
              `points` decimal(22,0) unsigned NOT NULL,
              `dot_after` decimal(22,0) unsigned NOT NULL,
              `dot_cap` varchar(1000) NOT NULL,
              `receive_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `type` (`type`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.0.84_2019-10-31
if (!manual_fieldexists('yl_welore_user', 'wear_merit')) {
    pdo_query("ALTER TABLE  `yl_welore_user` ADD  `wear_merit` VARCHAR( 1000 ) NOT NULL DEFAULT  '0' ;");
}

#v1.0.84_2019-10-31
if (!manual_fieldexists('yl_welore_user', 'level')) {
    pdo_query("ALTER TABLE  `yl_welore_user` ADD  `level` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.84_2019-10-31
if (!manual_fieldexists('yl_welore_user', 'experience')) {
    pdo_query("ALTER TABLE  `yl_welore_user` ADD  `experience` DECIMAL( 22 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.84_2019-10-31
if (!manual_fieldexists('yl_welore_user', 'honor_point')) {
    pdo_query("ALTER TABLE  `yl_welore_user` ADD  `honor_point` DECIMAL( 22 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.84_2019-10-31
if (!manual_fieldexists('yl_welore_territory', 'release_level')) {
    pdo_query("ALTER TABLE  `yl_welore_territory` ADD  `release_level` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.84_2019-10-31
if (!manual_fieldexists('yl_welore_advertise', 'pre_post_twig')) {
    pdo_query("ALTER TABLE  `yl_welore_advertise` ADD  `pre_post_twig` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.0.84_2019-10-31
if (!manual_fieldexists('yl_welore_advertise', 'pre_post_id')) {
    pdo_query("ALTER TABLE  `yl_welore_advertise` ADD  `pre_post_id` VARCHAR( 255 ) NULL ;");
}

#v1.0.92_2019-11-19
if (!manual_fieldexists('yl_welore_polling', 'playbill_name')) {
    pdo_query("ALTER TABLE  `yl_welore_polling` ADD  `playbill_name` VARCHAR( 500 ) NULL ;");
}

#v1.0.98_2020-01-08
if (!manual_tableexists('yl_welore_subscribe')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_subscribe` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `parallelism_data` longtext,
              `much_id` int(11) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.7_2020-03-03
if (!manual_tableexists('yl_welore_event_raffle')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_event_raffle` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `er_name` varchar(500) NOT NULL,
              `start_time` int(11) unsigned NOT NULL,
              `end_time` int(11) unsigned NOT NULL,
              `deplete_type` int(11) unsigned NOT NULL,
              `deplete_score` decimal(18,0) unsigned NOT NULL,
              `illustrate` longtext NOT NULL,
              `prize_content` text NOT NULL,
              `turning_speed` int(11) unsigned NOT NULL,
              `turntable_style` int(11) unsigned NOT NULL,
              `free_chance` int(11) unsigned NOT NULL,
              `draw_restrictions` int(11) unsigned NOT NULL,
              `define_time` int(11) unsigned NOT NULL,
              `status` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.7_2020-03-03
if (!manual_tableexists('yl_welore_user_raffle_records')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_raffle_records` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `record_number` varchar(500) NOT NULL,
              `user_id` int(11) unsigned NOT NULL,
              `er_id` int(11) unsigned NOT NULL,
              `join_time` int(11) unsigned NOT NULL,
              `win_type` int(11) unsigned NOT NULL,
              `prize_name` varchar(500) NOT NULL,
              `reward_score` decimal(18,0) unsigned NOT NULL DEFAULT '0',
              `address_details` varchar(1000) DEFAULT NULL,
              `courier_convert` varchar(500) DEFAULT NULL,
              `delivery_status` int(11) unsigned NOT NULL DEFAULT '0',
              `see_time` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.7_2020-03-03
if (!manual_fieldexists('yl_welore_user_honorary', 'chop_type')) {
    pdo_query("ALTER TABLE  `yl_welore_user_honorary` ADD  `chop_type` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.1.7_2020-03-03
if (!manual_fieldexists('yl_welore_user_honorary', 'define_price')) {
    pdo_query("ALTER TABLE  `yl_welore_user_honorary` ADD  `define_price` VARCHAR( 5000 ) NULL;");
}

#v1.1.8_2020-03-18
if (!manual_fieldexists('yl_welore_authority', 're_force_phone_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `re_force_phone_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.1.9_2020-04-02
if (!manual_fieldexists('yl_welore_authority', 'warrant_arbor')) {
    pdo_query("ALTER TABLE  `yl_welore_authority` ADD  `warrant_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT  '0';");
}

#v1.1.13_2020-04-30
if (!manual_fieldexists('yl_welore_polling', 'wx_app_url')) {
    pdo_query("ALTER TABLE  `yl_welore_polling` ADD  `wx_app_url` VARCHAR( 1000 ) NULL ;");
}

#v1.1.18_2020-07-11
if (!manual_fieldexists('yl_welore_territory', 'visit_level')) {
    pdo_query("ALTER TABLE `yl_welore_territory` ADD `visit_level` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.21_2020-08-05
if (!manual_fieldexists('yl_welore_paper_smingle', 'tractate_font_size')) {
    pdo_query("ALTER TABLE `yl_welore_paper_smingle` ADD `tractate_font_size` INT( 11 ) UNSIGNED NOT NULL DEFAULT '14';");
}

#v1.1.24_2020-09-13
if (!manual_fieldexists('yl_welore_event_raffle', 'free_ad_valve')) {
    pdo_query("ALTER TABLE `yl_welore_event_raffle` ADD `free_ad_valve` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.24_2020-09-13
if (!manual_tableexists('yl_welore_user_watch_ads')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_watch_ads` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `ad_type` int(11) unsigned NOT NULL,
              `fulfill_type` int(11) unsigned NOT NULL,
              `fulfill_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.24_2020-09-13
if (!manual_fieldexists('yl_welore_advertise', 'incentive_id')) {
    pdo_query("ALTER TABLE `yl_welore_advertise` ADD `incentive_id` VARCHAR( 255 ) NULL ;");
}

#v1.1.24_2020-09-13
if (!manual_fieldexists('yl_welore_advertise', 'incentive_duct')) {
    pdo_query("ALTER TABLE `yl_welore_advertise` ADD `incentive_duct` INT( 11 ) UNSIGNED NOT NULL DEFAULT '5';");
}

#v1.1.24_2020-09-16
if (!manual_tableexists('yl_welore_special_nickname')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_special_nickname` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `special_name` varchar(500) NOT NULL,
              `special_style` varchar(500) NOT NULL,
              `unlock_outlay` decimal(22,0) unsigned NOT NULL,
              `prepare_time` int(11) unsigned NOT NULL,
              `status` int(11) NOT NULL DEFAULT '1',
              `scores` int(11) unsigned NOT NULL DEFAULT '0',
              `purge_time` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.24_2020-09-16
if (!manual_tableexists('yl_welore_user_special_nickname')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_special_nickname` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `special_id` int(11) unsigned NOT NULL,
              `unlock_outlay` decimal(22,0) unsigned NOT NULL,
              `unlock_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `medal_id` (`special_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.24_2020-09-16
if (!manual_fieldexists('yl_welore_user', 'wear_special_id')) {
    pdo_query("ALTER TABLE `yl_welore_user` ADD `wear_special_id` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.24_2020-09-17
if (!manual_fieldexists('yl_welore_design', 'mall')) {
    pdo_query("ALTER TABLE `yl_welore_design` ADD `mall` VARCHAR( 500 ) NULL ;");
}

#v1.1.31_2020-12-09
if (!manual_fieldexists('yl_welore_authority', 'whisper_arbor')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `whisper_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.31_2020-12-09
if (!manual_fieldexists('yl_welore_paper_smingle', 'auto_hiss')) {
    pdo_query("ALTER TABLE `yl_welore_paper_smingle` ADD `auto_hiss` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.31_2020-12-09
if (!manual_fieldexists('yl_welore_paper_smingle', 'hiss_limit')) {
    pdo_query("ALTER TABLE `yl_welore_paper_smingle` ADD `hiss_limit` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.31_2020-12-09
if (!manual_fieldexists('yl_welore_paper_smingle', 'reply_auto_hiss')) {
    pdo_query("ALTER TABLE `yl_welore_paper_smingle` ADD `reply_auto_hiss` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.31_2020-12-09
if (!manual_fieldexists('yl_welore_paper_smingle', 'reply_hiss_limit')) {
    pdo_query("ALTER TABLE `yl_welore_paper_smingle` ADD `reply_hiss_limit` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.31_2020-12-09
if (!manual_tableexists('yl_welore_sprout')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_sprout` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `templet_id` varchar(500) NOT NULL,
              `user_id` int(11) unsigned NOT NULL,
              `at_user_id` int(11) unsigned NOT NULL DEFAULT '0',
              `content` varchar(2000) NOT NULL,
              `praise_number` int(11) unsigned NOT NULL DEFAULT '0',
              `status` int(11) unsigned NOT NULL DEFAULT '0',
              `send_time` int(11) unsigned NOT NULL,
              `check_time` int(11) unsigned NOT NULL DEFAULT '0',
              `reject_reason` varchar(500) DEFAULT NULL,
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `at_user_id` (`at_user_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.31_2020-12-09
if (!manual_tableexists('yl_welore_sprout_reply')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_sprout_reply` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `sp_id` int(11) unsigned NOT NULL,
              `re_user_id` int(11) unsigned NOT NULL,
              `re_content` varchar(2000) NOT NULL,
              `status` int(11) unsigned NOT NULL DEFAULT '0',
              `reply_time` int(11) unsigned NOT NULL,
              `check_time` int(11) unsigned NOT NULL DEFAULT '0',
              `reject_reason` varchar(500) DEFAULT NULL,
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `sp_id` (`sp_id`),
              KEY `re_user_id` (`re_user_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.37_2021-03-05
if (!manual_tableexists('yl_welore_avatar_frame')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_avatar_frame` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `adorn_icon` varchar(1000) NOT NULL,
              `adorn_name` varchar(1000) NOT NULL,
              `unlock_fraction` decimal(22,2) unsigned NOT NULL,
              `prepare_time` int(11) unsigned NOT NULL,
              `status` int(11) unsigned NOT NULL DEFAULT '1',
              `scores` int(11) unsigned NOT NULL DEFAULT '0',
              `purge_time` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `much_id` (`much_id`),
              KEY `scores` (`scores`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.37_2021-03-05
if (!manual_tableexists('yl_welore_user_avatar_frame')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_avatar_frame` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `af_id` int(11) unsigned NOT NULL,
              `unlock_outlay` decimal(22,0) unsigned NOT NULL,
              `unlock_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `medal_id` (`af_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.37_2021-03-05
if (!manual_fieldexists('yl_welore_user', 'wear_af')) {
    pdo_query("ALTER TABLE `yl_welore_user` ADD `wear_af` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.37_2021-03-05
if (!manual_fieldexists('yl_welore_paper', 'study_video_bulk')) {
    pdo_query("ALTER TABLE `yl_welore_paper` ADD `study_video_bulk` VARCHAR( 255 ) NOT NULL DEFAULT '0,0';");
}

#v1.1.37_2021-03-05
if (!manual_fieldexists('yl_welore_authority', 'video_auto_arbor')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `video_auto_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.37_2021-03-05
pdo_query("UPDATE `yl_welore_user_withdraw_money` SET `seek_time`=verify_time WHERE `seek_time` IS NULL;");

#v1.1.38_2021-03-20
if (!manual_tableexists('yl_welore_paper_vote')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_paper_vote` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `paper_id` int(11) unsigned NOT NULL,
              `ballot_name` varchar(500) NOT NULL,
              `cheat_ballot` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.38_2021-03-20
if (!manual_tableexists('yl_welore_user_vote')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_vote` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `paper_id` int(11) unsigned NOT NULL,
              `pv_id` int(11) unsigned NOT NULL,
              `decide_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.38_2021-03-20
if (!manual_fieldexists('yl_welore_paper', 'vote_deadline')) {
    pdo_query("ALTER TABLE `yl_welore_paper` ADD `vote_deadline` INT( 11 ) NOT NULL DEFAULT '0';");
}

#v1.1.38_2021-03-20
if (!manual_fieldexists('yl_welore_authority', 'hair_vote_arbor')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `hair_vote_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT '1';");
}

#v1.1.38_2021-03-20
if (!manual_fieldexists('yl_welore_authority', 'vote_member')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `vote_member` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.39_2021-03-25
if (!manual_tableexists('yl_welore_version_overdue')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_version_overdue` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `sign_code` varchar(255) NOT NULL,
              `update_time` int(11) NOT NULL,
              `most_above_time` int(11) NOT NULL DEFAULT '0',
              `much_id` int(11) NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.39_2021-03-25
if (!manual_fieldexists('yl_welore_user', 'token_impede')) {
    pdo_query("ALTER TABLE `yl_welore_user` ADD `token_impede` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.40_2021-03-30
if (!manual_fieldexists('yl_welore_outlying', 'upyun_follow')) {
    pdo_query("ALTER TABLE `yl_welore_outlying` ADD `upyun_follow` VARCHAR( 1000 ) NULL ;");
}

#v1.1.41_2021-05-12
if (!manual_fieldexists('yl_welore_territory', 'realm_back_img')) {
    pdo_query("ALTER TABLE `yl_welore_territory` ADD `realm_back_img` VARCHAR( 2000 ) NULL ;");
}

#v1.1.41_2021-05-12
if (!manual_fieldexists('yl_welore_advertise', 'lattice_twig')) {
    pdo_query("ALTER TABLE `yl_welore_advertise` ADD `lattice_twig` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.41_2021-05-12
if (!manual_fieldexists('yl_welore_advertise', 'lattice_id')) {
    pdo_query("ALTER TABLE `yl_welore_advertise` ADD `lattice_id` VARCHAR( 255 ) NULL ;");
}

#v1.1.43_2021-05-19
if (!manual_tableexists('yl_welore_camouflage_card')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_camouflage_card` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `forgery_name` varchar(255) NOT NULL,
              `forgery_head` varchar(2000) NOT NULL,
              `unlock_fraction` decimal(22,2) unsigned NOT NULL,
              `cost_day` int(11) unsigned NOT NULL,
              `scores` int(11) NOT NULL DEFAULT '0',
              `making_time` int(11) unsigned NOT NULL,
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.43_2021-05-19
if (!manual_tableexists('yl_welore_user_camouflage_card')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_camouflage_card` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `ccid` int(11) unsigned NOT NULL,
              `user_id` int(11) unsigned NOT NULL,
              `expired_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.43_2021-05-19
if (!manual_fieldexists('yl_welore_paper', 'uccid')) {
    pdo_query("ALTER TABLE `yl_welore_paper` ADD `uccid` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.43_2021-05-19
if (!manual_fieldexists('yl_welore_paper_reply', 'uccid')) {
    pdo_query("ALTER TABLE `yl_welore_paper_reply` ADD `uccid` INT( 111 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.43_2021-05-19
if (!manual_fieldexists('yl_welore_paper_reply_duplex', 'uccid')) {
    pdo_query("ALTER TABLE `yl_welore_paper_reply_duplex` ADD `uccid` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.43_2021-05-19
if (!manual_fieldexists('yl_welore_paper_reply_duplex', 're_uccid')) {
    pdo_query("ALTER TABLE `yl_welore_paper_reply_duplex` ADD `re_uccid` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.43_2021-05-19
if (!manual_fieldexists('yl_welore_authority', 'engrave_arbor')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `engrave_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.47_2021-07-17
if (!manual_fieldexists('yl_welore_authority', 'tory_sort_arbor')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `tory_sort_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.52_2021-09-09
if (!manual_fieldexists('yl_welore_paper', 'img_show_type')) {
    pdo_query("ALTER TABLE `yl_welore_paper` ADD `img_show_type` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.52_2021-09-13
if (!manual_tableexists('yl_welore_user_leaderboard')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_leaderboard` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `ranking_name` varchar(255) NOT NULL,
              `wont_sort` text NOT NULL,
              `status` int(11) unsigned NOT NULL,
              `scores` int(11) NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.52_2021-09-14
if (!manual_fieldexists('yl_welore_territory', 'release_count')) {
    pdo_query("ALTER TABLE `yl_welore_territory` ADD `release_count` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.52_2021-09-14
if (!manual_fieldexists('yl_welore_task', 'tory_id')) {
    pdo_query("ALTER TABLE `yl_welore_task` ADD `tory_id` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.52_2021-09-14
if (!manual_tableexists('yl_welore_attest')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_attest` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `at_name` varchar(255) NOT NULL,
              `at_icon` varchar(2000) NOT NULL,
              `handsel_day` int(11) unsigned NOT NULL DEFAULT '0',
              `custom_form` longtext NOT NULL,
              `introduction` longtext NOT NULL,
              `status` int(11) unsigned NOT NULL,
              `scores` int(11) NOT NULL DEFAULT '0',
              `create_time` int(11) unsigned NOT NULL,
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.52_2021-09-14
if (!manual_tableexists('yl_welore_user_attest')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_attest` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `at_id` int(11) unsigned NOT NULL,
              `user_id` int(11) unsigned NOT NULL,
              `postback_data` longtext NOT NULL,
              `adopt_status` int(11) unsigned NOT NULL DEFAULT '0',
              `refer_time` int(11) unsigned NOT NULL,
              `refuse_time` int(11) NOT NULL DEFAULT '0',
              `ut_inject` varchar(500) DEFAULT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.52_2021-09-18
if (!manual_fieldexists('yl_welore_authority', 'travel_arbor')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `travel_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.57_2021-11-04
if (!manual_tableexists('yl_welore_feeling')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_feeling` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `contact_person` varchar(255) NOT NULL,
              `age` int(11) unsigned NOT NULL,
              `gender` int(11) unsigned NOT NULL,
              `constellation` int(11) unsigned NOT NULL,
              `remain_city` varchar(255) NOT NULL,
              `restrict_city` varchar(255) NOT NULL DEFAULT '0',
              `hedge_content` longtext NOT NULL,
              `longevity` int(11) unsigned NOT NULL DEFAULT '0',
              `create_time` int(11) unsigned NOT NULL,
              `check_status` int(11) unsigned NOT NULL DEFAULT '0',
              `check_time` int(11) unsigned NOT NULL DEFAULT '0',
              `check_opinion` varchar(500) DEFAULT NULL,
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.57_2021-11-04
if (!manual_tableexists('yl_welore_feeling_pay')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_feeling_pay` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `order_id` varchar(255) NOT NULL,
              `user_id` int(11) unsigned NOT NULL,
              `fg_id` int(11) unsigned NOT NULL,
              `pay_type` int(11) unsigned NOT NULL,
              `pay_status` int(11) unsigned NOT NULL DEFAULT '0',
              `create_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.57_2021-11-04
if (!manual_tableexists('yl_welore_feeling_stipulate')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_feeling_stipulate` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `bare_location` int(11) unsigned NOT NULL DEFAULT '0',
              `bare_direction` int(11) unsigned NOT NULL DEFAULT '1',
              `direction_bottom` int(11) unsigned NOT NULL DEFAULT '20',
              `bare_img_url` varchar(2000) NOT NULL,
              `auto_careful` int(11) unsigned NOT NULL DEFAULT '0',
              `throw_price_male` decimal(18,2) unsigned NOT NULL,
              `throw_price_female` decimal(18,2) unsigned NOT NULL,
              `pick_price_male` decimal(18,2) unsigned NOT NULL,
              `pick_price_female` decimal(18,2) unsigned NOT NULL,
              `pay_type` int(11) unsigned NOT NULL,
              `throw_limit` int(11) unsigned NOT NULL DEFAULT '0',
              `pick_limit` int(11) unsigned NOT NULL DEFAULT '0',
              `ten_local_key` varchar(255) NOT NULL,
              `notice` longtext NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.57_2021-11-04
if (!manual_tableexists('yl_welore_user_feeling')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_feeling` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `fg_id` int(11) unsigned NOT NULL,
              `pull_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.57_2021-11-04
if (!manual_fieldexists('yl_welore_authority', 'feeling_arbor')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `feeling_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.58_2021-12-01
if (!manual_fieldexists('yl_welore_territory', 'group_qrcode')) {
    pdo_query("ALTER TABLE `yl_welore_territory` ADD `group_qrcode` VARCHAR( 1000 ) NULL ;");
}

#v1.1.60_2021-12-24
if (!manual_tableexists('yl_welore_new_user_task')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_new_user_task` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `reward_status` int(11) unsigned NOT NULL DEFAULT '0',
              `reg_less_day` int(11) unsigned NOT NULL DEFAULT '0',
              `reward_type` int(11) unsigned NOT NULL,
              `reward_code` decimal(18,2) unsigned NOT NULL,
              `reward_count` int(11) unsigned NOT NULL DEFAULT '1',
              `tory_ids` varchar(500) NOT NULL,
              `paper_ids` varchar(500) NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.60_2021-12-24
if (!manual_tableexists('yl_welore_new_user_task_record')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_new_user_task_record` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `tory_id` int(11) unsigned NOT NULL,
              `paper_id` int(11) unsigned NOT NULL,
              `reward_type` int(11) unsigned NOT NULL,
              `reward_code` decimal(18,2) unsigned NOT NULL,
              `reward_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.62_2022-02-13
if (!manual_fieldexists('yl_welore_paper_smingle', 'discuss_auto_review')) {
    pdo_query("ALTER TABLE `yl_welore_paper_smingle` ADD `discuss_auto_review` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.62_2022-02-13
if (!manual_fieldexists('yl_welore_paper_smingle', 'discuss_number_limit')) {
    pdo_query("ALTER TABLE `yl_welore_paper_smingle` ADD `discuss_number_limit` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.62_2022-02-13
if (!manual_fieldexists('yl_welore_paper_reply_duplex', 'duplex_status')) {
    pdo_query("ALTER TABLE `yl_welore_paper_reply_duplex` ADD `duplex_status` INT( 11 ) UNSIGNED NOT NULL DEFAULT '1';");
}

#v1.1.62_2022-02-13
if (!manual_fieldexists('yl_welore_paper_reply_duplex', 'check_opinion')) {
    pdo_query("ALTER TABLE `yl_welore_paper_reply_duplex` ADD `check_opinion` VARCHAR( 255 ) NULL ;");
}

#v1.1.63_2022-03-09
if (!manual_fieldexists('yl_welore_user_punch_range', 'currency_icon')) {
    pdo_query("ALTER TABLE `yl_welore_user_punch_range` ADD `currency_icon` VARCHAR( 1000 ) NOT NULL ;");
}

#v1.1.66_2022-03-17
if (!manual_tableexists('yl_welore_template_slot')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_template_slot` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `apollo` text NOT NULL,
              `prometheus` varchar(255) NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.66_2022-03-17
if (!manual_fieldexists('yl_welore_authority', 'overall_arbor')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `overall_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.67_2022-03-24
if (!manual_fieldexists('yl_welore_shop', 'auto_delivery')) {
    pdo_query("ALTER TABLE `yl_welore_shop` ADD `auto_delivery` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.67_2022-03-24
if (!manual_tableexists('yl_welore_mucilage')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_mucilage` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `card_code` varchar(255) NOT NULL,
              `card_type` int(11) unsigned NOT NULL,
              `financial_type` int(11) unsigned NOT NULL,
              `face_value` decimal(18,2) unsigned NOT NULL,
              `is_sell` int(11) unsigned NOT NULL DEFAULT '0',
              `is_use` int(11) unsigned NOT NULL DEFAULT '0',
              `status` int(11) unsigned NOT NULL,
              `shop_id` int(11) NOT NULL DEFAULT '0',
              `create_time` int(11) unsigned NOT NULL,
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `card_type` (`card_type`),
              KEY `financial_type` (`financial_type`),
              KEY `shop_id` (`shop_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.67_2022-03-24
if (!manual_tableexists('yl_welore_mucilage_use_annaly')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_mucilage_use_annaly` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `mu_id` int(11) unsigned NOT NULL,
              `user_id` int(11) unsigned NOT NULL,
              `use_ip` varchar(255) NOT NULL,
              `use_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `mu_id` (`mu_id`),
              KEY `user_id` (`user_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.67_2022-03-25
if (!manual_tableexists('yl_welore_template_slot_valve')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_template_slot_valve` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `petal_data` text NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.71_2022-04-08
if (!manual_tableexists('yl_welore_netdisc')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_netdisc` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `file_type` int(11) unsigned NOT NULL,
              `file_md5` varchar(255) NOT NULL,
              `file_suffix` varchar(255) NOT NULL,
              `file_size` decimal(32,0) NOT NULL,
              `file_address` varchar(2000) NOT NULL,
              `file_status` int(11) unsigned NOT NULL DEFAULT '1',
              `up_user_id` int(11) unsigned NOT NULL,
              `up_user_ip` varchar(255) NOT NULL,
              `add_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.71_2022-04-08
if (!manual_tableexists('yl_welore_netdisc_belong')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_netdisc_belong` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `nc_id` int(11) unsigned NOT NULL DEFAULT '0',
              `user_id` int(11) unsigned NOT NULL,
              `file_name` varchar(500) NOT NULL,
              `parent_path_id` int(11) unsigned NOT NULL DEFAULT '0',
              `is_dir` int(11) unsigned NOT NULL DEFAULT '0',
              `add_time` int(11) unsigned NOT NULL,
              `is_sell` int(11) unsigned NOT NULL,
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.71_2022-04-08
if (!manual_tableexists('yl_welore_netdisc_config')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_netdisc_config` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `disk_size` decimal(32,0) unsigned NOT NULL,
              `upload_size_limit` decimal(32,0) unsigned NOT NULL DEFAULT '10485760',
              `upload_type_limited` varchar(1000) DEFAULT NULL,
              `use_protocol` longtext NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.71_2022-04-08
if (!manual_tableexists('yl_welore_netdisc_sell')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_netdisc_sell` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `pa_id` int(11) unsigned NOT NULL,
              `nc_id` int(11) unsigned NOT NULL,
              `nb_id` int(11) unsigned NOT NULL,
              `is_sell` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `pa_id` (`pa_id`),
              KEY `nc_id` (`nc_id`),
              KEY `nb_id` (`nb_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.71_2022-04-08
if (!manual_tableexists('yl_welore_netdisc_user_volume')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_netdisc_user_volume` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `quota_size` decimal(32,0) unsigned NOT NULL,
              `exp_time` int(11) unsigned NOT NULL DEFAULT '0',
              `use_status` int(11) unsigned NOT NULL DEFAULT '1',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.71_2022-04-08
if (!manual_fieldexists('yl_welore_paper', 'call_phone')) {
    pdo_query("ALTER TABLE `yl_welore_paper` ADD `call_phone` VARCHAR( 255 ) NULL ;");
}

#v1.1.71_2022-04-11
if (!manual_fieldexists('yl_welore_authority', 'allow_user_topic')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `allow_user_topic` INT( 11 ) UNSIGNED NOT NULL DEFAULT '1';");
}

#v1.1.72_2022-04-13
if (!manual_tableexists('yl_welore_netdisc_storage')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_netdisc_storage` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `quicken_type` int(11) NOT NULL DEFAULT '-1',
              `oss_follow` varchar(1000) DEFAULT NULL,
              `qiniu_follow` varchar(1000) DEFAULT NULL,
              `cos_follow` varchar(1000) DEFAULT NULL,
              `upyun_follow` varchar(1000) DEFAULT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.72_2022-04-13
if (!manual_tableexists('yl_welore_wx_popular')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_wx_popular` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `wx_app_name` varchar(255) DEFAULT NULL,
              `wx_app_id` varchar(1000) DEFAULT NULL,
              `wx_app_secret` varchar(1000) DEFAULT NULL,
              `access_token` varchar(1000) DEFAULT NULL,
              `token_exp_time` int(11) unsigned NOT NULL DEFAULT '0',
              `imagine_data` text,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.72_2022-04-13
if (!manual_tableexists('yl_welore_wx_popular_bind_user')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_wx_popular_bind_user` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `open_id` varchar(500) NOT NULL,
              `bind_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.73_2022-04-16
if (!manual_fieldexists('yl_welore_wx_popular', 'wx_app_qrcode')) {
    pdo_query("ALTER TABLE `yl_welore_wx_popular` ADD `wx_app_qrcode` VARCHAR( 2000 ) NULL ;");
}

#v1.1.73_2022-04-16
if (!manual_fieldexists('yl_welore_user', 'user_home_access_status')) {
    pdo_query("ALTER TABLE `yl_welore_user` ADD `user_home_access_status` INT( 11 ) UNSIGNED NOT NULL DEFAULT '1';");
}

#v1.1.74_2022-04-18
if (!manual_fieldexists('yl_welore_user_leave_word', 'le_type')) {
    pdo_query("ALTER TABLE `yl_welore_user_leave_word` ADD `le_type` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.74_2022-04-19
if (!manual_fieldexists('yl_welore_user', 'user_wechat_union_id')) {
    pdo_query("ALTER TABLE `yl_welore_user` ADD `user_wechat_union_id` VARCHAR( 255 ) NULL ;");
}

#v1.1.74_2022-04-19
if (!manual_fieldexists('yl_welore_wx_popular_bind_user', 'union_id')) {
    pdo_query("ALTER TABLE `yl_welore_wx_popular_bind_user` ADD `union_id` VARCHAR( 500 ) NULL ;");
}

#v1.1.75_2022-04-21
if (!manual_tableexists('yl_welore_user_screenshot')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_user_screenshot` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `scene_name` varchar(255) NOT NULL,
              `scene_path` varchar(500) NOT NULL,
              `location_ip` varchar(500) NOT NULL,
              `add_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.75_2022-04-21
if (!manual_fieldexists('yl_welore_authority', 'pre_content_arbor')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `pre_content_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.77_2022-04-27
if (!manual_fieldexists('yl_welore_user', 'user_open_type')) {
    pdo_query("ALTER TABLE `yl_welore_user` ADD `user_open_type` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.80_2022-04-29
if (!manual_fieldexists('yl_welore_user_leave_word', 'le_read_status')) {
    pdo_query("ALTER TABLE `yl_welore_user_leave_word` ADD `le_read_status` INT( 11 ) UNSIGNED NOT NULL DEFAULT '1';");
}

#v1.1.82_2022-05-13
if (!manual_tableexists('yl_welore_phone_blacklist')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_phone_blacklist` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `block_type` int(11) unsigned NOT NULL,
              `block_rule` varchar(1000) NOT NULL,
              `intercept_type` int(11) unsigned NOT NULL,
              `create_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.82_2022-05-16
if (!manual_tableexists('yl_welore_paper_review_common_terms')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_paper_review_common_terms` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `common_content` longtext NOT NULL,
              `create_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.82_2022-05-16
if (!manual_tableexists('yl_welore_paper_review_score')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_paper_review_score` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `ne_id` int(11) unsigned NOT NULL,
              `tory_id` int(11) unsigned NOT NULL,
              `pa_id` int(11) unsigned NOT NULL,
              `user_id` int(11) unsigned NOT NULL,
              `assess_score` int(11) unsigned NOT NULL,
              `assess_content` longtext NOT NULL,
              `assess_time` int(11) unsigned NOT NULL,
              `is_show` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.82_2022-05-16
if (!manual_tableexists('yl_welore_call_phone_config')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_call_phone_config` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `force_input_phone` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.82_2022-05-18
if (!manual_fieldexists('yl_welore_shop', 'pay_type')) {
    pdo_query("ALTER TABLE `yl_welore_shop` ADD `pay_type` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.82_2022-05-18
if (!manual_fieldexists('yl_welore_shop_order', 'pay_type')) {
    pdo_query("ALTER TABLE `yl_welore_shop_order` ADD `pay_type` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.82_2022-05-18
if (!manual_fieldexists('yl_welore_shop_order', 'pay_status')) {
    pdo_query("ALTER TABLE `yl_welore_shop_order` ADD `pay_status` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.82_2022-05-19
if (!manual_fieldexists('yl_welore_authority', 'home_my_tory_arbor')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `home_my_tory_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.85_2022-05-31
if (!manual_fieldexists('yl_welore_paper_review_score', 'audit_status')) {
    pdo_query("ALTER TABLE `yl_welore_paper_review_score` ADD `audit_status` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.85_2022-05-31
if (!manual_fieldexists('yl_welore_paper_review_score', 'audit_reason')) {
    pdo_query("ALTER TABLE `yl_welore_paper_review_score` ADD `audit_reason` VARCHAR( 500 ) NULL ;");
}

#v1.1.85_2022-05-31
if (!manual_tableexists('yl_welore_paper_review_config')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_paper_review_config` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `is_auto_audit` int(11) unsigned NOT NULL DEFAULT '0',
              `is_all_review` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.86_2022-06-08
if (!manual_tableexists('yl_welore_lost_item')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_lost_item` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `item_type` int(11) unsigned NOT NULL,
              `release_type` int(11) unsigned NOT NULL,
              `item_name` varchar(500) NOT NULL,
              `item_detail` longtext NOT NULL,
              `lost_address` varchar(500) NOT NULL,
              `lost_time` int(11) unsigned NOT NULL,
              `contact_details` varchar(500) NOT NULL,
              `item_status` int(11) NOT NULL DEFAULT '1',
              `top_time` int(11) unsigned NOT NULL DEFAULT '0',
              `audit_status` int(11) unsigned NOT NULL DEFAULT '0',
              `audit_reason` varchar(500) DEFAULT NULL,
              `create_time` int(11) unsigned NOT NULL,
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.86_2022-06-07
if (!manual_tableexists('yl_welore_lost_item_config')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_lost_item_config` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `is_auto_audit` int(11) unsigned NOT NULL DEFAULT '0',
              `reply_is_auto_audit` int(11) unsigned NOT NULL DEFAULT '0',
              `top_twig` int(11) unsigned NOT NULL DEFAULT '0',
              `price_type` int(11) unsigned NOT NULL,
              `top_price` decimal(22,2) unsigned NOT NULL,
              `help_document` longtext NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.86_2022-06-07
if (!manual_tableexists('yl_welore_lost_item_reply')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_lost_item_reply` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `li_id` int(11) unsigned NOT NULL,
              `content` longtext NOT NULL,
              `is_secrecy` int(11) unsigned NOT NULL,
              `reply_time` int(11) unsigned NOT NULL,
              `audit_status` int(11) unsigned NOT NULL DEFAULT '0',
              `audit_reason` varchar(500) DEFAULT NULL,
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.86_2022-06-07
if (!manual_tableexists('yl_welore_lost_item_type')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_lost_item_type` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `name` varchar(255) NOT NULL,
              `status` int(11) unsigned NOT NULL DEFAULT '1',
              `sort` int(11) NOT NULL,
              `create_time` int(11) unsigned NOT NULL,
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `sort` (`sort`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.86_2022-06-14
if (!manual_tableexists('yl_welore_lost_item_top')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_lost_item_top` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `usl_id` int(11) unsigned NOT NULL DEFAULT '0',
              `li_id` int(11) unsigned NOT NULL,
              `user_id` int(11) unsigned NOT NULL,
              `top_day` int(11) unsigned NOT NULL,
              `pay_type` int(11) unsigned NOT NULL,
              `pay_price` decimal(22,2) unsigned NOT NULL,
              `add_time` int(11) unsigned NOT NULL,
              `is_pay` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `usl_id` (`usl_id`),
              KEY `li_id` (`li_id`),
              KEY `user_id` (`user_id`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.87_2022-09-07
if (!manual_tableexists('yl_welore_easy_info_config')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_easy_info_config` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `custom_title` varchar(255) NOT NULL,
              `is_show_btn` int(11) unsigned NOT NULL DEFAULT '0',
              `btn_icon` varchar(2000) NOT NULL,
              `waiter_qrcode` varchar(2000) NOT NULL,
              `precautions` longtext NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.87_2022-09-07
if (!manual_tableexists('yl_welore_easy_info_list')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_easy_info_list` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `merchant_type` int(11) unsigned NOT NULL,
              `merchant_name` varchar(500) NOT NULL,
              `merchant_icon_carousel` text NOT NULL,
              `address_name` varchar(500) NOT NULL,
              `address_longitude` varchar(255) NOT NULL,
              `address_latitude` varchar(255) NOT NULL,
              `merchant_phone` varchar(255) NOT NULL,
              `merchant_introduce` longtext NOT NULL,
              `status` int(11) unsigned NOT NULL DEFAULT '1',
              `create_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.87_2022-09-07
if (!manual_tableexists('yl_welore_easy_info_type')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_easy_info_type` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `name` varchar(255) NOT NULL,
              `icon` varchar(2000) NOT NULL,
              `status` int(11) unsigned NOT NULL DEFAULT '1',
              `sort` int(11) NOT NULL DEFAULT '0',
              `create_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.88_2022-09-21
if (!manual_fieldexists('yl_welore_easy_info_list', 'sort')) {
    pdo_query("ALTER TABLE `yl_welore_easy_info_list` ADD `sort` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.88_2022-10-09
if (!manual_fieldexists('yl_welore_paper_smingle', 'is_show_forum_declaration')) {
    pdo_query("ALTER TABLE `yl_welore_paper_smingle` ADD `is_show_forum_declaration` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.88_2022-10-09
if (!manual_fieldexists('yl_welore_paper_smingle', 'forum_declaration')) {
    pdo_query("ALTER TABLE `yl_welore_paper_smingle` ADD `forum_declaration` LONGTEXT NULL ;");
}

#v1.1.89_2022-10-13
if (!manual_fieldexists('yl_welore_shop', 'is_offline')) {
    pdo_query("ALTER TABLE `yl_welore_shop` ADD `is_offline` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.89_2022-10-13
if (!manual_fieldexists('yl_welore_shop_order', 'is_offline')) {
    pdo_query("ALTER TABLE `yl_welore_shop_order` ADD `is_offline` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.89_2022-10-25
if (!manual_tableexists('yl_welore_easy_info_shop_assistant')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_easy_info_shop_assistant` (
              `id` int(11) NOT NULL AUTO_INCREMENT ,
              `eil_id` int(11) unsigned NOT NULL ,
              `user_id` int(11) unsigned NOT NULL ,
              `status` int(11) unsigned NOT NULL DEFAULT '1' ,
              `create_time` int(11) unsigned NOT NULL ,
              `much_id` int(11) unsigned NOT NULL ,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.89_2022-10-25
if (!manual_tableexists('yl_welore_easy_info_shop_order')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_easy_info_shop_order` (
              `id` int(11) NOT NULL AUTO_INCREMENT ,
              `user_id` int(11) unsigned NOT NULL ,
              `product_id` int(11) unsigned NOT NULL ,
              `eil_id` int(11) unsigned NOT NULL ,
              `so_id` int(11) unsigned NOT NULL ,
              `redemption_code` varchar(255) NOT NULL ,
              `use_status` int(11) unsigned NOT NULL DEFAULT '0' ,
              `order_status` int(11) unsigned NOT NULL DEFAULT '1' ,
              `create_time` int(11) unsigned NOT NULL ,
              `much_id` int(11) unsigned NOT NULL ,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.89_2022-10-25
if (!manual_tableexists('yl_welore_easy_info_shop_order_verify_log')) {
    $sql = <<<EOT
          CREATE TABLE IF NOT EXISTS `yl_welore_easy_info_shop_order_verify_log` (
              `id` int(11) NOT NULL AUTO_INCREMENT ,
              `eiso_id` int(11) unsigned NOT NULL ,
              `user_id` int(11) unsigned NOT NULL ,
              `eil_id` int(11) unsigned NOT NULL ,
              `so_id` int(11) unsigned NOT NULL ,
              `verify_time` int(11) unsigned NOT NULL ,
              `much_id` int(11) unsigned NOT NULL ,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;  
EOT;
    pdo_run($sql);
}

#v1.1.89_2022-10-25
if (!manual_tableexists('yl_welore_easy_info_shop_products')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_easy_info_shop_products` (
              `id` int(11) NOT NULL AUTO_INCREMENT ,
              `product_id` int(11) unsigned NOT NULL ,
              `eil_id` int(11) unsigned NOT NULL ,
              `create_time` int(11) unsigned NOT NULL ,
              `much_id` int(11) unsigned NOT NULL ,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.89_2022-10-25
if (!manual_fieldexists('yl_welore_config', 'version_type')) {
    pdo_query("ALTER TABLE `yl_welore_config` ADD `version_type` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.89_2022-10-25
if (!manual_fieldexists('yl_welore_config', 'certificate_serial_number')) {
    pdo_query("ALTER TABLE `yl_welore_config` ADD `certificate_serial_number` TEXT NULL ;");
}

#v1.1.89_2022-10-25
if (!manual_fieldexists('yl_welore_config', 'app_key_v3')) {
    pdo_query("ALTER TABLE `yl_welore_config` ADD `app_key_v3` TEXT NULL ;");
}

#v1.1.90_2022-11-01
if (!manual_fieldexists('yl_welore_user_punch_range', 'currency_redemption_channel')) {
    pdo_query("ALTER TABLE `yl_welore_user_punch_range` ADD `currency_redemption_channel` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.90_2022-11-01
if (!manual_fieldexists('yl_welore_user_punch_range', 'fraction_redemption_channel')) {
    pdo_query("ALTER TABLE `yl_welore_user_punch_range` ADD `fraction_redemption_channel` INT( 11 ) UNSIGNED NOT NULL DEFAULT '1';");
}

#v1.1.90_2022-11-01
if (!manual_fieldexists('yl_welore_authority', 'video_compression_setting')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `video_compression_setting` INT( 11 ) UNSIGNED NOT NULL DEFAULT '1';");
}

#v1.1.90_2022-11-01
if (!manual_fieldexists('yl_welore_sprout', 'temp_name')) {
    pdo_query("ALTER TABLE `yl_welore_sprout` ADD `temp_name` VARCHAR( 255 ) NULL ;");
}

#v1.1.90_2022-11-01
if (!manual_fieldexists('yl_welore_sprout_reply', 'temp_name')) {
    pdo_query("ALTER TABLE `yl_welore_sprout_reply` ADD `temp_name` VARCHAR( 255 ) NULL ;");
}

#v1.1.90_2022-11-06
if (!manual_fieldexists('yl_welore_shop_order', 'vested_attribute')) {
    pdo_query("ALTER TABLE `yl_welore_shop_order` ADD `vested_attribute` TEXT NULL ;");
}

#v1.1.90_2022-11-06
if (!manual_fieldexists('yl_welore_paper_smingle', 'custom_hiss_title')) {
    pdo_query("ALTER TABLE `yl_welore_paper_smingle` ADD `custom_hiss_title` VARCHAR( 500 ) NULL ;");
}

#v1.1.90_2022-11-06
if (!manual_tableexists('yl_welore_shop_attribute')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_shop_attribute` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `at_name` varchar(255) NOT NULL,
              `create_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.90_2022-11-06
if (!manual_tableexists('yl_welore_shop_vested')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_shop_vested` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `sp_id` int(11) unsigned NOT NULL,
              `sa_name` varchar(500) DEFAULT NULL,
              `sa_list` longtext,
              `create_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `sp_id` (`sp_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.91_2022-11-11
if (!manual_fieldexists('yl_welore_authority', 'rel_paper_img_style')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `rel_paper_img_style` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.93_2023-02-20
if (!manual_fieldexists('yl_welore_authority', 'paper_browse_num_hide')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `paper_browse_num_hide` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.93_2023-02-20
if (!manual_fieldexists('yl_welore_authority', 'rel_paper_location_hide')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `rel_paper_location_hide` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.93_2023-02-20
if (!manual_fieldexists('yl_welore_authority', 'rel_paper_topicsd_hide')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `rel_paper_topicsd_hide` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.93_2023-02-20
if (!manual_fieldexists('yl_welore_netdisc_config', 'rel_paper_icon_hide')) {
    pdo_query("ALTER TABLE `yl_welore_netdisc_config` ADD `rel_paper_icon_hide` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.93_2023-02-20
if (!manual_fieldexists('yl_welore_user', 'is_enable_fans_privacy')) {
    pdo_query("ALTER TABLE `yl_welore_user` ADD `is_enable_fans_privacy` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.93_2023-02-20
if (!manual_fieldexists('yl_welore_user', 'is_enable_concern_privacy')) {
    pdo_query("ALTER TABLE `yl_welore_user` ADD `is_enable_concern_privacy` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.93_2023-02-20
if (!manual_fieldexists('yl_welore_user', 'virtual_fans_num')) {
    pdo_query("ALTER TABLE `yl_welore_user` ADD `virtual_fans_num` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.93_2023-02-21
if (!manual_fieldexists('yl_welore_user_violation', 'tencent_cloud_content_security_config')) {
    pdo_query("ALTER TABLE `yl_welore_user_violation` ADD `tencent_cloud_content_security_config` TEXT NULL ;");
}

#v1.1.94_2023-02-24
if (!manual_fieldexists('yl_welore_authority', 'rel_paper_image_hide')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `rel_paper_image_hide` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.95_2023-03-02
if (!manual_tableexists('yl_welore_video_analysis_config')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_video_analysis_config` (
              `id` bigint(20) NOT NULL AUTO_INCREMENT,
              `parse_name` varchar(255) NOT NULL,
              `parse_url` text NOT NULL,
              `req_method` int(11) unsigned NOT NULL,
              `req_type` int(11) unsigned NOT NULL,
              `req_params` text NOT NULL,
              `res_params` text NOT NULL,
              `is_default` int(11) unsigned NOT NULL DEFAULT '0',
              `app_remark` text NOT NULL,
              `create_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.96_2023-03-03
if (!manual_fieldexists('yl_welore_video_analysis_config', 'interface_type')) {
    pdo_query("ALTER TABLE `yl_welore_video_analysis_config` ADD `interface_type` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.1.96_2023-03-03
if (!manual_fieldexists('yl_welore_video_analysis_config', 'adaptation_domain')) {
    pdo_query("ALTER TABLE `yl_welore_video_analysis_config` ADD `adaptation_domain` TEXT NULL ;");
}

#v1.1.96_2023-03-03
if (!manual_fieldexists('yl_welore_video_analysis_config', 'default_video_cover')) {
    pdo_query("ALTER TABLE `yl_welore_video_analysis_config` ADD `default_video_cover` TEXT NULL ;");
}

#v1.1.97_2023-05-22
if (!manual_tableexists('yl_welore_used_goods_item')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_used_goods_item` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `item_type` int(11) unsigned NOT NULL,
              `release_type` int(11) unsigned NOT NULL,
              `item_name` varchar(500) NOT NULL,
              `item_price` varchar(255) DEFAULT NULL,
              `item_detail` longtext NOT NULL,
              `secondhand_address` varchar(500) NOT NULL,
              `contact_details` varchar(500) NOT NULL,
              `item_status` int(11) NOT NULL DEFAULT '1',
              `top_time` int(11) unsigned NOT NULL DEFAULT '0',
              `audit_status` int(11) unsigned NOT NULL DEFAULT '0',
              `audit_reason` varchar(500) DEFAULT NULL,
              `create_time` int(11) unsigned NOT NULL,
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              FULLTEXT KEY `idx_search` (`item_name`,`item_detail`),
              FULLTEXT KEY `item_name` (`item_name`),
              FULLTEXT KEY `item_detail` (`item_detail`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.97_2023-05-22
if (!manual_tableexists('yl_welore_used_goods_item_config')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_used_goods_item_config` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `custom_title` varchar(255) NOT NULL,
              `is_auto_audit` int(11) unsigned NOT NULL DEFAULT '0',
              `reply_is_auto_audit` int(11) unsigned NOT NULL DEFAULT '0',
              `top_twig` int(11) unsigned NOT NULL DEFAULT '0',
              `price_type` int(11) unsigned NOT NULL,
              `top_price` decimal(22,2) unsigned NOT NULL,
              `help_document` longtext NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.97_2023-05-22
if (!manual_tableexists('yl_welore_used_goods_item_reply')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_used_goods_item_reply` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `ugi_id` int(11) unsigned NOT NULL,
              `content` longtext NOT NULL,
              `is_secrecy` int(11) unsigned NOT NULL,
              `reply_time` int(11) unsigned NOT NULL,
              `audit_status` int(11) unsigned NOT NULL DEFAULT '0',
              `audit_reason` varchar(500) DEFAULT NULL,
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.97_2023-05-22
if (!manual_tableexists('yl_welore_used_goods_item_top')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_used_goods_item_top` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `usl_id` int(11) unsigned NOT NULL DEFAULT '0',
              `ugi_id` int(11) unsigned NOT NULL,
              `user_id` int(11) unsigned NOT NULL,
              `top_day` int(11) unsigned NOT NULL,
              `pay_type` int(11) unsigned NOT NULL,
              `pay_price` decimal(22,2) unsigned NOT NULL,
              `add_time` int(11) unsigned NOT NULL,
              `is_pay` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.97_2023-05-22
if (!manual_tableexists('yl_welore_used_goods_item_type')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_used_goods_item_type` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `name` varchar(255) NOT NULL,
              `status` int(11) unsigned NOT NULL DEFAULT '1',
              `sort` int(11) NOT NULL,
              `create_time` int(11) unsigned NOT NULL,
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.98_2023-06-11
if (!manual_tableexists('yl_welore_employment_item')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_employment_item` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `job_type` int(11) unsigned NOT NULL,
              `release_type` int(11) unsigned NOT NULL,
              `job_name` varchar(500) NOT NULL,
              `job_salary` varchar(255) DEFAULT NULL,
              `job_description` longtext NOT NULL,
              `work_address` varchar(1000) NOT NULL,
              `contact_details` varchar(500) NOT NULL,
              `top_time` int(11) unsigned NOT NULL DEFAULT '0',
              `audit_status` int(11) unsigned NOT NULL DEFAULT '0',
              `audit_reason` varchar(500) DEFAULT NULL,
              `create_time` int(11) unsigned NOT NULL,
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.98_2023-06-11
if (!manual_tableexists('yl_welore_employment_item_config')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_employment_item_config` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `custom_title` varchar(255) NOT NULL,
              `is_auto_audit` int(11) unsigned NOT NULL DEFAULT '0',
              `reply_is_auto_audit` int(11) unsigned NOT NULL DEFAULT '0',
              `top_twig` int(11) unsigned NOT NULL DEFAULT '0',
              `price_type` int(11) unsigned NOT NULL,
              `top_price` decimal(22,2) unsigned NOT NULL,
              `help_document` longtext NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.98_2023-06-11
if (!manual_tableexists('yl_welore_employment_item_top')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_employment_item_top` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `usl_id` int(11) unsigned NOT NULL DEFAULT '0',
              `ei_id` int(11) unsigned NOT NULL,
              `user_id` int(11) unsigned NOT NULL,
              `top_day` int(11) unsigned NOT NULL,
              `pay_type` int(11) unsigned NOT NULL,
              `pay_price` decimal(22,2) unsigned NOT NULL,
              `add_time` int(11) unsigned NOT NULL,
              `is_pay` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.98_2023-06-11
if (!manual_tableexists('yl_welore_employment_item_type')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_employment_item_type` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `name` varchar(255) NOT NULL,
              `status` int(11) unsigned NOT NULL DEFAULT '1',
              `sort` int(11) NOT NULL,
              `create_time` int(11) unsigned NOT NULL,
              `is_del` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.1.98_2023-06-11
if (!manual_tableexists('yl_welore_employment_user_attention')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_employment_user_attention` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `ei_id` int(11) unsigned NOT NULL,
              `create_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.2.2_2023-09-05
if (!manual_fieldexists('yl_welore_paper_reply', 'is_gift')) {
    pdo_query("ALTER TABLE `yl_welore_paper_reply` ADD `is_gift` TINYINT( 1 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.2.2_2023-09-14
if (!manual_tableexists('yl_welore_paper_heat_banner_ads')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_paper_heat_banner_ads` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `img_url` text NOT NULL,
              `jump_type` tinyint(1) NOT NULL,
              `appid` varchar(255) DEFAULT NULL,
              `url` text,
              `sort` int(11) NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.2.2_2023-09-14
if (!manual_tableexists('yl_welore_paper_heat_config')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_paper_heat_config` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `custom_title` varchar(255) NOT NULL,
              `custom_head_img` varchar(2000) NOT NULL,
              `statistics_time` tinyint(1) NOT NULL DEFAULT '0',
              `custom_sort_condition` text NOT NULL,
              `display_switch` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.2.2_2023-09-14
if (!manual_tableexists('yl_welore_paper_heat_put_top')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_paper_heat_put_top` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `paper_id` int(11) unsigned NOT NULL,
              `put_top_start_time` int(11) unsigned NOT NULL,
              `put_top_end_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.2.3_2023-10-07
if (!manual_fieldexists('yl_welore_paper_heat_config', 'style_type')) {
    pdo_query("ALTER TABLE `yl_welore_paper_heat_config` ADD `style_type` TINYINT( 1 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.2.3_2023-10-09
if (!manual_fieldexists('yl_welore_feeling_stipulate', 'custom_title')) {
    pdo_query("ALTER TABLE `yl_welore_feeling_stipulate` ADD `custom_title` VARCHAR( 255 ) NULL ;");
}

#v1.2.3_2023-10-10
if (!manual_fieldexists('yl_welore_authority', 'video_download_arbor')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `video_download_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.2.3_2023-10-22
if (!manual_tableexists('yl_welore_sweepstake_list')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_sweepstake_list` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `lottery_name` varchar(255) NOT NULL,
              `prize_list` longtext NOT NULL,
              `free_entry_count` int(11) unsigned NOT NULL,
              `video_entry_count` int(11) unsigned NOT NULL,
              `participant_num_limit` int(11) unsigned NOT NULL DEFAULT '0',
              `is_group` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `extract_type` int(11) unsigned NOT NULL,
              `random_extract_range_start` bigint(30) unsigned NOT NULL,
              `random_extract_range_end` bigint(30) unsigned NOT NULL,
              `start_time` int(11) unsigned NOT NULL,
              `end_time` int(11) unsigned NOT NULL,
              `draw_time` int(11) unsigned NOT NULL,
              `is_winning` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `campaign_desc` longtext,
              `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.2.3_2023-10-22
if (!manual_tableexists('yl_welore_sweepstake_participate')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_sweepstake_participate` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `sp_id` int(11) unsigned NOT NULL,
              `lucky_number` varchar(50) NOT NULL,
              `award_number_type` int(11) unsigned NOT NULL,
              `award_categories` int(11) unsigned NOT NULL,
              `award_level` int(11) unsigned NOT NULL,
              `award_type` int(11) unsigned NOT NULL,
              `is_award` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `is_payout_prizes` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `memo` varchar(500) DEFAULT NULL,
              `create_time` int(1) unsigned NOT NULL,
              `update_time` int(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.2.3_2023-10-22
if (!manual_tableexists('yl_welore_sweepstake_winning')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_sweepstake_winning` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `sp_id` int(11) unsigned NOT NULL,
              `prize_outcome` longtext NOT NULL,
              `prize_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.2.3_2023-10-24
if (!manual_tableexists('yl_welore_sweepstake_config')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_sweepstake_config` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `custom_title` varchar(255) NOT NULL,
              `ad_1` varchar(255) NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.2.7_2024-05-13
if (!manual_fieldexists('yl_welore_outlying', 'ftp_follow')) {
    pdo_query("ALTER TABLE `yl_welore_outlying` ADD `ftp_follow` VARCHAR( 1000 ) NULL ;");
}

#v1.2.7_2024-05-13
if (!manual_fieldexists('yl_welore_netdisc_storage', 'ftp_follow')) {
    pdo_query("ALTER TABLE `yl_welore_netdisc_storage` ADD `ftp_follow` VARCHAR( 1000 ) NULL ;");
}

#v1.2.8_2024-07-30
if (!manual_tableexists('yl_welore_micro_series_config')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_micro_series_config` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `custom_title` varchar(255) NOT NULL,
              `is_vip_free_look` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `is_allow_user_upload` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `is_info_auto_review` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `is_content_auto_review` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `is_comment_auto_review` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `is_require_user_copyright` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `is_allow_user_charge` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `min_amount_charged` decimal(32,2) unsigned NOT NULL DEFAULT '0.00',
              `max_amount_charged` decimal(32,2) unsigned NOT NULL DEFAULT '0.00',
              `every_day_free_look_num` int(11) unsigned NOT NULL DEFAULT '0',
              `is_enabled_look_ads_unlock_paid_content` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `look_ads_unlock_paid_content_max_num` int(11) unsigned NOT NULL DEFAULT '0',
              `charged_profit_rake_ratio` decimal(4,2) unsigned NOT NULL DEFAULT '0.50',
              `user_agreement` longtext NOT NULL,
              `disclaimer_warranties` longtext NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.2.8_2024-07-30
if (!manual_tableexists('yl_welore_micro_series_content_list')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_micro_series_content_list` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `msi_id` int(11) unsigned NOT NULL,
              `upload_user_id` int(11) unsigned DEFAULT '0',
              `msi_episode_number` varchar(255) NOT NULL,
              `msi_episode_url` text NOT NULL,
              `is_allow_only_vip` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `paid_unlocking_type` tinyint(2) unsigned NOT NULL DEFAULT '0',
              `paid_unlocking_price` decimal(32,2) NOT NULL DEFAULT '0.00',
              `status` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `audit_reason` text,
              `display_status` tinyint(1) unsigned NOT NULL DEFAULT '1',
              `sort` int(11) NOT NULL DEFAULT '0',
              `create_time` int(11) unsigned NOT NULL,
              `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `msi_id` (`msi_id`),
              KEY `is_allow_only_vip` (`is_allow_only_vip`),
              KEY `paid_unlocking_type` (`paid_unlocking_type`),
              KEY `sort` (`sort`),
              KEY `create_time` (`create_time`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.2.8_2024-07-30
if (!manual_tableexists('yl_welore_micro_series_info_list')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_micro_series_info_list` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `upload_user_id` int(11) unsigned NOT NULL DEFAULT '0',
              `title` varchar(255) NOT NULL,
              `type` varchar(255) NOT NULL,
              `poster_url` text NOT NULL,
              `director` varchar(255) NOT NULL,
              `screenwriter` varchar(255) NOT NULL,
              `lead_actors` varchar(255) NOT NULL,
              `production_country` varchar(100) NOT NULL,
              `language` varchar(100) NOT NULL,
              `release_date` varchar(255) NOT NULL,
              `duration_minutes` int(11) NOT NULL,
              `alias` varchar(100) DEFAULT NULL,
              `plot_summary` longtext,
              `total_episodes` varchar(255) NOT NULL,
              `user_copyright_img` text,
              `status` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `audit_reason` varchar(500) DEFAULT NULL,
              `display_status` tinyint(1) unsigned NOT NULL DEFAULT '1',
              `create_time` int(11) unsigned NOT NULL,
              `update_time` int(11) unsigned NOT NULL,
              `is_del` tinyint(11) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.2.8_2024-07-30
if (!manual_tableexists('yl_welore_micro_series_info_review')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_micro_series_info_review` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `msi_id` int(11) unsigned NOT NULL,
              `msc_id` int(11) unsigned NOT NULL DEFAULT '0',
              `user_id` int(11) unsigned NOT NULL,
              `comment` longtext NOT NULL,
              `rid` int(11) NOT NULL DEFAULT '0',
              `status` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `audit_reason` text,
              `create_time` int(11) unsigned NOT NULL,
              `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `msi_id` (`msi_id`),
              KEY `msc_id` (`msc_id`),
              KEY `user_id` (`user_id`),
              KEY `rid` (`rid`),
              KEY `is_del` (`is_del`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.2.8_2024-07-30
if (!manual_tableexists('yl_welore_micro_series_type')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_micro_series_type` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `name` varchar(255) NOT NULL,
              `status` tinyint(1) unsigned NOT NULL DEFAULT '1',
              `sort` int(11) NOT NULL DEFAULT '0',
              `create_time` int(11) unsigned NOT NULL,
              `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0',
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}


#v1.2.8_2024-07-30
if (!manual_tableexists('yl_welore_micro_series_unlock_paid_content_list')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_micro_series_unlock_paid_content_list` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) unsigned NOT NULL,
              `msi_id` int(11) unsigned NOT NULL,
              `msc_id` int(11) unsigned NOT NULL,
              `unlock_type` tinyint(1) NOT NULL,
              `unlock_price` decimal(32,2) NOT NULL DEFAULT '0.00',
              `charged_profit_rake_ratio` decimal(4,2) NOT NULL DEFAULT '0.50',
              `unlock_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              KEY `user_id` (`user_id`),
              KEY `msi_id` (`msi_id`),
              KEY `msc_id` (`msc_id`),
              KEY `unlock_type` (`unlock_type`),
              KEY `much_id` (`much_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.2.8_2024-07-30
if (!manual_tableexists('yl_welore_micro_series_user_like')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_micro_series_user_like` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `msi_id` int(11) unsigned NOT NULL,
              `msc_id` int(11) unsigned NOT NULL,
              `user_id` int(11) unsigned NOT NULL,
              `like_type` tinyint(1) unsigned NOT NULL,
              `create_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.2.8_2024-07-30
if (!manual_fieldexists('yl_welore_authority', 'short_drama_arbor')) {
    pdo_query("ALTER TABLE `yl_welore_authority` ADD `short_drama_arbor` INT( 11 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.2.15_2025-04-26
if (!manual_tableexists('yl_welore_paper_wechat_channel_video')) {
    $sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_paper_wechat_channel_video` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `paper_id` int(11) unsigned NOT NULL,
              `feed_token` varchar(255) NOT NULL,
              `create_time` int(11) unsigned NOT NULL,
              `much_id` int(11) unsigned NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `paper_id` (`paper_id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=1 ;
EOT;
    pdo_run($sql);
}

#v1.2.16_2025-06-10
if (!manual_fieldexists('yl_welore_outlying', 'aws_follow')) {
    pdo_query("ALTER TABLE `yl_welore_outlying` ADD `aws_follow` VARCHAR( 1000 ) NULL;");
}

#v1.2.17_2025-06-17
if (!manual_fieldexists('yl_welore_login_checking', 'access_type')) {
    pdo_query("ALTER TABLE `yl_welore_login_checking` ADD `access_type` TINYINT(1) NOT NULL DEFAULT '0';");
}

#v1.2.17_2025-06-17
if (!manual_fieldexists('yl_welore_login_checking', 'session_token')) {
    pdo_query("ALTER TABLE `yl_welore_login_checking` ADD `session_token` VARCHAR(128) NOT NULL;");
}

#v1.2.17_2025-06-17
if (!manual_fieldexists('yl_welore_login_checking', 'previous_session_token')) {
    pdo_query("ALTER TABLE `yl_welore_login_checking` ADD `previous_session_token` VARCHAR(128) DEFAULT NULL;");
}

#v1.2.17_2025-06-17
if (!manual_fieldexists('yl_welore_login_checking', 'token_rotated_at')) {
    pdo_query("ALTER TABLE `yl_welore_login_checking` ADD `token_rotated_at` INT(11) UNSIGNED DEFAULT NULL;");
}

#v1.2.17_2025-06-17
if (!manual_fieldexists('yl_welore_login_checking', 'token_last_refreshed_at')) {
    pdo_query("ALTER TABLE `yl_welore_login_checking` ADD `token_last_refreshed_at` INT(10) NOT NULL DEFAULT '0';");
}

#v1.2.17_2025-06-17
if (!manual_fieldexists('yl_welore_login_checking', 'session_fingerprint')) {
    pdo_query("ALTER TABLE `yl_welore_login_checking` ADD `session_fingerprint` VARCHAR(255) NOT NULL;");
}

#v1.2.17_2025-06-22
if (!manual_fieldexists('yl_welore_template_slot', 'apollo_history')) {
    pdo_query("ALTER TABLE `yl_welore_template_slot` ADD `apollo_history` TEXT NULL ;");
}

#v1.2.17_2025-06-22
if (!manual_fieldexists('yl_welore_template_slot_valve', 'is_reversal')) {
    pdo_query("ALTER TABLE `yl_welore_template_slot_valve` ADD `is_reversal` TINYINT( 1 ) UNSIGNED NOT NULL DEFAULT '0';");
}

#v1.2.17_2025-06-22
if (!manual_fieldexists('yl_welore_outlying', 'pan123_follow')) {
    pdo_query("ALTER TABLE `yl_welore_outlying` ADD `pan123_follow` VARCHAR( 1000 ) NULL ;");
}

#v1.1.36_2021-1-22
$sql = <<<EOT
            CREATE TABLE IF NOT EXISTS `yl_welore_outlying_allude` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `key` varchar(500) DEFAULT NULL,
              `value` varchar(500) DEFAULT NULL,
              `status` int(11) unsigned NOT NULL DEFAULT '1',
              `type` int(11) unsigned DEFAULT NULL,
              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=68 ;
            TRUNCATE TABLE `yl_welore_outlying_allude`;
            INSERT INTO `yl_welore_outlying_allude` VALUES(1, '华东1 ( 杭州 ) ', 'oss-cn-hangzhou.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(2, '华东1 ( 杭州 内网 ) ', 'oss-cn-hangzhou-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(3, '华东2 ( 上海 ) ', 'oss-cn-shanghai.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(4, '华东2 ( 上海 内网 ) ', 'oss-cn-shanghai-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(5, '华北1 ( 青岛 ) ', 'oss-cn-qingdao.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(6, '华北1 ( 青岛 内网 ) ', 'oss-cn-qingdao-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(7, '华北2 ( 北京 ) ', 'oss-cn-beijing.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(8, '华北2 ( 北京 内网 ) ', 'oss-cn-beijing-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(9, '华北3 ( 张家口 ) ', 'oss-cn-zhangjiakou.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(10, '华北3 ( 张家口 内网 ) ', 'oss-cn-zhangjiakou-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(11, '华北5 ( 呼和浩特 ) ', 'oss-cn-huhehaote.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(12, '华北5 ( 呼和浩特 内网 ) ', 'oss-cn-huhehaote-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(13, '华北6 ( 乌兰察布 ) ', 'oss-cn-wulanchabu.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(14, '华北6 ( 乌兰察布 内网 ) ', 'oss-cn-wulanchabu-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(15, '华南1 ( 深圳 ) ', 'oss-cn-shenzhen.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(16, '华南1 ( 深圳 内网 ) ', 'oss-cn-shenzhen-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(17, '华南2 ( 河源 ) ', 'oss-cn-heyuan.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(18, '华南2 ( 河源 内网 ) ', 'oss-cn-heyuan-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(19, '华南3 ( 广州 ) ', 'oss-cn-guangzhou.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(20, '华南3 ( 广州 内网 ) ', 'oss-cn-guangzhou-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(21, '西南1 ( 成都 ) ', 'oss-cn-chengdu.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(22, '西南1 ( 成都 内网 ) ', 'oss-cn-chengdu-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(23, '中国 ( 香港 ) ', 'oss-cn-hongkong.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(24, '中国 ( 香港 内网 ) ', 'oss-cn-hongkong-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(25, '美国西部1 ( 硅谷 ) ', 'oss-us-west-1.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(26, '美国西部1 ( 硅谷 内网 ) ', 'oss-us-west-1-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(27, '美国东部1 ( 弗吉尼亚 ) ', 'oss-us-east-1.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(28, '美国东部1 ( 弗吉尼亚 内网 ) ', 'oss-us-east-1-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(29, '亚太东南1 ( 新加坡 ) ', 'oss-ap-southeast-1.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(30, '亚太东南1 ( 新加坡 内网 ) ', 'oss-ap-southeast-1-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(31, '亚太东南2 ( 悉尼 ) ', 'oss-ap-southeast-2.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(32, '亚太东南2 ( 悉尼 内网 ) ', 'oss-ap-southeast-2-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(33, '亚太东南3 ( 吉隆坡 ) ', 'oss-ap-southeast-3.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(34, '亚太东南3 ( 吉隆坡 内网 ) ', 'oss-ap-southeast-3-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(35, '亚太东南5 ( 雅加达 ) ', 'oss-ap-southeast-5.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(36, '亚太东南5 ( 雅加达 内网 ) ', 'oss-ap-southeast-5-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(37, '亚太东北1 ( 日本 ) ', 'oss-ap-northeast-1.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(38, '亚太东北1 ( 日本 内网 ) ', 'oss-ap-northeast-1-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(39, '亚太南部1 ( 孟买 ) ', 'oss-ap-south-1.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(40, '亚太南部1 ( 孟买 内网 ) ', 'oss-ap-south-1-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(41, '欧洲中部1 ( 法兰克福 ) ', 'oss-eu-central-1.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(42, '欧洲中部1 ( 法兰克福 内网 ) ', 'oss-eu-central-1-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(43, '英国 ( 伦敦 ) ', 'oss-eu-west-1.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(44, '英国 ( 伦敦 内网 ) ', 'oss-eu-west-1-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(45, '中东东部1 ( 迪拜 ) ', 'oss-me-east-1.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(46, '中东东部1 ( 迪拜 内网 ) ', 'oss-me-east-1-internal.aliyuncs.com', 1, 0);
            INSERT INTO `yl_welore_outlying_allude` VALUES(47, '北京一区', 'ap-beijing-1', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(48, '北京', 'ap-beijing', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(49, '南京', 'ap-nanjing', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(50, '上海', 'ap-shanghai', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(51, '广州', 'ap-guangzhou', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(52, '成都', 'ap-chengdu', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(53, '重庆', 'ap-chongqing', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(54, '深圳金融', 'ap-shenzhen-fsi', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(55, '上海金融', 'ap-shanghai-fsi', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(56, '北京金融', 'ap-beijing-fsi', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(57, '中国香港', 'ap-hongkong', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(58, '新加坡', 'ap-singapore', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(59, '孟买', 'ap-mumbai', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(60, '首尔', 'ap-seoul', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(61, '曼谷', 'ap-bangkok', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(62, '东京', 'ap-tokyo', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(63, '硅谷 ( 美西 )', 'na-siliconvalley', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(64, '弗吉尼亚 ( 美东 )', 'na-ashburn', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(65, '多伦多', 'na-toronto', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(66, '法兰克福', 'eu-frankfurt', 1, 1);
            INSERT INTO `yl_welore_outlying_allude` VALUES(67, '莫斯科', 'eu-moscow', 1, 1);
EOT;
pdo_run($sql);

#v1.2.8_2024-07-30
$sql = <<<EOT
            DROP TABLE IF EXISTS `yl_welore_motion`;
            CREATE TABLE IF NOT EXISTS `yl_welore_motion` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `mot_name` varchar(300) DEFAULT NULL,
              `icon` varchar(255) DEFAULT NULL,
              `mot_url` varchar(300) DEFAULT NULL,
              `pid` int(11) unsigned DEFAULT NULL,
              `sort` int(11) unsigned DEFAULT '0',
              PRIMARY KEY (`id`),
              KEY `id` (`id`)
            ) ENGINE=InnoDB  DEFAULT CHARSET=utf8 AUTO_INCREMENT=162 ;
            TRUNCATE TABLE `yl_welore_motion`;
            INSERT INTO `yl_welore_motion` VALUES(1, '首页', 'am-icon-home', 'index/index', 0, 0);
            INSERT INTO `yl_welore_motion` VALUES(2, '广场管理', 'am-icon-map-signs', NULL, 0, 1);
            INSERT INTO `yl_welore_motion` VALUES(3, '广场列表', NULL, 'compass/nav', 2, 0);
            INSERT INTO `yl_welore_motion` VALUES(4, '用户管理', 'am-icon-users', NULL, 0, 7);
            INSERT INTO `yl_welore_motion` VALUES(5, '圈子列表', NULL, 'compass/fence', 2, 1);
            INSERT INTO `yl_welore_motion` VALUES(6, '圈子审核', NULL, 'compass/solicit', 2, 3);
            INSERT INTO `yl_welore_motion` VALUES(7, '帖子管理', 'am-icon-file-text', NULL, 0, 2);
            INSERT INTO `yl_welore_motion` VALUES(8, '帖子信息', NULL, 'essay/index', 125, 0);
            INSERT INTO `yl_welore_motion` VALUES(9, '帖子回复', NULL, 'essay/reply', 125, 2);
            INSERT INTO `yl_welore_motion` VALUES(10, '帖子设置', NULL, 'essay/ritual', 125, 4);
            INSERT INTO `yl_welore_motion` VALUES(11, '举报反馈', 'am-icon-shield', NULL, 0, 3);
            INSERT INTO `yl_welore_motion` VALUES(12, '帖子举报', NULL, 'journal/report', 11, 0);
            INSERT INTO `yl_welore_motion` VALUES(13, '帖子申诉', NULL, 'journal/appeal', 11, 1);
            INSERT INTO `yl_welore_motion` VALUES(14, '广告列表', NULL, 'systems/symbol', 42, 1);
            INSERT INTO `yl_welore_motion` VALUES(15, '系统管理', 'am-icon-cogs', NULL, 0, 9);
            INSERT INTO `yl_welore_motion` VALUES(16, '远程附件', NULL, 'systems/annex', 15, 7);
            INSERT INTO `yl_welore_motion` VALUES(17, '营销管理', 'am-icon-magic', NULL, 0, 5);
            INSERT INTO `yl_welore_motion` VALUES(18, '礼物管理', NULL, NULL, 17, 4);
            INSERT INTO `yl_welore_motion` VALUES(19, '站点设置', NULL, 'systems/warrior', 15, 6);
            INSERT INTO `yl_welore_motion` VALUES(20, '疑难解答', NULL, 'systems/help', 15, 2);
            INSERT INTO `yl_welore_motion` VALUES(21, '小程序设置', NULL, 'systems/applets', 42, 8);
            INSERT INTO `yl_welore_motion` VALUES(22, '会员设置', NULL, 'marketing/fabulous', 17, 1);
            INSERT INTO `yl_welore_motion` VALUES(23, '用户信息', NULL, NULL, 4, 0);
            INSERT INTO `yl_welore_motion` VALUES(24, '超管列表', NULL, 'user/inspect', 23, 2);
            INSERT INTO `yl_welore_motion` VALUES(25, '商品列表', NULL, 'marketing/shop', 76, 3);
            INSERT INTO `yl_welore_motion` VALUES(26, '圈子投诉', NULL, 'journal/spread', 11, 2);
            INSERT INTO `yl_welore_motion` VALUES(27, '管理投诉', NULL, 'journal/safety', 11, 3);
            INSERT INTO `yl_welore_motion` VALUES(28, '用户投诉', NULL, 'journal/usmur', 11, 4);
            INSERT INTO `yl_welore_motion` VALUES(29, '货币设置', NULL, 'systems/punch', 37, 0);
            INSERT INTO `yl_welore_motion` VALUES(30, '自定义转发', NULL, 'systems/partake', 42, 10);
            INSERT INTO `yl_welore_motion` VALUES(31, '流量主', NULL, 'systems/proclaim', 42, 0);
            INSERT INTO `yl_welore_motion` VALUES(32, '礼物税率', NULL, 'marketing/taxing', 18, 1);
            INSERT INTO `yl_welore_motion` VALUES(33, '商品分类', NULL, 'marketing/stype', 76, 0);
            INSERT INTO `yl_welore_motion` VALUES(34, '商品订单', NULL, 'marketing/sorder', 76, 4);
            INSERT INTO `yl_welore_motion` VALUES(35, '图片库', NULL, 'images/index', 15, 0);
            INSERT INTO `yl_welore_motion` VALUES(36, '自定义设置', NULL, 'systems/navigate', 42, 9);
            INSERT INTO `yl_welore_motion` VALUES(37, '钱包管理', NULL, NULL, 4, 1);
            INSERT INTO `yl_welore_motion` VALUES(38, '提现设置', NULL, 'rawls/setting', 37, 2);
            INSERT INTO `yl_welore_motion` VALUES(39, '提现列表', NULL, 'rawls/stand', 37, 3);
            INSERT INTO `yl_welore_motion` VALUES(40, '模板消息', NULL, NULL, 42, 5);
            INSERT INTO `yl_welore_motion` VALUES(41, '虚拟用户', NULL, 'user/theoretic', 23, 3);
            INSERT INTO `yl_welore_motion` VALUES(42, '小程序管理', 'am-icon-stumbleupon', NULL, 0, 8);
            INSERT INTO `yl_welore_motion` VALUES(43, '审核设置', NULL, 'systems/audit', 42, 4);
            INSERT INTO `yl_welore_motion` VALUES(45, '充值列表', NULL, 'user/water', 37, 1);
            INSERT INTO `yl_welore_motion` VALUES(46, '邀请列表', NULL, 'unlawful/salute', 23, 6);
            INSERT INTO `yl_welore_motion` VALUES(47, '视频设置', NULL, 'systems/video', 42, 3);
            INSERT INTO `yl_welore_motion` VALUES(48, '娱乐系统', 'am-icon-tags', NULL, 0, 4);
            INSERT INTO `yl_welore_motion` VALUES(49, '任务系统', NULL, NULL, 48, 0);
            INSERT INTO `yl_welore_motion` VALUES(50, '首页置顶', NULL, 'essay/home_topping', 125, 1);
            INSERT INTO `yl_welore_motion` VALUES(51, '功能开关', NULL, 'systems/switch_control', 15, 5);
            INSERT INTO `yl_welore_motion` VALUES(52, '勋章列表', NULL, 'manual/decorate', 48, 2);
            INSERT INTO `yl_welore_motion` VALUES(53, '安全防护', NULL, NULL, 15, 4);
            INSERT INTO `yl_welore_motion` VALUES(54, '模板市场', NULL, 'sketchpad/market', 42, 6);
            INSERT INTO `yl_welore_motion` VALUES(55, '话题列表', NULL, 'compass/theme', 2, 2);
            INSERT INTO `yl_welore_motion` VALUES(56, '游客列表', NULL, 'user/traveler', 23, 1);
            INSERT INTO `yl_welore_motion` VALUES(57, '等级列表', NULL, 'manual/level', 48, 3);
            INSERT INTO `yl_welore_motion` VALUES(58, '首页导航', NULL, 'leading/traction', 42, 2);
            INSERT INTO `yl_welore_motion` VALUES(59, '抽奖列表', NULL, NULL, 17, 3);
            INSERT INTO `yl_welore_motion` VALUES(60, '装扮列表', NULL, 'manual/deck', 48, 4);
            INSERT INTO `yl_welore_motion` VALUES(61, '小秘密列表', NULL, 'stealth/softly', 75, 0);
            INSERT INTO `yl_welore_motion` VALUES(62, '小秘密回复', NULL, 'stealth/janitor', 75, 1);
            INSERT INTO `yl_welore_motion` VALUES(63, '像框列表', NULL, 'manual/adhesion', 48, 1);
            INSERT INTO `yl_welore_motion` VALUES(64, '身份铭牌', NULL, 'shield/disguise', 48, 5);
            INSERT INTO `yl_welore_motion` VALUES(65, '榜单排行', NULL, 'unlawful/annunciation', 17, 2);
            INSERT INTO `yl_welore_motion` VALUES(66, '用户认证', NULL, NULL, 4, 2);
            INSERT INTO `yl_welore_motion` VALUES(67, '认证表单', NULL, 'depend/provision', 66, 0);
            INSERT INTO `yl_welore_motion` VALUES(68, '认证列表', NULL, 'depend/acquire', 66, 1);
            INSERT INTO `yl_welore_motion` VALUES(69, '纸条管理', NULL, NULL, 7, 3);
            INSERT INTO `yl_welore_motion` VALUES(70, '小纸条列表', NULL, 'tissue/wedge', 69, 0);
            INSERT INTO `yl_welore_motion` VALUES(71, '小纸条设置', NULL, 'tissue/choked', 69, 1);
            INSERT INTO `yl_welore_motion` VALUES(72, '新人营销', NULL, 'unlawful/attract', 17, 0);
            INSERT INTO `yl_welore_motion` VALUES(73, '评论回复', NULL, 'tissue/discuss', 125, 3);
            INSERT INTO `yl_welore_motion` VALUES(74, '插件列表', NULL, 'sketchpad/plugin', 81, 0);
            INSERT INTO `yl_welore_motion` VALUES(75, '树洞管理', NULL, NULL, 7, 2);
            INSERT INTO `yl_welore_motion` VALUES(76, '商品管理', NULL, NULL, 17, 5);
            INSERT INTO `yl_welore_motion` VALUES(77, '卡密列表', NULL, NULL, 81, 2);
            INSERT INTO `yl_welore_motion` VALUES(78, '商品卡密', NULL, 'cammy/shopAnomaly', 77, 0);
            INSERT INTO `yl_welore_motion` VALUES(79, '货币卡密', NULL, 'cammy/bankAnomaly', 77, 1);
            INSERT INTO `yl_welore_motion` VALUES(80, '兑换记录', NULL, 'cammy/exchangeAnomaly', 77, 2);
            INSERT INTO `yl_welore_motion` VALUES(81, '插件管理', 'am-icon-slack', NULL, 0, 6);
            INSERT INTO `yl_welore_motion` VALUES(82, '网盘列表', NULL, NULL, 81, 3);
            INSERT INTO `yl_welore_motion` VALUES(83, '文件列表', NULL, 'cloud/files', 82, 0);
            INSERT INTO `yl_welore_motion` VALUES(84, '用户网盘', NULL, 'cloud/personal', 82, 1);
            INSERT INTO `yl_welore_motion` VALUES(85, '网盘设置', NULL, 'cloud/dictate', 82, 2);
            INSERT INTO `yl_welore_motion` VALUES(86, '抽奖活动', NULL, 'unlawful/lottery', 59, 0);
            INSERT INTO `yl_welore_motion` VALUES(87, '抽奖记录', NULL, 'unlawful/cheer', 59, 1);
            INSERT INTO `yl_welore_motion` VALUES(88, '任务列表', NULL, 'manual/propagate', 49, 0);
            INSERT INTO `yl_welore_motion` VALUES(89, '完成排行', NULL, 'manual/taskleaderboard', 49, 1);
            INSERT INTO `yl_welore_motion` VALUES(90, '礼物列表', NULL, 'marketing/friendly', 18, 0);
            INSERT INTO `yl_welore_motion` VALUES(91, '排行列表', NULL, NULL, 4, 3);
            INSERT INTO `yl_welore_motion` VALUES(92, '发帖排行', NULL, 'user/speak_ranking', 91, 0);
            INSERT INTO `yl_welore_motion` VALUES(93, '积分排行', NULL, 'user/fraction_ranking', 91, 1);
            INSERT INTO `yl_welore_motion` VALUES(94, '贝壳排行', NULL, 'user/conch_ranking', 91, 2);
            INSERT INTO `yl_welore_motion` VALUES(95, '赠礼排行', NULL, 'user/gift_ranking', 91, 3);
            INSERT INTO `yl_welore_motion` VALUES(96, '收礼排行', NULL, 'user/receiving_ranking', 91, 4);
            INSERT INTO `yl_welore_motion` VALUES(97, '邀请排行', NULL, 'user/engage', 91, 5);
            INSERT INTO `yl_welore_motion` VALUES(98, '用户列表', NULL, 'user/index', 23, 0);
            INSERT INTO `yl_welore_motion` VALUES(99, '订阅消息', NULL, 'systems/inform', 40, 0);
            INSERT INTO `yl_welore_motion` VALUES(100, '推送消息', NULL, 'unlawful/messagePush', 40, 1);
            INSERT INTO `yl_welore_motion` VALUES(101, '存储设置', NULL, 'cloud/storage', 82, 3);
            INSERT INTO `yl_welore_motion` VALUES(102, '公众通知', NULL, NULL, 81, 4);
            INSERT INTO `yl_welore_motion` VALUES(103, '模板消息', NULL, 'people/mention', 102, 0);
            INSERT INTO `yl_welore_motion` VALUES(104, '微信配置', NULL, 'people/config', 102, 1);
            INSERT INTO `yl_welore_motion` VALUES(105, '私信列表', NULL, 'tissue/privateLetter', 23, 4);
            INSERT INTO `yl_welore_motion` VALUES(106, '截屏记录', NULL, 'tissue/peeping', 23, 5);
            INSERT INTO `yl_welore_motion` VALUES(107, '内容安全', NULL, 'unlawful/words', 53, 0);
            INSERT INTO `yl_welore_motion` VALUES(108, '拦截手机', NULL, 'unlawful/blockingPhone', 53, 1);
            INSERT INTO `yl_welore_motion` VALUES(109, '一键拨号', NULL, 'people/dial', 81, 1);
            INSERT INTO `yl_welore_motion` VALUES(110, '内容点评', NULL, NULL, 81, 5);
            INSERT INTO `yl_welore_motion` VALUES(111, '点评列表', NULL, 'people/correct', 110, 0);
            INSERT INTO `yl_welore_motion` VALUES(112, '常用语句', NULL, 'people/often', 110, 1);
            INSERT INTO `yl_welore_motion` VALUES(113, '点评设置', NULL, 'people/setup', 110, 2);
            INSERT INTO `yl_welore_motion` VALUES(114, '失物招领', NULL, NULL, 81, 6);
            INSERT INTO `yl_welore_motion` VALUES(115, '物品分类', NULL, 'people/lost_type', 114, 0);
            INSERT INTO `yl_welore_motion` VALUES(116, '招领列表', NULL, 'people/lost_found', 114, 1);
            INSERT INTO `yl_welore_motion` VALUES(117, '招领回复', NULL, 'people/lost_reply', 114, 2);
            INSERT INTO `yl_welore_motion` VALUES(118, '置顶列表', NULL, 'people/lost_top', 114, 3);
            INSERT INTO `yl_welore_motion` VALUES(119, '招领配置', NULL, 'people/lost_config', 114, 4);
            INSERT INTO `yl_welore_motion` VALUES(120, '同城信息', NULL, NULL, 81, 7);
            INSERT INTO `yl_welore_motion` VALUES(121, '商家分类', NULL, 'pluto/merchant_type', 120, 0);
            INSERT INTO `yl_welore_motion` VALUES(122, '商家列表', NULL, 'pluto/merchant_list', 120, 1);
            INSERT INTO `yl_welore_motion` VALUES(123, '信息设置', NULL, 'pluto/merchant_config', 120, 6);
            INSERT INTO `yl_welore_motion` VALUES(124, '小秘密设置', NULL, 'stealth/janitor_setting', 75, 2);
            INSERT INTO `yl_welore_motion` VALUES(125, '帖子列表', NULL, NULL, 7, 0);
            INSERT INTO `yl_welore_motion` VALUES(126, '店员列表', NULL, 'pluto/merchant_sales_clerk', 120, 2);
            INSERT INTO `yl_welore_motion` VALUES(127, '商品列表', NULL, 'pluto/merchant_commodity', 120, 3);
            INSERT INTO `yl_welore_motion` VALUES(128, '订单列表', NULL, 'pluto/merchant_commodity_order', 120, 4);
            INSERT INTO `yl_welore_motion` VALUES(129, '核销列表', NULL, 'pluto/merchant_commodity_order_redeem', 120, 5);
            INSERT INTO `yl_welore_motion` VALUES(130, '商品规格', NULL, 'tedious/shop_specs', 76, 1);
            INSERT INTO `yl_welore_motion` VALUES(131, '视频解析', NULL, NULL, 81, 8);
            INSERT INTO `yl_welore_motion` VALUES(132, '解析列表', NULL, 'resolve/parse_list', 131, 0);
            INSERT INTO `yl_welore_motion` VALUES(133, '适配列表', NULL, 'resolve/adaptation_parse_list', 131, 1);
            INSERT INTO `yl_welore_motion` VALUES(134, '二手交易', NULL, NULL, 81, 9);
            INSERT INTO `yl_welore_motion` VALUES(135, '分类目录', NULL, 'resale/used_goods_type', 134, 0);
            INSERT INTO `yl_welore_motion` VALUES(136, '发布信息', NULL, 'resale/used_goods_found', 134, 1);
            INSERT INTO `yl_welore_motion` VALUES(137, '交流互动', NULL, 'resale/used_goods_reply', 134, 2);
            INSERT INTO `yl_welore_motion` VALUES(138, '置顶列表', NULL, 'resale/used_goods_top', 134, 3);
            INSERT INTO `yl_welore_motion` VALUES(139, '配置选项', NULL, 'resale/used_goods_config', 134, 4);
            INSERT INTO `yl_welore_motion` VALUES(140, '求职招聘', NULL, NULL, 81, 10);
            INSERT INTO `yl_welore_motion` VALUES(141, '岗位类型', NULL, 'career/employment_type', 140, 0);
            INSERT INTO `yl_welore_motion` VALUES(142, '招聘列表', NULL, 'career/employment_found', 140, 1);
            INSERT INTO `yl_welore_motion` VALUES(143, '置顶列表', NULL, 'career/employment_top', 140, 2);
            INSERT INTO `yl_welore_motion` VALUES(144, '招聘配置', NULL, 'career/employment_config', 140, 3);
            INSERT INTO `yl_welore_motion` VALUES(145, '热帖管理', NULL, NULL, 7, 1);
            INSERT INTO `yl_welore_motion` VALUES(146, '横幅广告', NULL, 'shield/thread_banner_promotions', 145, 0);
            INSERT INTO `yl_welore_motion` VALUES(147, '热帖置顶', NULL, 'shield/thread_popularity', 145, 1);
            INSERT INTO `yl_welore_motion` VALUES(148, '热帖配置', NULL, 'shield/thread_config', 145, 2);
            INSERT INTO `yl_welore_motion` VALUES(149, '幸运抽奖', NULL, NULL, 81, 11);
            INSERT INTO `yl_welore_motion` VALUES(150, '抽奖列表', NULL, 'tedious/contest_list', 149, 0);
            INSERT INTO `yl_welore_motion` VALUES(151, '参与列表', NULL, 'tedious/involved_contest', 149, 1);
            INSERT INTO `yl_welore_motion` VALUES(152, '抽奖配置', NULL, 'tedious/contest_config', 149, 2);
            INSERT INTO `yl_welore_motion` VALUES(153, '短剧视频', NULL, NULL, 48, 5);
            INSERT INTO `yl_welore_motion` VALUES(154, '短剧类型', NULL, 'dramas/micro_series_type', 153, 0);
            INSERT INTO `yl_welore_motion` VALUES(155, '短剧信息', NULL, 'dramas/micro_series_info', 153, 1);
            INSERT INTO `yl_welore_motion` VALUES(156, '短剧视频', NULL, 'dramas/micro_series_content', 153, 2);
            INSERT INTO `yl_welore_motion` VALUES(157, '短剧付费', NULL, 'dramas/micro_series_content_unlock', 153, 3);
            INSERT INTO `yl_welore_motion` VALUES(158, '短剧评论', NULL, 'dramas/micro_series_review', 153, 4);
            INSERT INTO `yl_welore_motion` VALUES(159, '短剧配置', NULL, 'dramas/micro_series_config', 153, 5);
            INSERT INTO `yl_welore_motion` VALUES(160, '通用存储', NULL, 'leading/compliant', 81, 12);
            INSERT INTO `yl_welore_motion` VALUES(161, '123云盘', NULL, 'leading/pan123', 81, 13);
EOT;
pdo_run($sql);