.container {
    width: 100%;
    height: 75vh;
    border: 2px solid #ccc;
    box-sizing: border-box;
    padding: 0;
    display: flex;
    background: #e7e6e696;
}

.container ::-webkit-scrollbar {
    width: 5px;
    height: 1px;
}

.container ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #c1c1c1;
}

.container ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #ededed;
    border-radius: 10px;
}

/* ========  left  ======== */

.container .container-left {
    width: 20%;
    margin: 0.2% 0.1% 0.2% 0;
    border: 1px solid #e7e6e6;
    display: flex;
    flex-direction: column;
    background: #fff;
}

.container .container-left .container-left-top {
    box-sizing: border-box;
    display: flex;
    width: 100%;
    height: 40px;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #e7e6e6;
    font-weight: bold;
}

.container .container-left .container-left-top .prompt {
    font-size: 11px;
    margin-left: 10px;
    font-weight: bold;
}

.container .container-left .container-left-content {
    height: calc(75vh - 50px);
    overflow-y: auto;
    font-size: 14px;
}

.container .container-left .container-left-content .container-left-content-draggable {
    width: 100% !important;
}

.container .container-left .container-left-content .container-left-content-draggable > span {
    width: 100% !important;
    display: flex;
    box-sizing: border-box;
    flex-wrap: wrap;
    align-content: flex-start;
}

.container .container-left .container-left-content .controlName {
    width: 48%;
    height: 34px;
    padding: 5px;
    border: 1px solid #ededed;
    margin: 6% 1%;
    border-radius: 2px;
    color: #777;
    display: flex;
    justify-content: center;
    flex-shrink: 0;
}

.container .container-left .container-left-content .controlName:hover {
    color: #0377ed;
    cursor: move;
    border: 1px solid #0377ed;
}


/* ========  center  ======== */

.container .container-center {
    width: 60%;
    margin: 0.2% 0.1%;
    border: 1px solid #e7e6e6;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    background: #fff;
}

.container .container-center .container-center-top {
    box-sizing: border-box;
    display: flex;
    width: 100%;
    height: 40px;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e7e6e6;
}

.container-center-top-left {
    width: 50%;
    display: flex;
    justify-content: flex-start;
    padding-left: 5px;
}

.container-center-top-right {
    width: 50%;
    display: flex;
    justify-content: flex-end;
    padding-right: 5px;
}

.container-center-top-button {
    background: #fff;
    padding: 1px 5px;
    font-size: 12px;
    line-height: 1.5;
    border: 1px solid #dadada;
    margin: 0 2px;
}

.container-center-top-button:hover {
    color: #333;
}

.container-center-top-button-success {
    padding: 3px 6px;
    background-color: #5eb95e;
    color: #fff;
}

.container-center-top-button-success:hover {
    color: #fff;
}

.container .container-center .container-center-content {
    height: calc(75vh - 45px);
    overflow-y: auto;
    font-size: 14px;
    padding: 5px 8px 0 8px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

.container .container-center .container-center-content .container-center-content-draggable {
    min-height: calc(75vh - 60px);
}

.container .container-center .container-center-content .container-center-content-draggable > span {
    display: flex;
    flex-direction: column;
    width: 100%;
    min-height: calc(75vh - 60px);
}

.content-form {
    display: flex;
    width: 100%;
    padding: 10px 10px 10px 5px;
    background: #f9fcff;
    margin: 3px 0;
    border: #acdaec 2px solid;
    position: relative;
}

.content-form-activate {
    border: #19B2EEFF 2px dashed;
}

.content-form-activate-close {
    position: absolute;
    right: -2px;
    bottom: -2px;
    width: 25px;
    height: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #19B2EEFF;
    border: 2px solid #19B2EEFF;
    padding: 1px;
    cursor: pointer;
}

.content-form-activate-close-child {
    font-size: 18px;
    color: #fff;
}


.content-form-child {
    width: 100%;
    display: flex;
}

.content-form-child-1 {
    flex-direction: column;
}

.content-form-child-1 > .content-label, .content-form-child-2 > .content-label {
    justify-content: flex-start;
}

.required {
    color: #f00;
    margin: 0 2px;
}

.content-label {
    font-weight: normal !important;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 5px 10px 0 0;
}

.content-box {
    width: 100%;
}

.content-box .text {
    margin-left: 10px;
}

.content-box-textarea {
    height: 100px;
}

.content-box-input, .content-box-textarea, .content-box-select {
    resize: none;
    border: 1px solid #c2cad8 !important;
    padding: 6px 12px !important;
    background: #fff !important;
}

.content-box-input:focus, .content-box-textarea:focus, .content-box-select:focus, .container-center-top-button:focus, .container-right-top-button:focus {
    border-color: #66afe9 !important;
    outline: 0 !important;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6) !important;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, .6) !important;
}

.content-box-radio {
    width: 100%;
    min-height: 20px;
    font-weight: normal;
}

.content-box-radio-label {
    cursor: pointer;
}

.content-box-radio .content-box-radio-input {
    position: relative;
    top: 1px;
}

.content-box-checkbox {
    width: 100%;
    min-height: 20px;
    font-weight: normal;
}

.content-box-checkbox-label {
    cursor: pointer;
}

.content-box-checkbox .content-box-checkbox-input {
    position: relative;
    top: 1px;
}

.content-box-image-attach > span {
    position: absolute;
    top: 48%;
    padding-left: 20px;
    font-size: 12px;
    font-weight: bold;
    color: #f37070;
}

.content-box-image {
    font-size: 80px;
    color: #636363;
    cursor: pointer;
}

.content-box-button-flex {
    display: flex;
}

.content-box-button {
    width: 80%;
    height: 35px;
    margin: 0 auto;
    text-align: center;
}


/* ========  right  ======== */

.container .container-right {
    width: 20%;
    margin: 0.2% 0 0.2% 0.1%;
    border: 1px solid #e7e6e6;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    background: #fff;
}

.container .container-right .container-right-top {
    box-sizing: border-box;
    display: flex;
    width: 100%;
    height: 40px;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #e7e6e6;
    position: relative;
    font-weight: bold;
}

.container-right-top-button {
    background: #fff;
    padding: 1px 5px;
    font-size: 12px;
    line-height: 1.5;
    border: 1px solid #dadada;
    position: absolute;
    right: 4px;
}

.container-right-top-button:hover {
    color: #333;
}

.container .container-right .container-right-content {
    height: calc(75vh - 45px);
    overflow-y: auto;
    font-size: 14px;
    padding: 5px 5px 0 5px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

.container .container-right .container-right-form {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 5px;
    margin: 3px 0;
}

.container-right-form-radio {
    display: flex;
}

.container-right-form-radio-text {
    margin-left: 3px !important;
}

.content-box-remind {
    color: #5a99b9;
}

.container-right-form-label {
    justify-content: flex-start;
}

.container-right-form-label > span {
    font-size: 16px;
    font-weight: bold;
    display: flex;
}

.container-right-form-select {
    padding: 5px 8px !important;
}