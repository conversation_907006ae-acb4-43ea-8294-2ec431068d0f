window.wangEditor.fullscreen = {
    init: function (editorSelector) {
        $(editorSelector + " .w-e-toolbar").append(
            '<div class="w-e-menu"><' +
            'span class="_wangEditor_btn_fullscreen" onclick="window.wangEditor.fullscreen.toggleFullscreen(\'' + editorSelector + '\');">' +
            '<span class="am-icon-expand" title="全屏"></span>' +
            '</span>' +
            '</div>');
    },
    toggleFullscreen: function (editorSelector) {
        $(editorSelector).toggleClass('fullscreen-editor');
        if ($(editorSelector + ' ._wangEditor_btn_fullscreen').html() == '<span class="am-icon-expand" title="全屏"></span>') {
            $(editorSelector + ' ._wangEditor_btn_fullscreen').html('<span class="am-icon-compress" title="退出全屏"></span>');
        } else {
            $(editorSelector + ' ._wangEditor_btn_fullscreen').html('<span class="am-icon-expand" title="全屏"></span>');
        }
    }
};

window.wangEditor.secretSpace = {
    init: function (editorSelector) {
        $(editorSelector + " .w-e-toolbar").append(
            '<div class="w-e-menu">' +
            '<span title="插入要隐藏的内容" style="cursor:pointer;" onclick="appendTextStealth();">' +
            '<span class="am-icon-file-text-o"></span>' +
            '</span>' +
            '</div>'
        );
        $(editorSelector + " .w-e-toolbar").append(
            '<div class="w-e-menu">' +
            '<span title="插入自定义表情" style="cursor:pointer;" onclick="selectEmoji();">' +
            '<span class="am-icon-meh-o"></span>' +
            '</span>' +
            '</div>'
        );
    }
};

// 添加HTTP图片处理插件
window.wangEditor.httpImage = {
    // 处理HTML内容中的HTTP图片
    processHtml: function(html, editor) {
        if (!html || window.location.protocol !== 'https:') return html;

        var tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        
        var images = tempDiv.getElementsByTagName('img');
        var hasChanges = false;
        
        Array.from(images).forEach(function(img) {
            var src = img.getAttribute('src');
            if (src && src.startsWith('http://') && !img.hasAttribute('data-src')) {
                img.setAttribute('data-src', src);
                var proxyUrl = './index.php?s=/urge/proxy/proxy_resource';
                var displayUrl = proxyUrl + '&url=' + encodeURIComponent(src);
                img.setAttribute('src', displayUrl);
                hasChanges = true;
            }
        });
        
        return hasChanges ? tempDiv.innerHTML : html;
    },

    init: function(editor) {
        if (!editor) return;
        
        // 设置图片处理函数
        editor.customConfig.linkImgCallback = function(url) {
            if (window.location.protocol === 'https:' && url.startsWith('http://')) {
                var proxyUrl = './index.php?s=/urge/proxy/proxy_resource';
                return proxyUrl + '&url=' + encodeURIComponent(url);
            }
            return url;
        };

        // 监听内容变化
        editor.customConfig.onchange = function(html) {
            if (window.location.protocol !== 'https:') return;
            var processedHtml = window.wangEditor.httpImage.processHtml(html, editor);
            if (processedHtml !== html && editor.txt) {
                editor.txt.html(processedHtml);
            }
        };

        // 处理粘贴内容
        editor.customConfig.pasteTextHandle = function(content) {
            return window.wangEditor.httpImage.processHtml(content, editor);
        };
    },

    // 在编辑器创建完成后初始化额外功能
    initAfterCreate: function(editor) {
        if (!editor || !editor.txt) return;

        // 保存原始的html方法
        var originalHtml = editor.txt.html;
        
        // 重写html方法
        editor.txt.html = function(val) {
            if (typeof val === 'undefined') {
                // 获取内容时还原为原始URL
                var content = originalHtml.call(this);
                var tempDiv = document.createElement('div');
                tempDiv.innerHTML = content;
                
                var images = tempDiv.getElementsByTagName('img');
                Array.from(images).forEach(function(img) {
                    var originalSrc = img.getAttribute('data-src');
                    if (originalSrc) {
                        img.setAttribute('src', originalSrc);
                        img.removeAttribute('data-src');
                    }
                });
                
                return tempDiv.innerHTML;
            }
            
            // 设置内容时处理HTTP图片
            if (val && typeof val === 'string') {
                val = window.wangEditor.httpImage.processHtml(val, editor);
            }
            return originalHtml.call(this, val);
        };

        // 处理初始内容
        var initialContent = editor.txt.html();
        var processedContent = window.wangEditor.httpImage.processHtml(initialContent, editor);
        if (processedContent !== initialContent) {
            editor.txt.html(processedContent);
        }
    }
};

// 扩展wangEditor原型
var originalCreate = window.wangEditor.prototype.create;
window.wangEditor.prototype.create = function() {
    // 在创建前初始化基本配置
    window.wangEditor.httpImage.init(this);
    
    // 调用原始的create方法
    var result = originalCreate.call(this);
    
    // 在创建后初始化额外功能
    window.wangEditor.httpImage.initAfterCreate(this);
    
    return result;
};